{"version": 3, "file": "operationOptions.js", "sourceRoot": "", "sources": ["../../src/operationOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;AAuDlC;;;;GAIG;AACH,MAAM,UAAU,oCAAoC,CAClD,IAAO;IAEP,MAAM,EAAE,cAAc,EAAE,cAAc,KAA2B,IAAI,EAA1B,iBAAiB,UAAK,IAAI,EAA/D,oCAAwD,CAAO,CAAC;IAEtE,IAAI,MAAM,GAAuB,iBAAiB,CAAC;IAEnD,IAAI,cAAc,EAAE;QAClB,MAAM,mCAAQ,MAAM,GAAK,cAAc,CAAE,CAAC;KAC3C;IAED,IAAI,cAAc,EAAE;QAClB,MAAM,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAC;QACtD,+HAA+H;QAC/H,MAAM,CAAC,WAAW,GAAI,cAAsB,aAAtB,cAAc,uBAAd,cAAc,CAAU,WAAW,CAAC;KAC3D;IAED,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { RequestOptionsBase, TransferProgressEvent } from \"./webResource\";\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { HttpOperationResponse } from \"./httpOperationResponse\";\nimport { OperationTracingOptions } from \"@azure/core-tracing\";\n\n/**\n * The base options type for all operations.\n */\nexport interface OperationOptions {\n  /**\n   * The signal which can be used to abort requests.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Options used when creating and sending HTTP requests for this operation.\n   */\n  requestOptions?: OperationRequestOptions;\n  /**\n   * Options used when tracing is enabled.\n   */\n  tracingOptions?: OperationTracingOptions;\n}\n\n/**\n * Options that allow configuring the handling of HTTP requests made by an SDK operation.\n */\nexport interface OperationRequestOptions {\n  /**\n   * User defined custom request headers that will be applied before the request is sent.\n   */\n  customHeaders?: { [key: string]: string };\n\n  /**\n   * The number of milliseconds a request can take before automatically being terminated.\n   */\n  timeout?: number;\n\n  /**\n   * Callback which fires upon upload progress.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Callback which fires upon download progress.\n   */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n  /**\n   * Whether or not the HttpOperationResponse should be deserialized. If this is undefined, then the\n   * HttpOperationResponse should be deserialized.\n   */\n  shouldDeserialize?: boolean | ((response: HttpOperationResponse) => boolean);\n}\n\n/**\n * Converts an OperationOptions to a RequestOptionsBase\n *\n * @param opts - OperationOptions object to convert to RequestOptionsBase\n */\nexport function operationOptionsToRequestOptionsBase<T extends OperationOptions>(\n  opts: T\n): RequestOptionsBase {\n  const { requestOptions, tracingOptions, ...additionalOptions } = opts;\n\n  let result: RequestOptionsBase = additionalOptions;\n\n  if (requestOptions) {\n    result = { ...result, ...requestOptions };\n  }\n\n  if (tracingOptions) {\n    result.tracingContext = tracingOptions.tracingContext;\n    // By passing spanOptions if they exist at runtime, we're backwards compatible with @azure/core-tracing@preview.13 and earlier.\n    result.spanOptions = (tracingOptions as any)?.spanOptions;\n  }\n\n  return result;\n}\n"]}