{"version": 3, "file": "azureCliCredential.js", "sourceRoot": "", "sources": ["../../../src/credentials/azureCliCredential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAC/E,OAAO,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAExE,OAAO,EAAE,0BAA0B,EAAE,MAAM,WAAW,CAAC;AACvD,OAAO,aAAa,MAAM,eAAe,CAAC;AAC1C,OAAO,EACL,yBAAyB,EACzB,iCAAiC,GAClC,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD;;;GAGG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG;IACpC;;OAEG;IACH,iBAAiB;QACf,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;YAChC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;gBAC3B,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;aACrF;YACD,OAAO,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC;SAC/B;aAAM;YACL,OAAO,MAAM,CAAC;SACf;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,sBAAsB,CAC1B,QAAgB,EAChB,QAAiB;QAEjB,IAAI,aAAa,GAAa,EAAE,CAAC;QACjC,IAAI,QAAQ,EAAE;YACZ,aAAa,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;SACxC;QACD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI;gBACF,aAAa,CAAC,QAAQ,CACpB,IAAI,EACJ;oBACE,SAAS;oBACT,kBAAkB;oBAClB,UAAU;oBACV,MAAM;oBACN,YAAY;oBACZ,QAAQ;oBACR,GAAG,aAAa;iBACjB,EACD,EAAE,GAAG,EAAE,sBAAsB,CAAC,iBAAiB,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,EAChE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;oBACxB,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;gBACrD,CAAC,CACF,CAAC;aACH;YAAC,OAAO,GAAQ,EAAE;gBACjB,MAAM,CAAC,GAAG,CAAC,CAAC;aACb;QACH,CAAC,CAAC,CAAC;IACL,CAAC;CACF,CAAC;AAEF,MAAM,MAAM,GAAG,gBAAgB,CAAC,oBAAoB,CAAC,CAAC;AAEtD;;;;;GAKG;AACH,MAAM,OAAO,kBAAkB;IAI7B;;;;;;;OAOG;IACH,YAAY,OAAmC;QAC7C,IAAI,CAAC,QAAQ,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,CAAC;QAClC,IAAI,CAAC,4BAA4B,GAAG,iCAAiC,CACnE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,0BAA0B,CACpC,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,QAAQ,CACnB,MAAyB,EACzB,UAA2B,EAAE;QAE7B,MAAM,QAAQ,GAAG,yBAAyB,CACxC,IAAI,CAAC,QAAQ,EACb,OAAO,EACP,IAAI,CAAC,4BAA4B,CAClC,CAAC;QAEF,MAAM,KAAK,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC9D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,KAAK,EAAE,CAAC,CAAC;QACjD,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAChC,MAAM,QAAQ,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAEzC,OAAO,aAAa,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,WAAW,EAAE,OAAO,EAAE,KAAK,IAAI,EAAE;;YACrF,IAAI;gBACF,MAAM,GAAG,GAAG,MAAM,sBAAsB,CAAC,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACpF,MAAM,aAAa,GAAG,MAAA,GAAG,CAAC,MAAM,0CAAE,KAAK,CAAC,0BAA0B,CAAC,CAAC;gBACpE,MAAM,YAAY,GAAG,CAAA,MAAA,GAAG,CAAC,MAAM,0CAAE,KAAK,CAAC,kBAAkB,CAAC,KAAI,CAAC,aAAa,CAAC;gBAC7E,MAAM,iBAAiB,GACrB,CAAA,MAAA,GAAG,CAAC,MAAM,0CAAE,KAAK,CAAC,kBAAkB,CAAC,MAAI,MAAA,GAAG,CAAC,MAAM,0CAAE,UAAU,CAAC,wBAAwB,CAAC,CAAA,CAAC;gBAE5F,IAAI,iBAAiB,EAAE;oBACrB,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,kLAAkL,CACnL,CAAC;oBACF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;oBACjD,MAAM,KAAK,CAAC;iBACb;gBACD,IAAI,YAAY,EAAE;oBAChB,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,2FAA2F,CAC5F,CAAC;oBACF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;oBACjD,MAAM,KAAK,CAAC;iBACb;gBACD,IAAI;oBACF,MAAM,YAAY,GAAG,GAAG,CAAC,MAAM,CAAC;oBAChC,MAAM,QAAQ,GAA+C,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;oBACtF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;oBAC5C,MAAM,WAAW,GAAG;wBAClB,KAAK,EAAE,QAAQ,CAAC,WAAW;wBAC3B,kBAAkB,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE;qBAC3D,CAAC;oBACF,OAAO,WAAW,CAAC;iBACpB;gBAAC,OAAO,CAAM,EAAE;oBACf,IAAI,GAAG,CAAC,MAAM,EAAE;wBACd,MAAM,IAAI,0BAA0B,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;qBAClD;oBACD,MAAM,CAAC,CAAC;iBACT;aACF;YAAC,OAAO,GAAQ,EAAE;gBACjB,MAAM,KAAK,GACT,GAAG,CAAC,IAAI,KAAK,4BAA4B;oBACvC,CAAC,CAAC,GAAG;oBACL,CAAC,CAAC,IAAI,0BAA0B,CAC3B,GAAa,CAAC,OAAO,IAAI,yDAAyD,CACpF,CAAC;gBACR,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;gBACjD,MAAM,KAAK,CAAC;aACb;QACH,CAAC,CAAC,CAAC;IACL,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport { credentialLogger, formatError, formatSuccess } from \"../util/logging\";\nimport { ensureValidScope, getScopeResource } from \"../util/scopeUtils\";\nimport { AzureCliCredentialOptions } from \"./azureCliCredentialOptions\";\nimport { CredentialUnavailableError } from \"../errors\";\nimport child_process from \"child_process\";\nimport {\n  processMultiTenantRequest,\n  resolveAddionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { tracingClient } from \"../util/tracing\";\n\n/**\n * Mockable reference to the CLI credential cliCredentialFunctions\n * @internal\n */\nexport const cliCredentialInternals = {\n  /**\n   * @internal\n   */\n  getSafeWorkingDir(): string {\n    if (process.platform === \"win32\") {\n      if (!process.env.SystemRoot) {\n        throw new Error(\"Azure CLI credential expects a 'SystemRoot' environment variable\");\n      }\n      return process.env.SystemRoot;\n    } else {\n      return \"/bin\";\n    }\n  },\n\n  /**\n   * Gets the access token from Azure CLI\n   * @param resource - The resource to use when getting the token\n   * @internal\n   */\n  async getAzureCliAccessToken(\n    resource: string,\n    tenantId?: string\n  ): Promise<{ stdout: string; stderr: string; error: Error | null }> {\n    let tenantSection: string[] = [];\n    if (tenantId) {\n      tenantSection = [\"--tenant\", tenantId];\n    }\n    return new Promise((resolve, reject) => {\n      try {\n        child_process.execFile(\n          \"az\",\n          [\n            \"account\",\n            \"get-access-token\",\n            \"--output\",\n            \"json\",\n            \"--resource\",\n            resource,\n            ...tenantSection,\n          ],\n          { cwd: cliCredentialInternals.getSafeWorkingDir(), shell: true },\n          (error, stdout, stderr) => {\n            resolve({ stdout: stdout, stderr: stderr, error });\n          }\n        );\n      } catch (err: any) {\n        reject(err);\n      }\n    });\n  },\n};\n\nconst logger = credentialLogger(\"AzureCliCredential\");\n\n/**\n * This credential will use the currently logged-in user login information\n * via the Azure CLI ('az') commandline tool.\n * To do so, it will read the user access token and expire time\n * with Azure CLI command \"az account get-access-token\".\n */\nexport class AzureCliCredential implements TokenCredential {\n  private tenantId?: string;\n  private additionallyAllowedTenantIds: string[];\n\n  /**\n   * Creates an instance of the {@link AzureCliCredential}.\n   *\n   * To use this credential, ensure that you have already logged\n   * in via the 'az' tool using the command \"az login\" from the commandline.\n   *\n   * @param options - Options, to optionally allow multi-tenant requests.\n   */\n  constructor(options?: AzureCliCredentialOptions) {\n    this.tenantId = options?.tenantId;\n    this.additionallyAllowedTenantIds = resolveAddionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants\n    );\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  public async getToken(\n    scopes: string | string[],\n    options: GetTokenOptions = {}\n  ): Promise<AccessToken> {\n    const tenantId = processMultiTenantRequest(\n      this.tenantId,\n      options,\n      this.additionallyAllowedTenantIds\n    );\n\n    const scope = typeof scopes === \"string\" ? scopes : scopes[0];\n    logger.getToken.info(`Using the scope ${scope}`);\n    ensureValidScope(scope, logger);\n    const resource = getScopeResource(scope);\n\n    return tracingClient.withSpan(`${this.constructor.name}.getToken`, options, async () => {\n      try {\n        const obj = await cliCredentialInternals.getAzureCliAccessToken(resource, tenantId);\n        const specificScope = obj.stderr?.match(\"(.*)az login --scope(.*)\");\n        const isLoginError = obj.stderr?.match(\"(.*)az login(.*)\") && !specificScope;\n        const isNotInstallError =\n          obj.stderr?.match(\"az:(.*)not found\") || obj.stderr?.startsWith(\"'az' is not recognized\");\n\n        if (isNotInstallError) {\n          const error = new CredentialUnavailableError(\n            \"Azure CLI could not be found. Please visit https://aka.ms/azure-cli for installation instructions and then, once installed, authenticate to your Azure account using 'az login'.\"\n          );\n          logger.getToken.info(formatError(scopes, error));\n          throw error;\n        }\n        if (isLoginError) {\n          const error = new CredentialUnavailableError(\n            \"Please run 'az login' from a command prompt to authenticate before using this credential.\"\n          );\n          logger.getToken.info(formatError(scopes, error));\n          throw error;\n        }\n        try {\n          const responseData = obj.stdout;\n          const response: { accessToken: string; expiresOn: string } = JSON.parse(responseData);\n          logger.getToken.info(formatSuccess(scopes));\n          const returnValue = {\n            token: response.accessToken,\n            expiresOnTimestamp: new Date(response.expiresOn).getTime(),\n          };\n          return returnValue;\n        } catch (e: any) {\n          if (obj.stderr) {\n            throw new CredentialUnavailableError(obj.stderr);\n          }\n          throw e;\n        }\n      } catch (err: any) {\n        const error =\n          err.name === \"CredentialUnavailableError\"\n            ? err\n            : new CredentialUnavailableError(\n                (err as Error).message || \"Unknown error while trying to retrieve the access token\"\n              );\n        logger.getToken.info(formatError(scopes, error));\n        throw error;\n      }\n    });\n  }\n}\n"]}