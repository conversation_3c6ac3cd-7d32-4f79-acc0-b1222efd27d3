{"version": 3, "file": "disableResponseDecompressionPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/disableResponseDecompressionPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EACL,iBAAiB,GAIlB,MAAM,iBAAiB,CAAC;AAIzB;;;GAGG;AACH,MAAM,UAAU,kCAAkC;IAChD,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,EAAE,EAAE;YACnE,OAAO,IAAI,kCAAkC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACrE,CAAC;KACF,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,MAAM,OAAO,kCAAmC,SAAQ,iBAAiB;IACvE;;;;;OAKG;IACH,uCAAuC;IACvC,wEAAwE;IACxE,YAAY,UAAyB,EAAE,OAA6B;QAClE,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IAC7B,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,WAAW,CAAC,OAAoB;QAC3C,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAC;QACnC,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"./requestPolicy\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResource } from \"../webResource\";\n\n/**\n * Returns a request policy factory that can be used to create an instance of\n * {@link DisableResponseDecompressionPolicy}.\n */\nexport function disableResponseDecompressionPolicy(): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new DisableResponseDecompressionPolicy(nextPolicy, options);\n    },\n  };\n}\n\n/**\n * A policy to disable response decompression according to Accept-Encoding header\n * https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Encoding\n */\nexport class DisableResponseDecompressionPolicy extends BaseRequestPolicy {\n  /**\n   * Creates an instance of DisableResponseDecompressionPolicy.\n   *\n   * @param nextPolicy -\n   * @param options -\n   */\n  // The parent constructor is protected.\n  /* eslint-disable-next-line @typescript-eslint/no-useless-constructor */\n  constructor(nextPolicy: RequestPolicy, options: RequestPolicyOptions) {\n    super(nextPolicy, options);\n  }\n\n  /**\n   * Sends out request.\n   *\n   * @param request -\n   * @returns\n   */\n  public async sendRequest(request: WebResource): Promise<HttpOperationResponse> {\n    request.decompressResponse = false;\n    return this._nextPolicy.sendRequest(request);\n  }\n}\n"]}