// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
export { isNode } from "./isNode";
export { delay } from "./delay";
export { createAbortablePromise } from "./createAbortablePromise";
export { getRandomIntegerInclusive } from "./random";
export { isObject } from "./object";
export { isError, getErrorMessage } from "./error";
export { computeSha256Hash, computeSha256Hmac } from "./sha256";
export { isDefined, isObjectWithProperties, objectHasProperty } from "./typeGuards";
export { randomUUID } from "./uuidUtils";
//# sourceMappingURL=index.js.map