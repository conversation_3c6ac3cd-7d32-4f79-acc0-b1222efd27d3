/// <reference lib="esnext.asynciterable" />

import * as coreHttp from '@azure/core-http';
import { OperationOptions } from '@azure/core-http';
import { PagedAsyncIterableIterator } from '@azure/core-paging';
import { PageSettings } from '@azure/core-paging';
import { PipelineOptions } from '@azure/core-http';
import { RestResponse } from '@azure/core-http';
import { TokenCredential } from '@azure/core-http';

/**
 * Contains response data for the add operation.
 */
export declare type DigitalTwinModelsAddResponse = DigitalTwinsModelData[] & {
    /**
     * The underlying HTTP response.
     */
    _response: coreHttp.HttpResponse & {
        /**
         * The response body as text (string format)
         */
        bodyAsText: string;
        /**
         * The response body as parsed JSON or XML
         */
        parsedBody: DigitalTwinsModelData[];
    };
};

/**
 * Contains response data for the getById operation.
 */
export declare type DigitalTwinModelsGetByIdResponse = DigitalTwinsModelData & {
    /**
     * The underlying HTTP response.
     */
    _response: coreHttp.HttpResponse & {
        /**
         * The response body as text (string format)
         */
        bodyAsText: string;
        /**
         * The response body as parsed JSON or XML
         */
        parsedBody: DigitalTwinsModelData;
    };
};

/**
 * Contains response data for the list operation.
 */
export declare type DigitalTwinModelsListResponse = PagedDigitalTwinsModelDataCollection & {
    /**
     * The underlying HTTP response.
     */
    _response: coreHttp.HttpResponse & {
        /**
         * The response body as text (string format)
         */
        bodyAsText: string;
        /**
         * The response body as parsed JSON or XML
         */
        parsedBody: PagedDigitalTwinsModelDataCollection;
    };
};

/**
 * Defines headers for DigitalTwins_add operation.
 */
export declare interface DigitalTwinsAddHeaders {
    /**
     * Weak Etag.
     */
    etag?: string;
}

/**
 * Optional parameters.
 */
export declare interface DigitalTwinsAddOptionalParams extends coreHttp.OperationOptions {
    /**
     * Identifies the request in a distributed tracing system.
     */
    traceparent?: string;
    /**
     * Provides vendor-specific trace identification information and is a companion to traceparent.
     */
    tracestate?: string;
    /**
     * Only perform the operation if the entity does not already exist.
     */
    ifNoneMatch?: string;
}

/**
 * Defines headers for DigitalTwins_addRelationship operation.
 */
export declare interface DigitalTwinsAddRelationshipHeaders {
    /**
     * Weak Etag.
     */
    etag?: string;
}

/**
 * Optional parameters.
 */
export declare interface DigitalTwinsAddRelationshipOptionalParams extends coreHttp.OperationOptions {
    /**
     * Identifies the request in a distributed tracing system.
     */
    traceparent?: string;
    /**
     * Provides vendor-specific trace identification information and is a companion to traceparent.
     */
    tracestate?: string;
    /**
     * Only perform the operation if the entity does not already exist.
     */
    ifNoneMatch?: string;
}

/**
 * Contains response data for the addRelationship operation.
 */
export declare type DigitalTwinsAddRelationshipResponse = DigitalTwinsAddRelationshipHeaders & {
    /**
     * The parsed response body.
     */
    body: any;
    /**
     * The underlying HTTP response.
     */
    _response: coreHttp.HttpResponse & {
        /**
         * The response body as text (string format)
         */
        bodyAsText: string;
        /**
         * The response body as parsed JSON or XML
         */
        parsedBody: any;
        /**
         * The parsed HTTP response headers.
         */
        parsedHeaders: DigitalTwinsAddRelationshipHeaders;
    };
};

/**
 * Contains response data for the add operation.
 */
export declare type DigitalTwinsAddResponse = DigitalTwinsAddHeaders & {
    /**
     * The parsed response body.
     */
    body: any;
    /**
     * The underlying HTTP response.
     */
    _response: coreHttp.HttpResponse & {
        /**
         * The response body as text (string format)
         */
        bodyAsText: string;
        /**
         * The response body as parsed JSON or XML
         */
        parsedBody: any;
        /**
         * The parsed HTTP response headers.
         */
        parsedHeaders: DigitalTwinsAddHeaders;
    };
};

/**
 * Client for Azure IoT DigitalTwins API.
 */
export declare class DigitalTwinsClient {
    /**
     * A reference to the auto-generated AzureDigitalTwinsAPI
     */
    private readonly client;
    /**
     * Creates an instance of AzureDigitalTwinsAPI.
     *
     * Example usage:
     * ```ts
     * const { DigitalTwinsClient, ServiceClientCredentials } = require("@azure/digital-twins-core");
     *
     * const client = new DigitalTwinsClient(
     *   "<endpoint>",
     *   new DefaultAzureCredential();
     * );
     * ```
     * @param endpointUrl - The endpoint URL of the service.
     * @param credential - Used to authenticate requests to the service.
     * @param options - Used to configure the service client.
     */
    constructor(endpointUrl: string, credential: TokenCredential, options?: DigitalTwinsClientOptions);
    /**
     * Get a digital twin
     *
     * @param digitalTwinId - The Id of the digital twin.
     * @param options - The operation options
     * @returns The application/json digital twin and the http response.
     */
    getDigitalTwin(digitalTwinId: string, options?: OperationOptions): Promise<DigitalTwinsGetByIdResponse>;
    /**
     * Create or update a digital twin
     *
     * @param digitalTwinId - The Id of the digital twin to create or update.
     * @param digitalTwinJson - The application/json digital twin to create.
     * @param options - Extended operation options including
     *  ifNoneMatch: Only perform the operation if the entity does not already exist.
     * @returns The created application/json digital twin and the http response.
     */
    upsertDigitalTwin(digitalTwinId: string, digitalTwinJson: string, options?: DigitalTwinsAddOptionalParams): Promise<DigitalTwinsAddResponse>;
    /**
     * Update a digital twin using a json patch.
     *
     * @param digitalTwinId - The Id of the digital twin.
     * @param jsonPatch - An update specification described by JSON Patch. Updates to property values
     * and $model elements may happen in the same request. Operations are limited to add, replace and
     * remove.
     * @param options - Extended operation options including
     *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is provided.
     * @returns The http response.
     */
    updateDigitalTwin(digitalTwinId: string, jsonPatch: any, options?: DigitalTwinsUpdateOptionalParams): Promise<DigitalTwinsUpdateResponse>;
    /**
     * Delete a digital twin
     *
     * @param digitalTwinId - The Id of the digital twin to delete.
     * @param options - Extended operation options including
     *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is provided.
     * @returns The http response.
     */
    deleteDigitalTwin(digitalTwinId: string, options?: DigitalTwinsDeleteOptionalParams): Promise<RestResponse>;
    /**
     * Get a component on a digital twin.
     *
     * @param digitalTwinId - The Id of the digital twin.
     * @param componentName - The component being retrieved.
     * @param options - The operation options
     * @returns Json string representation of the component corresponding to the provided componentName and the HTTP response.
     */
    getComponent(digitalTwinId: string, componentName: string, options?: OperationOptions): Promise<DigitalTwinsGetComponentResponse>;
    /**
     * Update properties of a component on a digital twin using a JSON patch.
     *
     * @param digitalTwinId - The Id of the digital twin.
     * @param componentName - The component being updated.
     * @param jsonPatch - The application/json-patch+json operations to be performed on the specified digital twin's component.
     * @param enableUpdate - If true then update of an existing digital twin is enabled.
     * @param options - Extended operation options including
     *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is provided.
     * @returns The http response.
     */
    updateComponent(digitalTwinId: string, componentName: string, jsonPatch: any[], options?: DigitalTwinsUpdateComponentOptionalParams): Promise<DigitalTwinsUpdateComponentResponse>;
    /**
     * Get a relationship on a digital twin.
     *
     * @param digitalTwinId - The Id of the source digital twin.
     * @param relationshipId - The Id of the relationship to retrieve.
     * @param options - The operation options
     * @returns The pageable list of application/json relationships belonging to the specified digital twin and the http response.
     */
    getRelationship(digitalTwinId: string, relationshipId: string, options?: OperationOptions): Promise<DigitalTwinsGetRelationshipByIdResponse>;
    /**
     * Create or update a relationship on a digital twin.
     *
     * @param digitalTwinId - The Id of the source digital twin.
     * @param relationshipId - The Id of the relationship to create.
     * @param relationship - The application/json relationship to be created.
     * @param options - Extended operation options including
     *  ifNoneMatch: Only perform the operation if the entity does not already exist.
     */
    upsertRelationship(digitalTwinId: string, relationshipId: string, relationship: any, options?: DigitalTwinsAddRelationshipOptionalParams): Promise<DigitalTwinsAddRelationshipResponse>;
    /**
     * Updates the properties of a relationship on a digital twin using a JSON patch.
     *
     * @param digitalTwinId - The Id of the digital twin to delete.
     * @param relationshipId - The Id of the relationship to be updated.
     * @param jsonPatch - The application/json-patch+json operations to be performed on the specified digital twin's relationship.
     * @param options - Extended operation options
     *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is provided.
     */
    updateRelationship(digitalTwinId: string, relationshipId: string, jsonPatch: any[], options?: DigitalTwinsUpdateRelationshipOptionalParams): Promise<DigitalTwinsUpdateRelationshipResponse>;
    /**
     * Delete a relationship on a digital twin.
     *
     * @param digitalTwinId - The Id of the source digital twin.
     * @param relationshipId - The Id of the relationship to delete.
     * @param options - The operation options
     *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is
     * @returns The http response.
     */
    deleteRelationship(digitalTwinId: string, relationshipId: string, options?: DigitalTwinsDeleteRelationshipOptionalParams): Promise<RestResponse>;
    /**
     * Deals with the pagination of {@link listRelationships}.
     *
     * @param digitalTwinId - The Id of the digital twin.
     * @param options - Common options for the iterative endpoints.
     * @param continuationState - An object that indicates the position of the paginated request.
     *
     */
    private listRelationshipsPage;
    /**
     * Deals with the iteration of all the available results of {@link listRelationships}.
     * @param options - Common options for the iterative endpoints.
     */
    private listRelationshipsAll;
    /**
     * Retrieve relationships for a digital twin.
     *
     * @param digitalTwinId - The Id of the digital twin.
     */
    listRelationships(digitalTwinId: string, options?: OperationOptions & PageSettings): PagedAsyncIterableIterator<any, DigitalTwinsListRelationshipsResponse>;
    /**
     * Deals with the pagination of {@link listIncomingRelationships}.
     *
     * @param digitalTwinId - The Id of the digital twin.
     * @param options - Common options for the iterative endpoints.
     * @param continuationState - An object that indicates the position of the paginated request.
     *
     */
    private listIncomingRelationshipsPage;
    /**
     * Deals with the iteration of all the available results of {@link listIncomingRelationships}.
     * @param digitalTwinId - The Id of the digital twin.
     * @param options - Common options for the iterative endpoints.
     */
    private listIncomingRelationshipsAll;
    /**
     * Retrieve all incoming relationships for a digital twin.
     *
     * @param digitalTwinId - The Id of the digital twin.
     */
    listIncomingRelationships(digitalTwinId: string, options?: OperationOptions & PageSettings): PagedAsyncIterableIterator<IncomingRelationship, DigitalTwinsListIncomingRelationshipsResponse>;
    /**
     * Publish telemetry from a digital twin, which is then consumed by one or many destination endpoints (subscribers) defined under.
     *
     * @param digitalTwinId - The Id of the digital twin to delete.
     * @param payload - The application/json telemetry payload to be sent.
     * @param messageId - The message Id.
     * @param options - The operation options
     * @returns The http response.
     */
    publishTelemetry(digitalTwinId: string, payload: any, messageId: string, options?: OperationOptions): Promise<RestResponse>;
    /**
     * Publish telemetry from a digital twin's component, which is then consumed by one or many destination endpoints (subscribers) defined under.
     *
     * @param digitalTwinId - The Id of the digital twin to delete.
     * @param componentName - The name of the DTDL component.
     * @param payload - The application/json telemetry payload to be sent.
     * @param messageId - The message Id.
     * @param options - The operation options
     * @returns The http response.
     */
    publishComponentTelemetry(digitalTwinId: string, componentName: string, payload: string, messageId: string, options?: OperationOptions): Promise<RestResponse>;
    /**
     * Get a model, including the model metadata and the model definition.
     *
     * @param modelId - The Id of the model.
     * @param options - Extended operation options including
     *  includeModelDefinition: When true the model definition will be returned as part of the result. Default value: false.
     * @returns The application/json model and the http response.
     */
    getModel(modelId: string, includeModelDefinition?: boolean, options?: OperationOptions): Promise<DigitalTwinModelsGetByIdResponse>;
    /**
     * Deals with the pagination of {@link list}.
     *
     * @param options - Common options for the iterative endpoints.
     * @param continuationState - An object that indicates the position of the paginated request.
     *
     */
    private getModelsPage;
    /**
     * Deals with the iteration of all the available results of {@link list}.
     * @param options - Common options for the iterative endpoints.
     */
    private getModelsAll;
    /**
     * Get the list of models
     *
     * @param dependeciesFor - The model Ids to have dependencies retrieved. If omitted, all models are retrieved.
     * @param includeModelDefinition - Whether to include the model definition in the result. If false, only the model metadata will be returned.
     * @param resultsPerPage - The maximum number of items to retrieve per request. The server may choose to return less than the requested max.
     * @returns A pageable set of application/json models and the http response.
     */
    listModels(dependeciesFor?: string[], includeModelDefinition?: boolean, resultsPerPage?: number, options?: OperationOptions & PageSettings): PagedAsyncIterableIterator<DigitalTwinsModelData, DigitalTwinModelsListResponse>;
    /**
     * Create one or many
     *
     * @param models - The set of models to create. Each string corresponds to exactly one model.
     * @param options - The operation options
     * @returns The created application/json models and the http response.
     */
    createModels(dtdlModels: any[], options?: OperationOptions): Promise<DigitalTwinModelsAddResponse>;
    /**
     * Decommission a model using a json patch.
     * When a model is decommissioned, new digital twins will no longer be able to be
     * defined by this model. However, existing digital twins may continue to use this model.
     * Once a model is decommissioned, it may not be recommissioned.
     *
     * @param modelId - The Id of the model to decommission.
     * property can be replaced.
     * @param options - The operation options
     * @returns The http response.
     *
     */
    decomissionModel(modelId: string, options?: OperationOptions): Promise<RestResponse>;
    /**
     * Delete a model.
     *
     * @param modelId - The Id of the model to delete.
     * @param options - The operation options
     * @returns The http response.
     */
    deleteModel(modelId: string, options?: OperationOptions): Promise<RestResponse>;
    /**
     * Get an event route.
     *
     * @param modelId - The Id of the event route.
     * @param options - The operation options
     * @returns The application/json event route and the http response.
     */
    getEventRoute(eventRouteId: string, options?: OperationOptions): Promise<EventRoutesGetByIdResponse>;
    /**
     * Deals with the pagination of {@link list}.
     *
     * @param options - Common options for the iterative endpoints.
     * @param continuationState - An object that indicates the position of the paginated request.
     *
     */
    private getEventRoutesPage;
    /**
     * Deals with the iteration of all the available results of {@link list}.
     * @param options - Common options for the iterative endpoints.
     */
    private getEventRoutesAll;
    /**
     * List the event routes in a digital twins instance.
     *
     * @param resultsPerPage - The maximum number of items to retrieve per request. The server may choose to return less than
     * the requested max.
     * @returns The application/json event route and the http response.
     */
    listEventRoutes(resultsPerPage?: number, options?: OperationOptions & PageSettings): PagedAsyncIterableIterator<EventRoute, EventRoutesListNextResponse>;
    /**
     * Create or update an event route.
     *
     * @param eventRouteId - The Id of the event route to create or update.
     * @param endpointId - The id of the endpoint this event route is bound to.
     * @param filter - An expression which describes the events which are routed to the endpoint.
     * @param options - The operation options
     * @returns The http response.
     */
    upsertEventRoute(eventRouteId: string, endpointId: string, filter: string, options?: OperationOptions): Promise<RestResponse>;
    /**
     * Delete an event route.
     *
     * @param eventRouteId - The Id of the eventRoute to delete.
     * @param options - The operation options
     * @returns The http response.
     */
    deleteEventRoute(eventRouteId: string, options?: OperationOptions): Promise<RestResponse>;
    /**
     * Deals with the pagination of {@link query}.
     *
     * @param query - The query string, in SQL-like syntax.
     * @param options - Common options for the iterative endpoints.
     * @param continuationState - An object that indicates the position of the paginated request.
     *
     */
    private queryTwinsPage;
    /**
     * Deals with the iteration of all the available results of {@link query}.
     * @param query - The query string, in SQL-like syntax.
     * @param options - Common options for the iterative endpoints.
     */
    private queryTwinsAll;
    /**
     * Query for digital twins.
     *
     * @param query - The query string, in SQL-like syntax.
     * @param resultsPerPage - The maximum number of items to retrieve per request. The server may choose to return less than the requested max.
     * @returns The pageable list of query results.
     */
    queryTwins(query: string, resultsPerPage?: number, options?: OperationOptions & PageSettings): PagedAsyncIterableIterator<any, QueryQueryTwinsResponse>;
}

export declare interface DigitalTwinsClientOptions extends PipelineOptions {
    /**
     * Api Version
     */
    apiVersion?: string;
}

/**
 * Optional parameters.
 */
export declare interface DigitalTwinsDeleteOptionalParams extends coreHttp.OperationOptions {
    /**
     * Identifies the request in a distributed tracing system.
     */
    traceparent?: string;
    /**
     * Provides vendor-specific trace identification information and is a companion to traceparent.
     */
    tracestate?: string;
    /**
     * Only perform the operation if the entity's etag matches one of the etags provided or * is provided.
     */
    ifMatch?: string;
}

/**
 * Optional parameters.
 */
export declare interface DigitalTwinsDeleteRelationshipOptionalParams extends coreHttp.OperationOptions {
    /**
     * Identifies the request in a distributed tracing system.
     */
    traceparent?: string;
    /**
     * Provides vendor-specific trace identification information and is a companion to traceparent.
     */
    tracestate?: string;
    /**
     * Only perform the operation if the entity's etag matches one of the etags provided or * is provided.
     */
    ifMatch?: string;
}

/**
 * Defines headers for DigitalTwins_getById operation.
 */
export declare interface DigitalTwinsGetByIdHeaders {
    /**
     * Weak Etag.
     */
    etag?: string;
}

/**
 * Contains response data for the getById operation.
 */
export declare type DigitalTwinsGetByIdResponse = DigitalTwinsGetByIdHeaders & {
    /**
     * The parsed response body.
     */
    body: any;
    /**
     * The underlying HTTP response.
     */
    _response: coreHttp.HttpResponse & {
        /**
         * The response body as text (string format)
         */
        bodyAsText: string;
        /**
         * The response body as parsed JSON or XML
         */
        parsedBody: any;
        /**
         * The parsed HTTP response headers.
         */
        parsedHeaders: DigitalTwinsGetByIdHeaders;
    };
};

/**
 * Defines headers for DigitalTwins_getComponent operation.
 */
export declare interface DigitalTwinsGetComponentHeaders {
    /**
     * Weak Etag.
     */
    etag?: string;
}

/**
 * Contains response data for the getComponent operation.
 */
export declare type DigitalTwinsGetComponentResponse = DigitalTwinsGetComponentHeaders & {
    /**
     * The parsed response body.
     */
    body: any;
    /**
     * The underlying HTTP response.
     */
    _response: coreHttp.HttpResponse & {
        /**
         * The response body as text (string format)
         */
        bodyAsText: string;
        /**
         * The response body as parsed JSON or XML
         */
        parsedBody: any;
        /**
         * The parsed HTTP response headers.
         */
        parsedHeaders: DigitalTwinsGetComponentHeaders;
    };
};

/**
 * Defines headers for DigitalTwins_getRelationshipById operation.
 */
export declare interface DigitalTwinsGetRelationshipByIdHeaders {
    /**
     * Weak Etag.
     */
    etag?: string;
}

/**
 * Contains response data for the getRelationshipById operation.
 */
export declare type DigitalTwinsGetRelationshipByIdResponse = DigitalTwinsGetRelationshipByIdHeaders & {
    /**
     * The parsed response body.
     */
    body: any;
    /**
     * The underlying HTTP response.
     */
    _response: coreHttp.HttpResponse & {
        /**
         * The response body as text (string format)
         */
        bodyAsText: string;
        /**
         * The response body as parsed JSON or XML
         */
        parsedBody: any;
        /**
         * The parsed HTTP response headers.
         */
        parsedHeaders: DigitalTwinsGetRelationshipByIdHeaders;
    };
};

/**
 * Contains response data for the listIncomingRelationships operation.
 */
export declare type DigitalTwinsListIncomingRelationshipsResponse = IncomingRelationshipCollection & {
    /**
     * The underlying HTTP response.
     */
    _response: coreHttp.HttpResponse & {
        /**
         * The response body as text (string format)
         */
        bodyAsText: string;
        /**
         * The response body as parsed JSON or XML
         */
        parsedBody: IncomingRelationshipCollection;
    };
};

/**
 * Contains response data for the listRelationships operation.
 */
export declare type DigitalTwinsListRelationshipsResponse = RelationshipCollection & {
    /**
     * The underlying HTTP response.
     */
    _response: coreHttp.HttpResponse & {
        /**
         * The response body as text (string format)
         */
        bodyAsText: string;
        /**
         * The response body as parsed JSON or XML
         */
        parsedBody: RelationshipCollection;
    };
};

/**
 * A model definition and metadata for that model.
 */
export declare interface DigitalTwinsModelData {
    /**
     * A language map that contains the localized display names as specified in the model definition.
     */
    displayName?: {
        [propertyName: string]: string;
    };
    /**
     * A language map that contains the localized descriptions as specified in the model definition.
     */
    description?: {
        [propertyName: string]: string;
    };
    /**
     * The id of the model as specified in the model definition.
     */
    id: string;
    /**
     * The time the model was uploaded to the service.
     */
    uploadTime?: Date;
    /**
     * Indicates if the model is decommissioned. Decommissioned models cannot be referenced by newly created digital twins.
     */
    decommissioned?: boolean;
    /**
     * The model definition.
     */
    model?: any;
}

/**
 * Defines headers for DigitalTwins_updateComponent operation.
 */
export declare interface DigitalTwinsUpdateComponentHeaders {
    /**
     * Weak Etag.
     */
    etag?: string;
}

/**
 * Optional parameters.
 */
export declare interface DigitalTwinsUpdateComponentOptionalParams extends coreHttp.OperationOptions {
    /**
     * Identifies the request in a distributed tracing system.
     */
    traceparent?: string;
    /**
     * Provides vendor-specific trace identification information and is a companion to traceparent.
     */
    tracestate?: string;
    /**
     * Only perform the operation if the entity's etag matches one of the etags provided or * is provided.
     */
    ifMatch?: string;
}

/**
 * Contains response data for the updateComponent operation.
 */
export declare type DigitalTwinsUpdateComponentResponse = DigitalTwinsUpdateComponentHeaders & {
    /**
     * The underlying HTTP response.
     */
    _response: coreHttp.HttpResponse & {
        /**
         * The parsed HTTP response headers.
         */
        parsedHeaders: DigitalTwinsUpdateComponentHeaders;
    };
};

/**
 * Defines headers for DigitalTwins_update operation.
 */
export declare interface DigitalTwinsUpdateHeaders {
    /**
     * Weak Etag.
     */
    etag?: string;
}

/**
 * Optional parameters.
 */
export declare interface DigitalTwinsUpdateOptionalParams extends coreHttp.OperationOptions {
    /**
     * Identifies the request in a distributed tracing system.
     */
    traceparent?: string;
    /**
     * Provides vendor-specific trace identification information and is a companion to traceparent.
     */
    tracestate?: string;
    /**
     * Only perform the operation if the entity's etag matches one of the etags provided or * is provided.
     */
    ifMatch?: string;
}

/**
 * Defines headers for DigitalTwins_updateRelationship operation.
 */
export declare interface DigitalTwinsUpdateRelationshipHeaders {
    /**
     * Weak Etag.
     */
    etag?: string;
}

/**
 * Optional parameters.
 */
export declare interface DigitalTwinsUpdateRelationshipOptionalParams extends coreHttp.OperationOptions {
    /**
     * Identifies the request in a distributed tracing system.
     */
    traceparent?: string;
    /**
     * Provides vendor-specific trace identification information and is a companion to traceparent.
     */
    tracestate?: string;
    /**
     * Only perform the operation if the entity's etag matches one of the etags provided or * is provided.
     */
    ifMatch?: string;
}

/**
 * Contains response data for the updateRelationship operation.
 */
export declare type DigitalTwinsUpdateRelationshipResponse = DigitalTwinsUpdateRelationshipHeaders & {
    /**
     * The underlying HTTP response.
     */
    _response: coreHttp.HttpResponse & {
        /**
         * The parsed HTTP response headers.
         */
        parsedHeaders: DigitalTwinsUpdateRelationshipHeaders;
    };
};

/**
 * Contains response data for the update operation.
 */
export declare type DigitalTwinsUpdateResponse = DigitalTwinsUpdateHeaders & {
    /**
     * The underlying HTTP response.
     */
    _response: coreHttp.HttpResponse & {
        /**
         * The parsed HTTP response headers.
         */
        parsedHeaders: DigitalTwinsUpdateHeaders;
    };
};

/**
 * A route which directs notification and telemetry events to an endpoint. Endpoints are a destination outside of Azure Digital Twins such as an EventHub.
 */
export declare interface EventRoute {
    /**
     * The id of the event route.
     */
    readonly id?: string;
    /**
     * The name of the endpoint this event route is bound to.
     */
    endpointName: string;
    /**
     * An expression which describes the events which are routed to the endpoint.
     */
    filter: string;
}

/**
 * A collection of EventRoute objects.
 */
export declare interface EventRouteCollection {
    /**
     * The EventRoute objects.
     */
    value?: EventRoute[];
    /**
     * A URI to retrieve the next page of results.
     */
    nextLink?: string;
}

/**
 * Contains response data for the getById operation.
 */
export declare type EventRoutesGetByIdResponse = EventRoute & {
    /**
     * The underlying HTTP response.
     */
    _response: coreHttp.HttpResponse & {
        /**
         * The response body as text (string format)
         */
        bodyAsText: string;
        /**
         * The response body as parsed JSON or XML
         */
        parsedBody: EventRoute;
    };
};

/**
 * Contains response data for the listNext operation.
 */
export declare type EventRoutesListNextResponse = EventRouteCollection & {
    /**
     * The underlying HTTP response.
     */
    _response: coreHttp.HttpResponse & {
        /**
         * The response body as text (string format)
         */
        bodyAsText: string;
        /**
         * The response body as parsed JSON or XML
         */
        parsedBody: EventRouteCollection;
    };
};

/**
 * An incoming relationship.
 */
export declare interface IncomingRelationship {
    /**
     * A user-provided string representing the id of this relationship, unique in the context of the source digital twin, i.e. sourceId + relationshipId is unique in the context of the service.
     */
    relationshipId?: string;
    /**
     * The id of the source digital twin.
     */
    sourceId?: string;
    /**
     * The name of the relationship.
     */
    relationshipName?: string;
    /**
     * Link to the relationship, to be used for deletion.
     */
    relationshipLink?: string;
}

/**
 * A collection of incoming relationships which relate digital twins together.
 */
export declare interface IncomingRelationshipCollection {
    value?: IncomingRelationship[];
    /**
     * A URI to retrieve the next page of objects.
     */
    nextLink?: string;
}

/**
 * A collection of DigitalTwinsModelData objects.
 */
export declare interface PagedDigitalTwinsModelDataCollection {
    /**
     * The DigitalTwinsModelData objects.
     */
    value?: DigitalTwinsModelData[];
    /**
     * A URI to retrieve the next page of objects.
     */
    nextLink?: string;
}

/**
 * Defines headers for Query_queryTwins operation.
 */
export declare interface QueryQueryTwinsHeaders {
    /**
     * The query charge.
     */
    queryCharge?: number;
}

/**
 * Contains response data for the queryTwins operation.
 */
export declare type QueryQueryTwinsResponse = QueryQueryTwinsHeaders & QueryResult & {
    /**
     * The underlying HTTP response.
     */
    _response: coreHttp.HttpResponse & {
        /**
         * The response body as text (string format)
         */
        bodyAsText: string;
        /**
         * The response body as parsed JSON or XML
         */
        parsedBody: QueryResult;
        /**
         * The parsed HTTP response headers.
         */
        parsedHeaders: QueryQueryTwinsHeaders;
    };
};

/**
 * The results of a query operation and an optional continuation token.
 */
export declare interface QueryResult {
    /**
     * The query results.
     */
    value?: any[];
    /**
     * A token which can be used to construct a new QuerySpecification to retrieve the next set of results.
     */
    continuationToken?: string;
}

/**
 * A query specification containing either a query statement or a continuation token from a previous query result.
 */
export declare interface QuerySpecification {
    /**
     * The query to execute. This value is ignored if a continuation token is provided.
     */
    query?: string;
    /**
     * A token which is used to retrieve the next set of results from a previous query.
     */
    continuationToken?: string;
}

/**
 * A collection of relationships which relate digital twins together.
 */
export declare interface RelationshipCollection {
    /**
     * The relationship objects.
     */
    value?: any[];
    /**
     * A URI to retrieve the next page of objects.
     */
    nextLink?: string;
}

export { }
