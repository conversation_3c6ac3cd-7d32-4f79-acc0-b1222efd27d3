{"version": 3, "file": "logPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/logPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EACL,iBAAiB,GAIlB,MAAM,iBAAiB,CAAC;AAGzB,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAE9C,OAAO,EAAE,MAAM,IAAI,UAAU,EAAE,MAAM,QAAQ,CAAC;AAiC9C;;;;GAIG;AACH,MAAM,UAAU,SAAS,CAAC,iBAAmC,EAAE;IAC7D,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,EAAE,EAAE;YACnE,OAAO,IAAI,SAAS,CAAC,UAAU,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;QAC5D,CAAC;KACF,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,OAAO,SAAU,SAAQ,iBAAiB;IA4C9C,YACE,UAAyB,EACzB,OAA6B,EAC7B,EACE,MAAM,GAAG,UAAU,CAAC,IAAI,EACxB,kBAAkB,GAAG,EAAE,EACvB,sBAAsB,GAAG,EAAE,MACP,EAAE;QAExB,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC3B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,CAAC,EAAE,kBAAkB,EAAE,sBAAsB,EAAE,CAAC,CAAC;IACjF,CAAC;IApDD;;;;;;OAMG;IACH,IAAW,kBAAkB;QAC3B,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;IAC3C,CAAC;IAED;;;;;;OAMG;IACH,IAAW,kBAAkB,CAAC,kBAA+B;QAC3D,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;IACzD,CAAC;IAED;;;;OAIG;IACH,IAAW,sBAAsB;QAC/B,OAAO,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC;IAC/C,CAAC;IAED;;;;OAIG;IACH,IAAW,sBAAsB,CAAC,sBAAmC;QACnE,IAAI,CAAC,SAAS,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;IACjE,CAAC;IAgBM,WAAW,CAAC,OAAwB;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO;YAAE,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEvE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC9F,CAAC;IAEO,UAAU,CAAC,OAAwB;QACzC,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC9D,CAAC;IAEO,WAAW,CAAC,QAA+B;QACjD,IAAI,CAAC,MAAM,CAAC,yBAAyB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QACxD,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACrE,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"./requestPolicy\";\nimport { Debugger } from \"@azure/logger\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { Sanitizer } from \"../util/sanitizer\";\nimport { WebResourceLike } from \"../webResource\";\nimport { logger as coreLogger } from \"../log\";\n\n/**\n * Options to pass to the {@link logPolicy}.\n * By default only a set list of headers are logged, though this can be configured. Request and response bodies are never logged.\n */\nexport interface LogPolicyOptions {\n  /**\n   * Header names whose values will be logged when logging is enabled. Defaults to:\n   * x-ms-client-request-id, x-ms-return-client-request-id, x-ms-useragent, x-ms-correlation-request-id,\n   * x-ms-request-id, client-request-id, ms-cv, return-client-request-id, traceparent, Access-Control-Allow-Credentials,\n   * Access-Control-Allow-Headers, Access-Control-Allow-Methods, Access-Control-Allow-Origin, Access-Control-Expose-Headers,\n   * Access-Control-Max-Age, Access-Control-Request-Headers, Access-Control-Request-Method, Origin, Accept, Accept-Encoding,\n   * Cache-Control, Connection, Content-Length, Content-Type, Date, ETag, Expires, If-Match, If-Modified-Since, If-None-Match,\n   * If-Unmodified-Since, Last-Modified, Pragma, Request-Id, Retry-After, Server, Transfer-Encoding, and User-Agent.\n   *\n   * Any headers specified in this field will be added to that list.\n   * Any other values will be written to logs as \"REDACTED\".\n   */\n  allowedHeaderNames?: string[];\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   */\n  allowedQueryParameters?: string[];\n\n  /**\n   * The Debugger (logger) instance to use for writing pipeline logs.\n   */\n  logger?: Debugger;\n}\n\n/**\n * Creates a policy that logs information about the outgoing request and the incoming responses.\n * @param loggingOptions - Logging options.\n * @returns An instance of the {@link LogPolicy}\n */\nexport function logPolicy(loggingOptions: LogPolicyOptions = {}): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new LogPolicy(nextPolicy, options, loggingOptions);\n    },\n  };\n}\n\n/**\n * A policy that logs information about the outgoing request and the incoming responses.\n */\nexport class LogPolicy extends BaseRequestPolicy {\n  logger: Debugger;\n  sanitizer: Sanitizer;\n\n  /**\n   * Header names whose values will be logged when logging is enabled. Defaults to\n   * Date, traceparent, x-ms-client-request-id, and x-ms-request id.  Any headers\n   * specified in this field will be added to that list.  Any other values will\n   * be written to logs as \"REDACTED\".\n   * @deprecated Pass these into the constructor instead.\n   */\n  public get allowedHeaderNames(): Set<string> {\n    return this.sanitizer.allowedHeaderNames;\n  }\n\n  /**\n   * Header names whose values will be logged when logging is enabled. Defaults to\n   * Date, traceparent, x-ms-client-request-id, and x-ms-request id.  Any headers\n   * specified in this field will be added to that list.  Any other values will\n   * be written to logs as \"REDACTED\".\n   * @deprecated Pass these into the constructor instead.\n   */\n  public set allowedHeaderNames(allowedHeaderNames: Set<string>) {\n    this.sanitizer.allowedHeaderNames = allowedHeaderNames;\n  }\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   * @deprecated Pass these into the constructor instead.\n   */\n  public get allowedQueryParameters(): Set<string> {\n    return this.sanitizer.allowedQueryParameters;\n  }\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   * @deprecated Pass these into the constructor instead.\n   */\n  public set allowedQueryParameters(allowedQueryParameters: Set<string>) {\n    this.sanitizer.allowedQueryParameters = allowedQueryParameters;\n  }\n\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    {\n      logger = coreLogger.info,\n      allowedHeaderNames = [],\n      allowedQueryParameters = [],\n    }: LogPolicyOptions = {}\n  ) {\n    super(nextPolicy, options);\n    this.logger = logger;\n    this.sanitizer = new Sanitizer({ allowedHeaderNames, allowedQueryParameters });\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    if (!this.logger.enabled) return this._nextPolicy.sendRequest(request);\n\n    this.logRequest(request);\n    return this._nextPolicy.sendRequest(request).then((response) => this.logResponse(response));\n  }\n\n  private logRequest(request: WebResourceLike): void {\n    this.logger(`Request: ${this.sanitizer.sanitize(request)}`);\n  }\n\n  private logResponse(response: HttpOperationResponse): HttpOperationResponse {\n    this.logger(`Response status code: ${response.status}`);\n    this.logger(`Headers: ${this.sanitizer.sanitize(response.headers)}`);\n    return response;\n  }\n}\n"]}