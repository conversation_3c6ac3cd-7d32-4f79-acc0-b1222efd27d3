{"version": 3, "file": "arcMsi.js", "sourceRoot": "", "sources": ["../../../../src/credentials/managedIdentityCredential/arcMsi.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAEL,iBAAiB,EACjB,qBAAqB,GACtB,MAAM,2BAA2B,CAAC;AAEnC,OAAO,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC;AAC9B,OAAO,EAAE,mBAAmB,EAAE,MAAM,cAAc,CAAC;AACnD,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAEtD,OAAO,EAAE,mBAAmB,EAAE,MAAM,SAAS,CAAC;AAE9C,OAAO,EAAE,kBAAkB,EAAE,MAAM,aAAa,CAAC;AAEjD,MAAM,OAAO,GAAG,2CAA2C,CAAC;AAC5D,MAAM,MAAM,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAEzC;;GAEG;AACH,SAAS,qBAAqB,CAC5B,MAAyB,EACzB,QAAiB,EACjB,UAAmB;IAEnB,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAC7C,IAAI,CAAC,QAAQ,EAAE;QACb,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,sCAAsC,CAAC,CAAC;KACnE;IACD,MAAM,eAAe,GAA2B;QAC9C,QAAQ;QACR,aAAa,EAAE,kBAAkB;KAClC,CAAC;IAEF,IAAI,QAAQ,EAAE;QACZ,eAAe,CAAC,SAAS,GAAG,QAAQ,CAAC;KACtC;IACD,IAAI,UAAU,EAAE;QACd,eAAe,CAAC,UAAU,GAAG,UAAU,CAAC;KACzC;IAED,wIAAwI;IACxI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE;QAClC,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,mDAAmD,CAAC,CAAC;KAChF;IAED,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,eAAe,CAAC,CAAC;IAEnD,OAAO,qBAAqB,CAAC;QAC3B,8EAA8E;QAC9E,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,KAAK,CAAC,QAAQ,EAAE,EAAE;QAC3D,MAAM,EAAE,KAAK;QACb,OAAO,EAAE,iBAAiB,CAAC;YACzB,MAAM,EAAE,kBAAkB;YAC1B,QAAQ,EAAE,MAAM;SACjB,CAAC;KACH,CAAC,CAAC;AACL,CAAC;AAED;;;GAGG;AACH,SAAS,aAAa,CAAC,IAAY,EAAE,OAAqC;IACxE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,CACrC,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;QACpC,IAAI,GAAG,EAAE;YACP,MAAM,CAAC,GAAG,CAAC,CAAC;SACb;QACD,OAAO,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC,CAAC,CACH,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,eAAe,CAC5B,cAA8B,EAC9B,qBAA6C;IAE7C,MAAM,QAAQ,GAAG,MAAM,cAAc,CAAC,WAAW,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,CAAC,CAAC;IAEhG,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;QAC3B,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,IAAI,QAAQ,CAAC,UAAU,EAAE;YACvB,OAAO,GAAG,cAAc,QAAQ,CAAC,UAAU,EAAE,CAAC;SAC/C;QACD,MAAM,IAAI,mBAAmB,CAC3B,QAAQ,CAAC,MAAM,EACf,GAAG,OAAO,2FAA2F,OAAO,EAAE,CAC/G,CAAC;KACH;IAED,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;IAClE,IAAI;QACF,OAAO,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC1C;IAAC,OAAO,CAAM,EAAE;QACf,MAAM,KAAK,CAAC,2CAA2C,UAAU,EAAE,CAAC,CAAC;KACtE;AACH,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,MAAM,GAAQ;IACzB,KAAK,CAAC,WAAW,CAAC,EAAE,MAAM,EAAE;QAC1B,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,EAAE;YACb,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,mDAAmD,CAAC,CAAC;YAC3E,OAAO,KAAK,CAAC;SACd;QACD,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACnF,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,CAAC,IAAI,CACT,GAAG,OAAO,6EAA6E,CACxF,CAAC;SACH;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,KAAK,CAAC,QAAQ,CACZ,aAA+B,EAC/B,kBAAmC,EAAE;;QAErC,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,aAAa,CAAC;QAEvE,IAAI,QAAQ,EAAE;YACZ,MAAM,CAAC,OAAO,CACZ,GAAG,OAAO,kGAAkG,CAC7G,CAAC;SACH;QACD,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,OAAO,CACZ,GAAG,OAAO,uGAAuG,CAClH,CAAC;SACH;QAED,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,mBAAmB,CAAC,CAAC;QAE3C,MAAM,cAAc,iCAClB,0BAA0B,EAAE,IAAI,EAChC,qBAAqB,EAAE,SAAS,EAChC,WAAW,EAAE,eAAe,CAAC,WAAW,IACrC,qBAAqB,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC,KACtD,uBAAuB,EAAE,IAAI,GAC9B,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;QAEvE,IAAI,CAAC,QAAQ,EAAE;YACb,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,kCAAkC,CAAC,CAAC;SAC/D;QAED,MAAM,GAAG,GAAG,MAAM,aAAa,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC;QACjE,MAAA,cAAc,CAAC,OAAO,0CAAE,GAAG,CAAC,eAAe,EAAE,SAAS,GAAG,EAAE,CAAC,CAAC;QAE7D,MAAM,OAAO,GAAG,qBAAqB,iCAChC,cAAc;YACjB,0FAA0F;YAC1F,uBAAuB,EAAE,IAAI,IAC7B,CAAC;QACH,MAAM,aAAa,GAAG,MAAM,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACrE,OAAO,CAAC,aAAa,IAAI,aAAa,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC;IAC9D,CAAC;CACF,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  PipelineRequestOptions,\n  createHttpHeaders,\n  createPipelineRequest,\n} from \"@azure/core-rest-pipeline\";\nimport { AccessToken, GetTokenOptions } from \"@azure/core-auth\";\nimport { readFile } from \"fs\";\nimport { AuthenticationError } from \"../../errors\";\nimport { credentialLogger } from \"../../util/logging\";\nimport { IdentityClient } from \"../../client/identityClient\";\nimport { mapScopesToResource } from \"./utils\";\nimport { MSI, MSIConfiguration } from \"./models\";\nimport { azureArcAPIVersion } from \"./constants\";\n\nconst msiName = \"ManagedIdentityCredential - Azure Arc MSI\";\nconst logger = credentialLogger(msiName);\n\n/**\n * Generates the options used on the request for an access token.\n */\nfunction prepareRequestOptions(\n  scopes: string | string[],\n  clientId?: string,\n  resourceId?: string\n): PipelineRequestOptions {\n  const resource = mapScopesToResource(scopes);\n  if (!resource) {\n    throw new Error(`${msiName}: Multiple scopes are not supported.`);\n  }\n  const queryParameters: Record<string, string> = {\n    resource,\n    \"api-version\": azureArcAPIVersion,\n  };\n\n  if (clientId) {\n    queryParameters.client_id = clientId;\n  }\n  if (resourceId) {\n    queryParameters.msi_res_id = resourceId;\n  }\n\n  // This error should not bubble up, since we verify that this environment variable is defined in the isAvailable() method defined below.\n  if (!process.env.IDENTITY_ENDPOINT) {\n    throw new Error(`${msiName}: Missing environment variable: IDENTITY_ENDPOINT`);\n  }\n\n  const query = new URLSearchParams(queryParameters);\n\n  return createPipelineRequest({\n    // Should be similar to: http://localhost:40342/metadata/identity/oauth2/token\n    url: `${process.env.IDENTITY_ENDPOINT}?${query.toString()}`,\n    method: \"GET\",\n    headers: createHttpHeaders({\n      Accept: \"application/json\",\n      Metadata: \"true\",\n    }),\n  });\n}\n\n/**\n * Retrieves the file contents at the given path using promises.\n * Useful since `fs`'s readFileSync locks the thread, and to avoid extra dependencies.\n */\nfunction readFileAsync(path: string, options: { encoding: BufferEncoding }): Promise<string> {\n  return new Promise((resolve, reject) =>\n    readFile(path, options, (err, data) => {\n      if (err) {\n        reject(err);\n      }\n      resolve(data);\n    })\n  );\n}\n\n/**\n * Does a request to the authentication provider that results in a file path.\n */\nasync function filePathRequest(\n  identityClient: IdentityClient,\n  requestPrepareOptions: PipelineRequestOptions\n): Promise<string | undefined> {\n  const response = await identityClient.sendRequest(createPipelineRequest(requestPrepareOptions));\n\n  if (response.status !== 401) {\n    let message = \"\";\n    if (response.bodyAsText) {\n      message = ` Response: ${response.bodyAsText}`;\n    }\n    throw new AuthenticationError(\n      response.status,\n      `${msiName}: To authenticate with Azure Arc MSI, status code 401 is expected on the first request. ${message}`\n    );\n  }\n\n  const authHeader = response.headers.get(\"www-authenticate\") || \"\";\n  try {\n    return authHeader.split(\"=\").slice(1)[0];\n  } catch (e: any) {\n    throw Error(`Invalid www-authenticate header format: ${authHeader}`);\n  }\n}\n\n/**\n * Defines how to determine whether the Azure Arc MSI is available, and also how to retrieve a token from the Azure Arc MSI.\n */\nexport const arcMsi: MSI = {\n  async isAvailable({ scopes }): Promise<boolean> {\n    const resource = mapScopesToResource(scopes);\n    if (!resource) {\n      logger.info(`${msiName}: Unavailable. Multiple scopes are not supported.`);\n      return false;\n    }\n    const result = Boolean(process.env.IMDS_ENDPOINT && process.env.IDENTITY_ENDPOINT);\n    if (!result) {\n      logger.info(\n        `${msiName}: The environment variables needed are: IMDS_ENDPOINT and IDENTITY_ENDPOINT`\n      );\n    }\n    return result;\n  },\n  async getToken(\n    configuration: MSIConfiguration,\n    getTokenOptions: GetTokenOptions = {}\n  ): Promise<AccessToken | null> {\n    const { identityClient, scopes, clientId, resourceId } = configuration;\n\n    if (clientId) {\n      logger.warning(\n        `${msiName}: user-assigned identities not supported. The argument clientId might be ignored by the service.`\n      );\n    }\n    if (resourceId) {\n      logger.warning(\n        `${msiName}: user defined managed Identity by resource Id is not supported. Argument resourceId will be ignored.`\n      );\n    }\n\n    logger.info(`${msiName}: Authenticating.`);\n\n    const requestOptions = {\n      disableJsonStringifyOnBody: true,\n      deserializationMapper: undefined,\n      abortSignal: getTokenOptions.abortSignal,\n      ...prepareRequestOptions(scopes, clientId, resourceId),\n      allowInsecureConnection: true,\n    };\n\n    const filePath = await filePathRequest(identityClient, requestOptions);\n\n    if (!filePath) {\n      throw new Error(`${msiName}: Failed to find the token file.`);\n    }\n\n    const key = await readFileAsync(filePath, { encoding: \"utf-8\" });\n    requestOptions.headers?.set(\"Authorization\", `Basic ${key}`);\n\n    const request = createPipelineRequest({\n      ...requestOptions,\n      // Generally, MSI endpoints use the HTTP protocol, without transport layer security (TLS).\n      allowInsecureConnection: true,\n    });\n    const tokenResponse = await identityClient.sendTokenRequest(request);\n    return (tokenResponse && tokenResponse.accessToken) || null;\n  },\n};\n"]}