<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>FlipClock.js :: Source: Languages/it-it.js</title>

    <script src="scripts/prettify/prettify.js"></script>
    <script src="scripts/prettify/lang-css.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flipclock@0.10.5/dist/flipclock.js"></script>

    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->

    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css" integrity="sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO" crossorigin="anonymous">
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-default.css">
    <link type="text/css" rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flipclock@0.10.5/dist/flipclock.css" />
</head>

<body>

<div id="app" class="container">
    <div class="row">
        <div class="col-md-3">
            <nav>
                <h2><a href="index.html">Home</a></h2><ul><li><a href="tutorial-1-installation.html">Installation</a></li><li><a href="tutorial-2-getting-started.html">Getting Started</a></li><li><a href="tutorial-3-examples.html">Examples</a></li></ul><h3>Classes</h3><ul><li><a href="Component.html">Component</a></li><li><a href="Divider.html">Divider</a></li><li><a href="DomComponent.html">DomComponent</a></li><li><a href="Face.html">Face</a></li><li><a href="Faces.Counter.html">Counter</a></li><li><a href="Faces.DayCounter.html">DayCounter</a></li><li><a href="Faces.HourCounter.html">HourCounter</a></li><li><a href="Faces.MinuteCounter.html">MinuteCounter</a></li><li><a href="Faces.TwelveHourClock.html">TwelveHourClock</a></li><li><a href="Faces.TwentyFourHourClock.html">TwentyFourHourClock</a></li><li><a href="Faces.WeekCounter.html">WeekCounter</a></li><li><a href="Faces.YearCounter.html">YearCounter</a></li><li><a href="FaceValue.html">FaceValue</a></li><li><a href="FlipClock.html">FlipClock</a></li><li><a href="Group.html">Group</a></li><li><a href="Label.html">Label</a></li><li><a href="List.html">List</a></li><li><a href="ListItem.html">ListItem</a></li><li><a href="Timer.html">Timer</a></li></ul>
            </nav>
        </div>
        <div class="col-md-9">
            
                <h1 class="display-4">Source: Languages/it-it.js</h1>
            

            



    
    <section>
        <article>
            <pre class="prettyprint source linenums"><code>/**
 * @classdesc Italian Language Pack
 * @desc This class will used to translate tokens into the Italian language.
 * @namespace Languages.Italian
 */

/**
 * @constant dictionary
 * @type {object}
 * @memberof Languages.Italian
 */
export const dictionary = {
	'years'   : 'Anni',
	'months'  : 'Mesi',
	'days'    : 'Giorni',
	'hours'   : 'Ore',
	'minutes' : 'Minuti',
	'seconds' : 'Secondi'
};

/**
 * @constant aliases
 * @type {array}
 * @memberof Languages.Italian
 */
export const aliases = ['da', 'da-dk', 'danish'];
</code></pre>
        </article>
    </section>




        </div>
    </div>

    <footer class="text-center p-4 pt-3">
        &copy; 2019 Justin Kimbrell - Version 0.10.5
    </footer>

</div>

<script> prettyPrint(); </script>
<script src="scripts/linenumber.js"> </script>
</body>
</html>
