{"version": 3, "file": "httpHeaders.js", "sourceRoot": "", "sources": ["../../src/httpHeaders.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;GAEG;AACH,SAAS,YAAY,CAAC,UAAkB;IACtC,OAAO,UAAU,CAAC,WAAW,EAAE,CAAC;AAClC,CAAC;AA4ED,MAAM,UAAU,iBAAiB,CAAC,MAAgB;IAChD,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QACxC,MAAM,UAAU,GAAG,MAWlB,CAAC;QACF,IACE,OAAO,UAAU,CAAC,UAAU,KAAK,UAAU;YAC3C,OAAO,UAAU,CAAC,KAAK,KAAK,UAAU;YACtC,OAAO,UAAU,CAAC,GAAG,KAAK,UAAU;YACpC,OAAO,UAAU,CAAC,GAAG,KAAK,UAAU;YACpC,OAAO,UAAU,CAAC,QAAQ,KAAK,UAAU;YACzC,OAAO,UAAU,CAAC,MAAM,KAAK,UAAU;YACvC,OAAO,UAAU,CAAC,YAAY,KAAK,UAAU;YAC7C,OAAO,UAAU,CAAC,YAAY,KAAK,UAAU;YAC7C,OAAO,UAAU,CAAC,WAAW,KAAK,UAAU;YAC5C,OAAO,UAAU,CAAC,MAAM,KAAK,UAAU,EACvC;YACA,OAAO,IAAI,CAAC;SACb;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,MAAM,OAAO,WAAW;IAGtB,YAAY,UAA2B;QACrC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,UAAU,EAAE;YACd,KAAK,MAAM,UAAU,IAAI,UAAU,EAAE;gBACnC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;aAC9C;SACF;IACH,CAAC;IAED;;;;;OAKG;IACI,GAAG,CAAC,UAAkB,EAAE,WAA4B;QACzD,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,GAAG;YAC3C,IAAI,EAAE,UAAU;YAChB,KAAK,EAAE,WAAW,CAAC,QAAQ,EAAE;SAC9B,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,GAAG,CAAC,UAAkB;QAC3B,MAAM,MAAM,GAAe,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;QACtE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,QAAQ,CAAC,UAAkB;QAChC,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;IACtD,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,UAAkB;QAC9B,MAAM,MAAM,GAAY,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,UAAU;QACf,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,YAAY;QACjB,MAAM,OAAO,GAAiB,EAAE,CAAC;QACjC,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACxC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC;SAC3C;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,WAAW;QAChB,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,MAAM,OAAO,GAAiB,IAAI,CAAC,YAAY,EAAE,CAAC;QAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACvC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SACnC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,YAAY;QACjB,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,MAAM,OAAO,GAAiB,IAAI,CAAC,YAAY,EAAE,CAAC;QAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACvC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;SACrC;QACD,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,UAAsC,EAAE;QACpD,MAAM,MAAM,GAAmB,EAAE,CAAC;QAClC,IAAI,OAAO,CAAC,YAAY,EAAE;YACxB,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;gBACxC,MAAM,MAAM,GAAe,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;gBACvD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;aACpC;SACF;aAAM;YACL,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;gBACxC,MAAM,MAAM,GAAe,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;gBACvD,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;aAClD;SACF;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACI,KAAK;QACV,MAAM,sBAAsB,GAAmB,EAAE,CAAC;QAClD,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACxC,MAAM,MAAM,GAAe,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YACvD,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;SACpD;QACD,OAAO,IAAI,WAAW,CAAC,sBAAsB,CAAC,CAAC;IACjD,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * A collection of HttpHeaders that can be sent with a HTTP request.\n */\nfunction getHeaderKey(headerName: string): string {\n  return headerName.toLowerCase();\n}\n\n/**\n * An individual header within a HttpHeaders collection.\n */\nexport interface HttpHeader {\n  /**\n   * The name of the header.\n   */\n  name: string;\n\n  /**\n   * The value of the header.\n   */\n  value: string;\n}\n\n/**\n * A HttpHeaders collection represented as a simple JSON object.\n */\nexport type RawHttpHeaders = { [headerName: string]: string };\n\n/**\n * A collection of HTTP header key/value pairs.\n */\nexport interface HttpHeadersLike {\n  /**\n   * Set a header in this collection with the provided name and value. The name is\n   * case-insensitive.\n   * @param headerName - The name of the header to set. This value is case-insensitive.\n   * @param headerValue - The value of the header to set.\n   */\n  set(headerName: string, headerValue: string | number): void;\n  /**\n   * Get the header value for the provided header name, or undefined if no header exists in this\n   * collection with the provided name.\n   * @param headerName - The name of the header.\n   */\n  get(headerName: string): string | undefined;\n  /**\n   * Get whether or not this header collection contains a header entry for the provided header name.\n   */\n  contains(headerName: string): boolean;\n  /**\n   * Remove the header with the provided headerName. Return whether or not the header existed and\n   * was removed.\n   * @param headerName - The name of the header to remove.\n   */\n  remove(headerName: string): boolean;\n  /**\n   * Get the headers that are contained this collection as an object.\n   */\n  rawHeaders(): RawHttpHeaders;\n  /**\n   * Get the headers that are contained in this collection as an array.\n   */\n  headersArray(): HttpHeader[];\n  /**\n   * Get the header names that are contained in this collection.\n   */\n  headerNames(): string[];\n  /**\n   * Get the header values that are contained in this collection.\n   */\n  headerValues(): string[];\n  /**\n   * Create a deep clone/copy of this HttpHeaders collection.\n   */\n  clone(): HttpHeadersLike;\n  /**\n   * Get the JSON object representation of this HTTP header collection.\n   * The result is the same as `rawHeaders()`.\n   */\n  toJson(options?: { preserveCase?: boolean }): RawHttpHeaders;\n}\n\nexport function isHttpHeadersLike(object?: unknown): object is HttpHeadersLike {\n  if (object && typeof object === \"object\") {\n    const castObject = object as {\n      rawHeaders: unknown;\n      clone: unknown;\n      get: unknown;\n      set: unknown;\n      contains: unknown;\n      remove: unknown;\n      headersArray: unknown;\n      headerValues: unknown;\n      headerNames: unknown;\n      toJson: unknown;\n    };\n    if (\n      typeof castObject.rawHeaders === \"function\" &&\n      typeof castObject.clone === \"function\" &&\n      typeof castObject.get === \"function\" &&\n      typeof castObject.set === \"function\" &&\n      typeof castObject.contains === \"function\" &&\n      typeof castObject.remove === \"function\" &&\n      typeof castObject.headersArray === \"function\" &&\n      typeof castObject.headerValues === \"function\" &&\n      typeof castObject.headerNames === \"function\" &&\n      typeof castObject.toJson === \"function\"\n    ) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/**\n * A collection of HTTP header key/value pairs.\n */\nexport class HttpHeaders implements HttpHeadersLike {\n  private readonly _headersMap: { [headerKey: string]: HttpHeader };\n\n  constructor(rawHeaders?: RawHttpHeaders) {\n    this._headersMap = {};\n    if (rawHeaders) {\n      for (const headerName in rawHeaders) {\n        this.set(headerName, rawHeaders[headerName]);\n      }\n    }\n  }\n\n  /**\n   * Set a header in this collection with the provided name and value. The name is\n   * case-insensitive.\n   * @param headerName - The name of the header to set. This value is case-insensitive.\n   * @param headerValue - The value of the header to set.\n   */\n  public set(headerName: string, headerValue: string | number): void {\n    this._headersMap[getHeaderKey(headerName)] = {\n      name: headerName,\n      value: headerValue.toString(),\n    };\n  }\n\n  /**\n   * Get the header value for the provided header name, or undefined if no header exists in this\n   * collection with the provided name.\n   * @param headerName - The name of the header.\n   */\n  public get(headerName: string): string | undefined {\n    const header: HttpHeader = this._headersMap[getHeaderKey(headerName)];\n    return !header ? undefined : header.value;\n  }\n\n  /**\n   * Get whether or not this header collection contains a header entry for the provided header name.\n   */\n  public contains(headerName: string): boolean {\n    return !!this._headersMap[getHeaderKey(headerName)];\n  }\n\n  /**\n   * Remove the header with the provided headerName. Return whether or not the header existed and\n   * was removed.\n   * @param headerName - The name of the header to remove.\n   */\n  public remove(headerName: string): boolean {\n    const result: boolean = this.contains(headerName);\n    delete this._headersMap[getHeaderKey(headerName)];\n    return result;\n  }\n\n  /**\n   * Get the headers that are contained this collection as an object.\n   */\n  public rawHeaders(): RawHttpHeaders {\n    return this.toJson({ preserveCase: true });\n  }\n\n  /**\n   * Get the headers that are contained in this collection as an array.\n   */\n  public headersArray(): HttpHeader[] {\n    const headers: HttpHeader[] = [];\n    for (const headerKey in this._headersMap) {\n      headers.push(this._headersMap[headerKey]);\n    }\n    return headers;\n  }\n\n  /**\n   * Get the header names that are contained in this collection.\n   */\n  public headerNames(): string[] {\n    const headerNames: string[] = [];\n    const headers: HttpHeader[] = this.headersArray();\n    for (let i = 0; i < headers.length; ++i) {\n      headerNames.push(headers[i].name);\n    }\n    return headerNames;\n  }\n\n  /**\n   * Get the header values that are contained in this collection.\n   */\n  public headerValues(): string[] {\n    const headerValues: string[] = [];\n    const headers: HttpHeader[] = this.headersArray();\n    for (let i = 0; i < headers.length; ++i) {\n      headerValues.push(headers[i].value);\n    }\n    return headerValues;\n  }\n\n  /**\n   * Get the JSON object representation of this HTTP header collection.\n   */\n  public toJson(options: { preserveCase?: boolean } = {}): RawHttpHeaders {\n    const result: RawHttpHeaders = {};\n    if (options.preserveCase) {\n      for (const headerKey in this._headersMap) {\n        const header: HttpHeader = this._headersMap[headerKey];\n        result[header.name] = header.value;\n      }\n    } else {\n      for (const headerKey in this._headersMap) {\n        const header: HttpHeader = this._headersMap[headerKey];\n        result[getHeaderKey(header.name)] = header.value;\n      }\n    }\n    return result;\n  }\n\n  /**\n   * Get the string representation of this HTTP header collection.\n   */\n  public toString(): string {\n    return JSON.stringify(this.toJson({ preserveCase: true }));\n  }\n\n  /**\n   * Create a deep clone/copy of this HttpHeaders collection.\n   */\n  public clone(): HttpHeaders {\n    const resultPreservingCasing: RawHttpHeaders = {};\n    for (const headerKey in this._headersMap) {\n      const header: HttpHeader = this._headersMap[headerKey];\n      resultPreservingCasing[header.name] = header.value;\n    }\n    return new HttpHeaders(resultPreservingCasing);\n  }\n}\n"]}