/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import * as coreHttp from "@azure/core-http";
import * as Mappers from "../models/mappers";
import * as Parameters from "../models/parameters";
/**
 * Class representing a DigitalTwins.
 */
export class DigitalTwins {
    /**
     * Initialize a new instance of the class DigitalTwins class.
     * @param client Reference to the service client
     */
    constructor(client) {
        this.client = client;
    }
    /**
     * Retrieves a digital twin.
     * Status codes:
     * * 200 OK
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id is invalid.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param options The options parameters.
     */
    getById(id, options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, options: operationOptions }, getByIdOperationSpec);
    }
    /**
     * Adds or replaces a digital twin.
     * Status codes:
     * * 200 OK
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id or payload is invalid.
     *   * ModelDecommissioned - The model for the digital twin is decommissioned.
     *   * TwinLimitReached - The maximum number of digital twins allowed has been reached.
     *   * ValidationFailed - The digital twin payload is not valid.
     * * 412 Precondition Failed
     *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param twin The digital twin instance being added. If provided, the $dtId property is ignored.
     * @param options The options parameters.
     */
    add(id, twin, options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, twin, options: operationOptions }, addOperationSpec);
    }
    /**
     * Deletes a digital twin. All relationships referencing the digital twin must already be deleted.
     * Status codes:
     * * 204 No Content
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id is invalid.
     *   * RelationshipsNotDeleted - The digital twin contains relationships.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     * * 412 Precondition Failed
     *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param options The options parameters.
     */
    delete(id, options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, options: operationOptions }, deleteOperationSpec);
    }
    /**
     * Updates a digital twin.
     * Status codes:
     * * 204 No Content
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id or payload is invalid.
     *   * JsonPatchInvalid - The JSON Patch provided is invalid.
     *   * ValidationFailed - Applying the patch results in an invalid digital twin.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     * * 412 Precondition Failed
     *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param patchDocument An update specification described by JSON Patch. Updates to property values and
     *                      $model elements may happen in the same request. Operations are limited to add, replace and remove.
     * @param options The options parameters.
     */
    update(id, patchDocument, options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, patchDocument, options: operationOptions }, updateOperationSpec);
    }
    /**
     * Retrieves a relationship between two digital twins.
     * Status codes:
     * * 200 OK
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id or relationship id is invalid.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     *   * RelationshipNotFound - The relationship was not found.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param relationshipId The id of the relationship. The id is unique within the digital twin and case
     *                       sensitive.
     * @param options The options parameters.
     */
    getRelationshipById(id, relationshipId, options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, relationshipId, options: operationOptions }, getRelationshipByIdOperationSpec);
    }
    /**
     * Adds a relationship between two digital twins.
     * Status codes:
     * * 200 OK
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id, relationship id, or payload is invalid.
     *   * InvalidRelationship - The relationship is invalid.
     *   * OperationNotAllowed - The relationship cannot connect to the same digital twin.
     *   * ValidationFailed - The relationship content is invalid.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     *   * TargetTwinNotFound - The digital twin target of the relationship was not found.
     * * 412 Precondition Failed
     *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param relationshipId The id of the relationship. The id is unique within the digital twin and case
     *                       sensitive.
     * @param relationship The data for the relationship.
     * @param options The options parameters.
     */
    addRelationship(id, relationshipId, relationship, options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, relationshipId, relationship, options: operationOptions }, addRelationshipOperationSpec);
    }
    /**
     * Deletes a relationship between two digital twins.
     * Status codes:
     * * 204 No Content
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id or relationship id is invalid.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     *   * RelationshipNotFound - The relationship was not found.
     * * 412 Precondition Failed
     *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param relationshipId The id of the relationship. The id is unique within the digital twin and case
     *                       sensitive.
     * @param options The options parameters.
     */
    deleteRelationship(id, relationshipId, options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, relationshipId, options: operationOptions }, deleteRelationshipOperationSpec);
    }
    /**
     * Updates the properties on a relationship between two digital twins.
     * Status codes:
     * * 204 No Content
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id or relationship id is invalid.
     *   * InvalidRelationship - The relationship is invalid.
     *   * JsonPatchInvalid - The JSON Patch provided is invalid.
     *   * ValidationFailed - The relationship content is invalid.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     *   * RelationshipNotFound - The relationship was not found.
     * * 409 Conflict
     *   * RelationshipAlreadyExists - The relationship already exists.
     * * 412 Precondition Failed
     *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param relationshipId The id of the relationship. The id is unique within the digital twin and case
     *                       sensitive.
     * @param patchDocument JSON Patch description of the update to the relationship properties.
     * @param options The options parameters.
     */
    updateRelationship(id, relationshipId, patchDocument, options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, relationshipId, patchDocument, options: operationOptions }, updateRelationshipOperationSpec);
    }
    /**
     * Retrieves the relationships from a digital twin.
     * Status codes:
     * * 200 OK
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id is invalid.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param options The options parameters.
     */
    listRelationships(id, options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, options: operationOptions }, listRelationshipsOperationSpec);
    }
    /**
     * Retrieves all incoming relationship for a digital twin.
     * Status codes:
     * * 200 OK
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id is invalid.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param options The options parameters.
     */
    listIncomingRelationships(id, options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, options: operationOptions }, listIncomingRelationshipsOperationSpec);
    }
    /**
     * Sends telemetry on behalf of a digital twin.
     * Status codes:
     * * 204 No Content
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id or message id is invalid.
     *   * ValidationFailed - The telemetry content is invalid.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param messageId A unique message identifier (in the scope of the digital twin id) that is commonly
     *                  used for de-duplicating messages.
     * @param telemetry The telemetry measurements to send from the digital twin.
     * @param options The options parameters.
     */
    sendTelemetry(id, messageId, telemetry, options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, messageId, telemetry, options: operationOptions }, sendTelemetryOperationSpec);
    }
    /**
     * Sends telemetry on behalf of a component in a digital twin.
     * Status codes:
     * * 204 No Content
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id, message id, or component path is invalid.
     *   * ValidationFailed - The telemetry content is invalid.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     *   * ComponentNotFound - The component path was not found.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param componentPath The name of the DTDL component.
     * @param messageId A unique message identifier (in the scope of the digital twin id) that is commonly
     *                  used for de-duplicating messages.
     * @param telemetry The telemetry measurements to send from the digital twin's component.
     * @param options The options parameters.
     */
    sendComponentTelemetry(id, componentPath, messageId, telemetry, options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, componentPath, messageId, telemetry, options: operationOptions }, sendComponentTelemetryOperationSpec);
    }
    /**
     * Retrieves a component from a digital twin.
     * Status codes:
     * * 200 OK
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id or component path is invalid.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     *   * ComponentNotFound - The component path was not found.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param componentPath The name of the DTDL component.
     * @param options The options parameters.
     */
    getComponent(id, componentPath, options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, componentPath, options: operationOptions }, getComponentOperationSpec);
    }
    /**
     * Updates a component on a digital twin.
     * Status codes:
     * * 204 No Content
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id, component path, or payload is invalid.
     *   * JsonPatchInvalid - The JSON Patch provided is invalid.
     *   * ValidationFailed - Applying the patch results in an invalid digital twin.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     * * 412 Precondition Failed
     *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param componentPath The name of the DTDL component.
     * @param patchDocument An update specification described by JSON Patch. Updates to property values and
     *                      $model elements may happen in the same request. Operations are limited to add, replace and remove.
     * @param options The options parameters.
     */
    updateComponent(id, componentPath, patchDocument, options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, componentPath, patchDocument, options: operationOptions }, updateComponentOperationSpec);
    }
    /**
     * ListRelationshipsNext
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param nextLink The nextLink from the previous successful call to the ListRelationships method.
     * @param options The options parameters.
     */
    listRelationshipsNext(id, nextLink, options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, nextLink, options: operationOptions }, listRelationshipsNextOperationSpec);
    }
    /**
     * ListIncomingRelationshipsNext
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param nextLink The nextLink from the previous successful call to the ListIncomingRelationships
     *                 method.
     * @param options The options parameters.
     */
    listIncomingRelationshipsNext(id, nextLink, options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, nextLink, options: operationOptions }, listIncomingRelationshipsNextOperationSpec);
    }
}
// Operation Specifications
const serializer = new coreHttp.Serializer(Mappers, /* isXml */ false);
const getByIdOperationSpec = {
    path: "/digitaltwins/{id}",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: { type: { name: "any" } },
            headersMapper: Mappers.DigitalTwinsGetByIdHeaders
        },
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    queryParameters: [Parameters.apiVersion],
    urlParameters: [Parameters.$host, Parameters.id],
    headerParameters: [Parameters.traceparent, Parameters.tracestate],
    serializer
};
const addOperationSpec = {
    path: "/digitaltwins/{id}",
    httpMethod: "PUT",
    responses: {
        200: {
            bodyMapper: { type: { name: "any" } },
            headersMapper: Mappers.DigitalTwinsAddHeaders
        },
        202: {},
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    requestBody: Parameters.twin,
    queryParameters: [Parameters.apiVersion],
    urlParameters: [Parameters.$host, Parameters.id],
    headerParameters: [
        Parameters.contentType,
        Parameters.traceparent,
        Parameters.tracestate,
        Parameters.ifNoneMatch
    ],
    mediaType: "json",
    serializer
};
const deleteOperationSpec = {
    path: "/digitaltwins/{id}",
    httpMethod: "DELETE",
    responses: {
        204: {},
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    queryParameters: [Parameters.apiVersion],
    urlParameters: [Parameters.$host, Parameters.id],
    headerParameters: [
        Parameters.traceparent,
        Parameters.tracestate,
        Parameters.ifMatch
    ],
    serializer
};
const updateOperationSpec = {
    path: "/digitaltwins/{id}",
    httpMethod: "PATCH",
    responses: {
        202: {},
        204: {
            headersMapper: Mappers.DigitalTwinsUpdateHeaders
        },
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    requestBody: Parameters.patchDocument,
    queryParameters: [Parameters.apiVersion],
    urlParameters: [Parameters.$host, Parameters.id],
    headerParameters: [
        Parameters.traceparent,
        Parameters.tracestate,
        Parameters.contentType1,
        Parameters.ifMatch
    ],
    mediaType: "json",
    serializer
};
const getRelationshipByIdOperationSpec = {
    path: "/digitaltwins/{id}/relationships/{relationshipId}",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: { type: { name: "any" } },
            headersMapper: Mappers.DigitalTwinsGetRelationshipByIdHeaders
        },
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    queryParameters: [Parameters.apiVersion],
    urlParameters: [Parameters.$host, Parameters.id, Parameters.relationshipId],
    headerParameters: [Parameters.traceparent, Parameters.tracestate],
    serializer
};
const addRelationshipOperationSpec = {
    path: "/digitaltwins/{id}/relationships/{relationshipId}",
    httpMethod: "PUT",
    responses: {
        200: {
            bodyMapper: { type: { name: "any" } },
            headersMapper: Mappers.DigitalTwinsAddRelationshipHeaders
        },
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    requestBody: Parameters.relationship,
    queryParameters: [Parameters.apiVersion],
    urlParameters: [Parameters.$host, Parameters.id, Parameters.relationshipId],
    headerParameters: [
        Parameters.contentType,
        Parameters.traceparent,
        Parameters.tracestate,
        Parameters.ifNoneMatch
    ],
    mediaType: "json",
    serializer
};
const deleteRelationshipOperationSpec = {
    path: "/digitaltwins/{id}/relationships/{relationshipId}",
    httpMethod: "DELETE",
    responses: {
        204: {},
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    queryParameters: [Parameters.apiVersion],
    urlParameters: [Parameters.$host, Parameters.id, Parameters.relationshipId],
    headerParameters: [
        Parameters.traceparent,
        Parameters.tracestate,
        Parameters.ifMatch
    ],
    serializer
};
const updateRelationshipOperationSpec = {
    path: "/digitaltwins/{id}/relationships/{relationshipId}",
    httpMethod: "PATCH",
    responses: {
        204: {
            headersMapper: Mappers.DigitalTwinsUpdateRelationshipHeaders
        },
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    requestBody: Parameters.patchDocument,
    queryParameters: [Parameters.apiVersion],
    urlParameters: [Parameters.$host, Parameters.id, Parameters.relationshipId],
    headerParameters: [
        Parameters.traceparent,
        Parameters.tracestate,
        Parameters.contentType1,
        Parameters.ifMatch
    ],
    mediaType: "json",
    serializer
};
const listRelationshipsOperationSpec = {
    path: "/digitaltwins/{id}/relationships",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: Mappers.RelationshipCollection
        },
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    queryParameters: [Parameters.apiVersion, Parameters.relationshipName],
    urlParameters: [Parameters.$host, Parameters.id],
    headerParameters: [Parameters.traceparent, Parameters.tracestate],
    serializer
};
const listIncomingRelationshipsOperationSpec = {
    path: "/digitaltwins/{id}/incomingrelationships",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: Mappers.IncomingRelationshipCollection
        },
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    queryParameters: [Parameters.apiVersion],
    urlParameters: [Parameters.$host, Parameters.id],
    headerParameters: [Parameters.traceparent, Parameters.tracestate],
    serializer
};
const sendTelemetryOperationSpec = {
    path: "/digitaltwins/{id}/telemetry",
    httpMethod: "POST",
    responses: {
        204: {},
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    requestBody: Parameters.telemetry,
    queryParameters: [Parameters.apiVersion],
    urlParameters: [Parameters.$host, Parameters.id],
    headerParameters: [
        Parameters.contentType,
        Parameters.traceparent,
        Parameters.tracestate,
        Parameters.messageId,
        Parameters.telemetrySourceTime
    ],
    mediaType: "json",
    serializer
};
const sendComponentTelemetryOperationSpec = {
    path: "/digitaltwins/{id}/components/{componentPath}/telemetry",
    httpMethod: "POST",
    responses: {
        204: {},
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    requestBody: Parameters.telemetry,
    queryParameters: [Parameters.apiVersion],
    urlParameters: [Parameters.$host, Parameters.id, Parameters.componentPath],
    headerParameters: [
        Parameters.contentType,
        Parameters.traceparent,
        Parameters.tracestate,
        Parameters.messageId,
        Parameters.telemetrySourceTime
    ],
    mediaType: "json",
    serializer
};
const getComponentOperationSpec = {
    path: "/digitaltwins/{id}/components/{componentPath}",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: { type: { name: "any" } },
            headersMapper: Mappers.DigitalTwinsGetComponentHeaders
        },
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    queryParameters: [Parameters.apiVersion],
    urlParameters: [Parameters.$host, Parameters.id, Parameters.componentPath],
    headerParameters: [Parameters.traceparent, Parameters.tracestate],
    serializer
};
const updateComponentOperationSpec = {
    path: "/digitaltwins/{id}/components/{componentPath}",
    httpMethod: "PATCH",
    responses: {
        202: {},
        204: {
            headersMapper: Mappers.DigitalTwinsUpdateComponentHeaders
        },
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    requestBody: Parameters.patchDocument,
    queryParameters: [Parameters.apiVersion],
    urlParameters: [Parameters.$host, Parameters.id, Parameters.componentPath],
    headerParameters: [
        Parameters.traceparent,
        Parameters.tracestate,
        Parameters.contentType1,
        Parameters.ifMatch
    ],
    mediaType: "json",
    serializer
};
const listRelationshipsNextOperationSpec = {
    path: "{nextLink}",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: Mappers.RelationshipCollection
        },
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    queryParameters: [Parameters.apiVersion, Parameters.relationshipName],
    urlParameters: [Parameters.$host, Parameters.id, Parameters.nextLink],
    headerParameters: [Parameters.traceparent, Parameters.tracestate],
    serializer
};
const listIncomingRelationshipsNextOperationSpec = {
    path: "{nextLink}",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: Mappers.IncomingRelationshipCollection
        },
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    queryParameters: [Parameters.apiVersion],
    urlParameters: [Parameters.$host, Parameters.id, Parameters.nextLink],
    headerParameters: [Parameters.traceparent, Parameters.tracestate],
    serializer
};
//# sourceMappingURL=digitalTwins.js.map