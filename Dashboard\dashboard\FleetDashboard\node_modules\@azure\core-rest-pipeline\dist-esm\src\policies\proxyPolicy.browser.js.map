{"version": 3, "file": "proxyPolicy.browser.js", "sourceRoot": "", "sources": ["../../../src/policies/proxyPolicy.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;GAEG;AAEH,MAAM,YAAY,GAAG,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;AAEtF,MAAM,CAAC,MAAM,eAAe,GAAG,aAAa,CAAC;AAE7C,MAAM,UAAU,uBAAuB;IACrC,MAAM,YAAY,CAAC;AACrB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW;IACzB,MAAM,YAAY,CAAC;AACrB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,sBAAsB;IACpC,MAAM,YAAY,CAAC;AACrB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/*\n * NOTE: When moving this file, please update \"browser\" section in package.json\n */\n\nconst NotSupported = new Error(\"proxyPolicy is not supported in browser environment\");\n\nexport const proxyPolicyName = \"proxyPolicy\";\n\nexport function getDefaultProxySettings(): never {\n  throw NotSupported;\n}\n\n/**\n * proxyPolicy is not supported in the browser and attempting\n * to use it will raise an error.\n */\nexport function proxyPolicy(): never {\n  throw NotSupported;\n}\n\n/**\n * A function to reset the cached agents.\n * proxyPolicy is not supported in the browser and attempting\n * to use it will raise an error.\n * @internal\n */\nexport function resetCachedProxyAgents(): never {\n  throw NotSupported;\n}\n"]}