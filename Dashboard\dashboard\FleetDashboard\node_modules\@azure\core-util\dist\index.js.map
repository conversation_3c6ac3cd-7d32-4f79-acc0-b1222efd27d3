{"version": 3, "file": "index.js", "sources": ["../src/isNode.ts", "../src/createAbortablePromise.ts", "../src/delay.ts", "../src/random.ts", "../src/object.ts", "../src/error.ts", "../src/sha256.ts", "../src/typeGuards.ts", "../src/uuidUtils.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * A constant that indicates whether the environment the code is running is Node.JS.\n */\nexport const isNode =\n  typeof process !== \"undefined\" && <PERSON><PERSON><PERSON>(process.version) && <PERSON><PERSON><PERSON>(process.versions?.node);\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortError, AbortSignalLike } from \"@azure/abort-controller\";\n\n/**\n * Options for the createAbortablePromise function.\n */\nexport interface CreateAbortablePromiseOptions {\n  /** A function to be called if the promise was aborted */\n  cleanupBeforeAbort?: () => void;\n  /** An abort signal */\n  abortSignal?: AbortSignalLike;\n  /** An abort error message */\n  abortErrorMsg?: string;\n}\n\n/**\n * Creates an abortable promise.\n * @param buildPromise - A function that takes the resolve and reject functions as parameters.\n * @param options - The options for the abortable promise.\n * @returns A promise that can be aborted.\n */\nexport function createAbortablePromise<T>(\n  buildPromise: (\n    resolve: (value: T | PromiseLike<T>) => void,\n    reject: (reason?: any) => void\n  ) => void,\n  options?: CreateAbortablePromiseOptions\n): Promise<T> {\n  const { cleanupBeforeAbort, abortSignal, abortErrorMsg } = options ?? {};\n  return new Promise((resolve, reject) => {\n    function rejectOnAbort(): void {\n      reject(new AbortError(abortErrorMsg ?? \"The operation was aborted.\"));\n    }\n    function removeListeners(): void {\n      abortSignal?.removeEventListener(\"abort\", onAbort);\n    }\n    function onAbort(): void {\n      cleanupBeforeAbort?.();\n      removeListeners();\n      rejectOnAbort();\n    }\n    if (abortSignal?.aborted) {\n      return rejectOnAbort();\n    }\n    try {\n      buildPromise(\n        (x) => {\n          removeListeners();\n          resolve(x);\n        },\n        (x) => {\n          removeListeners();\n          reject(x);\n        }\n      );\n    } catch (err) {\n      reject(err);\n    }\n    abortSignal?.addEventListener(\"abort\", onAbort);\n  });\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { createAbortablePromise } from \"./createAbortablePromise\";\n\nconst StandardAbortMessage = \"The delay was aborted.\";\n\n/**\n * Options for support abort functionality for the delay method\n */\nexport interface DelayOptions {\n  /**\n   * The abortSignal associated with containing operation.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * The abort error message associated with containing operation.\n   */\n  abortErrorMsg?: string;\n}\n\n/**\n * A wrapper for setTimeout that resolves a promise after timeInMs milliseconds.\n * @param timeInMs - The number of milliseconds to be delayed.\n * @param options - The options for delay - currently abort options\n * @returns Promise that is resolved after timeInMs\n */\nexport function delay(timeInMs: number, options?: DelayOptions): Promise<void> {\n  let token: ReturnType<typeof setTimeout>;\n  const { abortSignal, abortErrorMsg } = options ?? {};\n  return createAbortablePromise(\n    (resolve) => {\n      token = setTimeout(resolve, timeInMs);\n    },\n    {\n      cleanupBeforeAbort: () => clearTimeout(token),\n      abortSignal,\n      abortErrorMsg: abortErrorMsg ?? StandardAbortMessage,\n    }\n  );\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Returns a random integer value between a lower and upper bound,\n * inclusive of both bounds.\n * Note that this uses Math.random and isn't secure. If you need to use\n * this for any kind of security purpose, find a better source of random.\n * @param min - The smallest integer value allowed.\n * @param max - The largest integer value allowed.\n */\nexport function getRandomIntegerInclusive(min: number, max: number): number {\n  // Make sure inputs are integers.\n  min = Math.ceil(min);\n  max = Math.floor(max);\n  // Pick a random offset from zero to the size of the range.\n  // Since Math.random() can never return 1, we have to make the range one larger\n  // in order to be inclusive of the maximum value after we take the floor.\n  const offset = Math.floor(Math.random() * (max - min + 1));\n  return offset + min;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * A generic shape for a plain JS object.\n */\nexport type UnknownObject = { [s: string]: unknown };\n\n/**\n * Helper to determine when an input is a generic JS object.\n * @returns true when input is an object type that is not null, Array, RegExp, or Date.\n */\nexport function isObject(input: unknown): input is UnknownObject {\n  return (\n    typeof input === \"object\" &&\n    input !== null &&\n    !Array.isArray(input) &&\n    !(input instanceof RegExp) &&\n    !(input instanceof Date)\n  );\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { isObject } from \"./object\";\n\n/**\n * Typeguard for an error object shape (has name and message)\n * @param e - Something caught by a catch clause.\n */\nexport function isError(e: unknown): e is Error {\n  if (isObject(e)) {\n    const hasName = typeof e.name === \"string\";\n    const hasMessage = typeof e.message === \"string\";\n    return hasName && hasMessage;\n  }\n  return false;\n}\n\n/**\n * Given what is thought to be an error object, return the message if possible.\n * If the message is missing, returns a stringified version of the input.\n * @param e - Something thrown from a try block\n * @returns The error message or a string of the input\n */\nexport function getErrorMessage(e: unknown): string {\n  if (isError(e)) {\n    return e.message;\n  } else {\n    let stringified: string;\n    try {\n      if (typeof e === \"object\" && e) {\n        stringified = JSON.stringify(e);\n      } else {\n        stringified = String(e);\n      }\n    } catch (err: any) {\n      stringified = \"[unable to stringify input]\";\n    }\n    return `Unknown error ${stringified}`;\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { createHash, createHmac } from \"crypto\";\n\n/**\n * Generates a SHA-256 HMAC signature.\n * @param key - The HMAC key represented as a base64 string, used to generate the cryptographic HMAC hash.\n * @param stringToSign - The data to be signed.\n * @param encoding - The textual encoding to use for the returned HMAC digest.\n */\nexport async function computeSha256Hmac(\n  key: string,\n  stringToSign: string,\n  encoding: \"base64\" | \"hex\"\n): Promise<string> {\n  const decodedKey = Buffer.from(key, \"base64\");\n\n  return createHmac(\"sha256\", decodedKey).update(stringToSign).digest(encoding);\n}\n\n/**\n * Generates a SHA-256 hash.\n * @param content - The data to be included in the hash.\n * @param encoding - The textual encoding to use for the returned hash.\n */\nexport async function computeSha256Hash(\n  content: string,\n  encoding: \"base64\" | \"hex\"\n): Promise<string> {\n  return createHash(\"sha256\").update(content).digest(encoding);\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Helper TypeGuard that checks if something is defined or not.\n * @param thing - Anything\n */\nexport function isDefined<T>(thing: T | undefined | null): thing is T {\n  return typeof thing !== \"undefined\" && thing !== null;\n}\n\n/**\n * Helper TypeGuard that checks if the input is an object with the specified properties.\n * @param thing - Anything.\n * @param properties - The name of the properties that should appear in the object.\n */\nexport function isObjectWithProperties<Thing, PropertyName extends string>(\n  thing: Thing,\n  properties: PropertyName[]\n): thing is Thing & Record<PropertyName, unknown> {\n  if (!isDefined(thing) || typeof thing !== \"object\") {\n    return false;\n  }\n\n  for (const property of properties) {\n    if (!objectHasProperty(thing, property)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Helper TypeGuard that checks if the input is an object with the specified property.\n * @param thing - Any object.\n * @param property - The name of the property that should appear in the object.\n */\nexport function objectHasProperty<Thing, PropertyName extends string>(\n  thing: Thing,\n  property: PropertyName\n): thing is Thing & Record<PropertyName, unknown> {\n  return (\n    isDefined(thing) && typeof thing === \"object\" && property in (thing as Record<string, unknown>)\n  );\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { randomUUID as v4RandomUUID } from \"crypto\";\n\ninterface Crypto {\n  randomUUID(): string;\n}\n\ndeclare const globalThis: {\n  crypto: Crypto;\n};\n\n// NOTE: This is a workaround until we can use `globalThis.crypto.randomUUID` in Node.js 19+.\nconst uuidFunction =\n  typeof globalThis?.crypto?.randomUUID === \"function\"\n    ? globalThis.crypto.randomUUID.bind(globalThis.crypto)\n    : v4RandomUUID;\n\n/**\n * Generated Universally Unique Identifier\n *\n * @returns RFC4122 v4 UUID.\n */\nexport function randomUUID(): string {\n  return uuidFunction();\n}\n"], "names": ["_a", "AbortError", "createHmac", "createHash", "v4RandomUUID"], "mappings": ";;;;;;;AAAA;AACA;;AAEA;;AAEG;AACU,MAAA,MAAM,GACjB,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,CAAAA,IAAA,GAAA,OAAO,CAAC,QAAQ,MAAA,IAAA,IAAAA,IAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,IAAA,CAAE,IAAI;;ACP9F;AAiBA;;;;;AAKG;AACa,SAAA,sBAAsB,CACpC,YAGS,EACT,OAAuC,EAAA;AAEvC,IAAA,MAAM,EAAE,kBAAkB,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,OAAO,aAAP,OAAO,KAAA,KAAA,CAAA,GAAP,OAAO,GAAI,EAAE,CAAC;IACzE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;AACrC,QAAA,SAAS,aAAa,GAAA;AACpB,YAAA,MAAM,CAAC,IAAIC,0BAAU,CAAC,aAAa,KAAA,IAAA,IAAb,aAAa,KAAA,KAAA,CAAA,GAAb,aAAa,GAAI,4BAA4B,CAAC,CAAC,CAAC;SACvE;AACD,QAAA,SAAS,eAAe,GAAA;YACtB,WAAW,KAAA,IAAA,IAAX,WAAW,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAX,WAAW,CAAE,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;SACpD;AACD,QAAA,SAAS,OAAO,GAAA;AACd,YAAA,kBAAkB,KAAlB,IAAA,IAAA,kBAAkB,KAAlB,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,kBAAkB,EAAI,CAAC;AACvB,YAAA,eAAe,EAAE,CAAC;AAClB,YAAA,aAAa,EAAE,CAAC;SACjB;AACD,QAAA,IAAI,WAAW,KAAX,IAAA,IAAA,WAAW,uBAAX,WAAW,CAAE,OAAO,EAAE;YACxB,OAAO,aAAa,EAAE,CAAC;AACxB,SAAA;QACD,IAAI;AACF,YAAA,YAAY,CACV,CAAC,CAAC,KAAI;AACJ,gBAAA,eAAe,EAAE,CAAC;gBAClB,OAAO,CAAC,CAAC,CAAC,CAAC;AACb,aAAC,EACD,CAAC,CAAC,KAAI;AACJ,gBAAA,eAAe,EAAE,CAAC;gBAClB,MAAM,CAAC,CAAC,CAAC,CAAC;AACZ,aAAC,CACF,CAAC;AACH,SAAA;AAAC,QAAA,OAAO,GAAG,EAAE;YACZ,MAAM,CAAC,GAAG,CAAC,CAAC;AACb,SAAA;QACD,WAAW,KAAA,IAAA,IAAX,WAAW,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAX,WAAW,CAAE,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAClD,KAAC,CAAC,CAAC;AACL;;AC9DA;AAMA,MAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAgBtD;;;;;AAKG;AACa,SAAA,KAAK,CAAC,QAAgB,EAAE,OAAsB,EAAA;AAC5D,IAAA,IAAI,KAAoC,CAAC;AACzC,IAAA,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAP,OAAO,GAAI,EAAE,CAAC;AACrD,IAAA,OAAO,sBAAsB,CAC3B,CAAC,OAAO,KAAI;AACV,QAAA,KAAK,GAAG,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AACxC,KAAC,EACD;AACE,QAAA,kBAAkB,EAAE,MAAM,YAAY,CAAC,KAAK,CAAC;QAC7C,WAAW;AACX,QAAA,aAAa,EAAE,aAAa,KAAA,IAAA,IAAb,aAAa,KAAb,KAAA,CAAA,GAAA,aAAa,GAAI,oBAAoB;AACrD,KAAA,CACF,CAAC;AACJ;;ACzCA;AACA;AAEA;;;;;;;AAOG;AACa,SAAA,yBAAyB,CAAC,GAAW,EAAE,GAAW,EAAA;;AAEhE,IAAA,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACrB,IAAA,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;;;;AAItB,IAAA,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3D,OAAO,MAAM,GAAG,GAAG,CAAC;AACtB;;ACpBA;AACA;AAOA;;;AAGG;AACG,SAAU,QAAQ,CAAC,KAAc,EAAA;AACrC,IAAA,QACE,OAAO,KAAK,KAAK,QAAQ;AACzB,QAAA,KAAK,KAAK,IAAI;AACd,QAAA,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;AACrB,QAAA,EAAE,KAAK,YAAY,MAAM,CAAC;AAC1B,QAAA,EAAE,KAAK,YAAY,IAAI,CAAC,EACxB;AACJ;;ACpBA;AAKA;;;AAGG;AACG,SAAU,OAAO,CAAC,CAAU,EAAA;AAChC,IAAA,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE;QACf,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC;QAC3C,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,OAAO,KAAK,QAAQ,CAAC;QACjD,OAAO,OAAO,IAAI,UAAU,CAAC;AAC9B,KAAA;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;AAKG;AACG,SAAU,eAAe,CAAC,CAAU,EAAA;AACxC,IAAA,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;QACd,OAAO,CAAC,CAAC,OAAO,CAAC;AAClB,KAAA;AAAM,SAAA;AACL,QAAA,IAAI,WAAmB,CAAC;QACxB,IAAI;AACF,YAAA,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,EAAE;AAC9B,gBAAA,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACjC,aAAA;AAAM,iBAAA;AACL,gBAAA,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACzB,aAAA;AACF,SAAA;AAAC,QAAA,OAAO,GAAQ,EAAE;YACjB,WAAW,GAAG,6BAA6B,CAAC;AAC7C,SAAA;QACD,OAAO,CAAA,cAAA,EAAiB,WAAW,CAAA,CAAE,CAAC;AACvC,KAAA;AACH;;ACxCA;AAKA;;;;;AAKG;AACI,eAAe,iBAAiB,CACrC,GAAW,EACX,YAAoB,EACpB,QAA0B,EAAA;IAE1B,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AAE9C,IAAA,OAAOC,iBAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AAChF,CAAC;AAED;;;;AAIG;AACI,eAAe,iBAAiB,CACrC,OAAe,EACf,QAA0B,EAAA;AAE1B,IAAA,OAAOC,iBAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC/D;;AC/BA;AACA;AAEA;;;AAGG;AACG,SAAU,SAAS,CAAI,KAA2B,EAAA;IACtD,OAAO,OAAO,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK,IAAI,CAAC;AACxD,CAAC;AAED;;;;AAIG;AACa,SAAA,sBAAsB,CACpC,KAAY,EACZ,UAA0B,EAAA;IAE1B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAClD,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AAED,IAAA,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE;AACjC,QAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;AACvC,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACF,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;AAIG;AACa,SAAA,iBAAiB,CAC/B,KAAY,EACZ,QAAsB,EAAA;AAEtB,IAAA,QACE,SAAS,CAAC,KAAK,CAAC,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,QAAQ,IAAK,KAAiC,EAC/F;AACJ;;AC7CA;AACA;;AAYA;AACA,MAAM,YAAY,GAChB,QAAO,CAAA,EAAA,GAAA,UAAU,KAAV,IAAA,IAAA,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,UAAU,CAAA,KAAK,UAAU;AAClD,MAAE,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;MACpDC,iBAAY,CAAC;AAEnB;;;;AAIG;SACa,UAAU,GAAA;IACxB,OAAO,YAAY,EAAE,CAAC;AACxB;;;;;;;;;;;;;;;;"}