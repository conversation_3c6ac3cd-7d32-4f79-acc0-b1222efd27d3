{"version": 3, "file": "azureDigitalTwinsAPI.js", "sourceRoot": "", "sources": ["../../../src/generated/azureDigitalTwinsAPI.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,OAAO,KAAK,UAAU,MAAM,cAAc,CAAC;AAC3C,OAAO,KAAK,MAAM,MAAM,UAAU,CAAC;AACnC,OAAO,KAAK,OAAO,MAAM,kBAAkB,CAAC;AAC5C,OAAO,EAAE,2BAA2B,EAAE,MAAM,+BAA+B,CAAC;AAG5E,MAAM,oBAAqB,SAAQ,2BAA2B;IAC5D;;;OAGG;IACH,YAAY,OAA4C;QACtD,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,iBAAiB,GAAG,IAAI,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAChE,IAAI,CAAC,KAAK,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,CAAC,YAAY,GAAG,IAAI,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACtD,IAAI,CAAC,WAAW,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;CAMF;AAED,2BAA2B;AAE3B,OAAO,EACL,oBAAoB,EACpB,2BAA2B,EAC3B,MAAM,IAAI,0BAA0B,EACpC,OAAO,IAAI,2BAA2B,EACvC,CAAC;AACF,cAAc,cAAc,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as operations from \"./operations\";\nimport * as Models from \"./models\";\nimport * as Mappers from \"./models/mappers\";\nimport { AzureDigitalTwinsAPIContext } from \"./azureDigitalTwinsAPIContext\";\nimport { AzureDigitalTwinsAPIOptionalParams } from \"./models\";\n\nclass AzureDigitalTwinsAPI extends AzureDigitalTwinsAPIContext {\n  /**\n   * Initializes a new instance of the AzureDigitalTwinsAPI class.\n   * @param options The parameter options\n   */\n  constructor(options?: AzureDigitalTwinsAPIOptionalParams) {\n    super(options);\n    this.digitalTwinModels = new operations.DigitalTwinModels(this);\n    this.query = new operations.Query(this);\n    this.digitalTwins = new operations.DigitalTwins(this);\n    this.eventRoutes = new operations.EventRoutes(this);\n  }\n\n  digitalTwinModels: operations.DigitalTwinModels;\n  query: operations.Query;\n  digitalTwins: operations.DigitalTwins;\n  eventRoutes: operations.EventRoutes;\n}\n\n// Operation Specifications\n\nexport {\n  AzureDigitalTwinsAPI,\n  AzureDigitalTwinsAPIContext,\n  Models as AzureDigitalTwinsAPIModels,\n  Mappers as AzureDigitalTwinsAPIMappers\n};\nexport * from \"./operations\";\n"]}