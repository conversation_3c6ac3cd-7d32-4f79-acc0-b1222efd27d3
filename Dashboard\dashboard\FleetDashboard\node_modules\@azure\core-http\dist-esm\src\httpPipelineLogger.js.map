{"version": 3, "file": "httpPipelineLogger.js", "sourceRoot": "", "sources": ["../../src/httpPipelineLogger.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAoB9D;;GAEG;AACH,MAAM,OAAO,yBAAyB;IACpC;;;OAGG;IACH,YAAmB,eAAqC;QAArC,oBAAe,GAAf,eAAe,CAAsB;IAAG,CAAC;IAE5D;;;;OAIG;IACH,GAAG,CAAC,QAA8B,EAAE,OAAe;QACjD,MAAM,UAAU,GAAG,GAAG,oBAAoB,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE,CAAC;QACnE,QAAQ,QAAQ,EAAE;YAChB,KAAK,oBAAoB,CAAC,KAAK;gBAC7B,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;gBAC1B,MAAM;YAER,KAAK,oBAAoB,CAAC,OAAO;gBAC/B,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACzB,MAAM;YAER,KAAK,oBAAoB,CAAC,IAAI;gBAC5B,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBACxB,MAAM;SACT;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpPipelineLogLevel } from \"./httpPipelineLogLevel\";\n\n/**\n * A Logger that can be added to a HttpPipeline. This enables each RequestPolicy to log messages\n * that can be used for debugging purposes.\n */\nexport interface HttpPipelineLogger {\n  /**\n   * The log level threshold for what logs will be logged.\n   */\n  minimumLogLevel: HttpPipelineLogLevel;\n\n  /**\n   * Log the provided message.\n   * @param logLevel - The HttpLogDetailLevel associated with this message.\n   * @param message - The message to log.\n   */\n  log(logLevel: HttpPipelineLogLevel, message: string): void;\n}\n\n/**\n * A HttpPipelineLogger that will send its logs to the console.\n */\nexport class ConsoleHttpPipelineLogger implements HttpPipelineLogger {\n  /**\n   * Create a new ConsoleHttpPipelineLogger.\n   * @param minimumLogLevel - The log level threshold for what logs will be logged.\n   */\n  constructor(public minimumLogLevel: HttpPipelineLogLevel) {}\n\n  /**\n   * Log the provided message.\n   * @param logLevel - The HttpLogDetailLevel associated with this message.\n   * @param message - The message to log.\n   */\n  log(logLevel: HttpPipelineLogLevel, message: string): void {\n    const logMessage = `${HttpPipelineLogLevel[logLevel]}: ${message}`;\n    switch (logLevel) {\n      case HttpPipelineLogLevel.ERROR:\n        console.error(logMessage);\n        break;\n\n      case HttpPipelineLogLevel.WARNING:\n        console.warn(logMessage);\n        break;\n\n      case HttpPipelineLogLevel.INFO:\n        console.log(logMessage);\n        break;\n    }\n  }\n}\n"]}