{"version": 3, "file": "httpClient.js", "sourceRoot": "", "sources": ["../../src/httpClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { RequestPolicy } from \"./policies/requestPolicy\";\n\n/**\n * An interface that can send HttpRequests and receive promised HttpResponses.\n */\nexport interface HttpClient extends RequestPolicy {}\n"]}