{"version": 3, "file": "createSpanLegacy.js", "sourceRoot": "", "sources": ["../../src/createSpanLegacy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,oEAAoE;AACpE,4EAA4E;AAC5E,6EAA6E;AAE7E,OAAO,EAAQ,kBAAkB,IAAI,6BAA6B,EAAE,MAAM,qBAAqB,CAAC;AAoBhG;;;;;;;;GAQG;AACH,MAAM,UAAU,kBAAkB,CAChC,IAAgB;IAKhB,OAAO,6BAA6B,CAAC,IAAI,CAAC,CAAC;AAC7C,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n// NOTE: we've moved this code into core-tracing but these functions\n// were a part of the GA'd library and can't be removed until the next major\n// release. They currently get called always, even if tracing is not enabled.\n\nimport { Span, createSpanFunction as coreTracingCreateSpanFunction } from \"@azure/core-tracing\";\nimport { OperationOptions } from \"./operationOptions\";\n\n/**\n * This function is only here for compatibility. Use createSpanFunction in core-tracing.\n *\n * @deprecated This function is only here for compatibility. Use core-tracing instead.\n * @hidden\n */\nexport interface SpanConfig {\n  /**\n   * Package name prefix\n   */\n  packagePrefix: string;\n  /**\n   * Service namespace\n   */\n  namespace: string;\n}\n\n/**\n * This function is only here for compatibility. Use createSpanFunction in core-tracing.\n *\n * @deprecated This function is only here for compatibility. Use createSpanFunction in core-tracing.\n * @hidden\n\n * @param spanConfig - The name of the operation being performed.\n * @param tracingOptions - The options for the underlying http request.\n */\nexport function createSpanFunction(\n  args: SpanConfig\n): <T extends OperationOptions>(\n  operationName: string,\n  operationOptions: T\n) => { span: Span; updatedOptions: T } {\n  return coreTracingCreateSpanFunction(args);\n}\n"]}