{"version": 3, "file": "azureApplicationCredential.js", "sourceRoot": "", "sources": ["../../../src/credentials/azureApplicationCredential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAC;AAClE,OAAO,EAAE,gCAAgC,EAAE,MAAM,0BAA0B,CAAC;AAC5E,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAWhE,MAAM,CAAC,MAAM,2BAA2B,GAA4C;IAClF,qBAAqB;IACrB,gCAAgC;CACjC,CAAC;AAEF;;;GAGG;AACH,MAAM,OAAO,0BAA2B,SAAQ,sBAAsB;IACpE;;;;;;;;;;;;;OAaG;IACH,YAAY,OAA2C;QACrD,KAAK,CAAC,GAAG,2BAA2B,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACvE,IAAI,CAAC,kBAAkB;YACrB,gLAAgL,CAAC;IACrL,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AzureApplicationCredentialOptions } from \"./azureApplicationCredentialOptions\";\nimport { ChainedTokenCredential } from \"./chainedTokenCredential\";\nimport { DefaultManagedIdentityCredential } from \"./defaultAzureCredential\";\nimport { EnvironmentCredential } from \"./environmentCredential\";\nimport { TokenCredential } from \"@azure/core-auth\";\n\n/**\n * The type of a class that implements TokenCredential and accepts\n * `ApplicationCredentialOptions`.\n */\ninterface AzureApplicationCredentialConstructor {\n  new (options?: AzureApplicationCredentialOptions): TokenCredential;\n}\n\nexport const AzureApplicationCredentials: AzureApplicationCredentialConstructor[] = [\n  EnvironmentCredential,\n  DefaultManagedIdentityCredential,\n];\n\n/**\n * Provides a default {@link ChainedTokenCredential} configuration that should\n * work for most applications that use the Azure SDK.\n */\nexport class AzureApplicationCredential extends ChainedTokenCredential {\n  /**\n   * Creates an instance of the AzureApplicationCredential class.\n   *\n   * The AzureApplicationCredential provides a default {@link ChainedTokenCredential} configuration that should\n   * work for most applications deployed on Azure. The following credential types will be tried, in order:\n   *\n   * - {@link EnvironmentCredential}\n   * - {@link ManagedIdentityCredential}\n   *\n   * Consult the documentation of these credential types for more information\n   * on how they attempt authentication.\n   *\n   * @param options - Optional parameters. See {@link AzureApplicationCredentialOptions}.\n   */\n  constructor(options?: AzureApplicationCredentialOptions) {\n    super(...AzureApplicationCredentials.map((ctor) => new ctor(options)));\n    this.UnavailableMessage =\n      \"ApplicationCredential => failed to retrieve a token from the included credentials. To troubleshoot, visit https://aka.ms/azsdk/js/identity/applicationcredential/troubleshoot.\";\n  }\n}\n"]}