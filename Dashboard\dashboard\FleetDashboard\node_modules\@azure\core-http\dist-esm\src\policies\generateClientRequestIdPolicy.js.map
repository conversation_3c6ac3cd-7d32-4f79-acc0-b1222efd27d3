{"version": 3, "file": "generateClientRequestIdPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/generateClientRequestIdPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EACL,iBAAiB,GAIlB,MAAM,iBAAiB,CAAC;AAIzB;;;GAGG;AACH,MAAM,UAAU,6BAA6B,CAC3C,mBAAmB,GAAG,wBAAwB;IAE9C,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,EAAE,EAAE;YACnE,OAAO,IAAI,6BAA6B,CAAC,UAAU,EAAE,OAAO,EAAE,mBAAmB,CAAC,CAAC;QACrF,CAAC;KACF,CAAC;AACJ,CAAC;AAED,MAAM,OAAO,6BAA8B,SAAQ,iBAAiB;IAClE,YACE,UAAyB,EACzB,OAA6B,EACrB,oBAA4B;QAEpC,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAFnB,yBAAoB,GAApB,oBAAoB,CAAQ;IAGtC,CAAC;IAEM,WAAW,CAAC,OAAwB;QACzC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE;YACxD,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;SACnE;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"./requestPolicy\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\n\n/**\n * Creates a policy that assigns a unique request id to outgoing requests.\n * @param requestIdHeaderName - The name of the header to use when assigning the unique id to the request.\n */\nexport function generateClientRequestIdPolicy(\n  requestIdHeaderName = \"x-ms-client-request-id\"\n): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new GenerateClientRequestIdPolicy(nextPolicy, options, requestIdHeaderName);\n    },\n  };\n}\n\nexport class GenerateClientRequestIdPolicy extends BaseRequestPolicy {\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    private _requestIdHeaderName: string\n  ) {\n    super(nextPolicy, options);\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    if (!request.headers.contains(this._requestIdHeaderName)) {\n      request.headers.set(this._requestIdHeaderName, request.requestId);\n    }\n    return this._nextPolicy.sendRequest(request);\n  }\n}\n"]}