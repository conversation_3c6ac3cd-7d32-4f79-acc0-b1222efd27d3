{"version": 3, "file": "environmentCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/environmentCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions\";\n\n/**\n * Enables authentication to Azure Active Directory depending on the available environment variables.\n * Defines options for the EnvironmentCredential class.\n */\nexport interface EnvironmentCredentialOptions extends MultiTenantTokenCredentialOptions {}\n"]}