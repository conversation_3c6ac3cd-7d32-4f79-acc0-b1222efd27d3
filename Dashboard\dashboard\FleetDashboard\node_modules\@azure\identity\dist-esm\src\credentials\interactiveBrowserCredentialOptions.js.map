{"version": 3, "file": "interactiveBrowserCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/interactiveBrowserCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { CredentialPersistenceOptions } from \"./credentialPersistenceOptions\";\nimport { InteractiveCredentialOptions } from \"./interactiveCredentialOptions\";\n\n/**\n * (Browser-only feature)\n * The \"login style\" to use in the authentication flow:\n * - \"redirect\" redirects the user to the authentication page and then\n *   redirects them back to the page once authentication is completed.\n * - \"popup\" opens a new browser window through with the redirect flow\n *   is initiated.  The user's existing browser window does not leave\n *   the current page\n */\nexport type BrowserLoginStyle = \"redirect\" | \"popup\";\n\n/**\n * Defines the common options for the InteractiveBrowserCredential class.\n */\nexport interface InteractiveBrowserCredentialNodeOptions\n  extends InteractiveCredentialOptions,\n    CredentialPersistenceOptions {\n  /**\n   * Gets the redirect URI of the application. This should be same as the value\n   * in the application registration portal.  Defaults to `window.location.href`.\n   */\n  redirectUri?: string | (() => string);\n\n  /**\n   * The Azure Active Directory tenant (directory) ID.\n   */\n  tenantId?: string;\n\n  /**\n   * The client (application) ID of an App Registration in the tenant.\n   */\n  clientId?: string;\n\n  /**\n   * loginHint allows a user name to be pre-selected for interactive logins.\n   * Setting this option skips the account selection prompt and immediately attempts to login with the specified account.\n   */\n  loginHint?: string;\n}\n\n/**\n * Defines the common options for the InteractiveBrowserCredential class.\n */\nexport interface InteractiveBrowserCredentialInBrowserOptions extends InteractiveCredentialOptions {\n  /**\n   * Gets the redirect URI of the application. This should be same as the value\n   * in the application registration portal.  Defaults to `window.location.href`.\n   */\n  redirectUri?: string | (() => string);\n\n  /**\n   * The Azure Active Directory tenant (directory) ID.\n   */\n  tenantId?: string;\n\n  /**\n   * The client (application) ID of an App Registration in the tenant.\n   * This parameter is required on the browser.\n   */\n  clientId: string;\n\n  /**\n   * Specifies whether a redirect or a popup window should be used to\n   * initiate the user authentication flow. Possible values are \"redirect\"\n   * or \"popup\" (default) for browser and \"popup\" (default) for node.\n   *\n   */\n  loginStyle?: BrowserLoginStyle;\n\n  /**\n   * loginHint allows a user name to be pre-selected for interactive logins.\n   * Setting this option skips the account selection prompt and immediately attempts to login with the specified account.\n   */\n  loginHint?: string;\n}\n"]}