{"version": 3, "file": "httpPipelineLogLevel.js", "sourceRoot": "", "sources": ["../../src/httpPipelineLogLevel.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;GAEG;AACH,MAAM,CAAN,IAAY,oBAoBX;AApBD,WAAY,oBAAoB;IAC9B;;OAEG;IACH,6DAAG,CAAA;IAEH;;OAEG;IACH,iEAAK,CAAA;IAEL;;OAEG;IACH,qEAAO,CAAA;IAEP;;OAEG;IACH,+DAAI,CAAA;AACN,CAAC,EApBW,oBAAoB,KAApB,oBAAoB,QAoB/B", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * The different levels of logs that can be used with the HttpPipelineLogger.\n */\nexport enum HttpPipelineLogLevel {\n  /**\n   * A log level that indicates that no logs will be logged.\n   */\n  OFF,\n\n  /**\n   * An error log.\n   */\n  ERROR,\n\n  /**\n   * A warning log.\n   */\n  WARNING,\n\n  /**\n   * An information log.\n   */\n  INFO,\n}\n"]}