{"version": 3, "file": "bearerTokenAuthenticationPolicy.d.ts", "sourceRoot": "", "sources": ["../../../../src/policies/bearerTokenAuthenticationPolicy.ts"], "names": [], "mappings": "AAGA,OAAO,EAAgC,eAAe,EAAE,MAAM,kBAAkB,CAAC;AACjF,OAAO,EAGL,oBAAoB,EAErB,MAAM,2BAA2B,CAAC;AAgBnC,UAAU,kBAAkB;IAC1B;;;;;;;OAOG;IACH,uBAAuB,EAAE,MAAM,CAAC;IAChC;;OAEG;IACH,iBAAiB,EAAE,MAAM,CAAC;IAC1B;;;OAGG;IACH,iBAAiB,EAAE,MAAM,CAAC;CAC3B;AAGD,eAAO,MAAM,sBAAsB,EAAE,kBAIpC,CAAC;AA2KF;;;;;;GAMG;AACH,wBAAgB,+BAA+B,CAC7C,UAAU,EAAE,eAAe,EAC3B,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,GACxB,oBAAoB,CAgCtB"}