{"version": 3, "file": "hex.js", "sourceRoot": "", "sources": ["../../src/hex.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;;;GAIG;AACH,MAAM,UAAU,WAAW,CAAC,MAAmB;IAC7C,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;IACrC,OAAO,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC7D,CAAC;AAED;;;;GAIG;AACH,SAAS,SAAS,CAAC,IAAY;IAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC9B,OAAO,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC;AAC5C,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Converts an ArrayBuffer to a hexadecimal string.\n * @param buffer - Raw binary data.\n * @internal\n */\nexport function bufferToHex(buffer: ArrayBuffer): string {\n  const bytes = new Uint8Array(buffer);\n  return Array.prototype.map.call(bytes, byteToHex).join(\"\");\n}\n\n/**\n * Converts a byte to a hexadecimal string.\n * @param byte - An integer representation of a byte.\n * @internal\n */\nfunction byteToHex(byte: number): string {\n  const hex = byte.toString(16);\n  return hex.length === 2 ? hex : `0${hex}`;\n}\n"]}