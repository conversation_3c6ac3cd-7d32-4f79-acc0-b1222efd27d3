'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var tslib = require('tslib');
var coreHttp = require('@azure/core-http');
var coreTracing = require('@azure/core-tracing');
var logger$1 = require('@azure/logger');

function _interopNamespace(e) {
    if (e && e.__esModule) return e;
    var n = Object.create(null);
    if (e) {
        Object.keys(e).forEach(function (k) {
            if (k !== 'default') {
                var d = Object.getOwnPropertyDescriptor(e, k);
                Object.defineProperty(n, k, d.get ? d : {
                    enumerable: true,
                    get: function () { return e[k]; }
                });
            }
        });
    }
    n["default"] = e;
    return Object.freeze(n);
}

var coreHttp__namespace = /*#__PURE__*/_interopNamespace(coreHttp);

/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
const DigitalTwinsModelData = {
    type: {
        name: "Composite",
        className: "DigitalTwinsModelData",
        modelProperties: {
            displayName: {
                serializedName: "displayName",
                type: {
                    name: "Dictionary",
                    value: { type: { name: "String" } }
                }
            },
            description: {
                serializedName: "description",
                type: {
                    name: "Dictionary",
                    value: { type: { name: "String" } }
                }
            },
            id: {
                serializedName: "id",
                required: true,
                type: {
                    name: "String"
                }
            },
            uploadTime: {
                serializedName: "uploadTime",
                type: {
                    name: "DateTime"
                }
            },
            decommissioned: {
                serializedName: "decommissioned",
                type: {
                    name: "Boolean"
                }
            },
            model: {
                serializedName: "model",
                type: {
                    name: "any"
                }
            }
        }
    }
};
const ErrorResponse = {
    type: {
        name: "Composite",
        className: "ErrorResponse",
        modelProperties: {
            error: {
                serializedName: "error",
                type: {
                    name: "Composite",
                    className: "ErrorModel"
                }
            }
        }
    }
};
const ErrorModel = {
    type: {
        name: "Composite",
        className: "ErrorModel",
        modelProperties: {
            code: {
                serializedName: "code",
                readOnly: true,
                type: {
                    name: "String"
                }
            },
            message: {
                serializedName: "message",
                readOnly: true,
                type: {
                    name: "String"
                }
            },
            details: {
                serializedName: "details",
                readOnly: true,
                type: {
                    name: "Sequence",
                    element: { type: { name: "Composite", className: "ErrorModel" } }
                }
            },
            innererror: {
                serializedName: "innererror",
                type: {
                    name: "Composite",
                    className: "InnerError"
                }
            }
        }
    }
};
const InnerError = {
    type: {
        name: "Composite",
        className: "InnerError",
        modelProperties: {
            code: {
                serializedName: "code",
                type: {
                    name: "String"
                }
            },
            innererror: {
                serializedName: "innererror",
                type: {
                    name: "Composite",
                    className: "InnerError"
                }
            }
        }
    }
};
const PagedDigitalTwinsModelDataCollection = {
    type: {
        name: "Composite",
        className: "PagedDigitalTwinsModelDataCollection",
        modelProperties: {
            value: {
                serializedName: "value",
                type: {
                    name: "Sequence",
                    element: {
                        type: { name: "Composite", className: "DigitalTwinsModelData" }
                    }
                }
            },
            nextLink: {
                serializedName: "nextLink",
                type: {
                    name: "String"
                }
            }
        }
    }
};
const QuerySpecification = {
    type: {
        name: "Composite",
        className: "QuerySpecification",
        modelProperties: {
            query: {
                serializedName: "query",
                type: {
                    name: "String"
                }
            },
            continuationToken: {
                serializedName: "continuationToken",
                type: {
                    name: "String"
                }
            }
        }
    }
};
const QueryResult = {
    type: {
        name: "Composite",
        className: "QueryResult",
        modelProperties: {
            value: {
                serializedName: "value",
                type: {
                    name: "Sequence",
                    element: { type: { name: "any" } }
                }
            },
            continuationToken: {
                serializedName: "continuationToken",
                type: {
                    name: "String"
                }
            }
        }
    }
};
const RelationshipCollection = {
    type: {
        name: "Composite",
        className: "RelationshipCollection",
        modelProperties: {
            value: {
                serializedName: "value",
                type: {
                    name: "Sequence",
                    element: { type: { name: "any" } }
                }
            },
            nextLink: {
                serializedName: "nextLink",
                type: {
                    name: "String"
                }
            }
        }
    }
};
const IncomingRelationshipCollection = {
    type: {
        name: "Composite",
        className: "IncomingRelationshipCollection",
        modelProperties: {
            value: {
                serializedName: "value",
                type: {
                    name: "Sequence",
                    element: {
                        type: { name: "Composite", className: "IncomingRelationship" }
                    }
                }
            },
            nextLink: {
                serializedName: "nextLink",
                type: {
                    name: "String"
                }
            }
        }
    }
};
const IncomingRelationship = {
    type: {
        name: "Composite",
        className: "IncomingRelationship",
        modelProperties: {
            relationshipId: {
                serializedName: "$relationshipId",
                type: {
                    name: "String"
                }
            },
            sourceId: {
                serializedName: "$sourceId",
                type: {
                    name: "String"
                }
            },
            relationshipName: {
                serializedName: "$relationshipName",
                type: {
                    name: "String"
                }
            },
            relationshipLink: {
                serializedName: "$relationshipLink",
                type: {
                    name: "String"
                }
            }
        }
    }
};
const EventRouteCollection = {
    type: {
        name: "Composite",
        className: "EventRouteCollection",
        modelProperties: {
            value: {
                serializedName: "value",
                type: {
                    name: "Sequence",
                    element: { type: { name: "Composite", className: "EventRoute" } }
                }
            },
            nextLink: {
                serializedName: "nextLink",
                type: {
                    name: "String"
                }
            }
        }
    }
};
const EventRoute = {
    type: {
        name: "Composite",
        className: "EventRoute",
        modelProperties: {
            id: {
                serializedName: "id",
                readOnly: true,
                type: {
                    name: "String"
                }
            },
            endpointName: {
                serializedName: "endpointName",
                required: true,
                type: {
                    name: "String"
                }
            },
            filter: {
                serializedName: "filter",
                required: true,
                type: {
                    name: "String"
                }
            }
        }
    }
};
const QueryQueryTwinsHeaders = {
    type: {
        name: "Composite",
        className: "QueryQueryTwinsHeaders",
        modelProperties: {
            queryCharge: {
                serializedName: "query-charge",
                type: {
                    name: "Number"
                }
            }
        }
    }
};
const DigitalTwinsGetByIdHeaders = {
    type: {
        name: "Composite",
        className: "DigitalTwinsGetByIdHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                type: {
                    name: "String"
                }
            }
        }
    }
};
const DigitalTwinsAddHeaders = {
    type: {
        name: "Composite",
        className: "DigitalTwinsAddHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                type: {
                    name: "String"
                }
            }
        }
    }
};
const DigitalTwinsUpdateHeaders = {
    type: {
        name: "Composite",
        className: "DigitalTwinsUpdateHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                type: {
                    name: "String"
                }
            }
        }
    }
};
const DigitalTwinsGetRelationshipByIdHeaders = {
    type: {
        name: "Composite",
        className: "DigitalTwinsGetRelationshipByIdHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                type: {
                    name: "String"
                }
            }
        }
    }
};
const DigitalTwinsAddRelationshipHeaders = {
    type: {
        name: "Composite",
        className: "DigitalTwinsAddRelationshipHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                type: {
                    name: "String"
                }
            }
        }
    }
};
const DigitalTwinsUpdateRelationshipHeaders = {
    type: {
        name: "Composite",
        className: "DigitalTwinsUpdateRelationshipHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                type: {
                    name: "String"
                }
            }
        }
    }
};
const DigitalTwinsGetComponentHeaders = {
    type: {
        name: "Composite",
        className: "DigitalTwinsGetComponentHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                type: {
                    name: "String"
                }
            }
        }
    }
};
const DigitalTwinsUpdateComponentHeaders = {
    type: {
        name: "Composite",
        className: "DigitalTwinsUpdateComponentHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                type: {
                    name: "String"
                }
            }
        }
    }
};

var Mappers = /*#__PURE__*/Object.freeze({
    __proto__: null,
    DigitalTwinsModelData: DigitalTwinsModelData,
    ErrorResponse: ErrorResponse,
    ErrorModel: ErrorModel,
    InnerError: InnerError,
    PagedDigitalTwinsModelDataCollection: PagedDigitalTwinsModelDataCollection,
    QuerySpecification: QuerySpecification,
    QueryResult: QueryResult,
    RelationshipCollection: RelationshipCollection,
    IncomingRelationshipCollection: IncomingRelationshipCollection,
    IncomingRelationship: IncomingRelationship,
    EventRouteCollection: EventRouteCollection,
    EventRoute: EventRoute,
    QueryQueryTwinsHeaders: QueryQueryTwinsHeaders,
    DigitalTwinsGetByIdHeaders: DigitalTwinsGetByIdHeaders,
    DigitalTwinsAddHeaders: DigitalTwinsAddHeaders,
    DigitalTwinsUpdateHeaders: DigitalTwinsUpdateHeaders,
    DigitalTwinsGetRelationshipByIdHeaders: DigitalTwinsGetRelationshipByIdHeaders,
    DigitalTwinsAddRelationshipHeaders: DigitalTwinsAddRelationshipHeaders,
    DigitalTwinsUpdateRelationshipHeaders: DigitalTwinsUpdateRelationshipHeaders,
    DigitalTwinsGetComponentHeaders: DigitalTwinsGetComponentHeaders,
    DigitalTwinsUpdateComponentHeaders: DigitalTwinsUpdateComponentHeaders
});

/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
const contentType = {
    parameterPath: ["options", "contentType"],
    mapper: {
        defaultValue: "application/json",
        isConstant: true,
        serializedName: "Content-Type",
        type: {
            name: "String"
        }
    }
};
const models = {
    parameterPath: ["options", "models"],
    mapper: {
        constraints: {
            MinItems: 1,
            UniqueItems: true
        },
        serializedName: "models",
        type: {
            name: "Sequence",
            element: { type: { name: "any" } }
        }
    }
};
const $host = {
    parameterPath: "$host",
    mapper: {
        serializedName: "$host",
        required: true,
        type: {
            name: "String"
        }
    },
    skipEncoding: true
};
const traceparent = {
    parameterPath: ["options", "traceparent"],
    mapper: {
        serializedName: "traceparent",
        type: {
            name: "String"
        }
    }
};
const tracestate = {
    parameterPath: ["options", "tracestate"],
    mapper: {
        serializedName: "tracestate",
        type: {
            name: "String"
        }
    }
};
const apiVersion = {
    parameterPath: "apiVersion",
    mapper: {
        defaultValue: "2022-05-31",
        isConstant: true,
        serializedName: "api-version",
        type: {
            name: "String"
        }
    }
};
const dependenciesFor = {
    parameterPath: ["options", "dependenciesFor"],
    mapper: {
        serializedName: "dependenciesFor",
        type: {
            name: "Sequence",
            element: { type: { name: "String" } }
        }
    },
    collectionFormat: coreHttp.QueryCollectionFormat.Csv
};
const includeModelDefinition = {
    parameterPath: ["options", "includeModelDefinition"],
    mapper: {
        serializedName: "includeModelDefinition",
        type: {
            name: "Boolean"
        }
    }
};
const maxItemsPerPage = {
    parameterPath: ["options", "maxItemsPerPage"],
    mapper: {
        serializedName: "max-items-per-page",
        type: {
            name: "Number"
        }
    }
};
const id = {
    parameterPath: "id",
    mapper: {
        serializedName: "id",
        required: true,
        type: {
            name: "String"
        }
    }
};
const contentType1 = {
    parameterPath: ["options", "contentType"],
    mapper: {
        defaultValue: "application/json-patch+json",
        isConstant: true,
        serializedName: "Content-Type",
        type: {
            name: "String"
        }
    }
};
const updateModel = {
    parameterPath: "updateModel",
    mapper: {
        serializedName: "updateModel",
        required: true,
        type: {
            name: "Sequence",
            element: { type: { name: "any" } }
        }
    }
};
const nextLink = {
    parameterPath: "nextLink",
    mapper: {
        serializedName: "nextLink",
        required: true,
        type: {
            name: "String"
        }
    },
    skipEncoding: true
};
const querySpecification = {
    parameterPath: "querySpecification",
    mapper: QuerySpecification
};
const twin = {
    parameterPath: "twin",
    mapper: {
        serializedName: "twin",
        required: true,
        type: {
            name: "any"
        }
    }
};
const ifNoneMatch = {
    parameterPath: ["options", "ifNoneMatch"],
    mapper: {
        serializedName: "If-None-Match",
        type: {
            name: "String"
        }
    }
};
const ifMatch = {
    parameterPath: ["options", "ifMatch"],
    mapper: {
        serializedName: "If-Match",
        type: {
            name: "String"
        }
    }
};
const patchDocument = {
    parameterPath: "patchDocument",
    mapper: {
        serializedName: "patchDocument",
        required: true,
        type: {
            name: "Sequence",
            element: { type: { name: "any" } }
        }
    }
};
const relationshipId = {
    parameterPath: "relationshipId",
    mapper: {
        serializedName: "relationshipId",
        required: true,
        type: {
            name: "String"
        }
    }
};
const relationship = {
    parameterPath: "relationship",
    mapper: {
        serializedName: "relationship",
        required: true,
        type: {
            name: "any"
        }
    }
};
const relationshipName = {
    parameterPath: ["options", "relationshipName"],
    mapper: {
        serializedName: "relationshipName",
        type: {
            name: "String"
        }
    }
};
const telemetry = {
    parameterPath: "telemetry",
    mapper: {
        serializedName: "telemetry",
        required: true,
        type: {
            name: "any"
        }
    }
};
const messageId = {
    parameterPath: "messageId",
    mapper: {
        serializedName: "Message-Id",
        required: true,
        type: {
            name: "String"
        }
    }
};
const telemetrySourceTime = {
    parameterPath: ["options", "telemetrySourceTime"],
    mapper: {
        serializedName: "Telemetry-Source-Time",
        type: {
            name: "String"
        }
    }
};
const componentPath = {
    parameterPath: "componentPath",
    mapper: {
        serializedName: "componentPath",
        required: true,
        type: {
            name: "String"
        }
    }
};
const eventRoute = {
    parameterPath: ["options", "eventRoute"],
    mapper: EventRoute
};

/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
/**
 * Class representing a DigitalTwinModels.
 */
class DigitalTwinModels {
    /**
     * Initialize a new instance of the class DigitalTwinModels class.
     * @param client Reference to the service client
     */
    constructor(client) {
        this.client = client;
    }
    /**
     * Uploads one or more models. When any error occurs, no models are uploaded.
     * Status codes:
     * * 201 Created
     * * 400 Bad Request
     *   * DTDLParserError - The models provided are not valid DTDL.
     *   * InvalidArgument - The model id is invalid.
     *   * LimitExceeded - The maximum number of model ids allowed in 'dependenciesFor' has been reached.
     *   * ModelVersionNotSupported - The version of DTDL used is not supported.
     * * 409 Conflict
     *   * ModelAlreadyExists - The model provided already exists.
     * @param options The options parameters.
     */
    add(options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ options: operationOptions }, addOperationSpec$2);
    }
    /**
     * Retrieves model metadata and, optionally, model definitions.
     * Status codes:
     * * 200 OK
     * * 400 Bad Request
     *   * InvalidArgument - The model id is invalid.
     *   * LimitExceeded - The maximum number of model ids allowed in 'dependenciesFor' has been reached.
     * * 404 Not Found
     *   * ModelNotFound - The model was not found.
     * @param options The options parameters.
     */
    list(options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ options: operationOptions }, listOperationSpec$1);
    }
    /**
     * Retrieves model metadata and optionally the model definition.
     * Status codes:
     * * 200 OK
     * * 400 Bad Request
     *   * InvalidArgument - The model id is invalid.
     *   * MissingArgument - The model id was not provided.
     * * 404 Not Found
     *   * ModelNotFound - The model was not found.
     * @param id The id for the model. The id is globally unique and case sensitive.
     * @param options The options parameters.
     */
    getById(id, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, options: operationOptions }, getByIdOperationSpec$2);
    }
    /**
     * Updates the metadata for a model.
     * Status codes:
     * * 204 No Content
     * * 400 Bad Request
     *   * InvalidArgument - The model id is invalid.
     *   * JsonPatchInvalid - The JSON Patch provided is invalid.
     *   * MissingArgument - The model id was not provided.
     * * 404 Not Found
     *   * ModelNotFound - The model was not found.
     * * 409 Conflict
     *   * ModelReferencesNotDecommissioned - The model refers to models that are not decommissioned.
     * @param id The id for the model. The id is globally unique and case sensitive.
     * @param updateModel An update specification described by JSON Patch. Only the decommissioned property
     *                    can be replaced.
     * @param options The options parameters.
     */
    update(id, updateModel, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, updateModel, options: operationOptions }, updateOperationSpec$1);
    }
    /**
     * Deletes a model. A model can only be deleted if no other models reference it.
     * Status codes:
     * * 204 No Content
     * * 400 Bad Request
     *   * InvalidArgument - The model id is invalid.
     *   * MissingArgument - The model id was not provided.
     * * 404 Not Found
     *   * ModelNotFound - The model was not found.
     * * 409 Conflict
     *   * ModelReferencesNotDeleted - The model refers to models that are not deleted.
     * @param id The id for the model. The id is globally unique and case sensitive.
     * @param options The options parameters.
     */
    delete(id, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, options: operationOptions }, deleteOperationSpec$2);
    }
    /**
     * ListNext
     * @param nextLink The nextLink from the previous successful call to the List method.
     * @param options The options parameters.
     */
    listNext(nextLink, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ nextLink, options: operationOptions }, listNextOperationSpec$1);
    }
}
// Operation Specifications
const serializer$3 = new coreHttp__namespace.Serializer(Mappers, /* isXml */ false);
const addOperationSpec$2 = {
    path: "/models",
    httpMethod: "POST",
    responses: {
        201: {
            bodyMapper: {
                type: {
                    name: "Sequence",
                    element: {
                        type: { name: "Composite", className: "DigitalTwinsModelData" }
                    }
                }
            }
        },
        default: {
            bodyMapper: ErrorResponse
        }
    },
    requestBody: models,
    queryParameters: [apiVersion],
    urlParameters: [$host],
    headerParameters: [
        contentType,
        traceparent,
        tracestate
    ],
    mediaType: "json",
    serializer: serializer$3
};
const listOperationSpec$1 = {
    path: "/models",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: PagedDigitalTwinsModelDataCollection
        },
        default: {
            bodyMapper: ErrorResponse
        }
    },
    queryParameters: [
        apiVersion,
        dependenciesFor,
        includeModelDefinition
    ],
    urlParameters: [$host],
    headerParameters: [
        traceparent,
        tracestate,
        maxItemsPerPage
    ],
    serializer: serializer$3
};
const getByIdOperationSpec$2 = {
    path: "/models/{id}",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: DigitalTwinsModelData
        },
        default: {
            bodyMapper: ErrorResponse
        }
    },
    queryParameters: [apiVersion, includeModelDefinition],
    urlParameters: [$host, id],
    headerParameters: [traceparent, tracestate],
    serializer: serializer$3
};
const updateOperationSpec$1 = {
    path: "/models/{id}",
    httpMethod: "PATCH",
    responses: {
        204: {},
        default: {
            bodyMapper: ErrorResponse
        }
    },
    requestBody: updateModel,
    queryParameters: [apiVersion],
    urlParameters: [$host, id],
    headerParameters: [
        traceparent,
        tracestate,
        contentType1
    ],
    mediaType: "json",
    serializer: serializer$3
};
const deleteOperationSpec$2 = {
    path: "/models/{id}",
    httpMethod: "DELETE",
    responses: {
        204: {},
        default: {
            bodyMapper: ErrorResponse
        }
    },
    queryParameters: [apiVersion],
    urlParameters: [$host, id],
    headerParameters: [traceparent, tracestate],
    serializer: serializer$3
};
const listNextOperationSpec$1 = {
    path: "{nextLink}",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: PagedDigitalTwinsModelDataCollection
        },
        default: {
            bodyMapper: ErrorResponse
        }
    },
    queryParameters: [
        apiVersion,
        dependenciesFor,
        includeModelDefinition
    ],
    urlParameters: [$host, nextLink],
    headerParameters: [
        traceparent,
        tracestate,
        maxItemsPerPage
    ],
    serializer: serializer$3
};

/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
/**
 * Class representing a Query.
 */
class Query {
    /**
     * Initialize a new instance of the class Query class.
     * @param client Reference to the service client
     */
    constructor(client) {
        this.client = client;
    }
    /**
     * Executes a query that allows traversing relationships and filtering by property values.
     * Status codes:
     * * 200 OK
     * * 400 Bad Request
     *   * BadRequest - The continuation token is invalid.
     *   * SqlQueryError - The query contains some errors.
     * * 429 Too Many Requests
     *   * QuotaReachedError - The maximum query rate limit has been reached.
     * @param querySpecification The query specification to execute.
     * @param options The options parameters.
     */
    queryTwins(querySpecification, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ querySpecification, options: operationOptions }, queryTwinsOperationSpec);
    }
}
// Operation Specifications
const serializer$2 = new coreHttp__namespace.Serializer(Mappers, /* isXml */ false);
const queryTwinsOperationSpec = {
    path: "/query",
    httpMethod: "POST",
    responses: {
        200: {
            bodyMapper: QueryResult,
            headersMapper: QueryQueryTwinsHeaders
        },
        default: {
            bodyMapper: ErrorResponse
        }
    },
    requestBody: querySpecification,
    queryParameters: [apiVersion],
    urlParameters: [$host],
    headerParameters: [
        contentType,
        traceparent,
        tracestate,
        maxItemsPerPage
    ],
    mediaType: "json",
    serializer: serializer$2
};

/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
/**
 * Class representing a DigitalTwins.
 */
class DigitalTwins {
    /**
     * Initialize a new instance of the class DigitalTwins class.
     * @param client Reference to the service client
     */
    constructor(client) {
        this.client = client;
    }
    /**
     * Retrieves a digital twin.
     * Status codes:
     * * 200 OK
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id is invalid.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param options The options parameters.
     */
    getById(id, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, options: operationOptions }, getByIdOperationSpec$1);
    }
    /**
     * Adds or replaces a digital twin.
     * Status codes:
     * * 200 OK
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id or payload is invalid.
     *   * ModelDecommissioned - The model for the digital twin is decommissioned.
     *   * TwinLimitReached - The maximum number of digital twins allowed has been reached.
     *   * ValidationFailed - The digital twin payload is not valid.
     * * 412 Precondition Failed
     *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param twin The digital twin instance being added. If provided, the $dtId property is ignored.
     * @param options The options parameters.
     */
    add(id, twin, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, twin, options: operationOptions }, addOperationSpec$1);
    }
    /**
     * Deletes a digital twin. All relationships referencing the digital twin must already be deleted.
     * Status codes:
     * * 204 No Content
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id is invalid.
     *   * RelationshipsNotDeleted - The digital twin contains relationships.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     * * 412 Precondition Failed
     *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param options The options parameters.
     */
    delete(id, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, options: operationOptions }, deleteOperationSpec$1);
    }
    /**
     * Updates a digital twin.
     * Status codes:
     * * 204 No Content
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id or payload is invalid.
     *   * JsonPatchInvalid - The JSON Patch provided is invalid.
     *   * ValidationFailed - Applying the patch results in an invalid digital twin.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     * * 412 Precondition Failed
     *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param patchDocument An update specification described by JSON Patch. Updates to property values and
     *                      $model elements may happen in the same request. Operations are limited to add, replace and remove.
     * @param options The options parameters.
     */
    update(id, patchDocument, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, patchDocument, options: operationOptions }, updateOperationSpec);
    }
    /**
     * Retrieves a relationship between two digital twins.
     * Status codes:
     * * 200 OK
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id or relationship id is invalid.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     *   * RelationshipNotFound - The relationship was not found.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param relationshipId The id of the relationship. The id is unique within the digital twin and case
     *                       sensitive.
     * @param options The options parameters.
     */
    getRelationshipById(id, relationshipId, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, relationshipId, options: operationOptions }, getRelationshipByIdOperationSpec);
    }
    /**
     * Adds a relationship between two digital twins.
     * Status codes:
     * * 200 OK
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id, relationship id, or payload is invalid.
     *   * InvalidRelationship - The relationship is invalid.
     *   * OperationNotAllowed - The relationship cannot connect to the same digital twin.
     *   * ValidationFailed - The relationship content is invalid.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     *   * TargetTwinNotFound - The digital twin target of the relationship was not found.
     * * 412 Precondition Failed
     *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param relationshipId The id of the relationship. The id is unique within the digital twin and case
     *                       sensitive.
     * @param relationship The data for the relationship.
     * @param options The options parameters.
     */
    addRelationship(id, relationshipId, relationship, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, relationshipId, relationship, options: operationOptions }, addRelationshipOperationSpec);
    }
    /**
     * Deletes a relationship between two digital twins.
     * Status codes:
     * * 204 No Content
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id or relationship id is invalid.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     *   * RelationshipNotFound - The relationship was not found.
     * * 412 Precondition Failed
     *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param relationshipId The id of the relationship. The id is unique within the digital twin and case
     *                       sensitive.
     * @param options The options parameters.
     */
    deleteRelationship(id, relationshipId, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, relationshipId, options: operationOptions }, deleteRelationshipOperationSpec);
    }
    /**
     * Updates the properties on a relationship between two digital twins.
     * Status codes:
     * * 204 No Content
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id or relationship id is invalid.
     *   * InvalidRelationship - The relationship is invalid.
     *   * JsonPatchInvalid - The JSON Patch provided is invalid.
     *   * ValidationFailed - The relationship content is invalid.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     *   * RelationshipNotFound - The relationship was not found.
     * * 409 Conflict
     *   * RelationshipAlreadyExists - The relationship already exists.
     * * 412 Precondition Failed
     *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param relationshipId The id of the relationship. The id is unique within the digital twin and case
     *                       sensitive.
     * @param patchDocument JSON Patch description of the update to the relationship properties.
     * @param options The options parameters.
     */
    updateRelationship(id, relationshipId, patchDocument, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, relationshipId, patchDocument, options: operationOptions }, updateRelationshipOperationSpec);
    }
    /**
     * Retrieves the relationships from a digital twin.
     * Status codes:
     * * 200 OK
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id is invalid.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param options The options parameters.
     */
    listRelationships(id, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, options: operationOptions }, listRelationshipsOperationSpec);
    }
    /**
     * Retrieves all incoming relationship for a digital twin.
     * Status codes:
     * * 200 OK
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id is invalid.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param options The options parameters.
     */
    listIncomingRelationships(id, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, options: operationOptions }, listIncomingRelationshipsOperationSpec);
    }
    /**
     * Sends telemetry on behalf of a digital twin.
     * Status codes:
     * * 204 No Content
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id or message id is invalid.
     *   * ValidationFailed - The telemetry content is invalid.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param messageId A unique message identifier (in the scope of the digital twin id) that is commonly
     *                  used for de-duplicating messages.
     * @param telemetry The telemetry measurements to send from the digital twin.
     * @param options The options parameters.
     */
    sendTelemetry(id, messageId, telemetry, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, messageId, telemetry, options: operationOptions }, sendTelemetryOperationSpec);
    }
    /**
     * Sends telemetry on behalf of a component in a digital twin.
     * Status codes:
     * * 204 No Content
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id, message id, or component path is invalid.
     *   * ValidationFailed - The telemetry content is invalid.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     *   * ComponentNotFound - The component path was not found.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param componentPath The name of the DTDL component.
     * @param messageId A unique message identifier (in the scope of the digital twin id) that is commonly
     *                  used for de-duplicating messages.
     * @param telemetry The telemetry measurements to send from the digital twin's component.
     * @param options The options parameters.
     */
    sendComponentTelemetry(id, componentPath, messageId, telemetry, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, componentPath, messageId, telemetry, options: operationOptions }, sendComponentTelemetryOperationSpec);
    }
    /**
     * Retrieves a component from a digital twin.
     * Status codes:
     * * 200 OK
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id or component path is invalid.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     *   * ComponentNotFound - The component path was not found.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param componentPath The name of the DTDL component.
     * @param options The options parameters.
     */
    getComponent(id, componentPath, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, componentPath, options: operationOptions }, getComponentOperationSpec);
    }
    /**
     * Updates a component on a digital twin.
     * Status codes:
     * * 204 No Content
     * * 400 Bad Request
     *   * InvalidArgument - The digital twin id, component path, or payload is invalid.
     *   * JsonPatchInvalid - The JSON Patch provided is invalid.
     *   * ValidationFailed - Applying the patch results in an invalid digital twin.
     * * 404 Not Found
     *   * DigitalTwinNotFound - The digital twin was not found.
     * * 412 Precondition Failed
     *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param componentPath The name of the DTDL component.
     * @param patchDocument An update specification described by JSON Patch. Updates to property values and
     *                      $model elements may happen in the same request. Operations are limited to add, replace and remove.
     * @param options The options parameters.
     */
    updateComponent(id, componentPath, patchDocument, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, componentPath, patchDocument, options: operationOptions }, updateComponentOperationSpec);
    }
    /**
     * ListRelationshipsNext
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param nextLink The nextLink from the previous successful call to the ListRelationships method.
     * @param options The options parameters.
     */
    listRelationshipsNext(id, nextLink, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, nextLink, options: operationOptions }, listRelationshipsNextOperationSpec);
    }
    /**
     * ListIncomingRelationshipsNext
     * @param id The id of the digital twin. The id is unique within the service and case sensitive.
     * @param nextLink The nextLink from the previous successful call to the ListIncomingRelationships
     *                 method.
     * @param options The options parameters.
     */
    listIncomingRelationshipsNext(id, nextLink, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, nextLink, options: operationOptions }, listIncomingRelationshipsNextOperationSpec);
    }
}
// Operation Specifications
const serializer$1 = new coreHttp__namespace.Serializer(Mappers, /* isXml */ false);
const getByIdOperationSpec$1 = {
    path: "/digitaltwins/{id}",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: { type: { name: "any" } },
            headersMapper: DigitalTwinsGetByIdHeaders
        },
        default: {
            bodyMapper: ErrorResponse
        }
    },
    queryParameters: [apiVersion],
    urlParameters: [$host, id],
    headerParameters: [traceparent, tracestate],
    serializer: serializer$1
};
const addOperationSpec$1 = {
    path: "/digitaltwins/{id}",
    httpMethod: "PUT",
    responses: {
        200: {
            bodyMapper: { type: { name: "any" } },
            headersMapper: DigitalTwinsAddHeaders
        },
        202: {},
        default: {
            bodyMapper: ErrorResponse
        }
    },
    requestBody: twin,
    queryParameters: [apiVersion],
    urlParameters: [$host, id],
    headerParameters: [
        contentType,
        traceparent,
        tracestate,
        ifNoneMatch
    ],
    mediaType: "json",
    serializer: serializer$1
};
const deleteOperationSpec$1 = {
    path: "/digitaltwins/{id}",
    httpMethod: "DELETE",
    responses: {
        204: {},
        default: {
            bodyMapper: ErrorResponse
        }
    },
    queryParameters: [apiVersion],
    urlParameters: [$host, id],
    headerParameters: [
        traceparent,
        tracestate,
        ifMatch
    ],
    serializer: serializer$1
};
const updateOperationSpec = {
    path: "/digitaltwins/{id}",
    httpMethod: "PATCH",
    responses: {
        202: {},
        204: {
            headersMapper: DigitalTwinsUpdateHeaders
        },
        default: {
            bodyMapper: ErrorResponse
        }
    },
    requestBody: patchDocument,
    queryParameters: [apiVersion],
    urlParameters: [$host, id],
    headerParameters: [
        traceparent,
        tracestate,
        contentType1,
        ifMatch
    ],
    mediaType: "json",
    serializer: serializer$1
};
const getRelationshipByIdOperationSpec = {
    path: "/digitaltwins/{id}/relationships/{relationshipId}",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: { type: { name: "any" } },
            headersMapper: DigitalTwinsGetRelationshipByIdHeaders
        },
        default: {
            bodyMapper: ErrorResponse
        }
    },
    queryParameters: [apiVersion],
    urlParameters: [$host, id, relationshipId],
    headerParameters: [traceparent, tracestate],
    serializer: serializer$1
};
const addRelationshipOperationSpec = {
    path: "/digitaltwins/{id}/relationships/{relationshipId}",
    httpMethod: "PUT",
    responses: {
        200: {
            bodyMapper: { type: { name: "any" } },
            headersMapper: DigitalTwinsAddRelationshipHeaders
        },
        default: {
            bodyMapper: ErrorResponse
        }
    },
    requestBody: relationship,
    queryParameters: [apiVersion],
    urlParameters: [$host, id, relationshipId],
    headerParameters: [
        contentType,
        traceparent,
        tracestate,
        ifNoneMatch
    ],
    mediaType: "json",
    serializer: serializer$1
};
const deleteRelationshipOperationSpec = {
    path: "/digitaltwins/{id}/relationships/{relationshipId}",
    httpMethod: "DELETE",
    responses: {
        204: {},
        default: {
            bodyMapper: ErrorResponse
        }
    },
    queryParameters: [apiVersion],
    urlParameters: [$host, id, relationshipId],
    headerParameters: [
        traceparent,
        tracestate,
        ifMatch
    ],
    serializer: serializer$1
};
const updateRelationshipOperationSpec = {
    path: "/digitaltwins/{id}/relationships/{relationshipId}",
    httpMethod: "PATCH",
    responses: {
        204: {
            headersMapper: DigitalTwinsUpdateRelationshipHeaders
        },
        default: {
            bodyMapper: ErrorResponse
        }
    },
    requestBody: patchDocument,
    queryParameters: [apiVersion],
    urlParameters: [$host, id, relationshipId],
    headerParameters: [
        traceparent,
        tracestate,
        contentType1,
        ifMatch
    ],
    mediaType: "json",
    serializer: serializer$1
};
const listRelationshipsOperationSpec = {
    path: "/digitaltwins/{id}/relationships",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: RelationshipCollection
        },
        default: {
            bodyMapper: ErrorResponse
        }
    },
    queryParameters: [apiVersion, relationshipName],
    urlParameters: [$host, id],
    headerParameters: [traceparent, tracestate],
    serializer: serializer$1
};
const listIncomingRelationshipsOperationSpec = {
    path: "/digitaltwins/{id}/incomingrelationships",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: IncomingRelationshipCollection
        },
        default: {
            bodyMapper: ErrorResponse
        }
    },
    queryParameters: [apiVersion],
    urlParameters: [$host, id],
    headerParameters: [traceparent, tracestate],
    serializer: serializer$1
};
const sendTelemetryOperationSpec = {
    path: "/digitaltwins/{id}/telemetry",
    httpMethod: "POST",
    responses: {
        204: {},
        default: {
            bodyMapper: ErrorResponse
        }
    },
    requestBody: telemetry,
    queryParameters: [apiVersion],
    urlParameters: [$host, id],
    headerParameters: [
        contentType,
        traceparent,
        tracestate,
        messageId,
        telemetrySourceTime
    ],
    mediaType: "json",
    serializer: serializer$1
};
const sendComponentTelemetryOperationSpec = {
    path: "/digitaltwins/{id}/components/{componentPath}/telemetry",
    httpMethod: "POST",
    responses: {
        204: {},
        default: {
            bodyMapper: ErrorResponse
        }
    },
    requestBody: telemetry,
    queryParameters: [apiVersion],
    urlParameters: [$host, id, componentPath],
    headerParameters: [
        contentType,
        traceparent,
        tracestate,
        messageId,
        telemetrySourceTime
    ],
    mediaType: "json",
    serializer: serializer$1
};
const getComponentOperationSpec = {
    path: "/digitaltwins/{id}/components/{componentPath}",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: { type: { name: "any" } },
            headersMapper: DigitalTwinsGetComponentHeaders
        },
        default: {
            bodyMapper: ErrorResponse
        }
    },
    queryParameters: [apiVersion],
    urlParameters: [$host, id, componentPath],
    headerParameters: [traceparent, tracestate],
    serializer: serializer$1
};
const updateComponentOperationSpec = {
    path: "/digitaltwins/{id}/components/{componentPath}",
    httpMethod: "PATCH",
    responses: {
        202: {},
        204: {
            headersMapper: DigitalTwinsUpdateComponentHeaders
        },
        default: {
            bodyMapper: ErrorResponse
        }
    },
    requestBody: patchDocument,
    queryParameters: [apiVersion],
    urlParameters: [$host, id, componentPath],
    headerParameters: [
        traceparent,
        tracestate,
        contentType1,
        ifMatch
    ],
    mediaType: "json",
    serializer: serializer$1
};
const listRelationshipsNextOperationSpec = {
    path: "{nextLink}",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: RelationshipCollection
        },
        default: {
            bodyMapper: ErrorResponse
        }
    },
    queryParameters: [apiVersion, relationshipName],
    urlParameters: [$host, id, nextLink],
    headerParameters: [traceparent, tracestate],
    serializer: serializer$1
};
const listIncomingRelationshipsNextOperationSpec = {
    path: "{nextLink}",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: IncomingRelationshipCollection
        },
        default: {
            bodyMapper: ErrorResponse
        }
    },
    queryParameters: [apiVersion],
    urlParameters: [$host, id, nextLink],
    headerParameters: [traceparent, tracestate],
    serializer: serializer$1
};

/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
/**
 * Class representing a EventRoutes.
 */
class EventRoutes {
    /**
     * Initialize a new instance of the class EventRoutes class.
     * @param client Reference to the service client
     */
    constructor(client) {
        this.client = client;
    }
    /**
     * Retrieves all event routes.
     * Status codes:
     * * 200 OK
     * @param options The options parameters.
     */
    list(options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ options: operationOptions }, listOperationSpec);
    }
    /**
     * Retrieves an event route.
     * Status codes:
     * * 200 OK
     * * 404 Not Found
     *   * EventRouteNotFound - The event route was not found.
     * @param id The id for an event route. The id is unique within event routes and case sensitive.
     * @param options The options parameters.
     */
    getById(id, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, options: operationOptions }, getByIdOperationSpec);
    }
    /**
     * Adds or replaces an event route.
     * Status codes:
     * * 204 No Content
     * * 400 Bad Request
     *   * EventRouteEndpointInvalid - The endpoint provided does not exist or is not active.
     *   * EventRouteFilterInvalid - The event route filter is invalid.
     *   * EventRouteIdInvalid - The event route id is invalid.
     *   * LimitExceeded - The maximum number of event routes allowed has been reached.
     * @param id The id for an event route. The id is unique within event routes and case sensitive.
     * @param options The options parameters.
     */
    add(id, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, options: operationOptions }, addOperationSpec);
    }
    /**
     * Deletes an event route.
     * Status codes:
     * * 204 No Content
     * * 404 Not Found
     *   * EventRouteNotFound - The event route was not found.
     * @param id The id for an event route. The id is unique within event routes and case sensitive.
     * @param options The options parameters.
     */
    delete(id, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, options: operationOptions }, deleteOperationSpec);
    }
    /**
     * ListNext
     * @param nextLink The nextLink from the previous successful call to the List method.
     * @param options The options parameters.
     */
    listNext(nextLink, options) {
        const operationOptions = coreHttp__namespace.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ nextLink, options: operationOptions }, listNextOperationSpec);
    }
}
// Operation Specifications
const serializer = new coreHttp__namespace.Serializer(Mappers, /* isXml */ false);
const listOperationSpec = {
    path: "/eventroutes",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: EventRouteCollection
        },
        default: {
            bodyMapper: ErrorResponse
        }
    },
    queryParameters: [apiVersion],
    urlParameters: [$host],
    headerParameters: [
        traceparent,
        tracestate,
        maxItemsPerPage
    ],
    serializer
};
const getByIdOperationSpec = {
    path: "/eventroutes/{id}",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: EventRoute
        },
        default: {
            bodyMapper: ErrorResponse
        }
    },
    queryParameters: [apiVersion],
    urlParameters: [$host, id],
    headerParameters: [traceparent, tracestate],
    serializer
};
const addOperationSpec = {
    path: "/eventroutes/{id}",
    httpMethod: "PUT",
    responses: {
        204: {},
        default: {
            bodyMapper: ErrorResponse
        }
    },
    requestBody: eventRoute,
    queryParameters: [apiVersion],
    urlParameters: [$host, id],
    headerParameters: [
        contentType,
        traceparent,
        tracestate
    ],
    mediaType: "json",
    serializer
};
const deleteOperationSpec = {
    path: "/eventroutes/{id}",
    httpMethod: "DELETE",
    responses: {
        204: {},
        default: {
            bodyMapper: ErrorResponse
        }
    },
    queryParameters: [apiVersion],
    urlParameters: [$host, id],
    headerParameters: [traceparent, tracestate],
    serializer
};
const listNextOperationSpec = {
    path: "{nextLink}",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: EventRouteCollection
        },
        default: {
            bodyMapper: ErrorResponse
        }
    },
    queryParameters: [apiVersion],
    urlParameters: [$host, nextLink],
    headerParameters: [
        traceparent,
        tracestate,
        maxItemsPerPage
    ],
    serializer
};

/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
const packageName = "@azure/digital-twins-core";
const packageVersion = "1.1.0";
class AzureDigitalTwinsAPIContext extends coreHttp__namespace.ServiceClient {
    /**
     * Initializes a new instance of the AzureDigitalTwinsAPIContext class.
     * @param options The parameter options
     */
    constructor(options) {
        // Initializing default values for options
        if (!options) {
            options = {};
        }
        if (!options.userAgent) {
            const defaultUserAgent = coreHttp__namespace.getDefaultUserAgentValue();
            options.userAgent = `${packageName}/${packageVersion} ${defaultUserAgent}`;
        }
        super(undefined, options);
        this.requestContentType = "application/json; charset=utf-8";
        this.baseUri =
            options.endpoint || "https://digitaltwins-name.digitaltwins.azure.net";
        // Assigning values to Constant parameters
        this.$host =
            options.$host || "https://digitaltwins-name.digitaltwins.azure.net";
        this.apiVersion = options.apiVersion || "2022-05-31";
    }
}

/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
class AzureDigitalTwinsAPI extends AzureDigitalTwinsAPIContext {
    /**
     * Initializes a new instance of the AzureDigitalTwinsAPI class.
     * @param options The parameter options
     */
    constructor(options) {
        super(options);
        this.digitalTwinModels = new DigitalTwinModels(this);
        this.query = new Query(this);
        this.digitalTwins = new DigitalTwins(this);
        this.eventRoutes = new EventRoutes(this);
    }
}

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
const SDK_VERSION$1 = "1.1.0";

// Copyright (c) Microsoft Corporation.
/**
 * Creates a tracing client to manage tracing spans.
 * @internal
 */
const tracingClient = coreTracing.createTracingClient({
    namespace: "Microsoft.DigitalTwins",
    packageName: "@azure/digital-twins-core",
    packageVersion: SDK_VERSION$1,
});

// Copyright (c) Microsoft Corporation.
/**
 * The \@azure/logger configuration for this package.
 */
const logger = logger$1.createClientLogger("azure-digitaltwins-core");

// Copyright (c) Microsoft Corporation.
const SDK_VERSION = "1.1.0";
const DEFAULT_DIGITALTWINS_SCOPE = "https://digitaltwins.azure.net/.default";
/**
 * Client for Azure IoT DigitalTwins API.
 */
class DigitalTwinsClient {
    /**
     * Creates an instance of AzureDigitalTwinsAPI.
     *
     * Example usage:
     * ```ts
     * const { DigitalTwinsClient, ServiceClientCredentials } = require("@azure/digital-twins-core");
     *
     * const client = new DigitalTwinsClient(
     *   "<endpoint>",
     *   new DefaultAzureCredential();
     * );
     * ```
     * @param endpointUrl - The endpoint URL of the service.
     * @param credential - Used to authenticate requests to the service.
     * @param options - Used to configure the service client.
     */
    constructor(endpointUrl, credential, options = {}) {
        const authPolicy = coreHttp.bearerTokenAuthenticationPolicy(credential, DEFAULT_DIGITALTWINS_SCOPE);
        const libInfo = `azsdk-js-digital-twins-core/${SDK_VERSION}`;
        const { apiVersion } = options, pipelineOptions = tslib.__rest(options, ["apiVersion"]);
        if (!pipelineOptions.userAgentOptions) {
            pipelineOptions.userAgentOptions = {};
        }
        if (pipelineOptions.userAgentOptions.userAgentPrefix) {
            pipelineOptions.userAgentOptions.userAgentPrefix = `${pipelineOptions.userAgentOptions.userAgentPrefix} ${libInfo}`;
        }
        else {
            pipelineOptions.userAgentOptions.userAgentPrefix = libInfo;
        }
        const internalPipelineOptions = Object.assign(Object.assign({}, pipelineOptions), {
            loggingOptions: {
                logger: logger.info,
                allowedHeaderNames: ["x-ms-request-id"],
            },
        });
        const pipeline = coreHttp.createPipelineFromOptions(internalPipelineOptions, authPolicy);
        this.client = new AzureDigitalTwinsAPI(Object.assign({ endpoint: endpointUrl, apiVersion }, pipeline));
    }
    /**
     * Get a digital twin
     *
     * @param digitalTwinId - The Id of the digital twin.
     * @param options - The operation options
     * @returns The application/json digital twin and the http response.
     */
    getDigitalTwin(digitalTwinId, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.getDigitalTwin", options, async (updatedOptions) => {
            return this.client.digitalTwins.getById(digitalTwinId, updatedOptions);
        });
    }
    /**
     * Create or update a digital twin
     *
     * @param digitalTwinId - The Id of the digital twin to create or update.
     * @param digitalTwinJson - The application/json digital twin to create.
     * @param options - Extended operation options including
     *  ifNoneMatch: Only perform the operation if the entity does not already exist.
     * @returns The created application/json digital twin and the http response.
     */
    upsertDigitalTwin(digitalTwinId, digitalTwinJson, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.upsertDigitalTwin", options, async (updatedOptions) => {
            const payload = JSON.parse(digitalTwinJson);
            return this.client.digitalTwins.add(digitalTwinId, payload, updatedOptions);
        });
    }
    /**
     * Update a digital twin using a json patch.
     *
     * @param digitalTwinId - The Id of the digital twin.
     * @param jsonPatch - An update specification described by JSON Patch. Updates to property values
     * and $model elements may happen in the same request. Operations are limited to add, replace and
     * remove.
     * @param options - Extended operation options including
     *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is provided.
     * @returns The http response.
     */
    updateDigitalTwin(digitalTwinId, 
    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types -- changing the type any would be a breaking change
    jsonPatch, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.updateDigitalTwin", options, async (updatedOptions) => {
            return this.client.digitalTwins.update(digitalTwinId, jsonPatch, updatedOptions);
        });
    }
    /**
     * Delete a digital twin
     *
     * @param digitalTwinId - The Id of the digital twin to delete.
     * @param options - Extended operation options including
     *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is provided.
     * @returns The http response.
     */
    deleteDigitalTwin(digitalTwinId, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.deleteDigitalTwin", options, async (updatedOptions) => {
            return this.client.digitalTwins.delete(digitalTwinId, updatedOptions);
        });
    }
    /**
     * Get a component on a digital twin.
     *
     * @param digitalTwinId - The Id of the digital twin.
     * @param componentName - The component being retrieved.
     * @param options - The operation options
     * @returns Json string representation of the component corresponding to the provided componentName and the HTTP response.
     */
    getComponent(digitalTwinId, componentName, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.getComponent", options, async (updatedOptions) => {
            return this.client.digitalTwins.getComponent(digitalTwinId, componentName, updatedOptions);
        });
    }
    /**
     * Update properties of a component on a digital twin using a JSON patch.
     *
     * @param digitalTwinId - The Id of the digital twin.
     * @param componentName - The component being updated.
     * @param jsonPatch - The application/json-patch+json operations to be performed on the specified digital twin's component.
     * @param enableUpdate - If true then update of an existing digital twin is enabled.
     * @param options - Extended operation options including
     *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is provided.
     * @returns The http response.
     */
    updateComponent(digitalTwinId, componentName, jsonPatch, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.updateComponent", options, async (updatedOptions) => {
            return this.client.digitalTwins.updateComponent(digitalTwinId, componentName, jsonPatch, updatedOptions);
        });
    }
    /**
     * Get a relationship on a digital twin.
     *
     * @param digitalTwinId - The Id of the source digital twin.
     * @param relationshipId - The Id of the relationship to retrieve.
     * @param options - The operation options
     * @returns The pageable list of application/json relationships belonging to the specified digital twin and the http response.
     */
    getRelationship(digitalTwinId, relationshipId, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.getRelationship", options, async (updatedOptions) => {
            return this.client.digitalTwins.getRelationshipById(digitalTwinId, relationshipId, updatedOptions);
        });
    }
    /**
     * Create or update a relationship on a digital twin.
     *
     * @param digitalTwinId - The Id of the source digital twin.
     * @param relationshipId - The Id of the relationship to create.
     * @param relationship - The application/json relationship to be created.
     * @param options - Extended operation options including
     *  ifNoneMatch: Only perform the operation if the entity does not already exist.
     */
    upsertRelationship(digitalTwinId, relationshipId, 
    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types -- changing the type any would be a breaking change
    relationship, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.upsertRelationship", options, async (updatedOptions) => {
            return this.client.digitalTwins.addRelationship(digitalTwinId, relationshipId, relationship, updatedOptions);
        });
    }
    /**
     * Updates the properties of a relationship on a digital twin using a JSON patch.
     *
     * @param digitalTwinId - The Id of the digital twin to delete.
     * @param relationshipId - The Id of the relationship to be updated.
     * @param jsonPatch - The application/json-patch+json operations to be performed on the specified digital twin's relationship.
     * @param options - Extended operation options
     *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is provided.
     */
    updateRelationship(digitalTwinId, relationshipId, jsonPatch, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.updateRelationship", options, async (updatedOptions) => {
            return this.client.digitalTwins.updateRelationship(digitalTwinId, relationshipId, jsonPatch, updatedOptions);
        });
    }
    /**
     * Delete a relationship on a digital twin.
     *
     * @param digitalTwinId - The Id of the source digital twin.
     * @param relationshipId - The Id of the relationship to delete.
     * @param options - The operation options
     *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is
     * @returns The http response.
     */
    deleteRelationship(digitalTwinId, relationshipId, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.deleteRelationship", options, async (updatedOptions) => {
            return this.client.digitalTwins.deleteRelationship(digitalTwinId, relationshipId, updatedOptions);
        });
    }
    /**
     * Deals with the pagination of {@link listRelationships}.
     *
     * @param digitalTwinId - The Id of the digital twin.
     * @param options - Common options for the iterative endpoints.
     * @param continuationState - An object that indicates the position of the paginated request.
     *
     */
    listRelationshipsPage(digitalTwinId, options, continuationState) {
        return tslib.__asyncGenerator(this, arguments, function* listRelationshipsPage_1() {
            if (continuationState.continuationToken == null) {
                const optionsComplete = Object.assign({}, options);
                const listRelationshipResponse = yield tslib.__await(this.client.digitalTwins.listRelationships(digitalTwinId, optionsComplete));
                continuationState.continuationToken = listRelationshipResponse.nextLink;
                yield yield tslib.__await(listRelationshipResponse);
            }
            while (continuationState.continuationToken) {
                const listRelationshipResponse = yield tslib.__await(this.client.digitalTwins.listRelationshipsNext("", continuationState.continuationToken, options));
                continuationState.continuationToken = listRelationshipResponse.nextLink;
                yield yield tslib.__await(listRelationshipResponse);
            }
        });
    }
    /**
     * Deals with the iteration of all the available results of {@link listRelationships}.
     * @param options - Common options for the iterative endpoints.
     */
    listRelationshipsAll(digitalTwinId, options) {
        return tslib.__asyncGenerator(this, arguments, function* listRelationshipsAll_1() {
            var e_1, _a;
            try {
                for (var _b = tslib.__asyncValues(this.listRelationshipsPage(digitalTwinId, options, {})), _c; _c = yield tslib.__await(_b.next()), !_c.done;) {
                    const page = _c.value;
                    const value = page.value || [];
                    for (const item of value) {
                        yield yield tslib.__await(item);
                    }
                }
            }
            catch (e_1_1) { e_1 = { error: e_1_1 }; }
            finally {
                try {
                    if (_c && !_c.done && (_a = _b.return)) yield tslib.__await(_a.call(_b));
                }
                finally { if (e_1) throw e_1.error; }
            }
        });
    }
    /**
     * Retrieve relationships for a digital twin.
     *
     * @param digitalTwinId - The Id of the digital twin.
     */
    listRelationships(digitalTwinId, options = {}) {
        const iter = this.listRelationshipsAll(digitalTwinId, options);
        return {
            next() {
                return iter.next();
            },
            [Symbol.asyncIterator]() {
                return this;
            },
            byPage: (settings = {}) => this.listRelationshipsPage(digitalTwinId, options, settings),
        };
    }
    /**
     * Deals with the pagination of {@link listIncomingRelationships}.
     *
     * @param digitalTwinId - The Id of the digital twin.
     * @param options - Common options for the iterative endpoints.
     * @param continuationState - An object that indicates the position of the paginated request.
     *
     */
    listIncomingRelationshipsPage(digitalTwinId, options, continuationState) {
        return tslib.__asyncGenerator(this, arguments, function* listIncomingRelationshipsPage_1() {
            if (continuationState.continuationToken == null) {
                const optionsComplete = Object.assign({}, options);
                const listIncomingRelationshipsResponse = yield tslib.__await(this.client.digitalTwins.listIncomingRelationships(digitalTwinId, optionsComplete));
                continuationState.continuationToken = listIncomingRelationshipsResponse.nextLink;
                yield yield tslib.__await(listIncomingRelationshipsResponse);
            }
            while (continuationState.continuationToken) {
                const listIncomingRelationshipsResponse = yield tslib.__await(this.client.digitalTwins.listIncomingRelationshipsNext("", continuationState.continuationToken, options));
                continuationState.continuationToken = listIncomingRelationshipsResponse.nextLink;
                yield yield tslib.__await(listIncomingRelationshipsResponse);
            }
        });
    }
    /**
     * Deals with the iteration of all the available results of {@link listIncomingRelationships}.
     * @param digitalTwinId - The Id of the digital twin.
     * @param options - Common options for the iterative endpoints.
     */
    listIncomingRelationshipsAll(digitalTwinId, options) {
        return tslib.__asyncGenerator(this, arguments, function* listIncomingRelationshipsAll_1() {
            var e_2, _a;
            try {
                for (var _b = tslib.__asyncValues(this.listIncomingRelationshipsPage(digitalTwinId, options, {})), _c; _c = yield tslib.__await(_b.next()), !_c.done;) {
                    const page = _c.value;
                    const value = page.value || [];
                    yield tslib.__await(yield* tslib.__asyncDelegator(tslib.__asyncValues(value)));
                }
            }
            catch (e_2_1) { e_2 = { error: e_2_1 }; }
            finally {
                try {
                    if (_c && !_c.done && (_a = _b.return)) yield tslib.__await(_a.call(_b));
                }
                finally { if (e_2) throw e_2.error; }
            }
        });
    }
    /**
     * Retrieve all incoming relationships for a digital twin.
     *
     * @param digitalTwinId - The Id of the digital twin.
     */
    listIncomingRelationships(digitalTwinId, options = {}) {
        const iter = this.listIncomingRelationshipsAll(digitalTwinId, options);
        return {
            next() {
                return iter.next();
            },
            [Symbol.asyncIterator]() {
                return this;
            },
            byPage: (settings = {}) => this.listIncomingRelationshipsPage(digitalTwinId, options, settings),
        };
    }
    /**
     * Publish telemetry from a digital twin, which is then consumed by one or many destination endpoints (subscribers) defined under.
     *
     * @param digitalTwinId - The Id of the digital twin to delete.
     * @param payload - The application/json telemetry payload to be sent.
     * @param messageId - The message Id.
     * @param options - The operation options
     * @returns The http response.
     */
    publishTelemetry(digitalTwinId, 
    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types -- changing the type any would be a breaking change
    payload, messageId, options = {}) {
        const digitalTwinsSendTelemetryOptionalParams = options;
        digitalTwinsSendTelemetryOptionalParams.telemetrySourceTime = new Date().toISOString();
        if (!messageId) {
            messageId = coreHttp.generateUuid();
        }
        return tracingClient.withSpan("DigitalTwinsClient.publishTelemetry", digitalTwinsSendTelemetryOptionalParams, async (updatedOptions) => {
            return this.client.digitalTwins.sendTelemetry(digitalTwinId, messageId, payload, updatedOptions);
        });
    }
    /**
     * Publish telemetry from a digital twin's component, which is then consumed by one or many destination endpoints (subscribers) defined under.
     *
     * @param digitalTwinId - The Id of the digital twin to delete.
     * @param componentName - The name of the DTDL component.
     * @param payload - The application/json telemetry payload to be sent.
     * @param messageId - The message Id.
     * @param options - The operation options
     * @returns The http response.
     */
    publishComponentTelemetry(digitalTwinId, componentName, payload, messageId, options = {}) {
        const digitalTwinsSendComponentTelemetryOptionalParams = options;
        digitalTwinsSendComponentTelemetryOptionalParams.telemetrySourceTime = new Date().toISOString();
        if (!messageId) {
            messageId = coreHttp.generateUuid();
        }
        return tracingClient.withSpan("DigitalTwinsClient.publishComponentTelemetry", digitalTwinsSendComponentTelemetryOptionalParams, async (updatedOptions) => {
            return this.client.digitalTwins.sendComponentTelemetry(digitalTwinId, componentName, payload, messageId, updatedOptions);
        });
    }
    /**
     * Get a model, including the model metadata and the model definition.
     *
     * @param modelId - The Id of the model.
     * @param options - Extended operation options including
     *  includeModelDefinition: When true the model definition will be returned as part of the result. Default value: false.
     * @returns The application/json model and the http response.
     */
    getModel(modelId, includeModelDefinition = false, options = {}) {
        const digitalTwinModelsGetByIdOptionalParams = options;
        digitalTwinModelsGetByIdOptionalParams.includeModelDefinition = includeModelDefinition;
        return tracingClient.withSpan("DigitalTwinsClient.getModel", digitalTwinModelsGetByIdOptionalParams, async (updatedOptions) => {
            return this.client.digitalTwinModels.getById(modelId, updatedOptions);
        });
    }
    /**
     * Deals with the pagination of {@link list}.
     *
     * @param options - Common options for the iterative endpoints.
     * @param continuationState - An object that indicates the position of the paginated request.
     *
     */
    getModelsPage(options, continuationState) {
        return tslib.__asyncGenerator(this, arguments, function* getModelsPage_1() {
            if (continuationState.continuationToken == null) {
                const optionsComplete = options;
                optionsComplete.maxItemsPerPage = continuationState.maxPageSize;
                const listResponse = yield tslib.__await(this.client.digitalTwinModels.list(optionsComplete));
                continuationState.continuationToken = listResponse.nextLink;
                yield yield tslib.__await(listResponse);
            }
            while (continuationState.continuationToken) {
                const listResponse = yield tslib.__await(this.client.digitalTwinModels.listNext(continuationState.continuationToken, options));
                continuationState.continuationToken = listResponse.nextLink;
                yield yield tslib.__await(listResponse);
            }
        });
    }
    /**
     * Deals with the iteration of all the available results of {@link list}.
     * @param options - Common options for the iterative endpoints.
     */
    getModelsAll(options) {
        return tslib.__asyncGenerator(this, arguments, function* getModelsAll_1() {
            var e_3, _a;
            const f = {};
            try {
                for (var _b = tslib.__asyncValues(this.getModelsPage(options, f)), _c; _c = yield tslib.__await(_b.next()), !_c.done;) {
                    const page = _c.value;
                    const value = page.value || [];
                    for (const item of value) {
                        yield yield tslib.__await(item);
                    }
                }
            }
            catch (e_3_1) { e_3 = { error: e_3_1 }; }
            finally {
                try {
                    if (_c && !_c.done && (_a = _b.return)) yield tslib.__await(_a.call(_b));
                }
                finally { if (e_3) throw e_3.error; }
            }
        });
    }
    /**
     * Get the list of models
     *
     * @param dependeciesFor - The model Ids to have dependencies retrieved. If omitted, all models are retrieved.
     * @param includeModelDefinition - Whether to include the model definition in the result. If false, only the model metadata will be returned.
     * @param resultsPerPage - The maximum number of items to retrieve per request. The server may choose to return less than the requested max.
     * @returns A pageable set of application/json models and the http response.
     */
    listModels(dependeciesFor, includeModelDefinition = false, resultsPerPage, options = {}) {
        let digitalTwinModelsListOptionalParams = options;
        digitalTwinModelsListOptionalParams = {
            maxItemsPerPage: resultsPerPage,
            dependenciesFor: dependeciesFor,
            includeModelDefinition: includeModelDefinition,
        };
        const iter = this.getModelsAll(digitalTwinModelsListOptionalParams);
        return {
            next() {
                return iter.next();
            },
            [Symbol.asyncIterator]() {
                return this;
            },
            byPage: (settings = {}) => this.getModelsPage(digitalTwinModelsListOptionalParams, settings),
        };
    }
    /**
     * Create one or many
     *
     * @param models - The set of models to create. Each string corresponds to exactly one model.
     * @param options - The operation options
     * @returns The created application/json models and the http response.
     */
    createModels(dtdlModels, options = {}) {
        const digitalTwinModelsAddOptionalParams = options;
        digitalTwinModelsAddOptionalParams.models = dtdlModels;
        return tracingClient.withSpan("DigitalTwinsClient.createModels", digitalTwinModelsAddOptionalParams, async (updatedOptions) => {
            return this.client.digitalTwinModels.add(updatedOptions);
        });
    }
    /**
     * Decommission a model using a json patch.
     * When a model is decommissioned, new digital twins will no longer be able to be
     * defined by this model. However, existing digital twins may continue to use this model.
     * Once a model is decommissioned, it may not be recommissioned.
     *
     * @param modelId - The Id of the model to decommission.
     * property can be replaced.
     * @param options - The operation options
     * @returns The http response.
     *
     */
    decomissionModel(modelId, options = {}) {
        const jsonPatch = [{ op: "replace", path: "/decommissioned", value: true }];
        return tracingClient.withSpan("DigitalTwinsClient.decomissionModel", options, async (updatedOptions) => {
            return this.client.digitalTwinModels.update(modelId, jsonPatch, updatedOptions);
        });
    }
    /**
     * Delete a model.
     *
     * @param modelId - The Id of the model to delete.
     * @param options - The operation options
     * @returns The http response.
     */
    deleteModel(modelId, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.deleteModel", options, async (updatedOptions) => {
            return this.client.digitalTwinModels.delete(modelId, updatedOptions);
        });
    }
    /**
     * Get an event route.
     *
     * @param modelId - The Id of the event route.
     * @param options - The operation options
     * @returns The application/json event route and the http response.
     */
    getEventRoute(eventRouteId, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.getEventRoute", options, async (updatedOptions) => {
            return this.client.eventRoutes.getById(eventRouteId, updatedOptions);
        });
    }
    /**
     * Deals with the pagination of {@link list}.
     *
     * @param options - Common options for the iterative endpoints.
     * @param continuationState - An object that indicates the position of the paginated request.
     *
     */
    getEventRoutesPage(options, continuationState) {
        return tslib.__asyncGenerator(this, arguments, function* getEventRoutesPage_1() {
            if (continuationState.continuationToken == null) {
                const optionsComplete = options;
                optionsComplete.maxItemsPerPage = continuationState.maxPageSize;
                const listResponse = yield tslib.__await(this.client.eventRoutes.list(optionsComplete));
                continuationState.continuationToken = listResponse.nextLink;
                yield yield tslib.__await(listResponse);
            }
            while (continuationState.continuationToken) {
                const listResponse = yield tslib.__await(this.client.eventRoutes.listNext(continuationState.continuationToken, options));
                continuationState.continuationToken = listResponse.nextLink;
                yield yield tslib.__await(listResponse);
            }
        });
    }
    /**
     * Deals with the iteration of all the available results of {@link list}.
     * @param options - Common options for the iterative endpoints.
     */
    getEventRoutesAll(options) {
        return tslib.__asyncGenerator(this, arguments, function* getEventRoutesAll_1() {
            var e_4, _a;
            const f = {};
            try {
                for (var _b = tslib.__asyncValues(this.getEventRoutesPage(options, f)), _c; _c = yield tslib.__await(_b.next()), !_c.done;) {
                    const page = _c.value;
                    const value = page.value || [];
                    for (const item of value) {
                        yield yield tslib.__await(item);
                    }
                }
            }
            catch (e_4_1) { e_4 = { error: e_4_1 }; }
            finally {
                try {
                    if (_c && !_c.done && (_a = _b.return)) yield tslib.__await(_a.call(_b));
                }
                finally { if (e_4) throw e_4.error; }
            }
        });
    }
    /**
     * List the event routes in a digital twins instance.
     *
     * @param resultsPerPage - The maximum number of items to retrieve per request. The server may choose to return less than
     * the requested max.
     * @returns The application/json event route and the http response.
     */
    listEventRoutes(resultsPerPage, options = {}) {
        let eventRoutesListOptionalParams = options;
        eventRoutesListOptionalParams = {
            maxItemsPerPage: resultsPerPage,
        };
        const iter = this.getEventRoutesAll(eventRoutesListOptionalParams);
        return {
            next() {
                return iter.next();
            },
            [Symbol.asyncIterator]() {
                return this;
            },
            byPage: (settings = {}) => this.getEventRoutesPage(eventRoutesListOptionalParams, settings),
        };
    }
    /**
     * Create or update an event route.
     *
     * @param eventRouteId - The Id of the event route to create or update.
     * @param endpointId - The id of the endpoint this event route is bound to.
     * @param filter - An expression which describes the events which are routed to the endpoint.
     * @param options - The operation options
     * @returns The http response.
     */
    upsertEventRoute(eventRouteId, endpointId, filter, options = {}) {
        const eventRoutesAddOptionalParams = options;
        const eventRoute = {
            endpointName: endpointId,
            filter: filter,
        };
        eventRoutesAddOptionalParams.eventRoute = eventRoute;
        return tracingClient.withSpan("DigitalTwinsClient.upsertEventRoute", eventRoutesAddOptionalParams, async (updatedOptions) => {
            return this.client.eventRoutes.add(eventRouteId, updatedOptions);
        });
    }
    /**
     * Delete an event route.
     *
     * @param eventRouteId - The Id of the eventRoute to delete.
     * @param options - The operation options
     * @returns The http response.
     */
    deleteEventRoute(eventRouteId, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.deleteEventRoute", options, async (updatedOptions) => {
            return this.client.eventRoutes.delete(eventRouteId, updatedOptions);
        });
    }
    /**
     * Deals with the pagination of {@link query}.
     *
     * @param query - The query string, in SQL-like syntax.
     * @param options - Common options for the iterative endpoints.
     * @param continuationState - An object that indicates the position of the paginated request.
     *
     */
    queryTwinsPage(query, options, continuationState) {
        return tslib.__asyncGenerator(this, arguments, function* queryTwinsPage_1() {
            if (continuationState.continuationToken == null) {
                const querySpecification = {
                    query: query,
                    continuationToken: continuationState.continuationToken,
                };
                const queryResult = yield tslib.__await(this.client.query.queryTwins(querySpecification, options));
                continuationState.continuationToken = queryResult.continuationToken;
                yield yield tslib.__await(queryResult);
            }
            while (continuationState.continuationToken) {
                const querySpecification = {
                    query: query,
                    continuationToken: continuationState.continuationToken,
                };
                const queryResult = yield tslib.__await(this.client.query.queryTwins(querySpecification, options));
                continuationState.continuationToken = queryResult.continuationToken;
                yield yield tslib.__await(queryResult);
            }
        });
    }
    /**
     * Deals with the iteration of all the available results of {@link query}.
     * @param query - The query string, in SQL-like syntax.
     * @param options - Common options for the iterative endpoints.
     */
    queryTwinsAll(query, options) {
        return tslib.__asyncGenerator(this, arguments, function* queryTwinsAll_1() {
            var e_5, _a;
            const f = {};
            try {
                for (var _b = tslib.__asyncValues(this.queryTwinsPage(query, options, f)), _c; _c = yield tslib.__await(_b.next()), !_c.done;) {
                    const page = _c.value;
                    if (page.value) {
                        for (const item of page.value) {
                            yield yield tslib.__await(item);
                        }
                    }
                }
            }
            catch (e_5_1) { e_5 = { error: e_5_1 }; }
            finally {
                try {
                    if (_c && !_c.done && (_a = _b.return)) yield tslib.__await(_a.call(_b));
                }
                finally { if (e_5) throw e_5.error; }
            }
        });
    }
    /**
     * Query for digital twins.
     *
     * @param query - The query string, in SQL-like syntax.
     * @param resultsPerPage - The maximum number of items to retrieve per request. The server may choose to return less than the requested max.
     * @returns The pageable list of query results.
     */
    queryTwins(query, resultsPerPage, options = {}) {
        let queryQueryTwinsOptionalParams = options;
        queryQueryTwinsOptionalParams = {
            maxItemsPerPage: resultsPerPage,
        };
        const iter = this.queryTwinsAll(query, queryQueryTwinsOptionalParams);
        return {
            next() {
                return iter.next();
            },
            [Symbol.asyncIterator]() {
                return this;
            },
            byPage: (settings = {}) => this.queryTwinsPage(query, queryQueryTwinsOptionalParams, settings),
        };
    }
}

exports.DigitalTwinsClient = DigitalTwinsClient;
//# sourceMappingURL=index.js.map
