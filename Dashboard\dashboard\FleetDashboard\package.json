{"name": "fleetdashboard", "version": "0.0.0", "private": true, "scripts": {"start": "node ./bin/www"}, "dependencies": {"@azure/digital-twins-core": "^2.0.0", "@azure/identity": "^4.13.0", "@azure/web-pubsub": "^1.2.0", "cookie-parser": "~1.4.4", "debug": "~2.6.9", "express": "^4.18.2", "flipclock": "^0.10.5", "http-errors": "~1.6.3", "morgan": "~1.9.1", "pug": "^3.0.2"}, "devDependencies": {"@azure/logger": "^1.0.4"}}