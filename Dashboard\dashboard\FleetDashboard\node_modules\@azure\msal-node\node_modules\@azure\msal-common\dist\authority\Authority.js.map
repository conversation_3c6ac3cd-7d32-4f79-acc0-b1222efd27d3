{"version": 3, "file": "Authority.js", "sources": ["../../src/authority/Authority.ts"], "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\n\nimport { AuthorityType } from \"./AuthorityType\";\nimport { isOpenIdConfigResponse, OpenIdConfigResponse } from \"./OpenIdConfigResponse\";\nimport { UrlString } from \"../url/UrlString\";\nimport { IUri } from \"../url/IUri\";\nimport { ClientAuthError } from \"../error/ClientAuthError\";\nimport { INetworkModule } from \"../network/INetworkModule\";\nimport { AuthorityMetadataSource, Constants, RegionDiscoveryOutcomes } from \"../utils/Constants\";\nimport { EndpointMetadata, InstanceDiscoveryMetadata } from \"./AuthorityMetadata\";\nimport { ClientConfigurationError } from \"../error/ClientConfigurationError\";\nimport { ProtocolMode } from \"./ProtocolMode\";\nimport { ICacheManager } from \"../cache/interface/ICacheManager\";\nimport { AuthorityMetadataEntity } from \"../cache/entities/AuthorityMetadataEntity\";\nimport { AuthorityOptions , AzureCloudInstance } from \"./AuthorityOptions\";\nimport { CloudInstanceDiscoveryResponse, isCloudInstanceDiscoveryResponse } from \"./CloudInstanceDiscoveryResponse\";\nimport { CloudInstanceDiscoveryErrorResponse, isCloudInstanceDiscoveryErrorResponse } from \"./CloudInstanceDiscoveryErrorResponse\";\nimport { CloudDiscoveryMetadata } from \"./CloudDiscoveryMetadata\";\nimport { RegionDiscovery } from \"./RegionDiscovery\";\nimport { RegionDiscoveryMetadata } from \"./RegionDiscoveryMetadata\";\nimport { ImdsOptions } from \"./ImdsOptions\";\nimport { AzureCloudOptions } from \"../config/ClientConfiguration\";\nimport { Logger } from \"../logger/Logger\";\nimport { AuthError } from \"../error/AuthError\";\nimport { IPerformanceClient } from \"../telemetry/performance/IPerformanceClient\";\nimport { PerformanceEvents } from \"../telemetry/performance/PerformanceEvent\";\n\n/**\n * The authority class validates the authority URIs used by the user, and retrieves the OpenID Configuration Data from the\n * endpoint. It will store the pertinent config data in this object for use during token calls.\n */\nexport class Authority {\n    // Canonical authority url string\n    private _canonicalAuthority: UrlString;\n    // Canonicaly authority url components\n    private _canonicalAuthorityUrlComponents: IUri | null;\n    // Network interface to make requests with.\n    protected networkInterface: INetworkModule;\n    // Cache Manager to cache network responses\n    protected cacheManager: ICacheManager;\n    // Protocol mode to construct endpoints\n    private authorityOptions: AuthorityOptions;\n    // Authority metadata\n    private metadata: AuthorityMetadataEntity;\n    // Region discovery service\n    private regionDiscovery: RegionDiscovery;\n    // Region discovery metadata\n    public regionDiscoveryMetadata: RegionDiscoveryMetadata;\n    // Logger object\n    private logger: Logger;\n    // Performance client\n    protected performanceClient: IPerformanceClient | undefined;\n    // Correlation Id\n    protected correlationId: string | undefined;\n\n    constructor(\n        authority: string,\n        networkInterface: INetworkModule,\n        cacheManager: ICacheManager,\n        authorityOptions: AuthorityOptions,\n        logger: Logger,\n        performanceClient?: IPerformanceClient,\n        correlationId?: string\n    ) {\n        this.canonicalAuthority = authority;\n        this._canonicalAuthority.validateAsUri();\n        this.networkInterface = networkInterface;\n        this.cacheManager = cacheManager;\n        this.authorityOptions = authorityOptions;\n        this.regionDiscoveryMetadata = { region_used: undefined, region_source: undefined, region_outcome: undefined };\n        this.logger = logger;\n        this.performanceClient = performanceClient;\n        this.correlationId = correlationId;\n        this.regionDiscovery = new RegionDiscovery(networkInterface, this.performanceClient, this.correlationId);\n    }\n\n    // See above for AuthorityType\n    public get authorityType(): AuthorityType {\n        const pathSegments = this.canonicalAuthorityUrlComponents.PathSegments;\n        if (pathSegments.length) {\n            switch(pathSegments[0].toLowerCase()) {\n                case Constants.ADFS:\n                    return AuthorityType.Adfs;\n                case Constants.DSTS:\n                    return AuthorityType.Dsts;\n                default:\n                    break;\n            }\n        }\n        return AuthorityType.Default;\n    }\n\n    /**\n     * ProtocolMode enum representing the way endpoints are constructed.\n     */\n    public get protocolMode(): ProtocolMode {\n        return this.authorityOptions.protocolMode;\n    }\n\n    /**\n     * Returns authorityOptions which can be used to reinstantiate a new authority instance\n     */\n    public get options(): AuthorityOptions {\n        return this.authorityOptions;\n    }\n\n    /**\n     * A URL that is the authority set by the developer\n     */\n    public get canonicalAuthority(): string {\n        return this._canonicalAuthority.urlString;\n    }\n\n    /**\n     * Sets canonical authority.\n     */\n    public set canonicalAuthority(url: string) {\n        this._canonicalAuthority = new UrlString(url);\n        this._canonicalAuthority.validateAsUri();\n        this._canonicalAuthorityUrlComponents = null;\n    }\n\n    /**\n     * Get authority components.\n     */\n    public get canonicalAuthorityUrlComponents(): IUri {\n        if (!this._canonicalAuthorityUrlComponents) {\n            this._canonicalAuthorityUrlComponents = this._canonicalAuthority.getUrlComponents();\n        }\n\n        return this._canonicalAuthorityUrlComponents;\n    }\n\n    /**\n     * Get hostname and port i.e. login.microsoftonline.com\n     */\n    public get hostnameAndPort(): string {\n        return this.canonicalAuthorityUrlComponents.HostNameAndPort.toLowerCase();\n    }\n\n    /**\n     * Get tenant for authority.\n     */\n    public get tenant(): string {\n        return this.canonicalAuthorityUrlComponents.PathSegments[0];\n    }\n\n    /**\n     * OAuth /authorize endpoint for requests\n     */\n    public get authorizationEndpoint(): string {\n        if(this.discoveryComplete()) {\n            const endpoint = this.replacePath(this.metadata.authorization_endpoint);\n            return this.replaceTenant(endpoint);\n        } else {\n            throw ClientAuthError.createEndpointDiscoveryIncompleteError(\"Discovery incomplete.\");\n        }\n    }\n\n    /**\n     * OAuth /token endpoint for requests\n     */\n    public get tokenEndpoint(): string {\n        if(this.discoveryComplete()) {\n            const endpoint = this.replacePath(this.metadata.token_endpoint);\n            return this.replaceTenant(endpoint);\n        } else {\n            throw ClientAuthError.createEndpointDiscoveryIncompleteError(\"Discovery incomplete.\");\n        }\n    }\n\n    public get deviceCodeEndpoint(): string {\n        if(this.discoveryComplete()) {\n            const endpoint = this.replacePath(this.metadata.token_endpoint.replace(\"/token\", \"/devicecode\"));\n            return this.replaceTenant(endpoint);\n        } else {\n            throw ClientAuthError.createEndpointDiscoveryIncompleteError(\"Discovery incomplete.\");\n        }\n    }\n\n    /**\n     * OAuth logout endpoint for requests\n     */\n    public get endSessionEndpoint(): string {\n        if(this.discoveryComplete()) {\n            // ROPC policies may not have end_session_endpoint set\n            if (!this.metadata.end_session_endpoint) {\n                throw ClientAuthError.createLogoutNotSupportedError();\n            }\n            const endpoint = this.replacePath(this.metadata.end_session_endpoint);\n            return this.replaceTenant(endpoint);\n        } else {\n            throw ClientAuthError.createEndpointDiscoveryIncompleteError(\"Discovery incomplete.\");\n        }\n    }\n\n    /**\n     * OAuth issuer for requests\n     */\n    public get selfSignedJwtAudience(): string {\n        if(this.discoveryComplete()) {\n            const endpoint = this.replacePath(this.metadata.issuer);\n            return this.replaceTenant(endpoint);\n        } else {\n            throw ClientAuthError.createEndpointDiscoveryIncompleteError(\"Discovery incomplete.\");\n        }\n    }\n\n    /**\n     * Jwks_uri for token signing keys\n     */\n    public get jwksUri(): string {\n        if(this.discoveryComplete()) {\n            const endpoint = this.replacePath(this.metadata.jwks_uri);\n            return this.replaceTenant(endpoint);\n        } else {\n            throw ClientAuthError.createEndpointDiscoveryIncompleteError(\"Discovery incomplete.\");\n        }\n    }\n\n    /**\n     * Replaces tenant in url path with current tenant. Defaults to common.\n     * @param urlString\n     */\n    private replaceTenant(urlString: string): string {\n        return urlString.replace(/{tenant}|{tenantid}/g, this.tenant);\n    }\n\n    /**\n     * Replaces path such as tenant or policy with the current tenant or policy.\n     * @param urlString\n     */\n    private replacePath(urlString: string): string {\n        let endpoint = urlString;\n        const cachedAuthorityUrl = new UrlString(this.metadata.canonical_authority);\n        const cachedAuthorityParts = cachedAuthorityUrl.getUrlComponents().PathSegments;\n        const currentAuthorityParts = this.canonicalAuthorityUrlComponents.PathSegments;\n\n        currentAuthorityParts.forEach((currentPart, index) => {\n            const cachedPart = cachedAuthorityParts[index];\n            if (currentPart !== cachedPart) {\n                endpoint = endpoint.replace(`/${cachedPart}/`, `/${currentPart}/`);\n            }\n        });\n\n        return endpoint;\n    }\n\n    /**\n     * The default open id configuration endpoint for any canonical authority.\n     */\n    protected get defaultOpenIdConfigurationEndpoint(): string {\n        if (\n            this.authorityType === AuthorityType.Adfs ||\n            this.authorityType === AuthorityType.Dsts ||\n            this.protocolMode === ProtocolMode.OIDC\n        ) {\n            return `${this.canonicalAuthority}.well-known/openid-configuration`;\n        }\n        return `${this.canonicalAuthority}v2.0/.well-known/openid-configuration`;\n    }\n\n    /**\n     * Boolean that returns whethr or not tenant discovery has been completed.\n     */\n    discoveryComplete(): boolean {\n        return !!this.metadata;\n    }\n\n    /**\n     * Perform endpoint discovery to discover aliases, preferred_cache, preferred_network\n     * and the /authorize, /token and logout endpoints.\n     */\n    public async resolveEndpointsAsync(): Promise<void> {\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.AuthorityResolveEndpointsAsync, this.correlationId);\n\n        let metadataEntity = this.cacheManager.getAuthorityMetadataByAlias(this.hostnameAndPort);\n        if (!metadataEntity) {\n            metadataEntity = new AuthorityMetadataEntity();\n            metadataEntity.updateCanonicalAuthority(this.canonicalAuthority);\n        }\n\n        this.performanceClient?.setPreQueueTime(PerformanceEvents.AuthorityUpdateCloudDiscoveryMetadata, this.correlationId);\n        const cloudDiscoverySource = await this.updateCloudDiscoveryMetadata(metadataEntity);\n        this.canonicalAuthority = this.canonicalAuthority.replace(this.hostnameAndPort, metadataEntity.preferred_network);\n        this.performanceClient?.setPreQueueTime(PerformanceEvents.AuthorityUpdateEndpointMetadata, this.correlationId);\n        const endpointSource = await this.updateEndpointMetadata(metadataEntity);\n\n        if (cloudDiscoverySource !== AuthorityMetadataSource.CACHE && endpointSource !== AuthorityMetadataSource.CACHE) {\n            // Reset the expiration time unless both values came from a successful cache lookup\n            metadataEntity.resetExpiresAt();\n            metadataEntity.updateCanonicalAuthority(this.canonicalAuthority);\n        }\n\n        const cacheKey = this.cacheManager.generateAuthorityMetadataCacheKey(metadataEntity.preferred_cache);\n        this.cacheManager.setAuthorityMetadata(cacheKey, metadataEntity);\n        this.metadata = metadataEntity;\n    }\n\n    /**\n     * Update AuthorityMetadataEntity with new endpoints and return where the information came from\n     * @param metadataEntity\n     */\n    private async updateEndpointMetadata(metadataEntity: AuthorityMetadataEntity): Promise<AuthorityMetadataSource> {\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.AuthorityUpdateEndpointMetadata, this.correlationId);\n\n        let metadata = this.getEndpointMetadataFromConfig();\n        if (metadata) {\n            metadataEntity.updateEndpointMetadata(metadata, false);\n            return AuthorityMetadataSource.CONFIG;\n        }\n\n        if (this.isAuthoritySameType(metadataEntity) && metadataEntity.endpointsFromNetwork && !metadataEntity.isExpired()) {\n            // No need to update\n            return AuthorityMetadataSource.CACHE;\n        }\n\n        let harcodedMetadata = this.getEndpointMetadataFromHardcodedValues();\n        this.performanceClient?.setPreQueueTime(PerformanceEvents.AuthorityGetEndpointMetadataFromNetwork, this.correlationId);\n        metadata = await this.getEndpointMetadataFromNetwork();\n        if (metadata) {\n            // If the user prefers to use an azure region replace the global endpoints with regional information.\n            if (this.authorityOptions.azureRegionConfiguration?.azureRegion) {\n                this.performanceClient?.setPreQueueTime(PerformanceEvents.AuthorityUpdateMetadataWithRegionalInformation, this.correlationId);\n                metadata = await this.updateMetadataWithRegionalInformation(metadata);\n            }\n\n            metadataEntity.updateEndpointMetadata(metadata, true);\n            return AuthorityMetadataSource.NETWORK;\n        }    \n\n        if (harcodedMetadata && !this.authorityOptions.skipAuthorityMetadataCache) {\n            // If the user prefers to use an azure region replace the global endpoints with regional information.\n            if (this.authorityOptions.azureRegionConfiguration?.azureRegion) {\n                this.performanceClient?.setPreQueueTime(PerformanceEvents.AuthorityUpdateMetadataWithRegionalInformation, this.correlationId);\n                harcodedMetadata = await this.updateMetadataWithRegionalInformation(\n                    harcodedMetadata\n                );\n            }\n\n            metadataEntity.updateEndpointMetadata(harcodedMetadata, false);\n            return AuthorityMetadataSource.HARDCODED_VALUES;\n        } else {\n            throw ClientAuthError.createUnableToGetOpenidConfigError(\n                this.defaultOpenIdConfigurationEndpoint\n            );\n        }\n    }\n\n    /**\n     * Compares the number of url components after the domain to determine if the cached \n     * authority metadata can be used for the requested authority. Protects against same domain different \n     * authority such as login.microsoftonline.com/tenant and login.microsoftonline.com/tfp/tenant/policy\n     * @param metadataEntity\n     */\n    private isAuthoritySameType(metadataEntity: AuthorityMetadataEntity): boolean {\n        const cachedAuthorityUrl = new UrlString(metadataEntity.canonical_authority);\n        const cachedParts = cachedAuthorityUrl.getUrlComponents().PathSegments;\n\n        return cachedParts.length === this.canonicalAuthorityUrlComponents.PathSegments.length;\n    }\n\n    /**\n     * Parse authorityMetadata config option\n     */\n    private getEndpointMetadataFromConfig(): OpenIdConfigResponse | null {\n        if (this.authorityOptions.authorityMetadata) {\n            try {\n                return JSON.parse(this.authorityOptions.authorityMetadata) as OpenIdConfigResponse;\n            } catch (e) {\n                throw ClientConfigurationError.createInvalidAuthorityMetadataError();\n            }\n        }\n\n        return null;\n    }\n\n    /**\n     * Gets OAuth endpoints from the given OpenID configuration endpoint.\n     * \n     * @param hasHardcodedMetadata boolean\n     */\n    private async getEndpointMetadataFromNetwork(): Promise<OpenIdConfigResponse | null> {\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.AuthorityGetEndpointMetadataFromNetwork, this.correlationId);\n\n        const options: ImdsOptions = {};\n        \n        /*\n         * TODO: Add a timeout if the authority exists in our library's \n         * hardcoded list of metadata\n         */\n\n        try {\n            const response = await this.networkInterface.\n                sendGetRequestAsync<OpenIdConfigResponse>(this.defaultOpenIdConfigurationEndpoint, options);\n            return isOpenIdConfigResponse(response.body) ? response.body : null;\n        } catch (e) {\n            return null;\n        }\n    }\n\n    /**\n     * Get OAuth endpoints for common authorities.\n     */\n    private getEndpointMetadataFromHardcodedValues(): OpenIdConfigResponse | null {\n        if (this.canonicalAuthority in EndpointMetadata) {\n            return EndpointMetadata[this.canonicalAuthority];\n        }\n\n        return null;\n    }\n\n    /**\n     * Update the retrieved metadata with regional information.\n     */\n    private async updateMetadataWithRegionalInformation(metadata: OpenIdConfigResponse): Promise<OpenIdConfigResponse> {\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.AuthorityUpdateMetadataWithRegionalInformation, this.correlationId);\n\n        this.performanceClient?.setPreQueueTime(PerformanceEvents.RegionDiscoveryDetectRegion, this.correlationId);\n        const autodetectedRegionName = await this.regionDiscovery.detectRegion(\n            this.authorityOptions.azureRegionConfiguration?.environmentRegion,\n            this.regionDiscoveryMetadata\n        );\n\n        const azureRegion = \n            this.authorityOptions.azureRegionConfiguration?.azureRegion === Constants.AZURE_REGION_AUTO_DISCOVER_FLAG\n                ? autodetectedRegionName\n                : this.authorityOptions.azureRegionConfiguration?.azureRegion;\n\n        if (this.authorityOptions.azureRegionConfiguration?.azureRegion === Constants.AZURE_REGION_AUTO_DISCOVER_FLAG) {\n            this.regionDiscoveryMetadata.region_outcome = autodetectedRegionName ?\n                RegionDiscoveryOutcomes.AUTO_DETECTION_REQUESTED_SUCCESSFUL :\n                RegionDiscoveryOutcomes.AUTO_DETECTION_REQUESTED_FAILED;\n        } else {\n            if (autodetectedRegionName) {\n                this.regionDiscoveryMetadata.region_outcome = (\n                    this.authorityOptions.azureRegionConfiguration?.azureRegion === autodetectedRegionName\n                ) ?\n                    RegionDiscoveryOutcomes.CONFIGURED_MATCHES_DETECTED :\n                    RegionDiscoveryOutcomes.CONFIGURED_NOT_DETECTED;\n            } else {\n                this.regionDiscoveryMetadata.region_outcome = RegionDiscoveryOutcomes.CONFIGURED_NO_AUTO_DETECTION;\n            }\n        }\n\n        if (azureRegion) {\n            this.regionDiscoveryMetadata.region_used = azureRegion;\n            return Authority.replaceWithRegionalInformation(metadata, azureRegion);\n        }\n\n        return metadata;\n    }\n\n    /**\n     * Updates the AuthorityMetadataEntity with new aliases, preferred_network and preferred_cache\n     * and returns where the information was retrieved from\n     * @param cachedMetadata\n     * @param newMetadata\n     */\n    private async updateCloudDiscoveryMetadata(metadataEntity: AuthorityMetadataEntity): Promise<AuthorityMetadataSource> {\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.AuthorityUpdateCloudDiscoveryMetadata, this.correlationId);\n        this.logger.verbose(\"Attempting to get cloud discovery metadata in the config\");\n        this.logger.verbosePii(`Known Authorities: ${this.authorityOptions.knownAuthorities || Constants.NOT_APPLICABLE}`);\n        this.logger.verbosePii(`Authority Metadata: ${this.authorityOptions.authorityMetadata || Constants.NOT_APPLICABLE}`);\n        this.logger.verbosePii(`Canonical Authority: ${metadataEntity.canonical_authority || Constants.NOT_APPLICABLE}`);\n        let metadata = this.getCloudDiscoveryMetadataFromConfig();\n        if (metadata) {\n            this.logger.verbose(\"Found cloud discovery metadata in the config.\");\n            metadataEntity.updateCloudDiscoveryMetadata(metadata, false);\n            return AuthorityMetadataSource.CONFIG;\n        }\n\n        // If the cached metadata came from config but that config was not passed to this instance, we must go to the network\n        this.logger.verbose(\"Did not find cloud discovery metadata in the config... Attempting to get cloud discovery metadata from the cache.\");\n        const metadataEntityExpired = metadataEntity.isExpired();\n        if (this.isAuthoritySameType(metadataEntity) && metadataEntity.aliasesFromNetwork && !metadataEntityExpired) {\n            this.logger.verbose(\"Found metadata in the cache.\");\n            // No need to update\n            return AuthorityMetadataSource.CACHE;\n        } else if (metadataEntityExpired) {\n            this.logger.verbose(\"The metadata entity is expired.\");\n        }\n\n        this.logger.verbose(\"Did not find cloud discovery metadata in the cache... Attempting to get cloud discovery metadata from the network.\");\n        this.performanceClient?.setPreQueueTime(PerformanceEvents.AuthorityGetCloudDiscoveryMetadataFromNetwork, this.correlationId);\n        metadata = await this.getCloudDiscoveryMetadataFromNetwork();\n        if (metadata) {\n            this.logger.verbose(\"cloud discovery metadata was successfully returned from getCloudDiscoveryMetadataFromNetwork()\");\n            metadataEntity.updateCloudDiscoveryMetadata(metadata, true);\n            return AuthorityMetadataSource.NETWORK;\n        }\n        \n        this.logger.verbose(\"Did not find cloud discovery metadata from the network... Attempting to get cloud discovery metadata from hardcoded values.\");\n        const harcodedMetadata = this.getCloudDiscoveryMetadataFromHarcodedValues();\n        if (harcodedMetadata && !this.options.skipAuthorityMetadataCache) {\n            this.logger.verbose(\"Found cloud discovery metadata from hardcoded values.\");\n            metadataEntity.updateCloudDiscoveryMetadata(harcodedMetadata, false);\n            return AuthorityMetadataSource.HARDCODED_VALUES;\n        }\n        \n        // Metadata could not be obtained from the config, cache, network or hardcoded values\n        this.logger.error(\"Did not find cloud discovery metadata from hardcoded values... Metadata could not be obtained from config, cache, network or hardcoded values. Throwing Untrusted Authority Error.\");\n        throw ClientConfigurationError.createUntrustedAuthorityError();\n    }\n\n    /**\n     * Parse cloudDiscoveryMetadata config or check knownAuthorities\n     */\n    private getCloudDiscoveryMetadataFromConfig(): CloudDiscoveryMetadata | null {\n        // Check if network response was provided in config\n        if (this.authorityOptions.cloudDiscoveryMetadata) {\n            this.logger.verbose(\"The cloud discovery metadata has been provided as a network response, in the config.\");\n            try {\n                this.logger.verbose(\"Attempting to parse the cloud discovery metadata.\");\n                const parsedResponse = JSON.parse(this.authorityOptions.cloudDiscoveryMetadata) as CloudInstanceDiscoveryResponse;\n                const metadata = Authority.getCloudDiscoveryMetadataFromNetworkResponse(\n                    parsedResponse.metadata,\n                    this.hostnameAndPort\n                );\n                this.logger.verbose(\"Parsed the cloud discovery metadata.\");\n                if (metadata) {\n                    this.logger.verbose(\"There is returnable metadata attached to the parsed cloud discovery metadata.\");\n                    return metadata;\n                } else {\n                    this.logger.verbose(\"There is no metadata attached to the parsed cloud discovery metadata.\");\n                }\n            } catch (e) {\n                this.logger.verbose(\"Unable to parse the cloud discovery metadata. Throwing Invalid Cloud Discovery Metadata Error.\");\n                throw ClientConfigurationError.createInvalidCloudDiscoveryMetadataError();\n            }\n        }\n\n        // If cloudDiscoveryMetadata is empty or does not contain the host, check knownAuthorities\n        if (this.isInKnownAuthorities()) {\n            this.logger.verbose(\"The host is included in knownAuthorities. Creating new cloud discovery metadata from the host.\");\n            return Authority.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort);\n        }\n\n        return null;\n    }\n\n    /**\n     * Called to get metadata from network if CloudDiscoveryMetadata was not populated by config\n     * \n     * @param hasHardcodedMetadata boolean\n     */\n    private async getCloudDiscoveryMetadataFromNetwork(): Promise<CloudDiscoveryMetadata | null> {\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.AuthorityGetCloudDiscoveryMetadataFromNetwork, this.correlationId);\n        const instanceDiscoveryEndpoint =\n            `${Constants.AAD_INSTANCE_DISCOVERY_ENDPT}${this.canonicalAuthority}oauth2/v2.0/authorize`;\n        const options: ImdsOptions = {};\n\n        /*\n         * TODO: Add a timeout if the authority exists in our library's\n         * hardcoded list of metadata\n         */\n\n        let match = null;\n        try {\n            const response =\n                await this.networkInterface.sendGetRequestAsync<CloudInstanceDiscoveryResponse | CloudInstanceDiscoveryErrorResponse>(\n                    instanceDiscoveryEndpoint,\n                    options\n                );\n            \n            let typedResponseBody: CloudInstanceDiscoveryResponse | CloudInstanceDiscoveryErrorResponse;\n            let metadata: Array<CloudDiscoveryMetadata>;\n            if (isCloudInstanceDiscoveryResponse(response.body)) {\n                typedResponseBody = response.body as CloudInstanceDiscoveryResponse;\n                metadata = typedResponseBody.metadata;\n\n                this.logger.verbosePii(`tenant_discovery_endpoint is: ${typedResponseBody.tenant_discovery_endpoint}`);\n            } else if (isCloudInstanceDiscoveryErrorResponse(response.body)) {\n                this.logger.warning(`A CloudInstanceDiscoveryErrorResponse was returned. The cloud instance discovery network request's status code is: ${response.status}`);\n\n                typedResponseBody = response.body as CloudInstanceDiscoveryErrorResponse;\n                if (typedResponseBody.error === Constants.INVALID_INSTANCE) {\n                    this.logger.error(\"The CloudInstanceDiscoveryErrorResponse error is invalid_instance.\");\n                    return null;\n                }\n\n                this.logger.warning(`The CloudInstanceDiscoveryErrorResponse error is ${typedResponseBody.error}`);\n                this.logger.warning(`The CloudInstanceDiscoveryErrorResponse error description is ${typedResponseBody.error_description}`);\n                \n                this.logger.warning(\"Setting the value of the CloudInstanceDiscoveryMetadata (returned from the network) to []\");\n                metadata = [];\n            } else {\n                this.logger.error(\"AAD did not return a CloudInstanceDiscoveryResponse or CloudInstanceDiscoveryErrorResponse\");\n                return null;\n            }\n\n            this.logger.verbose(\"Attempting to find a match between the developer's authority and the CloudInstanceDiscoveryMetadata returned from the network request.\");\n            match = Authority.getCloudDiscoveryMetadataFromNetworkResponse(\n                metadata,\n                this.hostnameAndPort\n            );\n        } catch (error) {\n            if (error instanceof AuthError) {\n                this.logger.error(`There was a network error while attempting to get the cloud discovery instance metadata.\\nError: ${error.errorCode}\\nError Description: ${error.errorMessage}`);\n            } else {\n                const typedError = error as Error;\n                this.logger.error(`A non-MSALJS error was thrown while attempting to get the cloud instance discovery metadata.\\nError: ${typedError.name}\\nError Description: ${typedError.message}`);\n            }\n            \n            return null;\n        }\n\n        // Custom Domain scenario, host is trusted because Instance Discovery call succeeded\n        if (!match) {\n            this.logger.warning(\"The developer's authority was not found within the CloudInstanceDiscoveryMetadata returned from the network request.\");\n            this.logger.verbose(\"Creating custom Authority for custom domain scenario.\");\n\n            match = Authority.createCloudDiscoveryMetadataFromHost(\n                this.hostnameAndPort\n            );\n        }\n        return match;\n    }\n\n    /**\n     * Get cloud discovery metadata for common authorities \n     */\n    private getCloudDiscoveryMetadataFromHarcodedValues(): CloudDiscoveryMetadata | null {\n        if (this.canonicalAuthority in InstanceDiscoveryMetadata) {\n            return InstanceDiscoveryMetadata[this.canonicalAuthority];\n        }\n\n        return null;\n    }\n\n    /**\n     * Helper function to determine if this host is included in the knownAuthorities config option\n     */\n    private isInKnownAuthorities(): boolean {\n        const matches = this.authorityOptions.knownAuthorities.filter((authority) => {\n            return UrlString.getDomainFromUrl(authority).toLowerCase() === this.hostnameAndPort;\n        });\n\n        return matches.length > 0;\n    }\n\n    /**\n     * helper function to populate the authority based on azureCloudOptions\n     * @param authorityString\n     * @param azureCloudOptions\n     */\n    static generateAuthority(authorityString: string, azureCloudOptions?: AzureCloudOptions): string {\n        let authorityAzureCloudInstance;\n\n        if (azureCloudOptions && azureCloudOptions.azureCloudInstance !== AzureCloudInstance.None) {\n            const tenant = azureCloudOptions.tenant ? azureCloudOptions.tenant : Constants.DEFAULT_COMMON_TENANT;\n            authorityAzureCloudInstance = `${azureCloudOptions.azureCloudInstance}/${tenant}/`;\n        }\n\n        return authorityAzureCloudInstance ? authorityAzureCloudInstance : authorityString;\n    }\n\n    /**\n     * Creates cloud discovery metadata object from a given host\n     * @param host\n     */\n    static createCloudDiscoveryMetadataFromHost(host: string): CloudDiscoveryMetadata {\n        return {\n            preferred_network: host,\n            preferred_cache: host,\n            aliases: [host]\n        };\n    }\n\n    /**\n     * Searches instance discovery network response for the entry that contains the host in the aliases list\n     * @param response\n     * @param authority\n     */\n    static getCloudDiscoveryMetadataFromNetworkResponse(\n        response: CloudDiscoveryMetadata[],\n        authority: string\n    ): CloudDiscoveryMetadata | null {\n        for (let i = 0; i < response.length; i++) {\n            const metadata = response[i];\n            if (metadata.aliases.indexOf(authority) > -1) {\n                return metadata;\n            }\n        }\n\n        return null;\n    }\n\n    /**\n     * helper function to generate environment from authority object\n     */\n    getPreferredCache(): string {\n        if(this.discoveryComplete()) {\n            return this.metadata.preferred_cache;\n        } else {\n            throw ClientAuthError.createEndpointDiscoveryIncompleteError(\"Discovery incomplete.\");\n        }\n    }\n\n    /**\n     * Returns whether or not the provided host is an alias of this authority instance\n     * @param host\n     */\n    isAlias(host: string): boolean {\n        return this.metadata.aliases.indexOf(host) > -1;\n    }\n\n    /**\n     * Checks whether the provided host is that of a public cloud authority\n     *\n     * @param authority string\n     * @returns bool\n     */\n    static isPublicCloudAuthority(host: string): boolean {\n        return Constants.KNOWN_PUBLIC_CLOUDS.indexOf(host) >= 0;\n    }\n\n    /**\n     * Rebuild the authority string with the region\n     *\n     * @param host string\n     * @param region string\n     */\n    static buildRegionalAuthorityString(host: string, region: string, queryString?: string): string {\n        // Create and validate a Url string object with the initial authority string\n        const authorityUrlInstance = new UrlString(host);\n        authorityUrlInstance.validateAsUri();\n\n        const authorityUrlParts = authorityUrlInstance.getUrlComponents();\n\n        let hostNameAndPort= `${region}.${authorityUrlParts.HostNameAndPort}`;\n\n        if (this.isPublicCloudAuthority(authorityUrlParts.HostNameAndPort)) {\n            hostNameAndPort = `${region}.${Constants.REGIONAL_AUTH_PUBLIC_CLOUD_SUFFIX}`;\n        }\n\n        // Include the query string portion of the url\n        const url = UrlString.constructAuthorityUriFromObject({\n            ...authorityUrlInstance.getUrlComponents(),\n            HostNameAndPort: hostNameAndPort\n        }).urlString;\n\n        // Add the query string if a query string was provided\n        if (queryString) return `${url}?${queryString}`;\n\n        return url;\n    }\n\n    /**\n     * Replace the endpoints in the metadata object with their regional equivalents.\n     *\n     * @param metadata OpenIdConfigResponse\n     * @param azureRegion string\n     */\n    static replaceWithRegionalInformation(metadata: OpenIdConfigResponse, azureRegion: string): OpenIdConfigResponse {\n        metadata.authorization_endpoint = Authority.buildRegionalAuthorityString(metadata.authorization_endpoint, azureRegion);\n        // TODO: Enquire on whether we should leave the query string or remove it before releasing the feature\n        metadata.token_endpoint = Authority.buildRegionalAuthorityString(\n            metadata.token_endpoint, azureRegion, Constants.REGIONAL_AUTH_NON_MSI_QUERY_STRING\n        );\n\n        if (metadata.end_session_endpoint) {\n            metadata.end_session_endpoint = Authority.buildRegionalAuthorityString(metadata.end_session_endpoint, azureRegion);\n        }\n\n        return metadata;\n    }\n}\n\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;;;AAGG;AA2BH;;;AAGG;AACH,IAAA,SAAA,kBAAA,YAAA;AAwBI,IAAA,SAAA,SAAA,CACI,SAAiB,EACjB,gBAAgC,EAChC,YAA2B,EAC3B,gBAAkC,EAClC,MAAc,EACd,iBAAsC,EACtC,aAAsB,EAAA;AAEtB,QAAA,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;AACpC,QAAA,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,CAAC;AACzC,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACzC,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACjC,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACzC,QAAA,IAAI,CAAC,uBAAuB,GAAG,EAAE,WAAW,EAAE,SAAS,EAAE,aAAa,EAAE,SAAS,EAAE,cAAc,EAAE,SAAS,EAAE,CAAC;AAC/G,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC3C,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACnC,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,CAAC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;KAC5G;AAGD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAa,CAAA,SAAA,EAAA,eAAA,EAAA;;AAAxB,QAAA,GAAA,EAAA,YAAA;AACI,YAAA,IAAM,YAAY,GAAG,IAAI,CAAC,+BAA+B,CAAC,YAAY,CAAC;YACvE,IAAI,YAAY,CAAC,MAAM,EAAE;AACrB,gBAAA,QAAO,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;oBAChC,KAAK,SAAS,CAAC,IAAI;wBACf,OAAO,aAAa,CAAC,IAAI,CAAC;oBAC9B,KAAK,SAAS,CAAC,IAAI;wBACf,OAAO,aAAa,CAAC,IAAI,CAAC;AAGjC,iBAAA;AACJ,aAAA;YACD,OAAO,aAAa,CAAC,OAAO,CAAC;SAChC;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAY,CAAA,SAAA,EAAA,cAAA,EAAA;AAHvB;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACI,YAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;SAC7C;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAO,CAAA,SAAA,EAAA,SAAA,EAAA;AAHlB;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;YACI,OAAO,IAAI,CAAC,gBAAgB,CAAC;SAChC;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAkB,CAAA,SAAA,EAAA,oBAAA,EAAA;AAH7B;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACI,YAAA,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC;SAC7C;AAED;;AAEG;AACH,QAAA,GAAA,EAAA,UAA8B,GAAW,EAAA;YACrC,IAAI,CAAC,mBAAmB,GAAG,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC;AAC9C,YAAA,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,CAAC;AACzC,YAAA,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC;SAChD;;;AATA,KAAA,CAAA,CAAA;AAcD,IAAA,MAAA,CAAA,cAAA,CAAW,SAA+B,CAAA,SAAA,EAAA,iCAAA,EAAA;AAH1C;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACI,YAAA,IAAI,CAAC,IAAI,CAAC,gCAAgC,EAAE;gBACxC,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,CAAC;AACvF,aAAA;YAED,OAAO,IAAI,CAAC,gCAAgC,CAAC;SAChD;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAe,CAAA,SAAA,EAAA,iBAAA,EAAA;AAH1B;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;YACI,OAAO,IAAI,CAAC,+BAA+B,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC;SAC7E;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAM,CAAA,SAAA,EAAA,QAAA,EAAA;AAHjB;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;YACI,OAAO,IAAI,CAAC,+BAA+B,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;SAC/D;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAqB,CAAA,SAAA,EAAA,uBAAA,EAAA;AAHhC;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACI,YAAA,IAAG,IAAI,CAAC,iBAAiB,EAAE,EAAE;AACzB,gBAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;AACxE,gBAAA,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACvC,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,eAAe,CAAC,sCAAsC,CAAC,uBAAuB,CAAC,CAAC;AACzF,aAAA;SACJ;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAa,CAAA,SAAA,EAAA,eAAA,EAAA;AAHxB;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACI,YAAA,IAAG,IAAI,CAAC,iBAAiB,EAAE,EAAE;AACzB,gBAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;AAChE,gBAAA,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACvC,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,eAAe,CAAC,sCAAsC,CAAC,uBAAuB,CAAC,CAAC;AACzF,aAAA;SACJ;;;AAAA,KAAA,CAAA,CAAA;AAED,IAAA,MAAA,CAAA,cAAA,CAAW,SAAkB,CAAA,SAAA,EAAA,oBAAA,EAAA;AAA7B,QAAA,GAAA,EAAA,YAAA;AACI,YAAA,IAAG,IAAI,CAAC,iBAAiB,EAAE,EAAE;AACzB,gBAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC;AACjG,gBAAA,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACvC,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,eAAe,CAAC,sCAAsC,CAAC,uBAAuB,CAAC,CAAC;AACzF,aAAA;SACJ;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAkB,CAAA,SAAA,EAAA,oBAAA,EAAA;AAH7B;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACI,YAAA,IAAG,IAAI,CAAC,iBAAiB,EAAE,EAAE;;AAEzB,gBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE;AACrC,oBAAA,MAAM,eAAe,CAAC,6BAA6B,EAAE,CAAC;AACzD,iBAAA;AACD,gBAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;AACtE,gBAAA,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACvC,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,eAAe,CAAC,sCAAsC,CAAC,uBAAuB,CAAC,CAAC;AACzF,aAAA;SACJ;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAqB,CAAA,SAAA,EAAA,uBAAA,EAAA;AAHhC;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACI,YAAA,IAAG,IAAI,CAAC,iBAAiB,EAAE,EAAE;AACzB,gBAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACxD,gBAAA,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACvC,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,eAAe,CAAC,sCAAsC,CAAC,uBAAuB,CAAC,CAAC;AACzF,aAAA;SACJ;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAO,CAAA,SAAA,EAAA,SAAA,EAAA;AAHlB;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACI,YAAA,IAAG,IAAI,CAAC,iBAAiB,EAAE,EAAE;AACzB,gBAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC1D,gBAAA,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACvC,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,eAAe,CAAC,sCAAsC,CAAC,uBAAuB,CAAC,CAAC;AACzF,aAAA;SACJ;;;AAAA,KAAA,CAAA,CAAA;AAED;;;AAGG;IACK,SAAa,CAAA,SAAA,CAAA,aAAA,GAArB,UAAsB,SAAiB,EAAA;QACnC,OAAO,SAAS,CAAC,OAAO,CAAC,sBAAsB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;KACjE,CAAA;AAED;;;AAGG;IACK,SAAW,CAAA,SAAA,CAAA,WAAA,GAAnB,UAAoB,SAAiB,EAAA;QACjC,IAAI,QAAQ,GAAG,SAAS,CAAC;QACzB,IAAM,kBAAkB,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;QAC5E,IAAM,oBAAoB,GAAG,kBAAkB,CAAC,gBAAgB,EAAE,CAAC,YAAY,CAAC;AAChF,QAAA,IAAM,qBAAqB,GAAG,IAAI,CAAC,+BAA+B,CAAC,YAAY,CAAC;AAEhF,QAAA,qBAAqB,CAAC,OAAO,CAAC,UAAC,WAAW,EAAE,KAAK,EAAA;AAC7C,YAAA,IAAM,UAAU,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAC/C,IAAI,WAAW,KAAK,UAAU,EAAE;AAC5B,gBAAA,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAA,GAAI,UAAU,GAAA,GAAG,EAAE,GAAA,GAAI,WAAW,GAAA,GAAG,CAAC,CAAC;AACtE,aAAA;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,QAAQ,CAAC;KACnB,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAc,SAAkC,CAAA,SAAA,EAAA,oCAAA,EAAA;AAHhD;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACI,YAAA,IACI,IAAI,CAAC,aAAa,KAAK,aAAa,CAAC,IAAI;AACzC,gBAAA,IAAI,CAAC,aAAa,KAAK,aAAa,CAAC,IAAI;AACzC,gBAAA,IAAI,CAAC,YAAY,KAAK,YAAY,CAAC,IAAI,EACzC;AACE,gBAAA,OAAU,IAAI,CAAC,kBAAkB,GAAA,kCAAkC,CAAC;AACvE,aAAA;AACD,YAAA,OAAU,IAAI,CAAC,kBAAkB,GAAA,uCAAuC,CAAC;SAC5E;;;AAAA,KAAA,CAAA,CAAA;AAED;;AAEG;AACH,IAAA,SAAA,CAAA,SAAA,CAAA,iBAAiB,GAAjB,YAAA;AACI,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;KAC1B,CAAA;AAED;;;AAGG;AACU,IAAA,SAAA,CAAA,SAAA,CAAA,qBAAqB,GAAlC,YAAA;;;;;;;AACI,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,8BAA8B,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;wBAE9G,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,2BAA2B,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;wBACzF,IAAI,CAAC,cAAc,EAAE;AACjB,4BAAA,cAAc,GAAG,IAAI,uBAAuB,EAAE,CAAC;AAC/C,4BAAA,cAAc,CAAC,wBAAwB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AACpE,yBAAA;AAED,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,qCAAqC,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;AACxF,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,4BAA4B,CAAC,cAAc,CAAC,CAAA,CAAA;;AAA9E,wBAAA,oBAAoB,GAAG,EAAuD,CAAA,IAAA,EAAA,CAAA;AACpF,wBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,cAAc,CAAC,iBAAiB,CAAC,CAAC;AAClH,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,+BAA+B,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;AACxF,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAA,CAAA;;AAAlE,wBAAA,cAAc,GAAG,EAAiD,CAAA,IAAA,EAAA,CAAA;wBAExE,IAAI,oBAAoB,KAAK,uBAAuB,CAAC,KAAK,IAAI,cAAc,KAAK,uBAAuB,CAAC,KAAK,EAAE;;4BAE5G,cAAc,CAAC,cAAc,EAAE,CAAC;AAChC,4BAAA,cAAc,CAAC,wBAAwB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AACpE,yBAAA;wBAEK,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,iCAAiC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;wBACrG,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;AACjE,wBAAA,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;;;;;AAClC,KAAA,CAAA;AAED;;;AAGG;IACW,SAAsB,CAAA,SAAA,CAAA,sBAAA,GAApC,UAAqC,cAAuC,EAAA;;;;;;;AACxE,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,+BAA+B,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;AAE/G,wBAAA,QAAQ,GAAG,IAAI,CAAC,6BAA6B,EAAE,CAAC;AACpD,wBAAA,IAAI,QAAQ,EAAE;AACV,4BAAA,cAAc,CAAC,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;4BACvD,OAAO,CAAA,CAAA,aAAA,uBAAuB,CAAC,MAAM,CAAC,CAAA;AACzC,yBAAA;AAED,wBAAA,IAAI,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,cAAc,CAAC,oBAAoB,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE;;4BAEhH,OAAO,CAAA,CAAA,aAAA,uBAAuB,CAAC,KAAK,CAAC,CAAA;AACxC,yBAAA;AAEG,wBAAA,gBAAgB,GAAG,IAAI,CAAC,sCAAsC,EAAE,CAAC;AACrE,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,uCAAuC,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;AAC5G,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,8BAA8B,EAAE,CAAA,CAAA;;wBAAtD,QAAQ,GAAG,SAA2C,CAAC;AACnD,wBAAA,IAAA,CAAA,QAAQ,EAAR,OAAQ,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAEJ,wBAAA,IAAA,EAAA,CAAA,EAAA,GAAA,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,0CAAE,WAAW,CAAA,EAAA,OAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAC3D,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,8CAA8C,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;AACnH,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,qCAAqC,CAAC,QAAQ,CAAC,CAAA,CAAA;;wBAArE,QAAQ,GAAG,SAA0D,CAAC;;;AAG1E,wBAAA,cAAc,CAAC,sBAAsB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;wBACtD,OAAO,CAAA,CAAA,aAAA,uBAAuB,CAAC,OAAO,CAAC,CAAA;;8BAGvC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,0BAA0B,CAAA,EAArE,OAAqE,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAEjE,wBAAA,IAAA,EAAA,CAAA,EAAA,GAAA,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,0CAAE,WAAW,CAAA,EAAA,OAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAC3D,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,8CAA8C,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;AAC3G,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,qCAAqC,CAC/D,gBAAgB,CACnB,CAAA,CAAA;;wBAFD,gBAAgB,GAAG,SAElB,CAAC;;;AAGN,wBAAA,cAAc,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;wBAC/D,OAAO,CAAA,CAAA,aAAA,uBAAuB,CAAC,gBAAgB,CAAC,CAAA;4BAEhD,MAAM,eAAe,CAAC,kCAAkC,CACpD,IAAI,CAAC,kCAAkC,CAC1C,CAAC;;;;AAET,KAAA,CAAA;AAED;;;;;AAKG;IACK,SAAmB,CAAA,SAAA,CAAA,mBAAA,GAA3B,UAA4B,cAAuC,EAAA;QAC/D,IAAM,kBAAkB,GAAG,IAAI,SAAS,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;QAC7E,IAAM,WAAW,GAAG,kBAAkB,CAAC,gBAAgB,EAAE,CAAC,YAAY,CAAC;QAEvE,OAAO,WAAW,CAAC,MAAM,KAAK,IAAI,CAAC,+BAA+B,CAAC,YAAY,CAAC,MAAM,CAAC;KAC1F,CAAA;AAED;;AAEG;AACK,IAAA,SAAA,CAAA,SAAA,CAAA,6BAA6B,GAArC,YAAA;AACI,QAAA,IAAI,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE;YACzC,IAAI;gBACA,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAyB,CAAC;AACtF,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACR,gBAAA,MAAM,wBAAwB,CAAC,mCAAmC,EAAE,CAAC;AACxE,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;AACW,IAAA,SAAA,CAAA,SAAA,CAAA,8BAA8B,GAA5C,YAAA;;;;;;;AACI,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,uCAAuC,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;wBAErH,OAAO,GAAgB,EAAE,CAAC;;;;wBAQX,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,gBAAgB;AACxC,gCAAA,mBAAmB,CAAuB,IAAI,CAAC,kCAAkC,EAAE,OAAO,CAAC,CAAA,CAAA;;AADzF,wBAAA,QAAQ,GAAG,EAC8E,CAAA,IAAA,EAAA,CAAA;AAC/F,wBAAA,OAAA,CAAA,CAAA,aAAO,sBAAsB,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,CAAA;;;AAEpE,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;;;AAEnB,KAAA,CAAA;AAED;;AAEG;AACK,IAAA,SAAA,CAAA,SAAA,CAAA,sCAAsC,GAA9C,YAAA;AACI,QAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,gBAAgB,EAAE;AAC7C,YAAA,OAAO,gBAAgB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AACpD,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;AAEG;IACW,SAAqC,CAAA,SAAA,CAAA,qCAAA,GAAnD,UAAoD,QAA8B,EAAA;;;;;;;AAC9E,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,8CAA8C,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;AAElI,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,2BAA2B,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;AAC5E,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CAAA,CAAA,EAAA,GAClE,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,0CAAE,iBAAiB,EACjE,IAAI,CAAC,uBAAuB,CAC/B,CAAA,CAAA;;AAHK,wBAAA,sBAAsB,GAAG,EAG9B,CAAA,IAAA,EAAA,CAAA;AAEK,wBAAA,WAAW,GACb,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,WAAW,MAAK,SAAS,CAAC,+BAA+B;AACrG,8BAAE,sBAAsB;8BACvB,CAAA,EAAA,GAAC,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,WAAW,CAAC;AAEtE,wBAAA,IAAI,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAW,MAAK,SAAS,CAAC,+BAA+B,EAAE;AAC3G,4BAAA,IAAI,CAAC,uBAAuB,CAAC,cAAc,GAAG,sBAAsB;gCAChE,uBAAuB,CAAC,mCAAmC;gCAC3D,uBAAuB,CAAC,+BAA+B,CAAC;AAC/D,yBAAA;AAAM,6BAAA;AACH,4BAAA,IAAI,sBAAsB,EAAE;AACxB,gCAAA,IAAI,CAAC,uBAAuB,CAAC,cAAc,GAAG,CAC1C,OAAA,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAW,MAAK,sBAAsB;oCAEtF,uBAAuB,CAAC,2BAA2B;oCACnD,uBAAuB,CAAC,uBAAuB,CAAC;AACvD,6BAAA;AAAM,iCAAA;gCACH,IAAI,CAAC,uBAAuB,CAAC,cAAc,GAAG,uBAAuB,CAAC,4BAA4B,CAAC;AACtG,6BAAA;AACJ,yBAAA;AAED,wBAAA,IAAI,WAAW,EAAE;AACb,4BAAA,IAAI,CAAC,uBAAuB,CAAC,WAAW,GAAG,WAAW,CAAC;4BACvD,OAAO,CAAA,CAAA,aAAA,SAAS,CAAC,8BAA8B,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAA;AAC1E,yBAAA;AAED,wBAAA,OAAA,CAAA,CAAA,aAAO,QAAQ,CAAC,CAAA;;;;AACnB,KAAA,CAAA;AAED;;;;;AAKG;IACW,SAA4B,CAAA,SAAA,CAAA,4BAAA,GAA1C,UAA2C,cAAuC,EAAA;;;;;;;AAC9E,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,qCAAqC,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;AACzH,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0DAA0D,CAAC,CAAC;AAChF,wBAAA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,yBAAsB,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,IAAI,SAAS,CAAC,cAAc,CAAE,CAAC,CAAC;AACnH,wBAAA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,0BAAuB,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,IAAI,SAAS,CAAC,cAAc,CAAE,CAAC,CAAC;AACrH,wBAAA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,uBAAwB,IAAA,cAAc,CAAC,mBAAmB,IAAI,SAAS,CAAC,cAAc,CAAE,CAAC,CAAC;AAC7G,wBAAA,QAAQ,GAAG,IAAI,CAAC,mCAAmC,EAAE,CAAC;AAC1D,wBAAA,IAAI,QAAQ,EAAE;AACV,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+CAA+C,CAAC,CAAC;AACrE,4BAAA,cAAc,CAAC,4BAA4B,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;4BAC7D,OAAO,CAAA,CAAA,aAAA,uBAAuB,CAAC,MAAM,CAAC,CAAA;AACzC,yBAAA;;AAGD,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mHAAmH,CAAC,CAAC;AACnI,wBAAA,qBAAqB,GAAG,cAAc,CAAC,SAAS,EAAE,CAAC;AACzD,wBAAA,IAAI,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,cAAc,CAAC,kBAAkB,IAAI,CAAC,qBAAqB,EAAE;AACzG,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;;4BAEpD,OAAO,CAAA,CAAA,aAAA,uBAAuB,CAAC,KAAK,CAAC,CAAA;AACxC,yBAAA;AAAM,6BAAA,IAAI,qBAAqB,EAAE;AAC9B,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC1D,yBAAA;AAED,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,oHAAoH,CAAC,CAAC;AAC1I,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,6CAA6C,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;AAClH,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,oCAAoC,EAAE,CAAA,CAAA;;wBAA5D,QAAQ,GAAG,SAAiD,CAAC;AAC7D,wBAAA,IAAI,QAAQ,EAAE;AACV,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gGAAgG,CAAC,CAAC;AACtH,4BAAA,cAAc,CAAC,4BAA4B,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;4BAC5D,OAAO,CAAA,CAAA,aAAA,uBAAuB,CAAC,OAAO,CAAC,CAAA;AAC1C,yBAAA;AAED,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,6HAA6H,CAAC,CAAC;AAC7I,wBAAA,gBAAgB,GAAG,IAAI,CAAC,2CAA2C,EAAE,CAAC;wBAC5E,IAAI,gBAAgB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,0BAA0B,EAAE;AAC9D,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uDAAuD,CAAC,CAAC;AAC7E,4BAAA,cAAc,CAAC,4BAA4B,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;4BACrE,OAAO,CAAA,CAAA,aAAA,uBAAuB,CAAC,gBAAgB,CAAC,CAAA;AACnD,yBAAA;;AAGD,wBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oLAAoL,CAAC,CAAC;AACxM,wBAAA,MAAM,wBAAwB,CAAC,6BAA6B,EAAE,CAAC;;;;AAClE,KAAA,CAAA;AAED;;AAEG;AACK,IAAA,SAAA,CAAA,SAAA,CAAA,mCAAmC,GAA3C,YAAA;;AAEI,QAAA,IAAI,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,EAAE;AAC9C,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sFAAsF,CAAC,CAAC;YAC5G,IAAI;AACA,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mDAAmD,CAAC,CAAC;AACzE,gBAAA,IAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,CAAmC,CAAC;AAClH,gBAAA,IAAM,QAAQ,GAAG,SAAS,CAAC,4CAA4C,CACnE,cAAc,CAAC,QAAQ,EACvB,IAAI,CAAC,eAAe,CACvB,CAAC;AACF,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5D,gBAAA,IAAI,QAAQ,EAAE;AACV,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+EAA+E,CAAC,CAAC;AACrG,oBAAA,OAAO,QAAQ,CAAC;AACnB,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uEAAuE,CAAC,CAAC;AAChG,iBAAA;AACJ,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACR,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gGAAgG,CAAC,CAAC;AACtH,gBAAA,MAAM,wBAAwB,CAAC,wCAAwC,EAAE,CAAC;AAC7E,aAAA;AACJ,SAAA;;AAGD,QAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE;AAC7B,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gGAAgG,CAAC,CAAC;YACtH,OAAO,SAAS,CAAC,oCAAoC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AAC/E,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;AACW,IAAA,SAAA,CAAA,SAAA,CAAA,oCAAoC,GAAlD,YAAA;;;;;;;AACI,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,6CAA6C,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;wBAC3H,yBAAyB,GAC3B,EAAG,GAAA,SAAS,CAAC,4BAA4B,GAAG,IAAI,CAAC,kBAAkB,GAAA,uBAAuB,CAAC;wBACzF,OAAO,GAAgB,EAAE,CAAC;wBAO5B,KAAK,GAAG,IAAI,CAAC;;;;wBAGT,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAC3C,yBAAyB,EACzB,OAAO,CACV,CAAA,CAAA;;AAJC,wBAAA,QAAQ,GACV,EAGC,CAAA,IAAA,EAAA,CAAA;AAED,wBAAA,iBAAiB,SAAsE,CAAC;AACxF,wBAAA,QAAQ,SAA+B,CAAC;AAC5C,wBAAA,IAAI,gCAAgC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACjD,4BAAA,iBAAiB,GAAG,QAAQ,CAAC,IAAsC,CAAC;AACpE,4BAAA,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,CAAC;4BAEtC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,mCAAiC,iBAAiB,CAAC,yBAA2B,CAAC,CAAC;AAC1G,yBAAA;AAAM,6BAAA,IAAI,qCAAqC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;4BAC7D,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,wHAAsH,QAAQ,CAAC,MAAQ,CAAC,CAAC;AAE7J,4BAAA,iBAAiB,GAAG,QAAQ,CAAC,IAA2C,CAAC;AACzE,4BAAA,IAAI,iBAAiB,CAAC,KAAK,KAAK,SAAS,CAAC,gBAAgB,EAAE;AACxD,gCAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oEAAoE,CAAC,CAAC;AACxF,gCAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;AACf,6BAAA;4BAED,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sDAAoD,iBAAiB,CAAC,KAAO,CAAC,CAAC;4BACnG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kEAAgE,iBAAiB,CAAC,iBAAmB,CAAC,CAAC;AAE3H,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,2FAA2F,CAAC,CAAC;4BACjH,QAAQ,GAAG,EAAE,CAAC;AACjB,yBAAA;AAAM,6BAAA;AACH,4BAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4FAA4F,CAAC,CAAC;AAChH,4BAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;AACf,yBAAA;AAED,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,wIAAwI,CAAC,CAAC;wBAC9J,KAAK,GAAG,SAAS,CAAC,4CAA4C,CAC1D,QAAQ,EACR,IAAI,CAAC,eAAe,CACvB,CAAC;;;;wBAEF,IAAI,OAAK,YAAY,SAAS,EAAE;AAC5B,4BAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mGAAoG,GAAA,OAAK,CAAC,SAAS,GAAwB,uBAAA,GAAA,OAAK,CAAC,YAAc,CAAC,CAAC;AACtL,yBAAA;AAAM,6BAAA;4BACG,UAAU,GAAG,OAAc,CAAC;AAClC,4BAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uGAAwG,GAAA,UAAU,CAAC,IAAI,GAAwB,uBAAA,GAAA,UAAU,CAAC,OAAS,CAAC,CAAC;AAC1L,yBAAA;AAED,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;wBAIhB,IAAI,CAAC,KAAK,EAAE;AACR,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sHAAsH,CAAC,CAAC;AAC5I,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uDAAuD,CAAC,CAAC;4BAE7E,KAAK,GAAG,SAAS,CAAC,oCAAoC,CAClD,IAAI,CAAC,eAAe,CACvB,CAAC;AACL,yBAAA;AACD,wBAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;;;;AAChB,KAAA,CAAA;AAED;;AAEG;AACK,IAAA,SAAA,CAAA,SAAA,CAAA,2CAA2C,GAAnD,YAAA;AACI,QAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,yBAAyB,EAAE;AACtD,YAAA,OAAO,yBAAyB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AAC7D,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;AAEG;AACK,IAAA,SAAA,CAAA,SAAA,CAAA,oBAAoB,GAA5B,YAAA;QAAA,IAMC,KAAA,GAAA,IAAA,CAAA;QALG,IAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAC,SAAS,EAAA;AACpE,YAAA,OAAO,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,KAAK,KAAI,CAAC,eAAe,CAAC;AACxF,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;KAC7B,CAAA;AAED;;;;AAIG;AACI,IAAA,SAAA,CAAA,iBAAiB,GAAxB,UAAyB,eAAuB,EAAE,iBAAqC,EAAA;AACnF,QAAA,IAAI,2BAA2B,CAAC;QAEhC,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,kBAAkB,KAAK,kBAAkB,CAAC,IAAI,EAAE;AACvF,YAAA,IAAM,MAAM,GAAG,iBAAiB,CAAC,MAAM,GAAG,iBAAiB,CAAC,MAAM,GAAG,SAAS,CAAC,qBAAqB,CAAC;AACrG,YAAA,2BAA2B,GAAM,iBAAiB,CAAC,kBAAkB,GAAI,GAAA,GAAA,MAAM,MAAG,CAAC;AACtF,SAAA;QAED,OAAO,2BAA2B,GAAG,2BAA2B,GAAG,eAAe,CAAC;KACtF,CAAA;AAED;;;AAGG;IACI,SAAoC,CAAA,oCAAA,GAA3C,UAA4C,IAAY,EAAA;QACpD,OAAO;AACH,YAAA,iBAAiB,EAAE,IAAI;AACvB,YAAA,eAAe,EAAE,IAAI;YACrB,OAAO,EAAE,CAAC,IAAI,CAAC;SAClB,CAAC;KACL,CAAA;AAED;;;;AAIG;AACI,IAAA,SAAA,CAAA,4CAA4C,GAAnD,UACI,QAAkC,EAClC,SAAiB,EAAA;AAEjB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,YAAA,IAAM,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;AAC1C,gBAAA,OAAO,QAAQ,CAAC;AACnB,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;AAEG;AACH,IAAA,SAAA,CAAA,SAAA,CAAA,iBAAiB,GAAjB,YAAA;AACI,QAAA,IAAG,IAAI,CAAC,iBAAiB,EAAE,EAAE;AACzB,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC;AACxC,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,eAAe,CAAC,sCAAsC,CAAC,uBAAuB,CAAC,CAAC;AACzF,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,SAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,IAAY,EAAA;AAChB,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;KACnD,CAAA;AAED;;;;;AAKG;IACI,SAAsB,CAAA,sBAAA,GAA7B,UAA8B,IAAY,EAAA;QACtC,OAAO,SAAS,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC3D,CAAA;AAED;;;;;AAKG;AACI,IAAA,SAAA,CAAA,4BAA4B,GAAnC,UAAoC,IAAY,EAAE,MAAc,EAAE,WAAoB,EAAA;;AAElF,QAAA,IAAM,oBAAoB,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC;QACjD,oBAAoB,CAAC,aAAa,EAAE,CAAC;AAErC,QAAA,IAAM,iBAAiB,GAAG,oBAAoB,CAAC,gBAAgB,EAAE,CAAC;AAElE,QAAA,IAAI,eAAe,GAAK,MAAM,SAAI,iBAAiB,CAAC,eAAiB,CAAC;QAEtE,IAAI,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,eAAe,CAAC,EAAE;AAChE,YAAA,eAAe,GAAM,MAAM,GAAA,GAAA,GAAI,SAAS,CAAC,iCAAmC,CAAC;AAChF,SAAA;;AAGD,QAAA,IAAM,GAAG,GAAG,SAAS,CAAC,+BAA+B,uBAC9C,oBAAoB,CAAC,gBAAgB,EAAE,KAC1C,eAAe,EAAE,eAAe,EAClC,CAAA,CAAA,CAAC,SAAS,CAAC;;AAGb,QAAA,IAAI,WAAW;YAAE,OAAU,GAAG,GAAI,GAAA,GAAA,WAAa,CAAC;AAEhD,QAAA,OAAO,GAAG,CAAC;KACd,CAAA;AAED;;;;;AAKG;AACI,IAAA,SAAA,CAAA,8BAA8B,GAArC,UAAsC,QAA8B,EAAE,WAAmB,EAAA;AACrF,QAAA,QAAQ,CAAC,sBAAsB,GAAG,SAAS,CAAC,4BAA4B,CAAC,QAAQ,CAAC,sBAAsB,EAAE,WAAW,CAAC,CAAC;;AAEvH,QAAA,QAAQ,CAAC,cAAc,GAAG,SAAS,CAAC,4BAA4B,CAC5D,QAAQ,CAAC,cAAc,EAAE,WAAW,EAAE,SAAS,CAAC,kCAAkC,CACrF,CAAC;QAEF,IAAI,QAAQ,CAAC,oBAAoB,EAAE;AAC/B,YAAA,QAAQ,CAAC,oBAAoB,GAAG,SAAS,CAAC,4BAA4B,CAAC,QAAQ,CAAC,oBAAoB,EAAE,WAAW,CAAC,CAAC;AACtH,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC;KACnB,CAAA;IACL,OAAC,SAAA,CAAA;AAAD,CAAC,EAAA;;;;"}