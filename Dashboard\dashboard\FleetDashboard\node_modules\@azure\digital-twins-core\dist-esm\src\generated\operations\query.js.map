{"version": 3, "file": "query.js", "sourceRoot": "", "sources": ["../../../../src/generated/operations/query.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,OAAO,KAAK,QAAQ,MAAM,kBAAkB,CAAC;AAC7C,OAAO,KAAK,OAAO,MAAM,mBAAmB,CAAC;AAC7C,OAAO,KAAK,UAAU,MAAM,sBAAsB,CAAC;AAQnD;;GAEG;AACH,MAAM,OAAO,KAAK;IAGhB;;;OAGG;IACH,YAAY,MAA4B;QACtC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;;;;;;;;;;OAWG;IACH,UAAU,CACR,kBAAsC,EACtC,OAAuC;QAEvC,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,kBAAkB,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjD,uBAAuB,CACY,CAAC;IACxC,CAAC;CACF;AACD,2BAA2B;AAE3B,MAAM,UAAU,GAAG,IAAI,QAAQ,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;AAEvE,MAAM,uBAAuB,GAA2B;IACtD,IAAI,EAAE,QAAQ;IACd,UAAU,EAAE,MAAM;IAClB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,WAAW;YAC/B,aAAa,EAAE,OAAO,CAAC,sBAAsB;SAC9C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,WAAW,EAAE,UAAU,CAAC,kBAAkB;IAC1C,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC;IACjC,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,eAAe;KAC3B;IACD,SAAS,EAAE,MAAM;IACjB,UAAU;CACX,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreHttp from \"@azure/core-http\";\nimport * as Mappers from \"../models/mappers\";\nimport * as Parameters from \"../models/parameters\";\nimport { AzureDigitalTwinsAPI } from \"../azureDigitalTwinsAPI\";\nimport {\n  QuerySpecification,\n  QueryQueryTwinsOptionalParams,\n  QueryQueryTwinsResponse\n} from \"../models\";\n\n/**\n * Class representing a Query.\n */\nexport class Query {\n  private readonly client: AzureDigitalTwinsAPI;\n\n  /**\n   * Initialize a new instance of the class Query class.\n   * @param client Reference to the service client\n   */\n  constructor(client: AzureDigitalTwinsAPI) {\n    this.client = client;\n  }\n\n  /**\n   * Executes a query that allows traversing relationships and filtering by property values.\n   * Status codes:\n   * * 200 OK\n   * * 400 Bad Request\n   *   * BadRequest - The continuation token is invalid.\n   *   * SqlQueryError - The query contains some errors.\n   * * 429 Too Many Requests\n   *   * QuotaReachedError - The maximum query rate limit has been reached.\n   * @param querySpecification The query specification to execute.\n   * @param options The options parameters.\n   */\n  queryTwins(\n    querySpecification: QuerySpecification,\n    options?: QueryQueryTwinsOptionalParams\n  ): Promise<QueryQueryTwinsResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { querySpecification, options: operationOptions },\n      queryTwinsOperationSpec\n    ) as Promise<QueryQueryTwinsResponse>;\n  }\n}\n// Operation Specifications\n\nconst serializer = new coreHttp.Serializer(Mappers, /* isXml */ false);\n\nconst queryTwinsOperationSpec: coreHttp.OperationSpec = {\n  path: \"/query\",\n  httpMethod: \"POST\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.QueryResult,\n      headersMapper: Mappers.QueryQueryTwinsHeaders\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.querySpecification,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.maxItemsPerPage\n  ],\n  mediaType: \"json\",\n  serializer\n};\n"]}