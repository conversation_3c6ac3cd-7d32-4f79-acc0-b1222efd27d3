{"version": 3, "file": "deviceCodeCredential.js", "sourceRoot": "", "sources": ["../../../src/credentials/deviceCodeCredential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EACL,yBAAyB,EACzB,iCAAiC,GAClC,MAAM,uBAAuB,CAAC;AAG/B,OAAO,EAAE,cAAc,EAAE,MAAM,kCAAkC,CAAC;AAElE,OAAO,EAAE,gBAAgB,EAAE,MAAM,iBAAiB,CAAC;AACnD,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,MAAM,MAAM,GAAG,gBAAgB,CAAC,sBAAsB,CAAC,CAAC;AAExD;;;GAGG;AACH,MAAM,UAAU,+BAA+B,CAAC,cAA8B;IAC5E,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AACtC,CAAC;AAED;;;GAGG;AACH,MAAM,OAAO,oBAAoB;IAM/B;;;;;;;;;;;;;;;;;;;OAmBG;IACH,YAAY,OAAqC;QAC/C,IAAI,CAAC,QAAQ,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,CAAC;QAClC,IAAI,CAAC,4BAA4B,GAAG,iCAAiC,CACnE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,0BAA0B,CACpC,CAAC;QACF,IAAI,CAAC,QAAQ,GAAG,IAAI,cAAc,iCAC7B,OAAO,KACV,MAAM,EACN,kBAAkB,EAAE,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,kBAAkB,KAAI,+BAA+B,EAClF,sBAAsB,EAAE,OAAO,IAAI,EAAE,IACrC,CAAC;QACH,IAAI,CAAC,8BAA8B,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,8BAA8B,CAAC;IAChF,CAAC;IAED;;;;;;;;;;;OAWG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAyB,EAAE,UAA2B,EAAE;QACrE,OAAO,aAAa,CAAC,QAAQ,CAC3B,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,WAAW,EACnC,OAAO,EACP,KAAK,EAAE,UAAU,EAAE,EAAE;YACnB,UAAU,CAAC,QAAQ,GAAG,yBAAyB,CAC7C,IAAI,CAAC,QAAQ,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,CAClC,CAAC;YAEF,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,kCACpC,UAAU,KACb,8BAA8B,EAAE,IAAI,CAAC,8BAA8B,IACnE,CAAC;QACL,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,YAAY,CAChB,MAAyB,EACzB,UAA2B,EAAE;QAE7B,OAAO,aAAa,CAAC,QAAQ,CAC3B,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,eAAe,EACvC,OAAO,EACP,KAAK,EAAE,UAAU,EAAE,EAAE;YACnB,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YAC9D,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YACtD,OAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;QAC1C,CAAC,CACF,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport {\n  processMultiTenantRequest,\n  resolveAddionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { DeviceCodeCredentialOptions, DeviceCodeInfo } from \"./deviceCodeCredentialOptions\";\nimport { AuthenticationRecord } from \"../msal/types\";\nimport { MsalDeviceCode } from \"../msal/nodeFlows/msalDeviceCode\";\nimport { MsalFlow } from \"../msal/flows\";\nimport { credentialLogger } from \"../util/logging\";\nimport { ensureScopes } from \"../util/scopeUtils\";\nimport { tracingClient } from \"../util/tracing\";\n\nconst logger = credentialLogger(\"DeviceCodeCredential\");\n\n/**\n * Method that logs the user code from the DeviceCodeCredential.\n * @param deviceCodeInfo - The device code.\n */\nexport function defaultDeviceCodePromptCallback(deviceCodeInfo: DeviceCodeInfo): void {\n  console.log(deviceCodeInfo.message);\n}\n\n/**\n * Enables authentication to Azure Active Directory using a device code\n * that the user can enter into https://microsoft.com/devicelogin.\n */\nexport class DeviceCodeCredential implements TokenCredential {\n  private tenantId?: string;\n  private additionallyAllowedTenantIds: string[];\n  private msalFlow: MsalFlow;\n  private disableAutomaticAuthentication?: boolean;\n\n  /**\n   * Creates an instance of DeviceCodeCredential with the details needed\n   * to initiate the device code authorization flow with Azure Active Directory.\n   *\n   * A message will be logged, giving users a code that they can use to authenticate once they go to https://microsoft.com/devicelogin\n   *\n   * Developers can configure how this message is shown by passing a custom `userPromptCallback`:\n   *\n   * ```js\n   * const credential = new DeviceCodeCredential({\n   *   tenantId: env.AZURE_TENANT_ID,\n   *   clientId: env.AZURE_CLIENT_ID,\n   *   userPromptCallback: (info) => {\n   *     console.log(\"CUSTOMIZED PROMPT CALLBACK\", info.message);\n   *   }\n   * });\n   * ```\n   *\n   * @param options - Options for configuring the client which makes the authentication requests.\n   */\n  constructor(options?: DeviceCodeCredentialOptions) {\n    this.tenantId = options?.tenantId;\n    this.additionallyAllowedTenantIds = resolveAddionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants\n    );\n    this.msalFlow = new MsalDeviceCode({\n      ...options,\n      logger,\n      userPromptCallback: options?.userPromptCallback || defaultDeviceCodePromptCallback,\n      tokenCredentialOptions: options || {},\n    });\n    this.disableAutomaticAuthentication = options?.disableAutomaticAuthentication;\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * If the user provided the option `disableAutomaticAuthentication`,\n   * once the token can't be retrieved silently,\n   * this method won't attempt to request user interaction to retrieve the token.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  async getToken(scopes: string | string[], options: GetTokenOptions = {}): Promise<AccessToken> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.getToken`,\n      options,\n      async (newOptions) => {\n        newOptions.tenantId = processMultiTenantRequest(\n          this.tenantId,\n          newOptions,\n          this.additionallyAllowedTenantIds\n        );\n\n        const arrayScopes = ensureScopes(scopes);\n        return this.msalFlow.getToken(arrayScopes, {\n          ...newOptions,\n          disableAutomaticAuthentication: this.disableAutomaticAuthentication,\n        });\n      }\n    );\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * If the token can't be retrieved silently, this method will require user interaction to retrieve the token.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                  TokenCredential implementation might make.\n   */\n  async authenticate(\n    scopes: string | string[],\n    options: GetTokenOptions = {}\n  ): Promise<AuthenticationRecord | undefined> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.authenticate`,\n      options,\n      async (newOptions) => {\n        const arrayScopes = Array.isArray(scopes) ? scopes : [scopes];\n        await this.msalFlow.getToken(arrayScopes, newOptions);\n        return this.msalFlow.getActiveAccount();\n      }\n    );\n  }\n}\n"]}