{"version": 3, "file": "serviceClientCredentials.js", "sourceRoot": "", "sources": ["../../../src/credentials/serviceClientCredentials.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { WebResourceLike } from \"../webResource\";\n\n/**\n * Represents an object or class with a `signRequest` method which will sign outgoing requests (for example, by setting the `Authorization` header).\n */\nexport interface ServiceClientCredentials {\n  /**\n   * Signs a request with the Authentication header.\n   *\n   * @param webResource - The WebResourceLike/request to be signed.\n   * @returns The signed request object;\n   */\n  signRequest(webResource: WebResourceLike): Promise<WebResourceLike>;\n}\n"]}