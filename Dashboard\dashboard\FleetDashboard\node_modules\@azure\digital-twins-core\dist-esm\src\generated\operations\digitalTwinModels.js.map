{"version": 3, "file": "digitalTwinModels.js", "sourceRoot": "", "sources": ["../../../../src/generated/operations/digitalTwinModels.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,OAAO,KAAK,QAAQ,MAAM,kBAAkB,CAAC;AAC7C,OAAO,KAAK,OAAO,MAAM,mBAAmB,CAAC;AAC7C,OAAO,KAAK,UAAU,MAAM,sBAAsB,CAAC;AAenD;;GAEG;AACH,MAAM,OAAO,iBAAiB;IAG5B;;;OAGG;IACH,YAAY,MAA4B;QACtC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,GAAG,CACD,OAA4C;QAE5C,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAC7B,gBAAgB,CACwB,CAAC;IAC7C,CAAC;IAED;;;;;;;;;;OAUG;IACH,IAAI,CACF,OAA6C;QAE7C,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAC7B,iBAAiB,CACwB,CAAC;IAC9C,CAAC;IAED;;;;;;;;;;;OAWG;IACH,OAAO,CACL,EAAU,EACV,OAAgD;QAEhD,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjC,oBAAoB,CACwB,CAAC;IACjD,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,MAAM,CACJ,EAAU,EACV,WAAkB,EAClB,OAA+C;QAE/C,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,WAAW,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAC9C,mBAAmB,CACc,CAAC;IACtC,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,MAAM,CACJ,EAAU,EACV,OAA+C;QAE/C,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjC,mBAAmB,CACc,CAAC;IACtC,CAAC;IAED;;;;OAIG;IACH,QAAQ,CACN,QAAgB,EAChB,OAAiD;QAEjD,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,QAAQ,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACvC,qBAAqB,CACwB,CAAC;IAClD,CAAC;CACF;AACD,2BAA2B;AAE3B,MAAM,UAAU,GAAG,IAAI,QAAQ,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;AAEvE,MAAM,gBAAgB,GAA2B;IAC/C,IAAI,EAAE,SAAS;IACf,UAAU,EAAE,MAAM;IAClB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE;gBACV,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,uBAAuB,EAAE;qBAChE;iBACF;aACF;SACF;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,WAAW,EAAE,UAAU,CAAC,MAAM;IAC9B,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC;IACjC,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,UAAU;KACtB;IACD,SAAS,EAAE,MAAM;IACjB,UAAU;CACX,CAAC;AACF,MAAM,iBAAiB,GAA2B;IAChD,IAAI,EAAE,SAAS;IACf,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,oCAAoC;SACzD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,sBAAsB;KAClC;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC;IACjC,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,eAAe;KAC3B;IACD,UAAU;CACX,CAAC;AACF,MAAM,oBAAoB,GAA2B;IACnD,IAAI,EAAE,cAAc;IACpB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,qBAAqB;SAC1C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,sBAAsB,CAAC;IAC3E,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC;IAChD,gBAAgB,EAAE,CAAC,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,UAAU,CAAC;IACjE,UAAU;CACX,CAAC;AACF,MAAM,mBAAmB,GAA2B;IAClD,IAAI,EAAE,cAAc;IACpB,UAAU,EAAE,OAAO;IACnB,SAAS,EAAE;QACT,GAAG,EAAE,EAAE;QACP,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,WAAW,EAAE,UAAU,CAAC,WAAW;IACnC,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC;IAChD,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,YAAY;KACxB;IACD,SAAS,EAAE,MAAM;IACjB,UAAU;CACX,CAAC;AACF,MAAM,mBAAmB,GAA2B;IAClD,IAAI,EAAE,cAAc;IACpB,UAAU,EAAE,QAAQ;IACpB,SAAS,EAAE;QACT,GAAG,EAAE,EAAE;QACP,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC;IAChD,gBAAgB,EAAE,CAAC,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,UAAU,CAAC;IACjE,UAAU;CACX,CAAC;AACF,MAAM,qBAAqB,GAA2B;IACpD,IAAI,EAAE,YAAY;IAClB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,oCAAoC;SACzD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,sBAAsB;KAClC;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,QAAQ,CAAC;IACtD,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,eAAe;KAC3B;IACD,UAAU;CACX,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreHttp from \"@azure/core-http\";\nimport * as Mappers from \"../models/mappers\";\nimport * as Parameters from \"../models/parameters\";\nimport { AzureDigitalTwinsAPI } from \"../azureDigitalTwinsAPI\";\nimport {\n  DigitalTwinModelsAddOptionalParams,\n  DigitalTwinModelsAddResponse,\n  DigitalTwinModelsListOptionalParams,\n  DigitalTwinModelsListResponse,\n  DigitalTwinModelsGetByIdOptionalParams,\n  DigitalTwinModelsGetByIdResponse,\n  DigitalTwinModelsUpdateOptionalParams,\n  DigitalTwinModelsDeleteOptionalParams,\n  DigitalTwinModelsListNextOptionalParams,\n  DigitalTwinModelsListNextResponse\n} from \"../models\";\n\n/**\n * Class representing a DigitalTwinModels.\n */\nexport class DigitalTwinModels {\n  private readonly client: AzureDigitalTwinsAPI;\n\n  /**\n   * Initialize a new instance of the class DigitalTwinModels class.\n   * @param client Reference to the service client\n   */\n  constructor(client: AzureDigitalTwinsAPI) {\n    this.client = client;\n  }\n\n  /**\n   * Uploads one or more models. When any error occurs, no models are uploaded.\n   * Status codes:\n   * * 201 Created\n   * * 400 Bad Request\n   *   * DTDLParserError - The models provided are not valid DTDL.\n   *   * InvalidArgument - The model id is invalid.\n   *   * LimitExceeded - The maximum number of model ids allowed in 'dependenciesFor' has been reached.\n   *   * ModelVersionNotSupported - The version of DTDL used is not supported.\n   * * 409 Conflict\n   *   * ModelAlreadyExists - The model provided already exists.\n   * @param options The options parameters.\n   */\n  add(\n    options?: DigitalTwinModelsAddOptionalParams\n  ): Promise<DigitalTwinModelsAddResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { options: operationOptions },\n      addOperationSpec\n    ) as Promise<DigitalTwinModelsAddResponse>;\n  }\n\n  /**\n   * Retrieves model metadata and, optionally, model definitions.\n   * Status codes:\n   * * 200 OK\n   * * 400 Bad Request\n   *   * InvalidArgument - The model id is invalid.\n   *   * LimitExceeded - The maximum number of model ids allowed in 'dependenciesFor' has been reached.\n   * * 404 Not Found\n   *   * ModelNotFound - The model was not found.\n   * @param options The options parameters.\n   */\n  list(\n    options?: DigitalTwinModelsListOptionalParams\n  ): Promise<DigitalTwinModelsListResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { options: operationOptions },\n      listOperationSpec\n    ) as Promise<DigitalTwinModelsListResponse>;\n  }\n\n  /**\n   * Retrieves model metadata and optionally the model definition.\n   * Status codes:\n   * * 200 OK\n   * * 400 Bad Request\n   *   * InvalidArgument - The model id is invalid.\n   *   * MissingArgument - The model id was not provided.\n   * * 404 Not Found\n   *   * ModelNotFound - The model was not found.\n   * @param id The id for the model. The id is globally unique and case sensitive.\n   * @param options The options parameters.\n   */\n  getById(\n    id: string,\n    options?: DigitalTwinModelsGetByIdOptionalParams\n  ): Promise<DigitalTwinModelsGetByIdResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, options: operationOptions },\n      getByIdOperationSpec\n    ) as Promise<DigitalTwinModelsGetByIdResponse>;\n  }\n\n  /**\n   * Updates the metadata for a model.\n   * Status codes:\n   * * 204 No Content\n   * * 400 Bad Request\n   *   * InvalidArgument - The model id is invalid.\n   *   * JsonPatchInvalid - The JSON Patch provided is invalid.\n   *   * MissingArgument - The model id was not provided.\n   * * 404 Not Found\n   *   * ModelNotFound - The model was not found.\n   * * 409 Conflict\n   *   * ModelReferencesNotDecommissioned - The model refers to models that are not decommissioned.\n   * @param id The id for the model. The id is globally unique and case sensitive.\n   * @param updateModel An update specification described by JSON Patch. Only the decommissioned property\n   *                    can be replaced.\n   * @param options The options parameters.\n   */\n  update(\n    id: string,\n    updateModel: any[],\n    options?: DigitalTwinModelsUpdateOptionalParams\n  ): Promise<coreHttp.RestResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, updateModel, options: operationOptions },\n      updateOperationSpec\n    ) as Promise<coreHttp.RestResponse>;\n  }\n\n  /**\n   * Deletes a model. A model can only be deleted if no other models reference it.\n   * Status codes:\n   * * 204 No Content\n   * * 400 Bad Request\n   *   * InvalidArgument - The model id is invalid.\n   *   * MissingArgument - The model id was not provided.\n   * * 404 Not Found\n   *   * ModelNotFound - The model was not found.\n   * * 409 Conflict\n   *   * ModelReferencesNotDeleted - The model refers to models that are not deleted.\n   * @param id The id for the model. The id is globally unique and case sensitive.\n   * @param options The options parameters.\n   */\n  delete(\n    id: string,\n    options?: DigitalTwinModelsDeleteOptionalParams\n  ): Promise<coreHttp.RestResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, options: operationOptions },\n      deleteOperationSpec\n    ) as Promise<coreHttp.RestResponse>;\n  }\n\n  /**\n   * ListNext\n   * @param nextLink The nextLink from the previous successful call to the List method.\n   * @param options The options parameters.\n   */\n  listNext(\n    nextLink: string,\n    options?: DigitalTwinModelsListNextOptionalParams\n  ): Promise<DigitalTwinModelsListNextResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { nextLink, options: operationOptions },\n      listNextOperationSpec\n    ) as Promise<DigitalTwinModelsListNextResponse>;\n  }\n}\n// Operation Specifications\n\nconst serializer = new coreHttp.Serializer(Mappers, /* isXml */ false);\n\nconst addOperationSpec: coreHttp.OperationSpec = {\n  path: \"/models\",\n  httpMethod: \"POST\",\n  responses: {\n    201: {\n      bodyMapper: {\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: { name: \"Composite\", className: \"DigitalTwinsModelData\" }\n          }\n        }\n      }\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.models,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.traceparent,\n    Parameters.tracestate\n  ],\n  mediaType: \"json\",\n  serializer\n};\nconst listOperationSpec: coreHttp.OperationSpec = {\n  path: \"/models\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.PagedDigitalTwinsModelDataCollection\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [\n    Parameters.apiVersion,\n    Parameters.dependenciesFor,\n    Parameters.includeModelDefinition\n  ],\n  urlParameters: [Parameters.$host],\n  headerParameters: [\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.maxItemsPerPage\n  ],\n  serializer\n};\nconst getByIdOperationSpec: coreHttp.OperationSpec = {\n  path: \"/models/{id}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.DigitalTwinsModelData\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion, Parameters.includeModelDefinition],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\nconst updateOperationSpec: coreHttp.OperationSpec = {\n  path: \"/models/{id}\",\n  httpMethod: \"PATCH\",\n  responses: {\n    204: {},\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.updateModel,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.contentType1\n  ],\n  mediaType: \"json\",\n  serializer\n};\nconst deleteOperationSpec: coreHttp.OperationSpec = {\n  path: \"/models/{id}\",\n  httpMethod: \"DELETE\",\n  responses: {\n    204: {},\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\nconst listNextOperationSpec: coreHttp.OperationSpec = {\n  path: \"{nextLink}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.PagedDigitalTwinsModelDataCollection\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [\n    Parameters.apiVersion,\n    Parameters.dependenciesFor,\n    Parameters.includeModelDefinition\n  ],\n  urlParameters: [Parameters.$host, Parameters.nextLink],\n  headerParameters: [\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.maxItemsPerPage\n  ],\n  serializer\n};\n"]}