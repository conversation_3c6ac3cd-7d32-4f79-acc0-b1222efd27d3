{"version": 3, "file": "interactiveCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/interactiveCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AuthenticationRecord } from \"../msal/types\";\nimport { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions\";\n\n/**\n * Common constructor options for the Identity credentials that requires user interaction.\n */\nexport interface InteractiveCredentialOptions extends MultiTenantTokenCredentialOptions {\n  /**\n   * Result of a previous authentication that can be used to retrieve the cached credentials of each individual account.\n   * This is necessary to provide in case the application wants to work with more than one account per\n   * Client ID and Tenant ID pair.\n   *\n   * This record can be retrieved by calling to the credential's `authenticate()` method, as follows:\n   *\n   *     const authenticationRecord = await credential.authenticate();\n   *\n   */\n  authenticationRecord?: AuthenticationRecord;\n\n  /**\n   * Makes getToken throw if a manual authentication is necessary.\n   * Developers will need to call to `authenticate()` to control when to manually authenticate.\n   */\n  disableAutomaticAuthentication?: boolean;\n}\n"]}