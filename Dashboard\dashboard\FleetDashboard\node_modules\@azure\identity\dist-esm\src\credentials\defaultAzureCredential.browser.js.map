{"version": 3, "file": "defaultAzureCredential.browser.js", "sourceRoot": "", "sources": ["../../../src/credentials/defaultAzureCredential.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAEhE,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAC;AAGlE,MAAM,wBAAwB,GAAG,IAAI,KAAK,CACxC,mGAAmG,CACpG,CAAC;AACF,MAAM,MAAM,GAAG,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;AAE1D;;;;;GAKG;AACH,MAAM,OAAO,sBAAuB,SAAQ,sBAAsB;IAChE;;;;OAIG;IACH,YAAY,uBAAgD;QAC1D,KAAK,EAAE,CAAC;QACR,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,wBAAwB,CAAC,CAAC,CAAC;QACvD,MAAM,wBAAwB,CAAC;IACjC,CAAC;IAEM,QAAQ;QACb,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,wBAAwB,CAAC,CAAC,CAAC;QAChE,MAAM,wBAAwB,CAAC;IACjC,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { credentialLogger, formatError } from \"../util/logging\";\nimport { AccessToken } from \"@azure/core-auth\";\nimport { ChainedTokenCredential } from \"./chainedTokenCredential\";\nimport { TokenCredentialOptions } from \"../tokenCredentialOptions\";\n\nconst BrowserNotSupportedError = new Error(\n  \"DefaultAzureCredential is not supported in the browser. Use InteractiveBrowserCredential instead.\"\n);\nconst logger = credentialLogger(\"DefaultAzureCredential\");\n\n/**\n * Provides a default {@link ChainedTokenCredential} configuration for\n * applications that will be deployed to Azure.\n *\n * Only available in Node.js.\n */\nexport class DefaultAzureCredential extends ChainedTokenCredential {\n  /**\n   * Creates an instance of the DefaultAzureCredential class.\n   *\n   * @param options - Options for configuring the client which makes the authentication request.\n   */\n  constructor(_tokenCredentialOptions?: TokenCredentialOptions) {\n    super();\n    logger.info(formatError(\"\", BrowserNotSupportedError));\n    throw BrowserNotSupportedError;\n  }\n\n  public getToken(): Promise<AccessToken> {\n    logger.getToken.info(formatError(\"\", BrowserNotSupportedError));\n    throw BrowserNotSupportedError;\n  }\n}\n"]}