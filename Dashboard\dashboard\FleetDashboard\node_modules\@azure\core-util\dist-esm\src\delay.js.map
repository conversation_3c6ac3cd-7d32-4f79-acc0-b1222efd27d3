{"version": 3, "file": "delay.js", "sourceRoot": "", "sources": ["../../src/delay.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAC;AAElE,MAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAgBtD;;;;;GAKG;AACH,MAAM,UAAU,KAAK,CAAC,QAAgB,EAAE,OAAsB;IAC5D,IAAI,KAAoC,CAAC;IACzC,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,CAAC;IACrD,OAAO,sBAAsB,CAC3B,CAAC,OAAO,EAAE,EAAE;QACV,KAAK,GAAG,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IACxC,CAAC,EACD;QACE,kBAAkB,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC;QAC7C,WAAW;QACX,aAAa,EAAE,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,oBAAoB;KACrD,CACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { createAbortablePromise } from \"./createAbortablePromise\";\n\nconst StandardAbortMessage = \"The delay was aborted.\";\n\n/**\n * Options for support abort functionality for the delay method\n */\nexport interface DelayOptions {\n  /**\n   * The abortSignal associated with containing operation.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * The abort error message associated with containing operation.\n   */\n  abortErrorMsg?: string;\n}\n\n/**\n * A wrapper for setTimeout that resolves a promise after timeInMs milliseconds.\n * @param timeInMs - The number of milliseconds to be delayed.\n * @param options - The options for delay - currently abort options\n * @returns Promise that is resolved after timeInMs\n */\nexport function delay(timeInMs: number, options?: DelayOptions): Promise<void> {\n  let token: ReturnType<typeof setTimeout>;\n  const { abortSignal, abortErrorMsg } = options ?? {};\n  return createAbortablePromise(\n    (resolve) => {\n      token = setTimeout(resolve, timeInMs);\n    },\n    {\n      cleanupBeforeAbort: () => clearTimeout(token),\n      abortSignal,\n      abortErrorMsg: abortErrorMsg ?? StandardAbortMessage,\n    }\n  );\n}\n"]}