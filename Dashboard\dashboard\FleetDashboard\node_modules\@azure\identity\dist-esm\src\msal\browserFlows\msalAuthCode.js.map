{"version": 3, "file": "msalAuthCode.js", "sourceRoot": "", "sources": ["../../../../src/msal/browserFlows/msalAuthCode.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,KAAK,WAAW,MAAM,qBAAqB,CAAC;AACnD,OAAO,EAAE,WAAW,EAA0B,MAAM,qBAAqB,CAAC;AAC1E,OAAO,EAAE,qBAAqB,EAAE,YAAY,EAAE,YAAY,EAAE,eAAe,EAAE,MAAM,UAAU,CAAC;AAG9F,OAAO,EAAE,2BAA2B,EAAE,MAAM,cAAc,CAAC;AAE3D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,uCAAuC;AACvC,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AAExC;;;;GAIG;AACH,MAAM,OAAO,YAAa,SAAQ,WAAW;IAI3C;;;;;OAKG;IACH,YAAY,OAA+B;QACzC,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QAEnC,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG;YACtB,aAAa,EAAE,gBAAgB;YAC/B,sBAAsB,EAAE,IAAI,EAAE,0DAA0D;SACzF,CAAC;QACF,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG;YACvB,aAAa,EAAE;gBACb,cAAc,EAAE,qBAAqB,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC;gBAC7D,QAAQ,EAAE,eAAe,CAAC,WAAW,EAAE,CAAC;aACzC;SACF,CAAC;QAEF,kCAAkC;QAClC,IAAI,CAAC,GAAG,GAAG,IAAI,WAAW,CAAC,uBAAuB,CAChD,IAAI,CAAC,UAAuC,CAC7C,CAAC;QACF,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;SACvD;IACH,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,mBAAmB,CAC/B,MAAyC;QAEzC,IAAI;YACF,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;gBAC/D,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC1C,OAAO,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;aACpD;YAED,6FAA6F;YAC7F,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,GAAI,CAAC,gBAAgB,EAAE,CAAC;YACzD,IAAI,aAAa,EAAE;gBACjB,OAAO,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;aACnD;YAED,kGAAkG;YAClG,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC;YAC3C,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;gBACvB,uFAAuF;gBACvF,kFAAkF;gBAClF,uEAAuE;gBACvE,uCAAuC;gBACvC,IAAI,CAAC,MAAM,CAAC,IAAI,CACd;;;;gKAIsJ,CACvJ,CAAC;gBACF,yGAAyG;gBACzG,4FAA4F;gBAC5F,8FAA8F;gBAC9F,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;oBACpB,kBAAkB,EAAE,GAAG,EAAE,CAAC,KAAK;iBAChC,CAAC,CAAC;gBACH,OAAO;aACR;YAED,+EAA+E;YAC/E,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;gBACzB,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC5B,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;gBACnC,OAAO,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;aAC7C;YAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;SAC1D;QAAC,OAAO,CAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;SACxE;QACD,OAAO;IACT,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,cAAc;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAC7B,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC,IAAI,SAAS,CAClE,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,KAAK,CAAC,SAA4B,EAAE;QAC/C,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QAC9D,MAAM,YAAY,GAAgC;YAChD,MAAM,EAAE,WAAW;YACnB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;QACF,QAAQ,IAAI,CAAC,UAAU,EAAE;YACvB,KAAK,UAAU,CAAC,CAAC;gBACf,MAAM,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;gBAC3C,OAAO;aACR;YACD,KAAK,OAAO;gBACV,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;SAC5E;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,gBAAgB;QAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QAC5C,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO;SACR;QACD,OAAO,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,cAAc,CACzB,MAAgB,EAChB,OAAuC;QAEvC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9C,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,2BAA2B,CAAC;gBACpC,MAAM;gBACN,eAAe,EAAE,OAAO;gBACxB,OAAO,EACL,sFAAsF;aACzF,CAAC,CAAC;SACJ;QAED,MAAM,UAAU,GAA8B;YAC5C,SAAS,EAAE,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,KAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAU;YAChE,aAAa,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa;YACrC,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM;YACvB,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC;YAC9B,YAAY,EAAE,KAAK;YACnB,MAAM;SACP,CAAC;QAEF,IAAI;YACF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACzD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YAC/D,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;SAC3D;QAAC,OAAO,GAAQ,EAAE;YACjB,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;SAC9C;IACH,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,UAAU,CACxB,MAAgB,EAChB,OAAuC;QAEvC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9C,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,2BAA2B,CAAC;gBACpC,MAAM;gBACN,eAAe,EAAE,OAAO;gBACxB,OAAO,EACL,sFAAsF;aACzF,CAAC,CAAC;SACJ;QAED,MAAM,UAAU,GAAgC;YAC9C,SAAS,EAAE,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,KAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAU;YAChE,aAAa,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa;YACrC,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM;YACvB,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC;YAC9B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,MAAM;SACP,CAAC;QAEF,QAAQ,IAAI,CAAC,UAAU,EAAE;YACvB,KAAK,UAAU;gBACb,gCAAgC;gBAChC,8DAA8D;gBAC9D,kDAAkD;gBAClD,MAAM,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;gBAChD,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,kBAAkB,EAAE,CAAC,EAAE,CAAC;YAC9C,KAAK,OAAO;gBACV,OAAO,IAAI,CAAC,YAAY,CACtB,MAAM,EACN,IAAI,CAAC,QAAQ,EACb,MAAM,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAC7C,CAAC;SACL;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as msal<PERSON>rowser from \"@azure/msal-browser\";\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, MsalBrowserFlowOptions } from \"./msalBrowserCommon\";\nimport { defaultLoggerCallback, msalToPublic, publicToMsal, getMSALLogLevel } from \"../utils\";\nimport { AccessToken } from \"@azure/core-auth\";\nimport { AuthenticationRecord } from \"../types\";\nimport { AuthenticationRequiredError } from \"../../errors\";\nimport { CredentialFlowGetTokenOptions } from \"../credentials\";\nimport { getLogLevel } from \"@azure/logger\";\n\n// We keep a copy of the redirect hash.\nconst redirectHash = self.location.hash;\n\n/**\n * Uses MSAL Browser 2.X for browser authentication,\n * which uses the [Auth Code Flow](https://docs.microsoft.com/en-us/azure/active-directory/develop/v2-oauth2-auth-code-flow).\n * @internal\n */\nexport class MSALAuthCode extends MsalBrowser {\n  protected app: msalBrowser.PublicClientApplication;\n  private loginHint?: string;\n\n  /**\n   * Sets up an MSAL object based on the given parameters.\n   * MSAL with Auth Code allows sending a previously obtained `authenticationRecord` through the optional parameters,\n   * which is set to be the active account.\n   * @param options - Parameters necessary and otherwise used to create the MSAL object.\n   */\n  constructor(options: MsalBrowserFlowOptions) {\n    super(options);\n    this.loginHint = options.loginHint;\n\n    this.msalConfig.cache = {\n      cacheLocation: \"sessionStorage\",\n      storeAuthStateInCookie: true, // Set to true to improve the experience on IE11 and Edge.\n    };\n    this.msalConfig.system = {\n      loggerOptions: {\n        loggerCallback: defaultLoggerCallback(this.logger, \"Browser\"),\n        logLevel: getMSALLogLevel(getLogLevel()),\n      },\n    };\n\n    // Preparing the MSAL application.\n    this.app = new msalBrowser.PublicClientApplication(\n      this.msalConfig as msalBrowser.Configuration\n    );\n    if (this.account) {\n      this.app.setActiveAccount(publicToMsal(this.account));\n    }\n  }\n\n  /**\n   * Loads the account based on the result of the authentication.\n   * If no result was received, tries to load the account from the cache.\n   * @param result - Result object received from MSAL.\n   */\n  private async handleBrowserResult(\n    result?: msalBrowser.AuthenticationResult\n  ): Promise<AuthenticationRecord | undefined> {\n    try {\n      if (result && result.account) {\n        this.logger.info(`MSAL Browser V2 authentication successful.`);\n        this.app.setActiveAccount(result.account);\n        return msalToPublic(this.clientId, result.account);\n      }\n\n      // If by this point we happen to have an active account, we should stop trying to parse this.\n      const activeAccount = await this.app!.getActiveAccount();\n      if (activeAccount) {\n        return msalToPublic(this.clientId, activeAccount);\n      }\n\n      // If we don't have an active account, we try to activate it from all the already loaded accounts.\n      const accounts = this.app.getAllAccounts();\n      if (accounts.length > 1) {\n        // If there's more than one account in memory, we force the user to authenticate again.\n        // At this point we can't identify which account should this credential work with,\n        // since at this point the user won't have provided enough information.\n        // We log a message in case that helps.\n        this.logger.info(\n          `More than one account was found authenticated for this Client ID and Tenant ID.\nHowever, no \"authenticationRecord\" has been provided for this credential,\ntherefore we're unable to pick between these accounts.\nA new login attempt will be requested, to ensure the correct account is picked.\nTo work with multiple accounts for the same Client ID and Tenant ID, please provide an \"authenticationRecord\" when initializing \"InteractiveBrowserCredential\".`\n        );\n        // To safely trigger a new login, we're also ensuring the local cache is cleared up for this MSAL object.\n        // However, we want to avoid kicking the user out of their authentication on the Azure side.\n        // We do this by calling to logout while specifying a `onRedirectNavigate` that returns false.\n        await this.app.logout({\n          onRedirectNavigate: () => false,\n        });\n        return;\n      }\n\n      // If there's only one account for this MSAL object, we can safely activate it.\n      if (accounts.length === 1) {\n        const account = accounts[0];\n        this.app.setActiveAccount(account);\n        return msalToPublic(this.clientId, account);\n      }\n\n      this.logger.info(`No accounts were found through MSAL.`);\n    } catch (e: any) {\n      this.logger.info(`Failed to acquire token through MSAL. ${e.message}`);\n    }\n    return;\n  }\n\n  /**\n   * Uses MSAL to handle the redirect.\n   */\n  public async handleRedirect(): Promise<AuthenticationRecord | undefined> {\n    return this.handleBrowserResult(\n      (await this.app.handleRedirectPromise(redirectHash)) || undefined\n    );\n  }\n\n  /**\n   * Uses MSAL to trigger a redirect or a popup login.\n   */\n  public async login(scopes: string | string[] = []): Promise<AuthenticationRecord | undefined> {\n    const arrayScopes = Array.isArray(scopes) ? scopes : [scopes];\n    const loginRequest: msalBrowser.RedirectRequest = {\n      scopes: arrayScopes,\n      loginHint: this.loginHint,\n    };\n    switch (this.loginStyle) {\n      case \"redirect\": {\n        await this.app.loginRedirect(loginRequest);\n        return;\n      }\n      case \"popup\":\n        return this.handleBrowserResult(await this.app.loginPopup(loginRequest));\n    }\n  }\n\n  /**\n   * Uses MSAL to retrieve the active account.\n   */\n  public async getActiveAccount(): Promise<AuthenticationRecord | undefined> {\n    const account = this.app.getActiveAccount();\n    if (!account) {\n      return;\n    }\n    return msalToPublic(this.clientId, account);\n  }\n\n  /**\n   * Attempts to retrieve a token from cache.\n   */\n  public async getTokenSilent(\n    scopes: string[],\n    options?: CredentialFlowGetTokenOptions\n  ): Promise<AccessToken> {\n    const account = await this.getActiveAccount();\n    if (!account) {\n      throw new AuthenticationRequiredError({\n        scopes,\n        getTokenOptions: options,\n        message:\n          \"Silent authentication failed. We couldn't retrieve an active account from the cache.\",\n      });\n    }\n\n    const parameters: msalBrowser.SilentRequest = {\n      authority: options?.authority || this.msalConfig.auth.authority!,\n      correlationId: options?.correlationId,\n      claims: options?.claims,\n      account: publicToMsal(account),\n      forceRefresh: false,\n      scopes,\n    };\n\n    try {\n      this.logger.info(\"Attempting to acquire token silently\");\n      const response = await this.app.acquireTokenSilent(parameters);\n      return this.handleResult(scopes, this.clientId, response);\n    } catch (err: any) {\n      throw this.handleError(scopes, err, options);\n    }\n  }\n\n  /**\n   * Attempts to retrieve the token in the browser.\n   */\n  protected async doGetToken(\n    scopes: string[],\n    options?: CredentialFlowGetTokenOptions\n  ): Promise<AccessToken> {\n    const account = await this.getActiveAccount();\n    if (!account) {\n      throw new AuthenticationRequiredError({\n        scopes,\n        getTokenOptions: options,\n        message:\n          \"Silent authentication failed. We couldn't retrieve an active account from the cache.\",\n      });\n    }\n\n    const parameters: msalBrowser.RedirectRequest = {\n      authority: options?.authority || this.msalConfig.auth.authority!,\n      correlationId: options?.correlationId,\n      claims: options?.claims,\n      account: publicToMsal(account),\n      loginHint: this.loginHint,\n      scopes,\n    };\n\n    switch (this.loginStyle) {\n      case \"redirect\":\n        // This will go out of the page.\n        // Once the InteractiveBrowserCredential is initialized again,\n        // we'll load the MSAL account in the constructor.\n        await this.app.acquireTokenRedirect(parameters);\n        return { token: \"\", expiresOnTimestamp: 0 };\n      case \"popup\":\n        return this.handleResult(\n          scopes,\n          this.clientId,\n          await this.app.acquireTokenPopup(parameters)\n        );\n    }\n  }\n}\n"]}