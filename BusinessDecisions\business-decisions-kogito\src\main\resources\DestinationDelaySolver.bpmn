<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:bpsim="http://www.bpsim.org/schemas/1.0" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:drools="http://www.jboss.org/drools" id="_3-hxYPMEEDutsPrpA80sbw" xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd http://www.jboss.org/drools drools.xsd http://www.bpsim.org/schemas/1.0 bpsim.xsd http://www.omg.org/spec/DD/20100524/DC DC.xsd http://www.omg.org/spec/DD/20100524/DI DI.xsd " exporter="jBPM Process Modeler" exporterVersion="2.0" targetNamespace="http://www.omg.org/bpmn20">
  <bpmn2:itemDefinition id="_modelItem" structureRef="com.ness.process.ProcessModel"/>
  <bpmn2:itemDefinition id="_currentDateTimeItem" structureRef="java.util.Date"/>
  <bpmn2:itemDefinition id="_currentlyDelayedItem" structureRef="Boolean"/>
  <bpmn2:itemDefinition id="__8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_SkippableInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_PriorityInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_CommentInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_DescriptionInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_CreatedByInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_TaskNameInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_GroupIdInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_ContentInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_NotStartedReassignInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_NotCompletedReassignInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_NotStartedNotifyInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_NotCompletedNotifyInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_currentDateInputOutputXItem" structureRef="java.util.Date"/>
  <bpmn2:itemDefinition id="__8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_currentlyDelayedInputOutputXItem" structureRef="Boolean"/>
  <bpmn2:itemDefinition id="__C5C11AB8-34FF-4748-A207-CF3A29B34F83_SkippableInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__C5C11AB8-34FF-4748-A207-CF3A29B34F83_PriorityInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__C5C11AB8-34FF-4748-A207-CF3A29B34F83_CommentInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__C5C11AB8-34FF-4748-A207-CF3A29B34F83_DescriptionInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__C5C11AB8-34FF-4748-A207-CF3A29B34F83_CreatedByInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__C5C11AB8-34FF-4748-A207-CF3A29B34F83_TaskNameInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__C5C11AB8-34FF-4748-A207-CF3A29B34F83_GroupIdInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__C5C11AB8-34FF-4748-A207-CF3A29B34F83_ContentInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__C5C11AB8-34FF-4748-A207-CF3A29B34F83_NotStartedReassignInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__C5C11AB8-34FF-4748-A207-CF3A29B34F83_NotCompletedReassignInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__C5C11AB8-34FF-4748-A207-CF3A29B34F83_NotStartedNotifyInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__C5C11AB8-34FF-4748-A207-CF3A29B34F83_NotCompletedNotifyInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__EDFAACF1-A8F0-41B3-8CF0-27EC07481653_SkippableInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__EDFAACF1-A8F0-41B3-8CF0-27EC07481653_PriorityInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__EDFAACF1-A8F0-41B3-8CF0-27EC07481653_CommentInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__EDFAACF1-A8F0-41B3-8CF0-27EC07481653_DescriptionInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__EDFAACF1-A8F0-41B3-8CF0-27EC07481653_CreatedByInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__EDFAACF1-A8F0-41B3-8CF0-27EC07481653_TaskNameInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__EDFAACF1-A8F0-41B3-8CF0-27EC07481653_GroupIdInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__EDFAACF1-A8F0-41B3-8CF0-27EC07481653_ContentInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__EDFAACF1-A8F0-41B3-8CF0-27EC07481653_NotStartedReassignInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__EDFAACF1-A8F0-41B3-8CF0-27EC07481653_NotCompletedReassignInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__EDFAACF1-A8F0-41B3-8CF0-27EC07481653_NotStartedNotifyInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="__EDFAACF1-A8F0-41B3-8CF0-27EC07481653_NotCompletedNotifyInputXItem" structureRef="Object"/>
  <bpmn2:collaboration id="_80F797B5-E6E9-4B48-ADFD-7102ADB86709" name="Default Collaboration">
    <bpmn2:participant id="_56B1576D-FBA6-4A63-B413-4619691733D6" name="Pool Participant" processRef="DestinationDelaySolver"/>
  </bpmn2:collaboration>
  <bpmn2:process id="DestinationDelaySolver" drools:packageName="com.example" drools:version="1.0" drools:adHoc="false" name="DestinationDelaySolver" isExecutable="true" processType="Public">
    <bpmn2:extensionElements>
      <drools:import name="com.ness.process.ProcessModel"/>
    </bpmn2:extensionElements>
    <bpmn2:property id="model" itemSubjectRef="_modelItem" name="model"/>
    <bpmn2:property id="currentDateTime" itemSubjectRef="_currentDateTimeItem" name="currentDateTime"/>
    <bpmn2:property id="currentlyDelayed" itemSubjectRef="_currentlyDelayedItem" name="currentlyDelayed"/>
    <bpmn2:sequenceFlow id="_8012D40D-BF09-4643-8A9F-EB56938CBDA8" sourceRef="_EDFAACF1-A8F0-41B3-8CF0-27EC07481653" targetRef="_298BA4E0-AB8C-4A64-8736-86B75B736828"/>
    <bpmn2:sequenceFlow id="_6C4303E0-B8ED-4F3E-A42F-95CC9618B69E" sourceRef="_A6A246AE-32C3-42B6-A436-0C8981CCE244" targetRef="_EDFAACF1-A8F0-41B3-8CF0-27EC07481653"/>
    <bpmn2:sequenceFlow id="_B3911253-AF1F-48C8-95EB-0E2259997134" sourceRef="_C5C11AB8-34FF-4748-A207-CF3A29B34F83" targetRef="_298BA4E0-AB8C-4A64-8736-86B75B736828"/>
    <bpmn2:sequenceFlow id="_29789E2E-076A-4C27-A98D-EF60CE23BAA3" sourceRef="_A6A246AE-32C3-42B6-A436-0C8981CCE244" targetRef="_C5C11AB8-34FF-4748-A207-CF3A29B34F83"/>
    <bpmn2:sequenceFlow id="_A81EADB0-35E0-4CAA-BEC1-01751A2B88DF" sourceRef="_0AF5AC9A-99A8-45A1-A418-30F1A7A2A77F" targetRef="_A6A246AE-32C3-42B6-A436-0C8981CCE244"/>
    <bpmn2:sequenceFlow id="_1E4312C6-E5B0-4147-B9E3-B76C25C9C20E" sourceRef="_25A74D50-D927-4551-B877-99ACD5F79673" targetRef="_0AF5AC9A-99A8-45A1-A418-30F1A7A2A77F">
      <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression" language="http://www.java.com/java"><![CDATA[return model.getDelayOccurences() == 3;]]></bpmn2:conditionExpression>
    </bpmn2:sequenceFlow>
    <bpmn2:sequenceFlow id="_EAF0AE85-BC7F-4DE2-8451-C1E802640DE5" sourceRef="_25A74D50-D927-4551-B877-99ACD5F79673" targetRef="_4C7ECFD5-74C5-42CD-A5C4-4C862B3219BF">
      <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression" language="http://www.java.com/java"><![CDATA[return model.getDelayOccurences() < 3;]]></bpmn2:conditionExpression>
    </bpmn2:sequenceFlow>
    <bpmn2:sequenceFlow id="_9C54A668-641C-473F-AF90-2A2342DD24DC" sourceRef="_4B2D32D8-B82A-46B5-A4E4-43C347BB82B6" targetRef="_4C7ECFD5-74C5-42CD-A5C4-4C862B3219BF"/>
    <bpmn2:sequenceFlow id="_93563AD1-25D6-44A2-890D-720E2BEBB8D2" sourceRef="_4C7ECFD5-74C5-42CD-A5C4-4C862B3219BF" targetRef="_8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F"/>
    <bpmn2:sequenceFlow id="_990078F6-904C-4755-8741-1E8F31091FEA" sourceRef="_298BA4E0-AB8C-4A64-8736-86B75B736828" targetRef="_B5194AC7-C0FF-4BDE-92BA-EDBF60BB453D"/>
    <bpmn2:sequenceFlow id="_13ED9625-DD02-4B59-835B-623D9B7A91CF" sourceRef="_3CA0433B-3C5E-45C9-8BEC-524E500AA6E3" targetRef="_25A74D50-D927-4551-B877-99ACD5F79673"/>
    <bpmn2:sequenceFlow id="_30A88ED1-A399-419F-8CB9-6A2B6B64915A" sourceRef="_8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F" targetRef="_3CA0433B-3C5E-45C9-8BEC-524E500AA6E3"/>
    <bpmn2:sequenceFlow id="_2CF3F9E2-D266-42B7-9827-8917E6B8982B" sourceRef="_2B231F1D-DE73-437B-9FBA-E3488A3D9718" targetRef="_4B2D32D8-B82A-46B5-A4E4-43C347BB82B6"/>
    <bpmn2:exclusiveGateway id="_298BA4E0-AB8C-4A64-8736-86B75B736828" gatewayDirection="Converging">
      <bpmn2:incoming>_B3911253-AF1F-48C8-95EB-0E2259997134</bpmn2:incoming>
      <bpmn2:incoming>_8012D40D-BF09-4643-8A9F-EB56938CBDA8</bpmn2:incoming>
      <bpmn2:outgoing>_990078F6-904C-4755-8741-1E8F31091FEA</bpmn2:outgoing>
    </bpmn2:exclusiveGateway>
    <bpmn2:textAnnotation id="_57608279-ED3F-44A7-A286-4668AC5D4562" name="Cancel or wait response provided by the user or a service.">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[Cancel or wait response provided by the user or a service.]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:text>Cancel or wait response provided by the user or a service.</bpmn2:text>
    </bpmn2:textAnnotation>
    <bpmn2:userTask id="_EDFAACF1-A8F0-41B3-8CF0-27EC07481653" name="Service Response">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[Service Response]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>_6C4303E0-B8ED-4F3E-A42F-95CC9618B69E</bpmn2:incoming>
      <bpmn2:outgoing>_8012D40D-BF09-4643-8A9F-EB56938CBDA8</bpmn2:outgoing>
      <bpmn2:ioSpecification>
        <bpmn2:dataInput id="_EDFAACF1-A8F0-41B3-8CF0-27EC07481653_TaskNameInputX" drools:dtype="Object" itemSubjectRef="__EDFAACF1-A8F0-41B3-8CF0-27EC07481653_TaskNameInputXItem" name="TaskName"/>
        <bpmn2:dataInput id="_EDFAACF1-A8F0-41B3-8CF0-27EC07481653_SkippableInputX" drools:dtype="Object" itemSubjectRef="__EDFAACF1-A8F0-41B3-8CF0-27EC07481653_SkippableInputXItem" name="Skippable"/>
        <bpmn2:inputSet>
          <bpmn2:dataInputRefs>_EDFAACF1-A8F0-41B3-8CF0-27EC07481653_TaskNameInputX</bpmn2:dataInputRefs>
          <bpmn2:dataInputRefs>_EDFAACF1-A8F0-41B3-8CF0-27EC07481653_SkippableInputX</bpmn2:dataInputRefs>
        </bpmn2:inputSet>
      </bpmn2:ioSpecification>
      <bpmn2:dataInputAssociation>
        <bpmn2:targetRef>_EDFAACF1-A8F0-41B3-8CF0-27EC07481653_TaskNameInputX</bpmn2:targetRef>
        <bpmn2:assignment>
          <bpmn2:from xsi:type="bpmn2:tFormalExpression"><![CDATA[serviceResponse]]></bpmn2:from>
          <bpmn2:to xsi:type="bpmn2:tFormalExpression"><![CDATA[_EDFAACF1-A8F0-41B3-8CF0-27EC07481653_TaskNameInputX]]></bpmn2:to>
        </bpmn2:assignment>
      </bpmn2:dataInputAssociation>
      <bpmn2:dataInputAssociation>
        <bpmn2:targetRef>_EDFAACF1-A8F0-41B3-8CF0-27EC07481653_SkippableInputX</bpmn2:targetRef>
        <bpmn2:assignment>
          <bpmn2:from xsi:type="bpmn2:tFormalExpression"><![CDATA[false]]></bpmn2:from>
          <bpmn2:to xsi:type="bpmn2:tFormalExpression"><![CDATA[_EDFAACF1-A8F0-41B3-8CF0-27EC07481653_SkippableInputX]]></bpmn2:to>
        </bpmn2:assignment>
      </bpmn2:dataInputAssociation>
      <bpmn2:potentialOwner id="_3-vMwPMEEDutsPrpA80sbw">
        <bpmn2:resourceAssignmentExpression id="_3-vz0PMEEDutsPrpA80sbw">
          <bpmn2:formalExpression>process</bpmn2:formalExpression>
        </bpmn2:resourceAssignmentExpression>
      </bpmn2:potentialOwner>
    </bpmn2:userTask>
    <bpmn2:parallelGateway id="_A6A246AE-32C3-42B6-A436-0C8981CCE244" gatewayDirection="Diverging">
      <bpmn2:incoming>_A81EADB0-35E0-4CAA-BEC1-01751A2B88DF</bpmn2:incoming>
      <bpmn2:outgoing>_29789E2E-076A-4C27-A98D-EF60CE23BAA3</bpmn2:outgoing>
      <bpmn2:outgoing>_6C4303E0-B8ED-4F3E-A42F-95CC9618B69E</bpmn2:outgoing>
    </bpmn2:parallelGateway>
    <bpmn2:exclusiveGateway id="_4C7ECFD5-74C5-42CD-A5C4-4C862B3219BF" drools:dg="_93563AD1-25D6-44A2-890D-720E2BEBB8D2" gatewayDirection="Converging" default="_93563AD1-25D6-44A2-890D-720E2BEBB8D2">
      <bpmn2:incoming>_9C54A668-641C-473F-AF90-2A2342DD24DC</bpmn2:incoming>
      <bpmn2:incoming>_EAF0AE85-BC7F-4DE2-8451-C1E802640DE5</bpmn2:incoming>
      <bpmn2:outgoing>_93563AD1-25D6-44A2-890D-720E2BEBB8D2</bpmn2:outgoing>
    </bpmn2:exclusiveGateway>
    <bpmn2:userTask id="_C5C11AB8-34FF-4748-A207-CF3A29B34F83" name="Customer Response">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[Customer Response]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>_29789E2E-076A-4C27-A98D-EF60CE23BAA3</bpmn2:incoming>
      <bpmn2:outgoing>_B3911253-AF1F-48C8-95EB-0E2259997134</bpmn2:outgoing>
      <bpmn2:ioSpecification>
        <bpmn2:dataInput id="_C5C11AB8-34FF-4748-A207-CF3A29B34F83_TaskNameInputX" drools:dtype="Object" itemSubjectRef="__C5C11AB8-34FF-4748-A207-CF3A29B34F83_TaskNameInputXItem" name="TaskName"/>
        <bpmn2:dataInput id="_C5C11AB8-34FF-4748-A207-CF3A29B34F83_SkippableInputX" drools:dtype="Object" itemSubjectRef="__C5C11AB8-34FF-4748-A207-CF3A29B34F83_SkippableInputXItem" name="Skippable"/>
        <bpmn2:inputSet>
          <bpmn2:dataInputRefs>_C5C11AB8-34FF-4748-A207-CF3A29B34F83_TaskNameInputX</bpmn2:dataInputRefs>
          <bpmn2:dataInputRefs>_C5C11AB8-34FF-4748-A207-CF3A29B34F83_SkippableInputX</bpmn2:dataInputRefs>
        </bpmn2:inputSet>
      </bpmn2:ioSpecification>
      <bpmn2:dataInputAssociation>
        <bpmn2:targetRef>_C5C11AB8-34FF-4748-A207-CF3A29B34F83_TaskNameInputX</bpmn2:targetRef>
        <bpmn2:assignment>
          <bpmn2:from xsi:type="bpmn2:tFormalExpression"><![CDATA[customerResponse]]></bpmn2:from>
          <bpmn2:to xsi:type="bpmn2:tFormalExpression"><![CDATA[_C5C11AB8-34FF-4748-A207-CF3A29B34F83_TaskNameInputX]]></bpmn2:to>
        </bpmn2:assignment>
      </bpmn2:dataInputAssociation>
      <bpmn2:dataInputAssociation>
        <bpmn2:targetRef>_C5C11AB8-34FF-4748-A207-CF3A29B34F83_SkippableInputX</bpmn2:targetRef>
        <bpmn2:assignment>
          <bpmn2:from xsi:type="bpmn2:tFormalExpression"><![CDATA[false]]></bpmn2:from>
          <bpmn2:to xsi:type="bpmn2:tFormalExpression"><![CDATA[_C5C11AB8-34FF-4748-A207-CF3A29B34F83_SkippableInputX]]></bpmn2:to>
        </bpmn2:assignment>
      </bpmn2:dataInputAssociation>
      <bpmn2:potentialOwner id="_3-xpAPMEEDutsPrpA80sbw">
        <bpmn2:resourceAssignmentExpression id="_3-xpAfMEEDutsPrpA80sbw">
          <bpmn2:formalExpression>customer</bpmn2:formalExpression>
        </bpmn2:resourceAssignmentExpression>
      </bpmn2:potentialOwner>
    </bpmn2:userTask>
    <bpmn2:scriptTask id="_0AF5AC9A-99A8-45A1-A418-30F1A7A2A77F" name="Send Delay Notification" scriptFormat="http://www.java.com/java">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[Send Delay Notification]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>_1E4312C6-E5B0-4147-B9E3-B76C25C9C20E</bpmn2:incoming>
      <bpmn2:outgoing>_A81EADB0-35E0-4CAA-BEC1-01751A2B88DF</bpmn2:outgoing>
      <bpmn2:script>System.out.println("Sending notification to customer");

model.sendNotificationToCustomer();</bpmn2:script>
    </bpmn2:scriptTask>
    <bpmn2:exclusiveGateway id="_25A74D50-D927-4551-B877-99ACD5F79673" gatewayDirection="Diverging">
      <bpmn2:incoming>_13ED9625-DD02-4B59-835B-623D9B7A91CF</bpmn2:incoming>
      <bpmn2:outgoing>_EAF0AE85-BC7F-4DE2-8451-C1E802640DE5</bpmn2:outgoing>
      <bpmn2:outgoing>_1E4312C6-E5B0-4147-B9E3-B76C25C9C20E</bpmn2:outgoing>
    </bpmn2:exclusiveGateway>
    <bpmn2:scriptTask id="_4B2D32D8-B82A-46B5-A4E4-43C347BB82B6" name="Init Delay" scriptFormat="http://www.java.com/java">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[Init Delay]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>_2CF3F9E2-D266-42B7-9827-8917E6B8982B</bpmn2:incoming>
      <bpmn2:outgoing>_9C54A668-641C-473F-AF90-2A2342DD24DC</bpmn2:outgoing>
      <bpmn2:script>System.out.println("Init Delay executed");</bpmn2:script>
    </bpmn2:scriptTask>
    <bpmn2:userTask id="_8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F" name="Delay State">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[Delay State]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>_93563AD1-25D6-44A2-890D-720E2BEBB8D2</bpmn2:incoming>
      <bpmn2:outgoing>_30A88ED1-A399-419F-8CB9-6A2B6B64915A</bpmn2:outgoing>
      <bpmn2:ioSpecification>
        <bpmn2:dataInput id="_8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_TaskNameInputX" drools:dtype="Object" itemSubjectRef="__8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_TaskNameInputXItem" name="TaskName"/>
        <bpmn2:dataInput id="_8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_SkippableInputX" drools:dtype="Object" itemSubjectRef="__8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_SkippableInputXItem" name="Skippable"/>
        <bpmn2:dataOutput id="_8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_currentDateInputOutputX" drools:dtype="java.util.Date" itemSubjectRef="__8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_currentDateInputOutputXItem" name="currentDateInput"/>
        <bpmn2:dataOutput id="_8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_currentlyDelayedInputOutputX" drools:dtype="Boolean" itemSubjectRef="__8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_currentlyDelayedInputOutputXItem" name="currentlyDelayedInput"/>
        <bpmn2:inputSet>
          <bpmn2:dataInputRefs>_8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_TaskNameInputX</bpmn2:dataInputRefs>
          <bpmn2:dataInputRefs>_8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_SkippableInputX</bpmn2:dataInputRefs>
        </bpmn2:inputSet>
        <bpmn2:outputSet>
          <bpmn2:dataOutputRefs>_8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_currentDateInputOutputX</bpmn2:dataOutputRefs>
          <bpmn2:dataOutputRefs>_8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_currentlyDelayedInputOutputX</bpmn2:dataOutputRefs>
        </bpmn2:outputSet>
      </bpmn2:ioSpecification>
      <bpmn2:dataInputAssociation>
        <bpmn2:targetRef>_8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_TaskNameInputX</bpmn2:targetRef>
        <bpmn2:assignment>
          <bpmn2:from xsi:type="bpmn2:tFormalExpression"><![CDATA[delayState]]></bpmn2:from>
          <bpmn2:to xsi:type="bpmn2:tFormalExpression"><![CDATA[_8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_TaskNameInputX]]></bpmn2:to>
        </bpmn2:assignment>
      </bpmn2:dataInputAssociation>
      <bpmn2:dataInputAssociation>
        <bpmn2:targetRef>_8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_SkippableInputX</bpmn2:targetRef>
        <bpmn2:assignment>
          <bpmn2:from xsi:type="bpmn2:tFormalExpression"><![CDATA[false]]></bpmn2:from>
          <bpmn2:to xsi:type="bpmn2:tFormalExpression"><![CDATA[_8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_SkippableInputX]]></bpmn2:to>
        </bpmn2:assignment>
      </bpmn2:dataInputAssociation>
      <bpmn2:dataOutputAssociation>
        <bpmn2:sourceRef>_8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_currentDateInputOutputX</bpmn2:sourceRef>
        <bpmn2:targetRef>currentDateTime</bpmn2:targetRef>
      </bpmn2:dataOutputAssociation>
      <bpmn2:dataOutputAssociation>
        <bpmn2:sourceRef>_8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_currentlyDelayedInputOutputX</bpmn2:sourceRef>
        <bpmn2:targetRef>currentlyDelayed</bpmn2:targetRef>
      </bpmn2:dataOutputAssociation>
      <bpmn2:potentialOwner id="_3-3IkPMEEDutsPrpA80sbw">
        <bpmn2:resourceAssignmentExpression id="_3-3voPMEEDutsPrpA80sbw">
          <bpmn2:formalExpression>process</bpmn2:formalExpression>
        </bpmn2:resourceAssignmentExpression>
      </bpmn2:potentialOwner>
    </bpmn2:userTask>
    <bpmn2:endEvent id="_B5194AC7-C0FF-4BDE-92BA-EDBF60BB453D">
      <bpmn2:incoming>_990078F6-904C-4755-8741-1E8F31091FEA</bpmn2:incoming>
    </bpmn2:endEvent>
    <bpmn2:scriptTask id="_3CA0433B-3C5E-45C9-8BEC-524E500AA6E3" name="Count Delay Occurences" scriptFormat="http://www.java.com/java">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[Count Delay Occurences]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>_30A88ED1-A399-419F-8CB9-6A2B6B64915A</bpmn2:incoming>
      <bpmn2:outgoing>_13ED9625-DD02-4B59-835B-623D9B7A91CF</bpmn2:outgoing>
      <bpmn2:script>model.setCurrentDateTime(currentDateTime);

if(currentlyDelayed)
{
    model.increaseDelayOccurences();
}
else
{
    model.resetDelayOccurences();
}
</bpmn2:script>
    </bpmn2:scriptTask>
    <bpmn2:startEvent id="_2B231F1D-DE73-437B-9FBA-E3488A3D9718" name="StartProcess">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[StartProcess]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:outgoing>_2CF3F9E2-D266-42B7-9827-8917E6B8982B</bpmn2:outgoing>
    </bpmn2:startEvent>
  </bpmn2:process>
  <bpmndi:BPMNDiagram>
    <bpmndi:BPMNPlane bpmnElement="DestinationDelaySolver">
      <bpmndi:BPMNShape id="shape__2B231F1D-DE73-437B-9FBA-E3488A3D9718" bpmnElement="_2B231F1D-DE73-437B-9FBA-E3488A3D9718">
        <dc:Bounds height="56" width="56" x="196" y="203"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape__3CA0433B-3C5E-45C9-8BEC-524E500AA6E3" bpmnElement="_3CA0433B-3C5E-45C9-8BEC-524E500AA6E3">
        <dc:Bounds height="100" width="198" x="803" y="182"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape__B5194AC7-C0FF-4BDE-92BA-EDBF60BB453D" bpmnElement="_B5194AC7-C0FF-4BDE-92BA-EDBF60BB453D">
        <dc:Bounds height="56" width="56" x="874" y="838"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape__8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F" bpmnElement="_8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F">
        <dc:Bounds height="102" width="154" x="617" y="182"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape__4B2D32D8-B82A-46B5-A4E4-43C347BB82B6" bpmnElement="_4B2D32D8-B82A-46B5-A4E4-43C347BB82B6">
        <dc:Bounds height="102" width="154" x="330" y="180"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape__25A74D50-D927-4551-B877-99ACD5F79673" bpmnElement="_25A74D50-D927-4551-B877-99ACD5F79673">
        <dc:Bounds height="56" width="56" x="873.9992156802907" y="314"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape__0AF5AC9A-99A8-45A1-A418-30F1A7A2A77F" bpmnElement="_0AF5AC9A-99A8-45A1-A418-30F1A7A2A77F">
        <dc:Bounds height="101.99999999999994" width="197.75" x="803" y="418.0988227619844"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape__C5C11AB8-34FF-4748-A207-CF3A29B34F83" bpmnElement="_C5C11AB8-34FF-4748-A207-CF3A29B34F83">
        <dc:Bounds height="102" width="154" x="638.5700883941613" y="571"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape__4C7ECFD5-74C5-42CD-A5C4-4C862B3219BF" bpmnElement="_4C7ECFD5-74C5-42CD-A5C4-4C862B3219BF">
        <dc:Bounds height="56" width="56" x="523" y="203"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape__A6A246AE-32C3-42B6-A436-0C8981CCE244" bpmnElement="_A6A246AE-32C3-42B6-A436-0C8981CCE244">
        <dc:Bounds height="56" width="56" x="874" y="594"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape__EDFAACF1-A8F0-41B3-8CF0-27EC07481653" bpmnElement="_EDFAACF1-A8F0-41B3-8CF0-27EC07481653">
        <dc:Bounds height="102" width="154" x="1011" y="571"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape__57608279-ED3F-44A7-A286-4668AC5D4562" bpmnElement="_57608279-ED3F-44A7-A286-4668AC5D4562">
        <dc:Bounds height="61" width="284" x="768" y="684"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape__298BA4E0-AB8C-4A64-8736-86B75B736828" bpmnElement="_298BA4E0-AB8C-4A64-8736-86B75B736828">
        <dc:Bounds height="56" width="56" x="874" y="741.75"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="edge_shape__2B231F1D-DE73-437B-9FBA-E3488A3D9718_to_shape__4B2D32D8-B82A-46B5-A4E4-43C347BB82B6" bpmnElement="_2CF3F9E2-D266-42B7-9827-8917E6B8982B">
        <di:waypoint x="224" y="231"/>
        <di:waypoint x="407" y="231"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape__8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F_to_shape__3CA0433B-3C5E-45C9-8BEC-524E500AA6E3" bpmnElement="_30A88ED1-A399-419F-8CB9-6A2B6B64915A">
        <di:waypoint x="694" y="233"/>
        <di:waypoint x="803" y="232"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape__3CA0433B-3C5E-45C9-8BEC-524E500AA6E3_to_shape__25A74D50-D927-4551-B877-99ACD5F79673" bpmnElement="_13ED9625-DD02-4B59-835B-623D9B7A91CF">
        <di:waypoint x="902" y="232"/>
        <di:waypoint x="901.9992156802907" y="342"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape__298BA4E0-AB8C-4A64-8736-86B75B736828_to_shape__B5194AC7-C0FF-4BDE-92BA-EDBF60BB453D" bpmnElement="_990078F6-904C-4755-8741-1E8F31091FEA">
        <di:waypoint x="902" y="769.75"/>
        <di:waypoint x="902" y="838"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape__4C7ECFD5-74C5-42CD-A5C4-4C862B3219BF_to_shape__8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F" bpmnElement="_93563AD1-25D6-44A2-890D-720E2BEBB8D2">
        <di:waypoint x="579" y="231"/>
        <di:waypoint x="694" y="233"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape__4B2D32D8-B82A-46B5-A4E4-43C347BB82B6_to_shape__4C7ECFD5-74C5-42CD-A5C4-4C862B3219BF" bpmnElement="_9C54A668-641C-473F-AF90-2A2342DD24DC">
        <di:waypoint x="407" y="231"/>
        <di:waypoint x="551" y="231"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape__25A74D50-D927-4551-B877-99ACD5F79673_to_shape__4C7ECFD5-74C5-42CD-A5C4-4C862B3219BF" bpmnElement="_EAF0AE85-BC7F-4DE2-8451-C1E802640DE5">
        <di:waypoint x="901.9992156802907" y="342"/>
        <di:waypoint x="551" y="341.9999999999331"/>
        <di:waypoint x="551" y="259"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape__25A74D50-D927-4551-B877-99ACD5F79673_to_shape__0AF5AC9A-99A8-45A1-A418-30F1A7A2A77F" bpmnElement="_1E4312C6-E5B0-4147-B9E3-B76C25C9C20E">
        <di:waypoint x="901.9992156802907" y="342"/>
        <di:waypoint x="901.875" y="469.0988227619844"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape__0AF5AC9A-99A8-45A1-A418-30F1A7A2A77F_to_shape__A6A246AE-32C3-42B6-A436-0C8981CCE244" bpmnElement="_A81EADB0-35E0-4CAA-BEC1-01751A2B88DF">
        <di:waypoint x="901.875" y="469.0988227619844"/>
        <di:waypoint x="902" y="594"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape__A6A246AE-32C3-42B6-A436-0C8981CCE244_to_shape__C5C11AB8-34FF-4748-A207-CF3A29B34F83" bpmnElement="_29789E2E-076A-4C27-A98D-EF60CE23BAA3">
        <di:waypoint x="902" y="622"/>
        <di:waypoint x="792.5700883941613" y="622"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape__C5C11AB8-34FF-4748-A207-CF3A29B34F83_to_shape__298BA4E0-AB8C-4A64-8736-86B75B736828" bpmnElement="_B3911253-AF1F-48C8-95EB-0E2259997134">
        <di:waypoint x="715.5700883941613" y="622"/>
        <di:waypoint x="715.5701359077182" y="769.75"/>
        <di:waypoint x="874" y="769.75"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape__A6A246AE-32C3-42B6-A436-0C8981CCE244_to_shape__EDFAACF1-A8F0-41B3-8CF0-27EC07481653" bpmnElement="_6C4303E0-B8ED-4F3E-A42F-95CC9618B69E">
        <di:waypoint x="902" y="622"/>
        <di:waypoint x="972.5" y="621.8120562024899"/>
        <di:waypoint x="1011" y="622"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape__EDFAACF1-A8F0-41B3-8CF0-27EC07481653_to_shape__298BA4E0-AB8C-4A64-8736-86B75B736828" bpmnElement="_8012D40D-BF09-4643-8A9F-EB56938CBDA8">
        <di:waypoint x="1088" y="622"/>
        <di:waypoint x="1088.0000000002249" y="769.75"/>
        <di:waypoint x="930" y="769.75"/>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
  <bpmn2:relationship type="BPSimData">
    <bpmn2:extensionElements>
      <bpsim:BPSimData>
        <bpsim:Scenario id="default" name="Simulationscenario">
          <bpsim:ScenarioParameters/>
          <bpsim:ElementParameters elementRef="_2B231F1D-DE73-437B-9FBA-E3488A3D9718">
            <bpsim:TimeParameters>
              <bpsim:ProcessingTime>
                <bpsim:NormalDistribution mean="0" standardDeviation="0"/>
              </bpsim:ProcessingTime>
            </bpsim:TimeParameters>
          </bpsim:ElementParameters>
          <bpsim:ElementParameters elementRef="_3CA0433B-3C5E-45C9-8BEC-524E500AA6E3">
            <bpsim:TimeParameters>
              <bpsim:ProcessingTime>
                <bpsim:NormalDistribution mean="0" standardDeviation="0"/>
              </bpsim:ProcessingTime>
            </bpsim:TimeParameters>
            <bpsim:ResourceParameters>
              <bpsim:Availability>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Availability>
              <bpsim:Quantity>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Quantity>
            </bpsim:ResourceParameters>
            <bpsim:CostParameters>
              <bpsim:UnitCost>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:UnitCost>
            </bpsim:CostParameters>
          </bpsim:ElementParameters>
          <bpsim:ElementParameters elementRef="_8B6E8D09-4059-4B6D-96D1-F3A2F9DBED0F">
            <bpsim:TimeParameters>
              <bpsim:ProcessingTime>
                <bpsim:NormalDistribution mean="0" standardDeviation="0"/>
              </bpsim:ProcessingTime>
            </bpsim:TimeParameters>
            <bpsim:ResourceParameters>
              <bpsim:Availability>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Availability>
              <bpsim:Quantity>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Quantity>
            </bpsim:ResourceParameters>
            <bpsim:CostParameters>
              <bpsim:UnitCost>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:UnitCost>
            </bpsim:CostParameters>
          </bpsim:ElementParameters>
          <bpsim:ElementParameters elementRef="_4B2D32D8-B82A-46B5-A4E4-43C347BB82B6">
            <bpsim:TimeParameters>
              <bpsim:ProcessingTime>
                <bpsim:NormalDistribution mean="0" standardDeviation="0"/>
              </bpsim:ProcessingTime>
            </bpsim:TimeParameters>
            <bpsim:ResourceParameters>
              <bpsim:Availability>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Availability>
              <bpsim:Quantity>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Quantity>
            </bpsim:ResourceParameters>
            <bpsim:CostParameters>
              <bpsim:UnitCost>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:UnitCost>
            </bpsim:CostParameters>
          </bpsim:ElementParameters>
          <bpsim:ElementParameters elementRef="_0AF5AC9A-99A8-45A1-A418-30F1A7A2A77F">
            <bpsim:TimeParameters>
              <bpsim:ProcessingTime>
                <bpsim:NormalDistribution mean="0" standardDeviation="0"/>
              </bpsim:ProcessingTime>
            </bpsim:TimeParameters>
            <bpsim:ResourceParameters>
              <bpsim:Availability>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Availability>
              <bpsim:Quantity>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Quantity>
            </bpsim:ResourceParameters>
            <bpsim:CostParameters>
              <bpsim:UnitCost>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:UnitCost>
            </bpsim:CostParameters>
          </bpsim:ElementParameters>
          <bpsim:ElementParameters elementRef="_C5C11AB8-34FF-4748-A207-CF3A29B34F83">
            <bpsim:TimeParameters>
              <bpsim:ProcessingTime>
                <bpsim:NormalDistribution mean="0" standardDeviation="0"/>
              </bpsim:ProcessingTime>
            </bpsim:TimeParameters>
            <bpsim:ResourceParameters>
              <bpsim:Availability>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Availability>
              <bpsim:Quantity>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Quantity>
            </bpsim:ResourceParameters>
            <bpsim:CostParameters>
              <bpsim:UnitCost>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:UnitCost>
            </bpsim:CostParameters>
          </bpsim:ElementParameters>
          <bpsim:ElementParameters elementRef="_EDFAACF1-A8F0-41B3-8CF0-27EC07481653">
            <bpsim:TimeParameters>
              <bpsim:ProcessingTime>
                <bpsim:NormalDistribution mean="0" standardDeviation="0"/>
              </bpsim:ProcessingTime>
            </bpsim:TimeParameters>
            <bpsim:ResourceParameters>
              <bpsim:Availability>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Availability>
              <bpsim:Quantity>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Quantity>
            </bpsim:ResourceParameters>
            <bpsim:CostParameters>
              <bpsim:UnitCost>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:UnitCost>
            </bpsim:CostParameters>
          </bpsim:ElementParameters>
        </bpsim:Scenario>
      </bpsim:BPSimData>
    </bpmn2:extensionElements>
    <bpmn2:source>_3-hxYPMEEDutsPrpA80sbw</bpmn2:source>
    <bpmn2:target>_3-hxYPMEEDutsPrpA80sbw</bpmn2:target>
  </bpmn2:relationship>
</bpmn2:definitions>