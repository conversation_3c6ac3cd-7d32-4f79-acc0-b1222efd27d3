{"version": 3, "file": "defaultAzureCredential.js", "sourceRoot": "", "sources": ["../../../src/credentials/defaultAzureCredential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAOlC,OAAO,EACL,yBAAyB,GAG1B,MAAM,6BAA6B,CAAC;AACrC,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AACxE,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAC;AAClE,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAehE;;;;;GAKG;AACH,MAAM,OAAO,gCAAiC,SAAQ,yBAAyB;IAK7E,2DAA2D;IAC3D,sIAAsI;IACtI,YAAY,OAAuC;;QACjD,MAAM,uBAAuB,GAC3B,MAAC,OAAiD,aAAjD,OAAO,uBAAP,OAAO,CAA4C,uBAAuB,mCAC3E,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;QAC9B,MAAM,iBAAiB,GAAI,OAAmD,aAAnD,OAAO,uBAAP,OAAO,CAC9B,yBAAyB,CAAC;QAE9B,yFAAyF;QACzF,IAAI,iBAAiB,EAAE;YACrB,MAAM,gCAAgC,mCACjC,OAAO,KACV,UAAU,EAAE,iBAAiB,GAC9B,CAAC;YACF,KAAK,CAAC,gCAAgC,CAAC,CAAC;SACzC;aAAM,IAAI,uBAAuB,EAAE;YAClC,MAAM,4BAA4B,mCAC7B,OAAO,KACV,QAAQ,EAAE,uBAAuB,GAClC,CAAC;YACF,KAAK,CAAC,4BAA4B,CAAC,CAAC;SACrC;aAAM;YACL,KAAK,CAAC,OAAO,CAAC,CAAC;SAChB;IACH,CAAC;CACF;AAED,MAAM,CAAC,MAAM,kBAAkB,GAAmC;IAChE,qBAAqB;IACrB,gCAAgC;IAChC,kBAAkB;IAClB,yBAAyB;CAC1B,CAAC;AAEF;;;GAGG;AACH,MAAM,OAAO,sBAAuB,SAAQ,sBAAsB;IA6DhE,YACE,OAGyC;QAEzC,KAAK,CAAC,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,kBAAkB;YACrB,kLAAkL,CAAC;IACvL,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  DefaultAzureCredentialClientIdOptions,\n  DefaultAzureCredentialOptions,\n  DefaultAzureCredentialResourceIdOptions,\n} from \"./defaultAzureCredentialOptions\";\nimport {\n  ManagedIdentityCredential,\n  ManagedIdentityCredentialClientIdOptions,\n  ManagedIdentityCredentialResourceIdOptions,\n} from \"./managedIdentityCredential\";\nimport { AzureCliCredential } from \"./azureCliCredential\";\nimport { AzurePowerShellCredential } from \"./azurePowerShellCredential\";\nimport { ChainedTokenCredential } from \"./chainedTokenCredential\";\nimport { EnvironmentCredential } from \"./environmentCredential\";\nimport { TokenCredential } from \"@azure/core-auth\";\n\n/**\n * The type of a class that implements TokenCredential and accepts either\n * {@link DefaultAzureCredentialClientIdOptions} or\n * {@link DefaultAzureCredentialResourceIdOptions} or\n * {@link DefaultAzureCredentialOptions}.\n */\ninterface DefaultCredentialConstructor {\n  new (options?: DefaultAzureCredentialOptions): TokenCredential;\n  new (options?: DefaultAzureCredentialResourceIdOptions): TokenCredential;\n  new (options?: DefaultAzureCredentialClientIdOptions): TokenCredential;\n}\n\n/**\n * A shim around ManagedIdentityCredential that adapts it to accept\n * `DefaultAzureCredentialOptions`.\n *\n * @internal\n */\nexport class DefaultManagedIdentityCredential extends ManagedIdentityCredential {\n  // Constructor overload with just client id options\n  constructor(options?: DefaultAzureCredentialClientIdOptions);\n  // Constructor overload with just resource id options\n  constructor(options?: DefaultAzureCredentialResourceIdOptions);\n  // Constructor overload with just the other default options\n  // Last constructor overload with Union of all options not required since the above two constructor overloads have optional properties\n  constructor(options?: DefaultAzureCredentialOptions) {\n    const managedIdentityClientId =\n      (options as DefaultAzureCredentialClientIdOptions)?.managedIdentityClientId ??\n      process.env.AZURE_CLIENT_ID;\n    const managedResourceId = (options as DefaultAzureCredentialResourceIdOptions)\n      ?.managedIdentityResourceId;\n\n    // ManagedIdentityCredential throws if both the resourceId and the clientId are provided.\n    if (managedResourceId) {\n      const managedIdentityResourceIdOptions: ManagedIdentityCredentialResourceIdOptions = {\n        ...options,\n        resourceId: managedResourceId,\n      };\n      super(managedIdentityResourceIdOptions);\n    } else if (managedIdentityClientId) {\n      const managedIdentityClientOptions: ManagedIdentityCredentialClientIdOptions = {\n        ...options,\n        clientId: managedIdentityClientId,\n      };\n      super(managedIdentityClientOptions);\n    } else {\n      super(options);\n    }\n  }\n}\n\nexport const defaultCredentials: DefaultCredentialConstructor[] = [\n  EnvironmentCredential,\n  DefaultManagedIdentityCredential,\n  AzureCliCredential,\n  AzurePowerShellCredential,\n];\n\n/**\n * Provides a default {@link ChainedTokenCredential} configuration that should\n * work for most applications that use the Azure SDK.\n */\nexport class DefaultAzureCredential extends ChainedTokenCredential {\n  /**\n   * Creates an instance of the DefaultAzureCredential class with {@link DefaultAzureCredentialClientIdOptions}\n   *\n   * This credential provides a default {@link ChainedTokenCredential} configuration that should\n   * work for most applications that use the Azure SDK.\n   *\n   * The following credential types will be tried, in order:\n   *\n   * - {@link EnvironmentCredential}\n   * - {@link ManagedIdentityCredential}\n   * - {@link AzureCliCredential}\n   * - {@link AzurePowerShellCredential}\n   *\n   * Consult the documentation of these credential types for more information\n   * on how they attempt authentication.\n   *\n   * @param options - Optional parameters. See {@link DefaultAzureCredentialClientIdOptions}.\n   */\n  constructor(options?: DefaultAzureCredentialClientIdOptions);\n\n  /**\n   *  Creates an instance of the DefaultAzureCredential class with {@link DefaultAzureCredentialResourceIdOptions}\n   *\n   * This credential provides a default {@link ChainedTokenCredential} configuration that should\n   * work for most applications that use the Azure SDK.\n   *\n   * The following credential types will be tried, in order:\n   *\n   * - {@link EnvironmentCredential}\n   * - {@link ManagedIdentityCredential}\n   * - {@link AzureCliCredential}\n   * - {@link AzurePowerShellCredential}\n   *\n   * Consult the documentation of these credential types for more information\n   * on how they attempt authentication.\n   *\n   * @param options - Optional parameters. See {@link DefaultAzureCredentialResourceIdOptions}.\n   */\n  constructor(options?: DefaultAzureCredentialResourceIdOptions);\n\n  /**\n   * Creates an instance of the DefaultAzureCredential class with {@link DefaultAzureCredentialOptions}\n   *\n   * This credential provides a default {@link ChainedTokenCredential} configuration that should\n   * work for most applications that use the Azure SDK.\n   *\n   * The following credential types will be tried, in order:\n   *\n   * - {@link EnvironmentCredential}\n   * - {@link ManagedIdentityCredential}\n   * - {@link AzureCliCredential}\n   * - {@link AzurePowerShellCredential}\n   *\n   * Consult the documentation of these credential types for more information\n   * on how they attempt authentication.\n   *\n   * @param options - Optional parameters. See {@link DefaultAzureCredentialOptions}.\n   */\n  constructor(options?: DefaultAzureCredentialOptions);\n\n  constructor(\n    options?:\n      | DefaultAzureCredentialOptions\n      | DefaultAzureCredentialResourceIdOptions\n      | DefaultAzureCredentialClientIdOptions\n  ) {\n    super(...defaultCredentials.map((ctor) => new ctor(options)));\n    this.UnavailableMessage =\n      \"DefaultAzureCredential => failed to retrieve a token from the included credentials. To troubleshoot, visit https://aka.ms/azsdk/js/identity/defaultazurecredential/troubleshoot.\";\n  }\n}\n"]}