// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
import { MsalNode } from "./msalNodeCommon";
import { credentialLogger } from "../../util/logging";
/**
 * This MSAL client sets up a web server to listen for redirect callbacks, then calls to the MSAL's public application's `acquireTokenByDeviceCode` during `doGetToken`
 * to trigger the authentication flow, and then respond based on the values obtained from the redirect callback
 * @internal
 */
export class MsalAuthorizationCode extends MsalNode {
    constructor(options) {
        super(options);
        this.logger = credentialLogger("Node.js MSAL Authorization Code");
        this.redirectUri = options.redirectUri;
        this.authorizationCode = options.authorizationCode;
        if (options.clientSecret) {
            this.msalConfig.auth.clientSecret = options.clientSecret;
        }
    }
    async getAuthCodeUrl(options) {
        await this.init();
        return (this.confidentialApp || this.publicApp).getAuthCodeUrl(options);
    }
    async doGetToken(scopes, options) {
        var _a;
        try {
            const result = await ((_a = (this.confidentialApp || this.publicApp)) === null || _a === void 0 ? void 0 : _a.acquireTokenByCode({
                scopes,
                redirectUri: this.redirectUri,
                code: this.authorizationCode,
                correlationId: options === null || options === void 0 ? void 0 : options.correlationId,
                authority: options === null || options === void 0 ? void 0 : options.authority,
                claims: options === null || options === void 0 ? void 0 : options.claims,
            }));
            // The Client Credential flow does not return an account,
            // so each time getToken gets called, we will have to acquire a new token through the service.
            return this.handleResult(scopes, this.clientId, result || undefined);
        }
        catch (err) {
            throw this.handleError(scopes, err, options);
        }
    }
}
//# sourceMappingURL=msalAuthorizationCode.js.map