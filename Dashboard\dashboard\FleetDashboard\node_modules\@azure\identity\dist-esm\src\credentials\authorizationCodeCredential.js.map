{"version": 3, "file": "authorizationCodeCredential.js", "sourceRoot": "", "sources": ["../../../src/credentials/authorizationCodeCredential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EACL,yBAAyB,EACzB,iCAAiC,GAClC,MAAM,uBAAuB,CAAC;AAE/B,OAAO,EAAE,qBAAqB,EAAE,MAAM,yCAAyC,CAAC;AAEhF,OAAO,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AACtD,OAAO,EAAE,gBAAgB,EAAE,MAAM,iBAAiB,CAAC;AACnD,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,MAAM,MAAM,GAAG,gBAAgB,CAAC,6BAA6B,CAAC,CAAC;AAE/D;;;;;;GAMG;AACH,MAAM,OAAO,2BAA2B;IAkEtC;;;OAGG;IACH,YACE,QAA2B,EAC3B,QAAgB,EAChB,+BAAuC,EACvC,8BAAsC,EACtC,oBAA6E,EAC7E,OAA4C;QAE5C,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAChC,IAAI,YAAY,GAAuB,+BAA+B,CAAC;QAEvE,IAAI,OAAO,oBAAoB,KAAK,QAAQ,EAAE;YAC5C,wCAAwC;YACxC,IAAI,CAAC,iBAAiB,GAAG,8BAA8B,CAAC;YACxD,IAAI,CAAC,WAAW,GAAG,oBAAoB,CAAC;YACxC,8CAA8C;SAC/C;aAAM;YACL,gBAAgB;YAChB,IAAI,CAAC,iBAAiB,GAAG,+BAA+B,CAAC;YACzD,IAAI,CAAC,WAAW,GAAG,8BAAwC,CAAC;YAC5D,YAAY,GAAG,SAAS,CAAC;YACzB,OAAO,GAAG,oBAA0D,CAAC;SACtE;QAED,oCAAoC;QACpC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,4BAA4B,GAAG,iCAAiC,CACnE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,0BAA0B,CACpC,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,IAAI,qBAAqB,iCACpC,OAAO,KACV,YAAY;YACZ,QAAQ;YACR,QAAQ,EACR,sBAAsB,EAAE,OAAO,IAAI,EAAE,EACrC,MAAM,EACN,WAAW,EAAE,IAAI,CAAC,WAAW,EAC7B,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,IACzC,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAyB,EAAE,UAA2B,EAAE;QACrE,OAAO,aAAa,CAAC,QAAQ,CAC3B,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,WAAW,EACnC,OAAO,EACP,KAAK,EAAE,UAAU,EAAE,EAAE;YACnB,MAAM,QAAQ,GAAG,yBAAyB,CACxC,IAAI,CAAC,QAAQ,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,CAClC,CAAC;YACF,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAE/B,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,kCACpC,UAAU,KACb,8BAA8B,EAAE,IAAI,CAAC,8BAA8B,IACnE,CAAC;QACL,CAAC,CACF,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport {\n  processMultiTenantRequest,\n  resolveAddionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { AuthorizationCodeCredentialOptions } from \"./authorizationCodeCredentialOptions\";\nimport { MsalAuthorizationCode } from \"../msal/nodeFlows/msalAuthorizationCode\";\nimport { MsalFlow } from \"../msal/flows\";\nimport { checkTenantId } from \"../util/tenantIdUtils\";\nimport { credentialLogger } from \"../util/logging\";\nimport { ensureScopes } from \"../util/scopeUtils\";\nimport { tracingClient } from \"../util/tracing\";\n\nconst logger = credentialLogger(\"AuthorizationCodeCredential\");\n\n/**\n * Enables authentication to Azure Active Directory using an authorization code\n * that was obtained through the authorization code flow, described in more detail\n * in the Azure Active Directory documentation:\n *\n * https://docs.microsoft.com/en-us/azure/active-directory/develop/v2-oauth2-auth-code-flow\n */\nexport class AuthorizationCodeCredential implements TokenCredential {\n  private msalFlow: MsalFlow;\n  private disableAutomaticAuthentication?: boolean;\n  private authorizationCode: string;\n  private redirectUri: string;\n  private tenantId?: string;\n  private additionallyAllowedTenantIds: string[];\n\n  /**\n   * Creates an instance of AuthorizationCodeCredential with the details needed\n   * to request an access token using an authentication that was obtained\n   * from Azure Active Directory.\n   *\n   * It is currently necessary for the user of this credential to initiate\n   * the authorization code flow to obtain an authorization code to be used\n   * with this credential.  A full example of this flow is provided here:\n   *\n   * https://github.com/Azure/azure-sdk-for-js/blob/main/sdk/identity/identity/samples/v2/manual/authorizationCodeSample.ts\n   *\n   * @param tenantId - The Azure Active Directory tenant (directory) ID or name.\n   *                 'common' may be used when dealing with multi-tenant scenarios.\n   * @param clientId - The client (application) ID of an App Registration in the tenant.\n   * @param clientSecret - A client secret that was generated for the App Registration\n   * @param authorizationCode - An authorization code that was received from following the\n                              authorization code flow.  This authorization code must not\n                              have already been used to obtain an access token.\n   * @param redirectUri - The redirect URI that was used to request the authorization code.\n                        Must be the same URI that is configured for the App Registration.\n   * @param options - Options for configuring the client which makes the access token request.\n   */\n  constructor(\n    tenantId: string | \"common\",\n    clientId: string,\n    clientSecret: string,\n    authorizationCode: string,\n    redirectUri: string,\n    options?: AuthorizationCodeCredentialOptions\n  );\n  /**\n   * Creates an instance of AuthorizationCodeCredential with the details needed\n   * to request an access token using an authentication that was obtained\n   * from Azure Active Directory.\n   *\n   * It is currently necessary for the user of this credential to initiate\n   * the authorization code flow to obtain an authorization code to be used\n   * with this credential.  A full example of this flow is provided here:\n   *\n   * https://github.com/Azure/azure-sdk-for-js/blob/main/sdk/identity/identity/samples/v2/manual/authorizationCodeSample.ts\n   *\n   * @param tenantId - The Azure Active Directory tenant (directory) ID or name.\n   *                 'common' may be used when dealing with multi-tenant scenarios.\n   * @param clientId - The client (application) ID of an App Registration in the tenant.\n   * @param authorizationCode - An authorization code that was received from following the\n                              authorization code flow.  This authorization code must not\n                              have already been used to obtain an access token.\n   * @param redirectUri - The redirect URI that was used to request the authorization code.\n                        Must be the same URI that is configured for the App Registration.\n   * @param options - Options for configuring the client which makes the access token request.\n   */\n  constructor(\n    tenantId: string | \"common\",\n    clientId: string,\n    authorizationCode: string,\n    redirectUri: string,\n    options?: AuthorizationCodeCredentialOptions\n  );\n  /**\n   * @hidden\n   * @internal\n   */\n  constructor(\n    tenantId: string | \"common\",\n    clientId: string,\n    clientSecretOrAuthorizationCode: string,\n    authorizationCodeOrRedirectUri: string,\n    redirectUriOrOptions: string | AuthorizationCodeCredentialOptions | undefined,\n    options?: AuthorizationCodeCredentialOptions\n  ) {\n    checkTenantId(logger, tenantId);\n    let clientSecret: string | undefined = clientSecretOrAuthorizationCode;\n\n    if (typeof redirectUriOrOptions === \"string\") {\n      // the clientId+clientSecret constructor\n      this.authorizationCode = authorizationCodeOrRedirectUri;\n      this.redirectUri = redirectUriOrOptions;\n      // in this case, options are good as they come\n    } else {\n      // clientId only\n      this.authorizationCode = clientSecretOrAuthorizationCode;\n      this.redirectUri = authorizationCodeOrRedirectUri as string;\n      clientSecret = undefined;\n      options = redirectUriOrOptions as AuthorizationCodeCredentialOptions;\n    }\n\n    // TODO: Validate tenant if provided\n    this.tenantId = tenantId;\n    this.additionallyAllowedTenantIds = resolveAddionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants\n    );\n\n    this.msalFlow = new MsalAuthorizationCode({\n      ...options,\n      clientSecret,\n      clientId,\n      tenantId,\n      tokenCredentialOptions: options || {},\n      logger,\n      redirectUri: this.redirectUri,\n      authorizationCode: this.authorizationCode,\n    });\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  async getToken(scopes: string | string[], options: GetTokenOptions = {}): Promise<AccessToken> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.getToken`,\n      options,\n      async (newOptions) => {\n        const tenantId = processMultiTenantRequest(\n          this.tenantId,\n          newOptions,\n          this.additionallyAllowedTenantIds\n        );\n        newOptions.tenantId = tenantId;\n\n        const arrayScopes = ensureScopes(scopes);\n        return this.msalFlow.getToken(arrayScopes, {\n          ...newOptions,\n          disableAutomaticAuthentication: this.disableAutomaticAuthentication,\n        });\n      }\n    );\n  }\n}\n"]}