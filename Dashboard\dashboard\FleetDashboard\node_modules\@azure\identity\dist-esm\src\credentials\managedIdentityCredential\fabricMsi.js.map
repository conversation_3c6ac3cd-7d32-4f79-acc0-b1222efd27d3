{"version": 3, "file": "fabricMsi.js", "sourceRoot": "", "sources": ["../../../../src/credentials/managedIdentityCredential/fabricMsi.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAEL,iBAAiB,EACjB,qBAAqB,GACtB,MAAM,2BAA2B,CAAC;AAEnC,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAEtD,OAAO,EAAE,mBAAmB,EAAE,MAAM,SAAS,CAAC;AAC9C,OAAO,EAAE,kBAAkB,EAAE,MAAM,aAAa,CAAC;AAEjD,sGAAsG;AACtG,EAAE;AACF,iBAAiB;AACjB,2CAA2C;AAC3C,4BAA4B;AAC5B,EAAE;AACF,kCAAkC;AAClC,EAAE;AACF,wIAAwI;AACxI,EAAE;AAEF,MAAM,OAAO,GAAG,wCAAwC,CAAC;AACzD,MAAM,MAAM,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAEzC;;GAEG;AACH,SAAS,qBAAqB,CAC5B,MAAyB,EACzB,QAAiB,EACjB,UAAmB;IAEnB,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAC7C,IAAI,CAAC,QAAQ,EAAE;QACb,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,sCAAsC,CAAC,CAAC;KACnE;IAED,MAAM,eAAe,GAA2B;QAC9C,QAAQ;QACR,aAAa,EAAE,kBAAkB;KAClC,CAAC;IAEF,IAAI,QAAQ,EAAE;QACZ,eAAe,CAAC,SAAS,GAAG,QAAQ,CAAC;KACtC;IACD,IAAI,UAAU,EAAE;QACd,eAAe,CAAC,UAAU,GAAG,UAAU,CAAC;KACzC;IACD,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,eAAe,CAAC,CAAC;IAEnD,wIAAwI;IACxI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE;QAClC,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;KACpE;IACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE;QAChC,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;KAClE;IAED,OAAO;QACL,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,KAAK,CAAC,QAAQ,EAAE,EAAE;QAC3D,MAAM,EAAE,KAAK;QACb,OAAO,EAAE,iBAAiB,CAAC;YACzB,MAAM,EAAE,kBAAkB;YAC1B,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe;SACpC,CAAC;KACH,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,SAAS,GAAQ;IAC5B,KAAK,CAAC,WAAW,CAAC,EAAE,MAAM,EAAE;QAC1B,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,EAAE;YACb,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,mDAAmD,CAAC,CAAC;YAC3E,OAAO,KAAK,CAAC;SACd;QACD,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;QACxB,MAAM,MAAM,GAAG,OAAO,CACpB,GAAG,CAAC,iBAAiB,IAAI,GAAG,CAAC,eAAe,IAAI,GAAG,CAAC,0BAA0B,CAC/E,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,CAAC,IAAI,CACT,GAAG,OAAO,wHAAwH,CACnI,CAAC;SACH;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,KAAK,CAAC,QAAQ,CACZ,aAA+B,EAC/B,kBAAmC,EAAE;QAErC,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,aAAa,CAAC;QAEvE,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,OAAO,CACZ,GAAG,OAAO,uHAAuH,CAClI,CAAC;SACH;QAED,MAAM,CAAC,IAAI,CACT;YACE,GAAG,OAAO,GAAG;YACb,0EAA0E;YAC1E,qBAAqB,OAAO,CAAC,GAAG,CAAC,iBAAiB,GAAG;YACrD,gCAAgC;YAChC,wCAAwC;SACzC,CAAC,IAAI,CAAC,GAAG,CAAC,CACZ,CAAC;QAEF,MAAM,OAAO,GAAG,qBAAqB,iBACnC,WAAW,EAAE,eAAe,CAAC,WAAW,IACrC,qBAAqB,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC,EAGtD,CAAC;QAEH,OAAO,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC;YAC9B,+EAA+E;YAC/E,uGAAuG;YACvG,kBAAkB,EAAE,KAAK;SAC1B,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG,MAAM,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACrE,OAAO,CAAC,aAAa,IAAI,aAAa,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC;IAC9D,CAAC;CACF,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport https from \"https\";\nimport {\n  PipelineRequestOptions,\n  createHttpHeaders,\n  createPipelineRequest,\n} from \"@azure/core-rest-pipeline\";\nimport { AccessToken, GetTokenOptions } from \"@azure/core-auth\";\nimport { credentialLogger } from \"../../util/logging\";\nimport { MSI, MSIConfiguration } from \"./models\";\nimport { mapScopesToResource } from \"./utils\";\nimport { azureFabricVersion } from \"./constants\";\n\n// This MSI can be easily tested by deploying a container to Azure Service Fabric with the Dockerfile:\n//\n//   FROM node:12\n//   RUN wget https://host.any/path/bash.sh\n//   CMD [\"bash\", \"bash.sh\"]\n//\n// Where the bash script contains:\n//\n//   curl --insecure $IDENTITY_ENDPOINT'?api-version=2019-07-01-preview&resource=https://vault.azure.net/' -H \"Secret: $IDENTITY_HEADER\"\n//\n\nconst msiName = \"ManagedIdentityCredential - Fabric MSI\";\nconst logger = credentialLogger(msiName);\n\n/**\n * Generates the options used on the request for an access token.\n */\nfunction prepareRequestOptions(\n  scopes: string | string[],\n  clientId?: string,\n  resourceId?: string\n): PipelineRequestOptions {\n  const resource = mapScopesToResource(scopes);\n  if (!resource) {\n    throw new Error(`${msiName}: Multiple scopes are not supported.`);\n  }\n\n  const queryParameters: Record<string, string> = {\n    resource,\n    \"api-version\": azureFabricVersion,\n  };\n\n  if (clientId) {\n    queryParameters.client_id = clientId;\n  }\n  if (resourceId) {\n    queryParameters.msi_res_id = resourceId;\n  }\n  const query = new URLSearchParams(queryParameters);\n\n  // This error should not bubble up, since we verify that this environment variable is defined in the isAvailable() method defined below.\n  if (!process.env.IDENTITY_ENDPOINT) {\n    throw new Error(\"Missing environment variable: IDENTITY_ENDPOINT\");\n  }\n  if (!process.env.IDENTITY_HEADER) {\n    throw new Error(\"Missing environment variable: IDENTITY_HEADER\");\n  }\n\n  return {\n    url: `${process.env.IDENTITY_ENDPOINT}?${query.toString()}`,\n    method: \"GET\",\n    headers: createHttpHeaders({\n      Accept: \"application/json\",\n      secret: process.env.IDENTITY_HEADER,\n    }),\n  };\n}\n\n/**\n * Defines how to determine whether the Azure Service Fabric MSI is available, and also how to retrieve a token from the Azure Service Fabric MSI.\n */\nexport const fabricMsi: MSI = {\n  async isAvailable({ scopes }): Promise<boolean> {\n    const resource = mapScopesToResource(scopes);\n    if (!resource) {\n      logger.info(`${msiName}: Unavailable. Multiple scopes are not supported.`);\n      return false;\n    }\n    const env = process.env;\n    const result = Boolean(\n      env.IDENTITY_ENDPOINT && env.IDENTITY_HEADER && env.IDENTITY_SERVER_THUMBPRINT\n    );\n    if (!result) {\n      logger.info(\n        `${msiName}: Unavailable. The environment variables needed are: IDENTITY_ENDPOINT, IDENTITY_HEADER and IDENTITY_SERVER_THUMBPRINT`\n      );\n    }\n    return result;\n  },\n  async getToken(\n    configuration: MSIConfiguration,\n    getTokenOptions: GetTokenOptions = {}\n  ): Promise<AccessToken | null> {\n    const { scopes, identityClient, clientId, resourceId } = configuration;\n\n    if (resourceId) {\n      logger.warning(\n        `${msiName}: user defined managed Identity by resource Id is not supported. Argument resourceId might be ignored by the service.`\n      );\n    }\n\n    logger.info(\n      [\n        `${msiName}:`,\n        \"Using the endpoint and the secret coming from the environment variables:\",\n        `IDENTITY_ENDPOINT=${process.env.IDENTITY_ENDPOINT},`,\n        \"IDENTITY_HEADER=[REDACTED] and\",\n        \"IDENTITY_SERVER_THUMBPRINT=[REDACTED].\",\n      ].join(\" \")\n    );\n\n    const request = createPipelineRequest({\n      abortSignal: getTokenOptions.abortSignal,\n      ...prepareRequestOptions(scopes, clientId, resourceId),\n      // The service fabric MSI endpoint will be HTTPS (however, the certificate will be self-signed).\n      // allowInsecureConnection: true\n    });\n\n    request.agent = new https.Agent({\n      // This is necessary because Service Fabric provides a self-signed certificate.\n      // The alternative path is to verify the certificate using the IDENTITY_SERVER_THUMBPRINT env variable.\n      rejectUnauthorized: false,\n    });\n\n    const tokenResponse = await identityClient.sendTokenRequest(request);\n    return (tokenResponse && tokenResponse.accessToken) || null;\n  },\n};\n"]}