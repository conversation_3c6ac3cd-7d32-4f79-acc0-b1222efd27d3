{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/generated/models/index.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreHttp from \"@azure/core-http\";\n\n/**\n * A model definition and metadata for that model.\n */\nexport interface DigitalTwinsModelData {\n  /**\n   * A language map that contains the localized display names as specified in the model definition.\n   */\n  displayName?: { [propertyName: string]: string };\n  /**\n   * A language map that contains the localized descriptions as specified in the model definition.\n   */\n  description?: { [propertyName: string]: string };\n  /**\n   * The id of the model as specified in the model definition.\n   */\n  id: string;\n  /**\n   * The time the model was uploaded to the service.\n   */\n  uploadTime?: Date;\n  /**\n   * Indicates if the model is decommissioned. Decommissioned models cannot be referenced by newly created digital twins.\n   */\n  decommissioned?: boolean;\n  /**\n   * The model definition.\n   */\n  model?: any;\n}\n\n/**\n * Error response.\n */\nexport interface ErrorResponse {\n  /**\n   * The error details.\n   */\n  error?: ErrorModel;\n}\n\n/**\n * Error definition.\n */\nexport interface ErrorModel {\n  /**\n   * Service specific error code which serves as the substatus for the HTTP error code.\n   */\n  readonly code?: string;\n  /**\n   * A human-readable representation of the error.\n   */\n  readonly message?: string;\n  /**\n   * Internal error details.\n   */\n  readonly details?: ErrorModel[];\n  /**\n   * An object containing more specific information than the current object about the error.\n   */\n  innererror?: InnerError;\n}\n\n/**\n * A more specific error description than was provided by the containing error.\n */\nexport interface InnerError {\n  /**\n   * A more specific error code than was provided by the containing error.\n   */\n  code?: string;\n  /**\n   * An object containing more specific information than the current object about the error.\n   */\n  innererror?: InnerError;\n}\n\n/**\n * A collection of DigitalTwinsModelData objects.\n */\nexport interface PagedDigitalTwinsModelDataCollection {\n  /**\n   * The DigitalTwinsModelData objects.\n   */\n  value?: DigitalTwinsModelData[];\n  /**\n   * A URI to retrieve the next page of objects.\n   */\n  nextLink?: string;\n}\n\n/**\n * A query specification containing either a query statement or a continuation token from a previous query result.\n */\nexport interface QuerySpecification {\n  /**\n   * The query to execute. This value is ignored if a continuation token is provided.\n   */\n  query?: string;\n  /**\n   * A token which is used to retrieve the next set of results from a previous query.\n   */\n  continuationToken?: string;\n}\n\n/**\n * The results of a query operation and an optional continuation token.\n */\nexport interface QueryResult {\n  /**\n   * The query results.\n   */\n  value?: any[];\n  /**\n   * A token which can be used to construct a new QuerySpecification to retrieve the next set of results.\n   */\n  continuationToken?: string;\n}\n\n/**\n * A collection of relationships which relate digital twins together.\n */\nexport interface RelationshipCollection {\n  /**\n   * The relationship objects.\n   */\n  value?: any[];\n  /**\n   * A URI to retrieve the next page of objects.\n   */\n  nextLink?: string;\n}\n\n/**\n * A collection of incoming relationships which relate digital twins together.\n */\nexport interface IncomingRelationshipCollection {\n  value?: IncomingRelationship[];\n  /**\n   * A URI to retrieve the next page of objects.\n   */\n  nextLink?: string;\n}\n\n/**\n * An incoming relationship.\n */\nexport interface IncomingRelationship {\n  /**\n   * A user-provided string representing the id of this relationship, unique in the context of the source digital twin, i.e. sourceId + relationshipId is unique in the context of the service.\n   */\n  relationshipId?: string;\n  /**\n   * The id of the source digital twin.\n   */\n  sourceId?: string;\n  /**\n   * The name of the relationship.\n   */\n  relationshipName?: string;\n  /**\n   * Link to the relationship, to be used for deletion.\n   */\n  relationshipLink?: string;\n}\n\n/**\n * A collection of EventRoute objects.\n */\nexport interface EventRouteCollection {\n  /**\n   * The EventRoute objects.\n   */\n  value?: EventRoute[];\n  /**\n   * A URI to retrieve the next page of results.\n   */\n  nextLink?: string;\n}\n\n/**\n * A route which directs notification and telemetry events to an endpoint. Endpoints are a destination outside of Azure Digital Twins such as an EventHub.\n */\nexport interface EventRoute {\n  /**\n   * The id of the event route.\n   */\n  readonly id?: string;\n  /**\n   * The name of the endpoint this event route is bound to.\n   */\n  endpointName: string;\n  /**\n   * An expression which describes the events which are routed to the endpoint.\n   */\n  filter: string;\n}\n\n/**\n * Defines headers for Query_queryTwins operation.\n */\nexport interface QueryQueryTwinsHeaders {\n  /**\n   * The query charge.\n   */\n  queryCharge?: number;\n}\n\n/**\n * Defines headers for DigitalTwins_getById operation.\n */\nexport interface DigitalTwinsGetByIdHeaders {\n  /**\n   * Weak Etag.\n   */\n  etag?: string;\n}\n\n/**\n * Defines headers for DigitalTwins_add operation.\n */\nexport interface DigitalTwinsAddHeaders {\n  /**\n   * Weak Etag.\n   */\n  etag?: string;\n}\n\n/**\n * Defines headers for DigitalTwins_update operation.\n */\nexport interface DigitalTwinsUpdateHeaders {\n  /**\n   * Weak Etag.\n   */\n  etag?: string;\n}\n\n/**\n * Defines headers for DigitalTwins_getRelationshipById operation.\n */\nexport interface DigitalTwinsGetRelationshipByIdHeaders {\n  /**\n   * Weak Etag.\n   */\n  etag?: string;\n}\n\n/**\n * Defines headers for DigitalTwins_addRelationship operation.\n */\nexport interface DigitalTwinsAddRelationshipHeaders {\n  /**\n   * Weak Etag.\n   */\n  etag?: string;\n}\n\n/**\n * Defines headers for DigitalTwins_updateRelationship operation.\n */\nexport interface DigitalTwinsUpdateRelationshipHeaders {\n  /**\n   * Weak Etag.\n   */\n  etag?: string;\n}\n\n/**\n * Defines headers for DigitalTwins_getComponent operation.\n */\nexport interface DigitalTwinsGetComponentHeaders {\n  /**\n   * Weak Etag.\n   */\n  etag?: string;\n}\n\n/**\n * Defines headers for DigitalTwins_updateComponent operation.\n */\nexport interface DigitalTwinsUpdateComponentHeaders {\n  /**\n   * Weak Etag.\n   */\n  etag?: string;\n}\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinModelsAddOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * An array of models to add.\n   */\n  models?: any[];\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n}\n\n/**\n * Contains response data for the add operation.\n */\nexport type DigitalTwinModelsAddResponse = DigitalTwinsModelData[] & {\n  /**\n   * The underlying HTTP response.\n   */\n  _response: coreHttp.HttpResponse & {\n    /**\n     * The response body as text (string format)\n     */\n    bodyAsText: string;\n\n    /**\n     * The response body as parsed JSON or XML\n     */\n    parsedBody: DigitalTwinsModelData[];\n  };\n};\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinModelsListOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n  /**\n   * The set of the models which will have their dependencies retrieved. If omitted, all models are retrieved.\n   */\n  dependenciesFor?: string[];\n  /**\n   * When true the model definition will be returned as part of the result.\n   */\n  includeModelDefinition?: boolean;\n  /**\n   * The maximum number of items to retrieve per request. The server may choose to return less than the requested number.\n   */\n  maxItemsPerPage?: number;\n}\n\n/**\n * Contains response data for the list operation.\n */\nexport type DigitalTwinModelsListResponse = PagedDigitalTwinsModelDataCollection & {\n  /**\n   * The underlying HTTP response.\n   */\n  _response: coreHttp.HttpResponse & {\n    /**\n     * The response body as text (string format)\n     */\n    bodyAsText: string;\n\n    /**\n     * The response body as parsed JSON or XML\n     */\n    parsedBody: PagedDigitalTwinsModelDataCollection;\n  };\n};\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinModelsGetByIdOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n  /**\n   * When true the model definition will be returned as part of the result.\n   */\n  includeModelDefinition?: boolean;\n}\n\n/**\n * Contains response data for the getById operation.\n */\nexport type DigitalTwinModelsGetByIdResponse = DigitalTwinsModelData & {\n  /**\n   * The underlying HTTP response.\n   */\n  _response: coreHttp.HttpResponse & {\n    /**\n     * The response body as text (string format)\n     */\n    bodyAsText: string;\n\n    /**\n     * The response body as parsed JSON or XML\n     */\n    parsedBody: DigitalTwinsModelData;\n  };\n};\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinModelsUpdateOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n}\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinModelsDeleteOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n}\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinModelsListNextOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n  /**\n   * The set of the models which will have their dependencies retrieved. If omitted, all models are retrieved.\n   */\n  dependenciesFor?: string[];\n  /**\n   * When true the model definition will be returned as part of the result.\n   */\n  includeModelDefinition?: boolean;\n  /**\n   * The maximum number of items to retrieve per request. The server may choose to return less than the requested number.\n   */\n  maxItemsPerPage?: number;\n}\n\n/**\n * Contains response data for the listNext operation.\n */\nexport type DigitalTwinModelsListNextResponse = PagedDigitalTwinsModelDataCollection & {\n  /**\n   * The underlying HTTP response.\n   */\n  _response: coreHttp.HttpResponse & {\n    /**\n     * The response body as text (string format)\n     */\n    bodyAsText: string;\n\n    /**\n     * The response body as parsed JSON or XML\n     */\n    parsedBody: PagedDigitalTwinsModelDataCollection;\n  };\n};\n\n/**\n * Optional parameters.\n */\nexport interface QueryQueryTwinsOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n  /**\n   * The maximum number of items to retrieve per request. The server may choose to return less than the requested number.\n   */\n  maxItemsPerPage?: number;\n}\n\n/**\n * Contains response data for the queryTwins operation.\n */\nexport type QueryQueryTwinsResponse = QueryQueryTwinsHeaders &\n  QueryResult & {\n    /**\n     * The underlying HTTP response.\n     */\n    _response: coreHttp.HttpResponse & {\n      /**\n       * The response body as text (string format)\n       */\n      bodyAsText: string;\n\n      /**\n       * The response body as parsed JSON or XML\n       */\n      parsedBody: QueryResult;\n      /**\n       * The parsed HTTP response headers.\n       */\n      parsedHeaders: QueryQueryTwinsHeaders;\n    };\n  };\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinsGetByIdOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n}\n\n/**\n * Contains response data for the getById operation.\n */\nexport type DigitalTwinsGetByIdResponse = DigitalTwinsGetByIdHeaders & {\n  /**\n   * The parsed response body.\n   */\n  body: any;\n\n  /**\n   * The underlying HTTP response.\n   */\n  _response: coreHttp.HttpResponse & {\n    /**\n     * The response body as text (string format)\n     */\n    bodyAsText: string;\n\n    /**\n     * The response body as parsed JSON or XML\n     */\n    parsedBody: any;\n    /**\n     * The parsed HTTP response headers.\n     */\n    parsedHeaders: DigitalTwinsGetByIdHeaders;\n  };\n};\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinsAddOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n  /**\n   * Only perform the operation if the entity does not already exist.\n   */\n  ifNoneMatch?: string;\n}\n\n/**\n * Contains response data for the add operation.\n */\nexport type DigitalTwinsAddResponse = DigitalTwinsAddHeaders & {\n  /**\n   * The parsed response body.\n   */\n  body: any;\n\n  /**\n   * The underlying HTTP response.\n   */\n  _response: coreHttp.HttpResponse & {\n    /**\n     * The response body as text (string format)\n     */\n    bodyAsText: string;\n\n    /**\n     * The response body as parsed JSON or XML\n     */\n    parsedBody: any;\n    /**\n     * The parsed HTTP response headers.\n     */\n    parsedHeaders: DigitalTwinsAddHeaders;\n  };\n};\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinsDeleteOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n  /**\n   * Only perform the operation if the entity's etag matches one of the etags provided or * is provided.\n   */\n  ifMatch?: string;\n}\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinsUpdateOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n  /**\n   * Only perform the operation if the entity's etag matches one of the etags provided or * is provided.\n   */\n  ifMatch?: string;\n}\n\n/**\n * Contains response data for the update operation.\n */\nexport type DigitalTwinsUpdateResponse = DigitalTwinsUpdateHeaders & {\n  /**\n   * The underlying HTTP response.\n   */\n  _response: coreHttp.HttpResponse & {\n    /**\n     * The parsed HTTP response headers.\n     */\n    parsedHeaders: DigitalTwinsUpdateHeaders;\n  };\n};\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinsGetRelationshipByIdOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n}\n\n/**\n * Contains response data for the getRelationshipById operation.\n */\nexport type DigitalTwinsGetRelationshipByIdResponse = DigitalTwinsGetRelationshipByIdHeaders & {\n  /**\n   * The parsed response body.\n   */\n  body: any;\n\n  /**\n   * The underlying HTTP response.\n   */\n  _response: coreHttp.HttpResponse & {\n    /**\n     * The response body as text (string format)\n     */\n    bodyAsText: string;\n\n    /**\n     * The response body as parsed JSON or XML\n     */\n    parsedBody: any;\n    /**\n     * The parsed HTTP response headers.\n     */\n    parsedHeaders: DigitalTwinsGetRelationshipByIdHeaders;\n  };\n};\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinsAddRelationshipOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n  /**\n   * Only perform the operation if the entity does not already exist.\n   */\n  ifNoneMatch?: string;\n}\n\n/**\n * Contains response data for the addRelationship operation.\n */\nexport type DigitalTwinsAddRelationshipResponse = DigitalTwinsAddRelationshipHeaders & {\n  /**\n   * The parsed response body.\n   */\n  body: any;\n\n  /**\n   * The underlying HTTP response.\n   */\n  _response: coreHttp.HttpResponse & {\n    /**\n     * The response body as text (string format)\n     */\n    bodyAsText: string;\n\n    /**\n     * The response body as parsed JSON or XML\n     */\n    parsedBody: any;\n    /**\n     * The parsed HTTP response headers.\n     */\n    parsedHeaders: DigitalTwinsAddRelationshipHeaders;\n  };\n};\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinsDeleteRelationshipOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n  /**\n   * Only perform the operation if the entity's etag matches one of the etags provided or * is provided.\n   */\n  ifMatch?: string;\n}\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinsUpdateRelationshipOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n  /**\n   * Only perform the operation if the entity's etag matches one of the etags provided or * is provided.\n   */\n  ifMatch?: string;\n}\n\n/**\n * Contains response data for the updateRelationship operation.\n */\nexport type DigitalTwinsUpdateRelationshipResponse = DigitalTwinsUpdateRelationshipHeaders & {\n  /**\n   * The underlying HTTP response.\n   */\n  _response: coreHttp.HttpResponse & {\n    /**\n     * The parsed HTTP response headers.\n     */\n    parsedHeaders: DigitalTwinsUpdateRelationshipHeaders;\n  };\n};\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinsListRelationshipsOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n  /**\n   * The name of the relationship.\n   */\n  relationshipName?: string;\n}\n\n/**\n * Contains response data for the listRelationships operation.\n */\nexport type DigitalTwinsListRelationshipsResponse = RelationshipCollection & {\n  /**\n   * The underlying HTTP response.\n   */\n  _response: coreHttp.HttpResponse & {\n    /**\n     * The response body as text (string format)\n     */\n    bodyAsText: string;\n\n    /**\n     * The response body as parsed JSON or XML\n     */\n    parsedBody: RelationshipCollection;\n  };\n};\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinsListIncomingRelationshipsOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n}\n\n/**\n * Contains response data for the listIncomingRelationships operation.\n */\nexport type DigitalTwinsListIncomingRelationshipsResponse = IncomingRelationshipCollection & {\n  /**\n   * The underlying HTTP response.\n   */\n  _response: coreHttp.HttpResponse & {\n    /**\n     * The response body as text (string format)\n     */\n    bodyAsText: string;\n\n    /**\n     * The response body as parsed JSON or XML\n     */\n    parsedBody: IncomingRelationshipCollection;\n  };\n};\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinsSendTelemetryOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n  /**\n   * An RFC 3339 timestamp that identifies the time the telemetry was measured.\n   */\n  telemetrySourceTime?: string;\n}\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinsSendComponentTelemetryOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n  /**\n   * An RFC 3339 timestamp that identifies the time the telemetry was measured.\n   */\n  telemetrySourceTime?: string;\n}\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinsGetComponentOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n}\n\n/**\n * Contains response data for the getComponent operation.\n */\nexport type DigitalTwinsGetComponentResponse = DigitalTwinsGetComponentHeaders & {\n  /**\n   * The parsed response body.\n   */\n  body: any;\n\n  /**\n   * The underlying HTTP response.\n   */\n  _response: coreHttp.HttpResponse & {\n    /**\n     * The response body as text (string format)\n     */\n    bodyAsText: string;\n\n    /**\n     * The response body as parsed JSON or XML\n     */\n    parsedBody: any;\n    /**\n     * The parsed HTTP response headers.\n     */\n    parsedHeaders: DigitalTwinsGetComponentHeaders;\n  };\n};\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinsUpdateComponentOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n  /**\n   * Only perform the operation if the entity's etag matches one of the etags provided or * is provided.\n   */\n  ifMatch?: string;\n}\n\n/**\n * Contains response data for the updateComponent operation.\n */\nexport type DigitalTwinsUpdateComponentResponse = DigitalTwinsUpdateComponentHeaders & {\n  /**\n   * The underlying HTTP response.\n   */\n  _response: coreHttp.HttpResponse & {\n    /**\n     * The parsed HTTP response headers.\n     */\n    parsedHeaders: DigitalTwinsUpdateComponentHeaders;\n  };\n};\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinsListRelationshipsNextOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n  /**\n   * The name of the relationship.\n   */\n  relationshipName?: string;\n}\n\n/**\n * Contains response data for the listRelationshipsNext operation.\n */\nexport type DigitalTwinsListRelationshipsNextResponse = RelationshipCollection & {\n  /**\n   * The underlying HTTP response.\n   */\n  _response: coreHttp.HttpResponse & {\n    /**\n     * The response body as text (string format)\n     */\n    bodyAsText: string;\n\n    /**\n     * The response body as parsed JSON or XML\n     */\n    parsedBody: RelationshipCollection;\n  };\n};\n\n/**\n * Optional parameters.\n */\nexport interface DigitalTwinsListIncomingRelationshipsNextOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n}\n\n/**\n * Contains response data for the listIncomingRelationshipsNext operation.\n */\nexport type DigitalTwinsListIncomingRelationshipsNextResponse = IncomingRelationshipCollection & {\n  /**\n   * The underlying HTTP response.\n   */\n  _response: coreHttp.HttpResponse & {\n    /**\n     * The response body as text (string format)\n     */\n    bodyAsText: string;\n\n    /**\n     * The response body as parsed JSON or XML\n     */\n    parsedBody: IncomingRelationshipCollection;\n  };\n};\n\n/**\n * Optional parameters.\n */\nexport interface EventRoutesListOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n  /**\n   * The maximum number of items to retrieve per request. The server may choose to return less than the requested number.\n   */\n  maxItemsPerPage?: number;\n}\n\n/**\n * Contains response data for the list operation.\n */\nexport type EventRoutesListResponse = EventRouteCollection & {\n  /**\n   * The underlying HTTP response.\n   */\n  _response: coreHttp.HttpResponse & {\n    /**\n     * The response body as text (string format)\n     */\n    bodyAsText: string;\n\n    /**\n     * The response body as parsed JSON or XML\n     */\n    parsedBody: EventRouteCollection;\n  };\n};\n\n/**\n * Optional parameters.\n */\nexport interface EventRoutesGetByIdOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n}\n\n/**\n * Contains response data for the getById operation.\n */\nexport type EventRoutesGetByIdResponse = EventRoute & {\n  /**\n   * The underlying HTTP response.\n   */\n  _response: coreHttp.HttpResponse & {\n    /**\n     * The response body as text (string format)\n     */\n    bodyAsText: string;\n\n    /**\n     * The response body as parsed JSON or XML\n     */\n    parsedBody: EventRoute;\n  };\n};\n\n/**\n * Optional parameters.\n */\nexport interface EventRoutesAddOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n  /**\n   * The event route data\n   */\n  eventRoute?: EventRoute;\n}\n\n/**\n * Optional parameters.\n */\nexport interface EventRoutesDeleteOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n}\n\n/**\n * Optional parameters.\n */\nexport interface EventRoutesListNextOptionalParams\n  extends coreHttp.OperationOptions {\n  /**\n   * Identifies the request in a distributed tracing system.\n   */\n  traceparent?: string;\n  /**\n   * Provides vendor-specific trace identification information and is a companion to traceparent.\n   */\n  tracestate?: string;\n  /**\n   * The maximum number of items to retrieve per request. The server may choose to return less than the requested number.\n   */\n  maxItemsPerPage?: number;\n}\n\n/**\n * Contains response data for the listNext operation.\n */\nexport type EventRoutesListNextResponse = EventRouteCollection & {\n  /**\n   * The underlying HTTP response.\n   */\n  _response: coreHttp.HttpResponse & {\n    /**\n     * The response body as text (string format)\n     */\n    bodyAsText: string;\n\n    /**\n     * The response body as parsed JSON or XML\n     */\n    parsedBody: EventRouteCollection;\n  };\n};\n\n/**\n * Optional parameters.\n */\nexport interface AzureDigitalTwinsAPIOptionalParams\n  extends coreHttp.ServiceClientOptions {\n  /**\n   * server parameter\n   */\n  $host?: string;\n  /**\n   * Api Version\n   */\n  apiVersion?: string;\n  /**\n   * Overrides client endpoint.\n   */\n  endpoint?: string;\n}\n"]}