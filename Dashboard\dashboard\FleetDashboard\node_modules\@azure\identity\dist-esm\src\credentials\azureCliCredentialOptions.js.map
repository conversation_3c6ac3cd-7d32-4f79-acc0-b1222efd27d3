{"version": 3, "file": "azureCliCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/azureCliCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions\";\n\n/**\n * Options for the {@link AzureCliCredential}\n */\nexport interface AzureCliCredentialOptions extends MultiTenantTokenCredentialOptions {\n  /**\n   * Allows specifying a tenant ID\n   */\n  tenantId?: string;\n}\n"]}