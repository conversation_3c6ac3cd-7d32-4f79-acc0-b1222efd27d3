{"version": 3, "file": "uuidUtils.js", "sourceRoot": "", "sources": ["../../src/uuidUtils.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;AAElC,OAAO,EAAE,UAAU,IAAI,YAAY,EAAE,MAAM,QAAQ,CAAC;AAUpD,6FAA6F;AAC7F,MAAM,YAAY,GAChB,OAAO,CAAA,MAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,MAAM,0CAAE,UAAU,CAAA,KAAK,UAAU;IAClD,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;IACtD,CAAC,CAAC,YAAY,CAAC;AAEnB;;;;GAIG;AACH,MAAM,UAAU,UAAU;IACxB,OAAO,YAAY,EAAE,CAAC;AACxB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { randomUUID as v4RandomUUID } from \"crypto\";\n\ninterface Crypto {\n  randomUUID(): string;\n}\n\ndeclare const globalThis: {\n  crypto: Crypto;\n};\n\n// NOTE: This is a workaround until we can use `globalThis.crypto.randomUUID` in Node.js 19+.\nconst uuidFunction =\n  typeof globalThis?.crypto?.randomUUID === \"function\"\n    ? globalThis.crypto.randomUUID.bind(globalThis.crypto)\n    : v4RandomUUID;\n\n/**\n * Generated Universally Unique Identifier\n *\n * @returns RFC4122 v4 UUID.\n */\nexport function randomUUID(): string {\n  return uuidFunction();\n}\n"]}