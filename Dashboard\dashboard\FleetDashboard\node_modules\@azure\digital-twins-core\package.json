{"name": "@azure/digital-twins-core", "version": "1.1.0", "description": "An isomorphic client library for Azure Digital Twins", "sdk-type": "client", "author": "Microsoft Corporation", "license": "MIT", "main": "dist/index.js", "module": "dist-esm/src/index.js", "types": "types/digital-twins-core.d.ts", "engines": {"node": ">=12.0.0"}, "scripts": {"audit": "node ../../../common/scripts/rush-audit.js && rimraf node_modules package-lock.json && npm i --package-lock-only 2>&1 && npm audit", "build:browser": "tsc -p . && dev-tool run bundle", "build:node": "tsc -p . && dev-tool run bundle && npm run extract-api", "build": "npm run clean && tsc -p . && dev-tool run bundle && api-extractor run --local", "build:test:browser": "tsc -p . && dev-tool run bundle", "build:test:node": "tsc -p . && dev-tool run bundle", "build:test": "tsc -p . && dev-tool run bundle", "build:samples": "echo Obsolete.", "check-format": "prettier --list-different --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.ts\" \"test/**/*.ts\" \"samples-dev/**/*.ts\" \"*.{js,json}\"", "clean": "rimraf dist dist-esm dist-browser test-dist test-browser types *.tgz *.log", "coverage": "nyc --reporter=lcov --exclude-after-remap=false mocha -t 120000 dist-test/index.node.js --reporter ../../../common/tools/mocha-multi-reporter.js", "execute:js-samples": "node ../../../common/scripts/run-samples.js samples/javascript/", "execute:samples": "dev-tool samples run samples-dev", "extract-api": "tsc -p . && api-extractor run --local", "format": "prettier --write --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.ts\" \"test/**/*.ts\" \"samples-dev/**/*.ts\" \"*.{js,json}\"", "integration-test:browser": "npm run build:test:browser && cross-env TEST_MODE=live karma start --single-run", "integration-test:node": "npm run build:test:node && nyc mocha -r esm --require source-map-support/register --reporter ../../../common/tools/mocha-multi-reporter.js --timeout 180000 --full-trace \"dist-esm/test/{,!(browser)/**/}*.spec.js\"", "integration-test": "npm run integration-test:node && npm run integration-test:browser", "lint:fix": "eslint package.json api-extractor.json README.md src test --ext .ts,.javascript,.js --fix --fix-type [problem,suggestion]", "lint": "eslint package.json api-extractor.json README.md src test --ext .ts,.javascript,.js", "pack": "npm pack 2>&1", "test:node": "npm run clean && npm run build:test:node && npm run unit-test:node", "test:browser": "npm run clean && npm run build:test:browser && npm run unit-test:browser", "test": "npm run clean && npm run build:test && npm run unit-test", "unit-test:browser": "karma start --single-run", "unit-test:node": "mocha -r esm --require ts-node/register --reporter ../../../common/tools/mocha-multi-reporter.js --timeout 180000 --full-trace \"test/{,!(browser)/**/}*.spec.ts\"", "unit-test": "npm run unit-test:node && npm run unit-test:browser"}, "files": ["dist/", "dist-esm/src/", "types/digital-twins-core.d.ts", "README.md", "LICENSE"], "keywords": ["azure", "iot", "digital-twins-core", "cloud", "twins", "node", "typescript", "browser", "isomorphic"], "repository": "github:Azure/azure-sdk-for-js", "homepage": "https://github.com/Azure/azure-sdk-for-js/tree/main/sdk/digitaltwins/digital-twins-core/", "bugs": {"url": "https://github.com/Azure/azure-sdk-for-js/issues"}, "dependencies": {"@azure/core-http": "^2.2.5", "@azure/core-paging": "^1.1.1", "@azure/core-tracing": "^1.0.0", "@azure/logger": "^1.0.0", "tslib": "^2.2.0"}, "devDependencies": {"@azure/dev-tool": "^1.0.0", "@azure/eslint-plugin-azure-sdk": "^3.0.0", "@azure/identity": "^2.0.1", "@azure-tools/test-credential": "^1.0.0", "@azure-tools/test-recorder": "^1.0.0", "@microsoft/api-extractor": "7.18.11", "@types/chai": "^4.1.6", "@types/mocha": "^7.0.2", "@types/node": "^12.0.0", "@types/sinon": "^9.0.4", "chai": "^4.2.0", "cross-env": "^7.0.2", "dotenv": "^8.2.0", "eslint": "^8.0.0", "inherits": "^2.0.3", "karma": "^6.2.0", "karma-chrome-launcher": "^3.0.0", "karma-coverage": "^2.0.0", "karma-edge-launcher": "^0.4.2", "karma-env-preprocessor": "^0.1.1", "karma-firefox-launcher": "^1.1.0", "karma-ie-launcher": "^1.0.0", "karma-json-preprocessor": "^0.3.3", "karma-json-to-file-reporter": "^1.0.1", "karma-junit-reporter": "^2.0.1", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-sourcemap-loader": "^0.3.8", "mocha": "^7.1.1", "mocha-junit-reporter": "^2.0.0", "nyc": "^15.0.0", "prettier": "^2.5.1", "rimraf": "^3.0.0", "sinon": "^9.0.2", "typescript": "~4.2.0", "util": "^0.12.1", "uuid": "^8.3.0", "@types/uuid": "^8.0.0"}, "sideEffects": false, "//metadata": {"constantPaths": [{"path": "src/generated/azureDigitalTwinsAPIContext.ts", "prefix": "packageVersion"}, {"path": "src/constants.ts", "prefix": "SDK_VERSION"}, {"path": "swagger/README.md", "prefix": "package-version"}]}, "//sampleConfiguration": {"productName": "Azure Digital Twins", "productSlugs": ["azure", "azure-digital-twins"], "extraFiles": {"./samples-dev/dtdl": ["javascript/dtdl", "typescript/src/dtdl"]}, "requiredResources": {"Azure Digital Twins instance": "https://docs.microsoft.com/azure/digital-twins/how-to-set-up-instance-portal"}, "skipFolder": true}}