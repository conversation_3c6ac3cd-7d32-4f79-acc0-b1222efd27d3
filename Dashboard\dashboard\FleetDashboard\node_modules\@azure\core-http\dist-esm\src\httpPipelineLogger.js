// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
import { HttpPipelineLogLevel } from "./httpPipelineLogLevel";
/**
 * A HttpPipelineLogger that will send its logs to the console.
 */
export class ConsoleHttpPipelineLogger {
    /**
     * Create a new ConsoleHttpPipelineLogger.
     * @param minimumLogLevel - The log level threshold for what logs will be logged.
     */
    constructor(minimumLogLevel) {
        this.minimumLogLevel = minimumLogLevel;
    }
    /**
     * Log the provided message.
     * @param logLevel - The HttpLogDetailLevel associated with this message.
     * @param message - The message to log.
     */
    log(logLevel, message) {
        const logMessage = `${HttpPipelineLogLevel[logLevel]}: ${message}`;
        switch (logLevel) {
            case HttpPipelineLogLevel.ERROR:
                console.error(logMessage);
                break;
            case HttpPipelineLogLevel.WARNING:
                console.warn(logMessage);
                break;
            case HttpPipelineLogLevel.INFO:
                console.log(logMessage);
                break;
        }
    }
}
//# sourceMappingURL=httpPipelineLogger.js.map