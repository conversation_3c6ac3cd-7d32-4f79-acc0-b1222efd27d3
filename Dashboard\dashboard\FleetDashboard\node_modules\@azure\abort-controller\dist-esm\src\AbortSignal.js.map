{"version": 3, "file": "AbortSignal.js", "sourceRoot": "", "sources": ["../../src/AbortSignal.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,6CAA6C;AAI7C,MAAM,YAAY,GAAG,IAAI,OAAO,EAAqC,CAAC;AACtE,MAAM,UAAU,GAAG,IAAI,OAAO,EAAwB,CAAC;AA6BvD;;;;;;;;;;;;GAYG;AACH,MAAM,OAAO,WAAW;IACtB;QA2BA;;WAEG;QACI,YAAO,GAAiC,IAAI,CAAC;QA7BlD,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAC3B,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACH,IAAW,OAAO;QAChB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACzB,MAAM,IAAI,SAAS,CAAC,mDAAmD,CAAC,CAAC;SAC1E;QAED,OAAO,UAAU,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;IAC/B,CAAC;IAED;;;;OAIG;IACI,MAAM,KAAK,IAAI;QACpB,OAAO,IAAI,WAAW,EAAE,CAAC;IAC3B,CAAC;IAOD;;;;;OAKG;IACI,gBAAgB;IACrB,yCAAyC;IACzC,KAAc,EACd,QAAiD;QAEjD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC3B,MAAM,IAAI,SAAS,CAAC,mDAAmD,CAAC,CAAC;SAC1E;QAED,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;QAC1C,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC3B,CAAC;IAED;;;;;OAKG;IACI,mBAAmB;IACxB,yCAAyC;IACzC,KAAc,EACd,QAAiD;QAEjD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC3B,MAAM,IAAI,SAAS,CAAC,mDAAmD,CAAC,CAAC;SAC1E;QAED,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;QAE1C,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC1C,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACd,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC5B;IACH,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAAa;QACzB,MAAM,IAAI,KAAK,CACb,kHAAkH,CACnH,CAAC;IACJ,CAAC;CACF;AAED;;;;;;;;GAQG;AACH,wEAAwE;AACxE,MAAM,UAAU,WAAW,CAAC,MAAmB;IAC7C,IAAI,MAAM,CAAC,OAAO,EAAE;QAClB,OAAO;KACR;IAED,IAAI,MAAM,CAAC,OAAO,EAAE;QAClB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAC7B;IAED,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC;IAC5C,IAAI,SAAS,EAAE;QACb,uDAAuD;QACvD,6DAA6D;QAC7D,aAAa;QACb,SAAS,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YACrC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;KACJ;IAED,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAC/B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n// eslint-disable-next-line @typescript-eslint/triple-slash-reference\n/// <reference path=\"../shims-public.d.ts\" />\n\ntype AbortEventListener = (this: AbortSignalLike, ev?: any) => any;\n\nconst listenersMap = new WeakMap<AbortSignal, AbortEventListener[]>();\nconst abortedMap = new WeakMap<AbortSignal, boolean>();\n\n/**\n * Allows the request to be aborted upon firing of the \"abort\" event.\n * Compatible with the browser built-in AbortSignal and common polyfills.\n */\nexport interface AbortSignalLike {\n  /**\n   * Indicates if the signal has already been aborted.\n   */\n  readonly aborted: boolean;\n  /**\n   * Add new \"abort\" event listener, only support \"abort\" event.\n   */\n  addEventListener(\n    type: \"abort\",\n    listener: (this: AbortSignalLike, ev: any) => any,\n    options?: any\n  ): void;\n  /**\n   * Remove \"abort\" event listener, only support \"abort\" event.\n   */\n  removeEventListener(\n    type: \"abort\",\n    listener: (this: AbortSignalLike, ev: any) => any,\n    options?: any\n  ): void;\n}\n\n/**\n * An aborter instance implements AbortSignal interface, can abort HTTP requests.\n *\n * - Call AbortSignal.none to create a new AbortSignal instance that cannot be cancelled.\n * Use `AbortSignal.none` when you are required to pass a cancellation token but the operation\n * cannot or will not ever be cancelled.\n *\n * @example\n * Abort without timeout\n * ```ts\n * await doAsyncWork(AbortSignal.none);\n * ```\n */\nexport class AbortSignal implements AbortSignalLike {\n  constructor() {\n    listenersMap.set(this, []);\n    abortedMap.set(this, false);\n  }\n\n  /**\n   * Status of whether aborted or not.\n   *\n   * @readonly\n   */\n  public get aborted(): boolean {\n    if (!abortedMap.has(this)) {\n      throw new TypeError(\"Expected `this` to be an instance of AbortSignal.\");\n    }\n\n    return abortedMap.get(this)!;\n  }\n\n  /**\n   * Creates a new AbortSignal instance that will never be aborted.\n   *\n   * @readonly\n   */\n  public static get none(): AbortSignal {\n    return new AbortSignal();\n  }\n\n  /**\n   * onabort event listener.\n   */\n  public onabort: ((ev?: Event) => any) | null = null;\n\n  /**\n   * Added new \"abort\" event listener, only support \"abort\" event.\n   *\n   * @param _type - Only support \"abort\" event\n   * @param listener - The listener to be added\n   */\n  public addEventListener(\n    // tslint:disable-next-line:variable-name\n    _type: \"abort\",\n    listener: (this: AbortSignalLike, ev: any) => any\n  ): void {\n    if (!listenersMap.has(this)) {\n      throw new TypeError(\"Expected `this` to be an instance of AbortSignal.\");\n    }\n\n    const listeners = listenersMap.get(this)!;\n    listeners.push(listener);\n  }\n\n  /**\n   * Remove \"abort\" event listener, only support \"abort\" event.\n   *\n   * @param _type - Only support \"abort\" event\n   * @param listener - The listener to be removed\n   */\n  public removeEventListener(\n    // tslint:disable-next-line:variable-name\n    _type: \"abort\",\n    listener: (this: AbortSignalLike, ev: any) => any\n  ): void {\n    if (!listenersMap.has(this)) {\n      throw new TypeError(\"Expected `this` to be an instance of AbortSignal.\");\n    }\n\n    const listeners = listenersMap.get(this)!;\n\n    const index = listeners.indexOf(listener);\n    if (index > -1) {\n      listeners.splice(index, 1);\n    }\n  }\n\n  /**\n   * Dispatches a synthetic event to the AbortSignal.\n   */\n  dispatchEvent(_event: Event): boolean {\n    throw new Error(\n      \"This is a stub dispatchEvent implementation that should not be used.  It only exists for type-checking purposes.\"\n    );\n  }\n}\n\n/**\n * Helper to trigger an abort event immediately, the onabort and all abort event listeners will be triggered.\n * Will try to trigger abort event for all linked AbortSignal nodes.\n *\n * - If there is a timeout, the timer will be cancelled.\n * - If aborted is true, nothing will happen.\n *\n * @internal\n */\n// eslint-disable-next-line @azure/azure-sdk/ts-use-interface-parameters\nexport function abortSignal(signal: AbortSignal): void {\n  if (signal.aborted) {\n    return;\n  }\n\n  if (signal.onabort) {\n    signal.onabort.call(signal);\n  }\n\n  const listeners = listenersMap.get(signal)!;\n  if (listeners) {\n    // Create a copy of listeners so mutations to the array\n    // (e.g. via removeListener calls) don't affect the listeners\n    // we invoke.\n    listeners.slice().forEach((listener) => {\n      listener.call(signal, { type: \"abort\" });\n    });\n  }\n\n  abortedMap.set(signal, true);\n}\n"]}