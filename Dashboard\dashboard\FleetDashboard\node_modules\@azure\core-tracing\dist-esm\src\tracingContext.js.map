{"version": 3, "file": "tracingContext.js", "sourceRoot": "", "sources": ["../../src/tracingContext.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,gBAAgB;AAChB,MAAM,CAAC,MAAM,gBAAgB,GAAG;IAC9B,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,0BAA0B,CAAC;IAC5C,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,+BAA+B,CAAC;CACvD,CAAC;AAEF;;;;;;GAMG;AACH,MAAM,UAAU,oBAAoB,CAAC,UAAuC,EAAE;IAC5E,IAAI,OAAO,GAAmB,IAAI,kBAAkB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;IAC5E,IAAI,OAAO,CAAC,IAAI,EAAE;QAChB,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;KACjE;IACD,IAAI,OAAO,CAAC,SAAS,EAAE;QACrB,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;KAC3E;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,gBAAgB;AAChB,MAAM,OAAO,kBAAkB;IAE7B,YAAY,cAA+B;QACzC,IAAI,CAAC,WAAW;YACd,cAAc,YAAY,kBAAkB;gBAC1C,CAAC,CAAC,IAAI,GAAG,CAAkB,cAAc,CAAC,WAAW,CAAC;gBACtD,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC;IAClB,CAAC;IAED,QAAQ,CAAC,GAAW,EAAE,KAAc;QAClC,MAAM,UAAU,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAChD,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACvC,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,QAAQ,CAAC,GAAW;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,WAAW,CAAC,GAAW;QACrB,MAAM,UAAU,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAChD,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACnC,OAAO,UAAU,CAAC;IACpB,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { TracingContext, TracingSpan } from \"./interfaces\";\n\n/** @internal */\nexport const knownContextKeys = {\n  span: Symbol.for(\"@azure/core-tracing span\"),\n  namespace: Symbol.for(\"@azure/core-tracing namespace\"),\n};\n\n/**\n * Creates a new {@link TracingContext} with the given options.\n * @param options - A set of known keys that may be set on the context.\n * @returns A new {@link TracingContext} with the given options.\n *\n * @internal\n */\nexport function createTracingContext(options: CreateTracingContextOptions = {}): TracingContext {\n  let context: TracingContext = new TracingContextImpl(options.parentContext);\n  if (options.span) {\n    context = context.setValue(knownContextKeys.span, options.span);\n  }\n  if (options.namespace) {\n    context = context.setValue(knownContextKeys.namespace, options.namespace);\n  }\n  return context;\n}\n\n/** @internal */\nexport class TracingContextImpl implements TracingContext {\n  private _contextMap: Map<symbol, unknown>;\n  constructor(initialContext?: TracingContext) {\n    this._contextMap =\n      initialContext instanceof TracingContextImpl\n        ? new Map<symbol, unknown>(initialContext._contextMap)\n        : new Map();\n  }\n\n  setValue(key: symbol, value: unknown): TracingContext {\n    const newContext = new TracingContextImpl(this);\n    newContext._contextMap.set(key, value);\n    return newContext;\n  }\n\n  getValue(key: symbol): unknown {\n    return this._contextMap.get(key);\n  }\n\n  deleteValue(key: symbol): TracingContext {\n    const newContext = new TracingContextImpl(this);\n    newContext._contextMap.delete(key);\n    return newContext;\n  }\n}\n\n/**\n * Represents a set of items that can be set when creating a new {@link TracingContext}.\n */\nexport interface CreateTracingContextOptions {\n  /** The {@link parentContext} - the newly created context will contain all the values of the parent context unless overridden. */\n  parentContext?: TracingContext;\n  /** An initial span to set on the context. */\n  span?: TracingSpan;\n  /** The namespace to set on any child spans. */\n  namespace?: string;\n}\n"]}