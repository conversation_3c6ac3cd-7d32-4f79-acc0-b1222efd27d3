{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,KAAK,EAAgB,MAAM,SAAS,CAAC;AAC9C,OAAO,EAAE,sBAAsB,EAAiC,MAAM,0BAA0B,CAAC;AACjG,OAAO,EAAE,yBAAyB,EAAE,MAAM,UAAU,CAAC;AACrD,OAAO,EAAE,QAAQ,EAAiB,MAAM,UAAU,CAAC;AACnD,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,SAAS,CAAC;AACnD,OAAO,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,MAAM,UAAU,CAAC;AAChE,OAAO,EAAE,SAAS,EAAE,sBAAsB,EAAE,iBAAiB,EAAE,MAAM,cAAc,CAAC;AACpF,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nexport { isNode } from \"./isNode\";\nexport { delay, DelayOptions } from \"./delay\";\nexport { createAbortablePromise, CreateAbortablePromiseOptions } from \"./createAbortablePromise\";\nexport { getRandomIntegerInclusive } from \"./random\";\nexport { isObject, UnknownObject } from \"./object\";\nexport { isError, getErrorMessage } from \"./error\";\nexport { computeSha256Hash, computeSha256Hmac } from \"./sha256\";\nexport { isDefined, isObjectWithProperties, objectHasProperty } from \"./typeGuards\";\nexport { randomUUID } from \"./uuidUtils\";\n"]}