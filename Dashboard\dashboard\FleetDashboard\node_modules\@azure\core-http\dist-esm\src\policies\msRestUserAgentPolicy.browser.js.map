{"version": 3, "file": "msRestUserAgentPolicy.browser.js", "sourceRoot": "", "sources": ["../../../src/policies/msRestUserAgentPolicy.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAalC,MAAM,UAAU,sBAAsB;IACpC,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED,MAAM,UAAU,uBAAuB;IACrC,MAAM,SAAS,GAAG,IAAI,CAAC,SAAwB,CAAC;IAChD,MAAM,MAAM,GAAG;QACb,GAAG,EAAE,IAAI;QACT,KAAK,EAAE,CAAC,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;KAChE,CAAC;IAEF,OAAO,CAAC,MAAM,CAAC,CAAC;AAClB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/*\n * NOTE: When moving this file, please update \"browser\" section in package.json.\n */\n\nimport { TelemetryInfo } from \"./userAgentPolicy\";\n\ninterface NavigatorEx extends Navigator {\n  // oscpu is not yet standards-compliant, but can not be undefined in TypeScript 3.6.2\n  readonly oscpu: string;\n}\n\nexport function getDefaultUserAgentKey(): string {\n  return \"x-ms-useragent\";\n}\n\nexport function getPlatformSpecificData(): TelemetryInfo[] {\n  const navigator = self.navigator as NavigatorEx;\n  const osInfo = {\n    key: \"OS\",\n    value: (navigator.oscpu || navigator.platform).replace(\" \", \"\"),\n  };\n\n  return [osInfo];\n}\n"]}