/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import * as coreHttp from "@azure/core-http";
const packageName = "@azure/digital-twins-core";
const packageVersion = "1.1.0";
export class AzureDigitalTwinsAPIContext extends coreHttp.ServiceClient {
    /**
     * Initializes a new instance of the AzureDigitalTwinsAPIContext class.
     * @param options The parameter options
     */
    constructor(options) {
        // Initializing default values for options
        if (!options) {
            options = {};
        }
        if (!options.userAgent) {
            const defaultUserAgent = coreHttp.getDefaultUserAgentValue();
            options.userAgent = `${packageName}/${packageVersion} ${defaultUserAgent}`;
        }
        super(undefined, options);
        this.requestContentType = "application/json; charset=utf-8";
        this.baseUri =
            options.endpoint || "https://digitaltwins-name.digitaltwins.azure.net";
        // Assigning values to Constant parameters
        this.$host =
            options.$host || "https://digitaltwins-name.digitaltwins.azure.net";
        this.apiVersion = options.apiVersion || "2022-05-31";
    }
}
//# sourceMappingURL=azureDigitalTwinsAPIContext.js.map