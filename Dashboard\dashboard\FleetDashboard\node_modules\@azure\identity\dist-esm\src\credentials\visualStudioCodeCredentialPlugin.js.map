{"version": 3, "file": "visualStudioCodeCredentialPlugin.js", "sourceRoot": "", "sources": ["../../../src/credentials/visualStudioCodeCredentialPlugin.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * A function that searches for credentials in the Visual Studio Code credential store.\n *\n * @returns an array of credentials (username and password)\n * @internal\n */\nexport type VSCodeCredentialFinder = () => Promise<Array<{ account: string; password: string }>>;\n"]}