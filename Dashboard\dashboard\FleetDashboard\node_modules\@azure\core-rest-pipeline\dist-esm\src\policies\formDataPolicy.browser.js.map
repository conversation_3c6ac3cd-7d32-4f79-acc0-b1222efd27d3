{"version": 3, "file": "formDataPolicy.browser.js", "sourceRoot": "", "sources": ["../../../src/policies/formDataPolicy.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAKlC;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAEnD;;GAEG;AACH,MAAM,UAAU,cAAc;IAC5B,OAAO;QACL,IAAI,EAAE,kBAAkB;QACxB,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;YAC3D,IAAI,OAAO,CAAC,QAAQ,EAAE;gBACpB,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;gBAClC,MAAM,WAAW,GAAG,IAAI,QAAQ,EAAE,CAAC;gBACnC,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;oBAC3C,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;oBACpC,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;wBAC5B,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;4BAChC,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;yBACvC;qBACF;yBAAM;wBACL,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;qBACxC;iBACF;gBAED,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC;gBAC3B,OAAO,CAAC,QAAQ,GAAG,SAAS,CAAC;gBAC7B,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;gBACxD,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,CAAC,mCAAmC,CAAC,KAAK,CAAC,CAAC,EAAE;oBAClF,OAAO,CAAC,IAAI,GAAG,IAAI,eAAe,CAAC,WAAkB,CAAC,CAAC,QAAQ,EAAE,CAAC;iBACnE;qBAAM,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC3E,kEAAkE;oBAClE,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;iBACxC;aACF;YACD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces\";\nimport { PipelinePolicy } from \"../pipeline\";\n\n/**\n * The programmatic identifier of the formDataPolicy.\n */\nexport const formDataPolicyName = \"formDataPolicy\";\n\n/**\n * A policy that encodes FormData on the request into the body.\n */\nexport function formDataPolicy(): PipelinePolicy {\n  return {\n    name: formDataPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      if (request.formData) {\n        const formData = request.formData;\n        const requestForm = new FormData();\n        for (const formKey of Object.keys(formData)) {\n          const formValue = formData[formKey];\n          if (Array.isArray(formValue)) {\n            for (const subValue of formValue) {\n              requestForm.append(formKey, subValue);\n            }\n          } else {\n            requestForm.append(formKey, formValue);\n          }\n        }\n\n        request.body = requestForm;\n        request.formData = undefined;\n        const contentType = request.headers.get(\"Content-Type\");\n        if (contentType && contentType.indexOf(\"application/x-www-form-urlencoded\") !== -1) {\n          request.body = new URLSearchParams(requestForm as any).toString();\n        } else if (contentType && contentType.indexOf(\"multipart/form-data\") !== -1) {\n          // browser will automatically apply a suitable content-type header\n          request.headers.delete(\"Content-Type\");\n        }\n      }\n      return next(request);\n    },\n  };\n}\n"]}