{"version": 3, "file": "tokenCachePersistenceOptions.js", "sourceRoot": "", "sources": ["../../../../src/msal/nodeFlows/tokenCachePersistenceOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Parameters that enable token cache persistence in the Identity credentials.\n */\nexport interface TokenCachePersistenceOptions {\n  /**\n   * If set to true, persistent token caching will be enabled for this credential instance.\n   */\n  enabled: boolean;\n  /**\n   * Unique identifier for the persistent token cache.\n   *\n   * Based on this identifier, the persistence file will be located in any of the following places:\n   * - Darwin: '/Users/<USER>/.IdentityService/<name>'\n   * - Windows 8+: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\.IdentityService\\\\<name>'\n   * - Linux: '/home/<USER>/.IdentityService/<name>'\n   */\n  name?: string;\n  /**\n   * If set to true, the cache will be stored without encryption if no OS level user encryption is available.\n   * When set to false, the PersistentTokenCache will throw an error if no OS level user encryption is available.\n   */\n  unsafeAllowUnencryptedStorage?: boolean;\n}\n"]}