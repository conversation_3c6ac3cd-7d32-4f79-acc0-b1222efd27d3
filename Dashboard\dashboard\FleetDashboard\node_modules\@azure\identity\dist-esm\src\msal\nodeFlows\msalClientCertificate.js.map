{"version": 3, "file": "msalClientCertificate.js", "sourceRoot": "", "sources": ["../../../../src/msal/nodeFlows/msalClientCertificate.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAOlC,OAAO,EAAE,QAAQ,EAAmB,MAAM,kBAAkB,CAAC;AAC7D,OAAO,EAAE,UAAU,EAAE,gBAAgB,EAAE,MAAM,QAAQ,CAAC;AAItD,OAAO,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AACjD,OAAO,EAAE,SAAS,EAAE,MAAM,MAAM,CAAC;AACjC,OAAO,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC;AAE9B,MAAM,aAAa,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC;AAqC1C;;;;;;;GAOG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB,CACpC,aAA0D,EAC1D,oBAA8B;IAE9B,MAAM,gBAAgB,GAA8B,EAAE,CAAC;IAEvD,MAAM,WAAW,GAAwB,aAAiD;SACvF,WAAW,CAAC;IACf,MAAM,eAAe,GAAwB,aAAqD;SAC/F,eAAe,CAAC;IACnB,gBAAgB,CAAC,mBAAmB;QAClC,WAAW,IAAI,CAAC,MAAM,aAAa,CAAC,eAAgB,EAAE,MAAM,CAAC,CAAC,CAAC;IACjE,IAAI,oBAAoB,EAAE;QACxB,gBAAgB,CAAC,GAAG,GAAG,gBAAgB,CAAC,mBAAmB,CAAC;KAC7D;IAED,MAAM,kBAAkB,GACtB,+FAA+F,CAAC;IAClG,MAAM,UAAU,GAAa,EAAE,CAAC;IAEhC,qHAAqH;IACrH,IAAI,KAAK,CAAC;IACV,GAAG;QACD,KAAK,GAAG,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;QACtE,IAAI,KAAK,EAAE;YACT,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SAC3B;KACF,QAAQ,KAAK,EAAE;IAEhB,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;QAC3B,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;KAC/F;IAED,gBAAgB,CAAC,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC;SAC7C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;SAC5C,MAAM,CAAC,KAAK,CAAC;SACb,WAAW,EAAE,CAAC;IAEjB,OAAO,gBAAoC,CAAC;AAC9C,CAAC;AAED;;;GAGG;AACH,MAAM,OAAO,qBAAsB,SAAQ,QAAQ;IAIjD,YAAY,OAAqC;QAC/C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAC3C,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,CAAC;IAC3D,CAAC;IAED,iDAAiD;IACjD,KAAK,CAAC,IAAI,CAAC,OAAuC;QAChD,IAAI;YACF,MAAM,KAAK,GAAG,MAAM,gBAAgB,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAEpF,IAAI,UAA8B,CAAC;YACnC,IAAI,IAAI,CAAC,aAAa,CAAC,mBAAmB,KAAK,SAAS,EAAE;gBACxD,MAAM,gBAAgB,GAAG,gBAAgB,CAAC;oBACxC,GAAG,EAAE,KAAK,CAAC,mBAAmB;oBAC9B,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,mBAAmB;oBAClD,MAAM,EAAE,KAAK;iBACd,CAAC,CAAC;gBAEH,UAAU,GAAG,gBAAgB;qBAC1B,MAAM,CAAC;oBACN,MAAM,EAAE,KAAK;oBACb,IAAI,EAAE,OAAO;iBACd,CAAC;qBACD,QAAQ,EAAE,CAAC;aACf;iBAAM;gBACL,UAAU,GAAG,KAAK,CAAC,mBAAmB,CAAC;aACxC;YAED,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,GAAG;gBACvC,UAAU,EAAE,KAAK,CAAC,UAAU;gBAC5B,UAAU,EAAE,UAAU;gBACtB,GAAG,EAAE,KAAK,CAAC,GAAG;aACf,CAAC;SACH;QAAC,OAAO,KAAU,EAAE;YACnB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;YACzC,MAAM,KAAK,CAAC;SACb;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC;IAES,KAAK,CAAC,UAAU,CACxB,MAAgB,EAChB,UAAyC,EAAE;QAE3C,IAAI;YACF,MAAM,aAAa,GAA4B;gBAC7C,MAAM;gBACN,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,CAAC;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAgB,CAAC,8BAA8B,CAAC,aAAa,CAAC,CAAC;YACzF,iHAAiH;YACjH,sGAAsG;YACtG,8FAA8F;YAC9F,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,CAAC;SACtE;QAAC,OAAO,GAAQ,EAAE;YACjB,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;SAC9C;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  ClientCertificateCredentialPEMConfiguration,\n  ClientCertificatePEMCertificate,\n  ClientCertificatePEMCertificatePath,\n} from \"../../credentials/clientCertificateCredential\";\nimport { MsalN<PERSON>, MsalNodeOptions } from \"./msalNodeCommon\";\nimport { createHash, createPrivate<PERSON><PERSON> } from \"crypto\";\nimport { AccessToken } from \"@azure/core-auth\";\nimport { ClientCredentialRequest } from \"@azure/msal-node\";\nimport { CredentialFlowGetTokenOptions } from \"../credentials\";\nimport { formatError } from \"../../util/logging\";\nimport { promisify } from \"util\";\nimport { readFile } from \"fs\";\n\nconst readFileAsync = promisify(readFile);\n\n/**\n * Options that can be passed to configure MSAL to handle client certificates.\n * @internal\n */\nexport interface MsalClientCertificateOptions extends MsalNodeOptions {\n  /**\n   * Location of the PEM certificate.\n   */\n  configuration: ClientCertificateCredentialPEMConfiguration;\n  /**\n   * Option to include x5c header for SubjectName and Issuer name authorization.\n   * Set this option to send base64 encoded public certificate in the client assertion header as an x5c claim\n   */\n  sendCertificateChain?: boolean;\n}\n\n/**\n * Parts of a certificate, as understood by MSAL.\n * @internal\n */\ninterface CertificateParts {\n  /**\n   * Hex encoded X.509 SHA-1 thumbprint of the certificate\n   */\n  thumbprint: string;\n  /**\n   * The PEM encoded private key (string should contain -----BEGIN PRIVATE KEY----- ... -----END PRIVATE KEY-----\n   */\n  certificateContents: string;\n  /**\n   * x5c header.\n   */\n  x5c: string;\n}\n\n/**\n * Tries to asynchronously load a certificate from the given path.\n *\n * @param configuration - Either the PEM value or the path to the certificate.\n * @param sendCertificateChain - Option to include x5c header for SubjectName and Issuer name authorization.\n * @returns - The certificate parts, or `undefined` if the certificate could not be loaded.\n * @internal\n */\nexport async function parseCertificate(\n  configuration: ClientCertificateCredentialPEMConfiguration,\n  sendCertificateChain?: boolean\n): Promise<CertificateParts> {\n  const certificateParts: Partial<CertificateParts> = {};\n\n  const certificate: string | undefined = (configuration as ClientCertificatePEMCertificate)\n    .certificate;\n  const certificatePath: string | undefined = (configuration as ClientCertificatePEMCertificatePath)\n    .certificatePath;\n  certificateParts.certificateContents =\n    certificate || (await readFileAsync(certificatePath!, \"utf8\"));\n  if (sendCertificateChain) {\n    certificateParts.x5c = certificateParts.certificateContents;\n  }\n\n  const certificatePattern =\n    /(-+BEGIN CERTIFICATE-+)(\\n\\r?|\\r\\n?)([A-Za-z0-9+/\\n\\r]+=*)(\\n\\r?|\\r\\n?)(-+END CERTIFICATE-+)/g;\n  const publicKeys: string[] = [];\n\n  // Match all possible certificates, in the order they are in the file. These will form the chain that is used for x5c\n  let match;\n  do {\n    match = certificatePattern.exec(certificateParts.certificateContents);\n    if (match) {\n      publicKeys.push(match[3]);\n    }\n  } while (match);\n\n  if (publicKeys.length === 0) {\n    throw new Error(\"The file at the specified path does not contain a PEM-encoded certificate.\");\n  }\n\n  certificateParts.thumbprint = createHash(\"sha1\")\n    .update(Buffer.from(publicKeys[0], \"base64\"))\n    .digest(\"hex\")\n    .toUpperCase();\n\n  return certificateParts as CertificateParts;\n}\n\n/**\n * MSAL client certificate client. Calls to MSAL's confidential application's `acquireTokenByClientCredential` during `doGetToken`.\n * @internal\n */\nexport class MsalClientCertificate extends MsalNode {\n  private configuration: ClientCertificateCredentialPEMConfiguration;\n  private sendCertificateChain?: boolean;\n\n  constructor(options: MsalClientCertificateOptions) {\n    super(options);\n    this.requiresConfidential = true;\n    this.configuration = options.configuration;\n    this.sendCertificateChain = options.sendCertificateChain;\n  }\n\n  // Changing the MSAL configuration asynchronously\n  async init(options?: CredentialFlowGetTokenOptions): Promise<void> {\n    try {\n      const parts = await parseCertificate(this.configuration, this.sendCertificateChain);\n\n      let privateKey: string | undefined;\n      if (this.configuration.certificatePassword !== undefined) {\n        const privateKeyObject = createPrivateKey({\n          key: parts.certificateContents,\n          passphrase: this.configuration.certificatePassword,\n          format: \"pem\",\n        });\n\n        privateKey = privateKeyObject\n          .export({\n            format: \"pem\",\n            type: \"pkcs8\",\n          })\n          .toString();\n      } else {\n        privateKey = parts.certificateContents;\n      }\n\n      this.msalConfig.auth.clientCertificate = {\n        thumbprint: parts.thumbprint,\n        privateKey: privateKey,\n        x5c: parts.x5c,\n      };\n    } catch (error: any) {\n      this.logger.info(formatError(\"\", error));\n      throw error;\n    }\n    return super.init(options);\n  }\n\n  protected async doGetToken(\n    scopes: string[],\n    options: CredentialFlowGetTokenOptions = {}\n  ): Promise<AccessToken> {\n    try {\n      const clientCredReq: ClientCredentialRequest = {\n        scopes,\n        correlationId: options.correlationId,\n        azureRegion: this.azureRegion,\n        authority: options.authority,\n        claims: options.claims,\n      };\n      const result = await this.confidentialApp!.acquireTokenByClientCredential(clientCredReq);\n      // Even though we're providing the same default in memory persistence cache that we use for DeviceCodeCredential,\n      // The Client Credential flow does not return the account information from the authentication service,\n      // so each time getToken gets called, we will have to acquire a new token through the service.\n      return this.handleResult(scopes, this.clientId, result || undefined);\n    } catch (err: any) {\n      throw this.handleError(scopes, err, options);\n    }\n  }\n}\n"]}