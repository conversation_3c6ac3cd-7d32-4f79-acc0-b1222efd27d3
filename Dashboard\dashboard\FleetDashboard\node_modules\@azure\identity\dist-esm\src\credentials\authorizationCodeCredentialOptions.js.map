{"version": 3, "file": "authorizationCodeCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/authorizationCodeCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions\";\n\n/**\n * Options for the {@link AuthorizationCodeCredential}\n */\nexport interface AuthorizationCodeCredentialOptions extends MultiTenantTokenCredentialOptions {}\n"]}