#!/usr/bin/env python
# Eclipse SUMO, Simulation of Urban MObility; see https://eclipse.org/sumo
# Copyright (C) 2009-2023 German Aerospace Center (DLR) and others.

from __future__ import absolute_import
from __future__ import print_function

from azure.digitaltwins.core import DigitalTwinsClient
from azure.identity import DefaultAzureCredential

from datetime import datetime, time, timedelta

import os
import sys
import optparse
import random
import json

ADT_URL = "https://fmdt-ADT-4y3qztzk2fyka.api.weu.digitaltwins.azure.net"
simulationStartTime = datetime.combine(datetime.today(), time(8, 0));

# we need to import python modules from the $SUMO_HOME/tools directory
if 'SUMO_HOME' in os.environ:
    tools = os.path.join(os.environ['SUMO_HOME'], 'tools')
    sys.path.append(tools)
else:
    sys.exit("please declare environment variable 'SUMO_HOME'")

from sumolib import checkBinary  # noqa
import traci  # noqa

def run():
    """execute the TraCI control loop"""

    # Set up the credentials
    credential = DefaultAzureCredential()

    # Create a DigitalTwinsClient object
    dt_client = DigitalTwinsClient(ADT_URL, credential)

    digitalTwinInizialized = False
    speedingUpSimulation = False;
    jumpInTime = False;
    trackedVehicleId = "marosVehicle"

    while traci.simulation.getMinExpectedNumber() > 0:
        traci.simulationStep(traci.simulation.getTime() + 2) # 2 seconds per step to speed up simulation and don't send too many messages to ADT
        if(traci.vehicle.getIDList().__contains__(trackedVehicleId)):
        
            x, y = traci.vehicle.getPosition(trackedVehicleId)
            lon, lat = traci.simulation.convertGeo(x, y)
            routeIndex = traci.vehicle.getRouteIndex(trackedVehicleId)
            distance = traci.vehicle.getDistance(trackedVehicleId)
            nextStops = traci.vehicle.getStops(trackedVehicleId)
            stop_state = traci.vehicle.getStopState(trackedVehicleId)
            vehicleType = traci.vehicle.getTypeID(trackedVehicleId)
            vehicleMaxSpeed = traci.vehicle.getMaxSpeed(trackedVehicleId)
            vehicleRoad = traci.vehicle.getRoadID(trackedVehicleId)
            batteryCapacity = traci.vehicle.getParameter(trackedVehicleId, "device.battery.actualBatteryCapacity");
            maximumCapacity = traci.vehicle.getParameter(trackedVehicleId, "device.battery.maximumBatteryCapacity");
            batteryLevel = float(batteryCapacity) / float(maximumCapacity);
            electricityConsumtion = traci.vehicle.getElectricityConsumption(trackedVehicleId)
            isStopped = traci.vehicle.isStopped(trackedVehicleId)
            #mWh = traci.vehicle.getDistance(trackedVehicleId) / float(traci.vehicle.getParameter(trackedVehicleId, "device.battery.totalEnergyConsumed"))
            #remainingRange = float(traci.vehicle.getParameter(trackedVehicleId, "device.battery.actualBatteryCapacity")) * mWh
            currentEdge = vehicleRoad
            travelTimeToStops = []
            desctinationEtas = {}
            destinationNumber = 0

            # when a vehicle max speed is changed a type is updated to custom one vehicleType@vehicleId
            if(vehicleType.__contains__("@")):
                vehicleType = vehicleType.split("@")[0]

            if(traci.simulation.getTime() == 980):
                #traci.vehicle.setMaxSpeed(trackedVehicleId, 3)
                traci.edge.setMaxSpeed("*********", 0.27)
            vehicleMaxSpeed = traci.vehicle.getMaxSpeed(trackedVehicleId)
            if(isStopped == False and speedingUpSimulation == True):
                speedingUpSimulation = False

            if(isStopped == True and speedingUpSimulation == False):
                speedingUpSimulation = True
                jumpInTime = True

            for stop in nextStops:
                #print("Stop duration: ", stop[4], " Until: ", stop[5])
                destinationNumber+=1
                res1p_res2p = int(traci.simulation.findRoute(currentEdge, traci.lane.getEdgeID(stop[0]), vehicleType).travelTime)
                travelTimeToStops.append(res1p_res2p);
                desctinationEtas[str(destinationNumber)] = str(res1p_res2p)
                if(jumpInTime == True and destinationNumber == 1):
                    # set the current stop distance to 0 since it was already reached
                    desctinationEtas[str(destinationNumber)] = "0"
                currentEdge = traci.lane.getEdgeID(stop[0])

            if(digitalTwinInizialized == False):
                setup_adts_graph(credential, dt_client, travelTimeToStops)
                digitalTwinInizialized = True

            print("Travel time to stops: ", travelTimeToStops)
            # use this traci.vehicle.getDrivingDistance()
            # ??? traci.vehicle.getAdaptedTraveltime("marosVehicle")
            # ??? traci.vehicle.getDrivingDistance("marosVehicle", "51o", 0);

            route = traci.vehicle.getRoute(trackedVehicleId)
            # traci.simulation.findRoute
            # route = traci.simulation.findRoute(start_edge, dest_edge) vehivletype

            digita_twin_id = getVehicleDTId('marosVehicle')
            digital_twin_update = [
                {
                    "op": "replace",
                    "path": "/LocationLongitude",
                    "value": lon
                },
                {
                    "op": "replace",
                    "path": "/LocationLatitude",
                    "value": lat
                },
                                {
                    "op": "replace",
                    "path": "/SimulationIteration",
                    "value": traci.simulation.getTime()
                },
                                {
                    "op": "replace",
                    "path": "/DestinationsETA",
                    "value": desctinationEtas
                },
                {
                    "op": "replace",
                    "path": "/BatteryLevel",
                    "value": batteryLevel
                }
            ]

            dt_client.update_digital_twin(
                digita_twin_id,
                digital_twin_update
            )

            print("Stop state: ", stop_state, " isStopped: ", isStopped)
            print("Simulation time",traci.simulation.getTime()," Vehicle position: ", lat, lon, " Route count: ", route.__len__(), " Route index: ", routeIndex, " Distance: ", distance, " Battery capacity:", batteryCapacity, " Electricity consumtion: ",electricityConsumtion)

            if(jumpInTime == True):
                traci.simulationStep(traci.simulation.getTime() + 600)
                jumpInTime = False
    traci.close()
    sys.stdout.flush()


def get_options():
    optParser = optparse.OptionParser()
    optParser.add_option("--nogui", action="store_true",
                         default=False, help="run the commandline version of sumo")
    options, args = optParser.parse_args()
    return options

def getVehicleDTId(vehicleName):
    return vehicleName + '-vehicle-dt'

def setup_adts_graph(credential : DefaultAzureCredential, dt_client: DigitalTwinsClient, travelTimeToStops : list):

    usecase_config_path = os.path.abspath("../Data/SumoUseCases/OneVehicleFourStops/2023-02-23-12-40-56/dt-config.json")
    
    with open(usecase_config_path) as f:
        configuration = json.load(f)

    print(configuration)        

    # Get all the digital twins in the instance
    query = "SELECT * FROM digitaltwins"
    results = dt_client.query_twins(query)

    # Delete all the digital twins in the instance
    for twin in results:
        relationships = dt_client.list_relationships(twin["$dtId"])
        for relationship in relationships:
            dt_client.delete_relationship(twin["$dtId"],relationship["$relationshipId"])

        relationships = dt_client.list_incoming_relationships(twin["$dtId"])
        for relationship in relationships:
            dt_client.delete_relationship(relationship.sourceId, relationship.relationshipId)

        dt_client.delete_digital_twin(twin["$dtId"])


    # Create fleet digital twin
    fleet_twin_id = configuration["fleetName"] + '-dt'
    fleet_twin = {
        "$metadata": {
            "$model": "dtmi:ness:Fleet;1"
        },
        "$dtId": fleet_twin_id,
        "Name": configuration["fleetName"],
    }

    created_twin = dt_client.upsert_digital_twin(fleet_twin_id, fleet_twin)

    # Create the digital twins for vehicles
    vehicleCount = 1
    for vehicle_config in configuration["vehicles"]:
        vehicle_twin_id = getVehicleDTId(vehicle_config["name"])
        vehicle_twin = {
        "$metadata": {
            "$model": "dtmi:ness:Vehicle;1"
        },
        "$dtId": vehicle_twin_id,
        "LicensePlate": vehicle_config["licensePlate"],
        "LocationLongitude" : vehicle_config["startLongitude"],
        "LocationLatitude" : vehicle_config["startLatitude"],
        "SimulationIteration" : 0,
        "BatteryLevel" : 0,
        "DestinationsETA" : {},
        }

        dt_client.upsert_digital_twin(vehicle_twin_id, vehicle_twin)

        # Create the digital twins for driver
        driver_twin_id = vehicle_config["driverName"].replace(" ", "_") + '-driver-dt'
        driver_twin = {
        "$metadata": {
            "$model": "dtmi:ness:Driver;1"
        },
        "$dtId": driver_twin_id,
        "Name": vehicle_config["driverName"],
        }

        dt_client.upsert_digital_twin(driver_twin_id, driver_twin)

        #create the digital twins for vehicle trip
        trip_twin_id = f'trip-dt-{vehicleCount}'
        trip_twin = {
        "$metadata": {
            "$model": "dtmi:ness:VehicleTrip;1"
        },
        "$dtId": trip_twin_id,
        }

        dt_client.upsert_digital_twin(trip_twin_id, trip_twin)

        # Create vehicle relationships
        vehicle_relationships = [
            {
                "$relationshipId": "FleetVehicle" + vehicle_config["name"],
                "$sourceId": fleet_twin_id,
                "$relationshipName": "contains",
                "$targetId": vehicle_twin_id
            },
            {
                "$relationshipId": "VehicleDriver" + vehicle_config["name"],
                "$sourceId": vehicle_twin_id,
                "$relationshipName": "HasDriver",
                "$targetId": driver_twin_id
            },
            {
                "$relationshipId": "TripForVehicle_" + vehicle_config["name"],
                "$sourceId": vehicle_twin_id,
                "$relationshipName": "HasTrip",
                "$targetId": trip_twin_id,
                "Date" : datetime.now()
            }
        ]

        for relationship in vehicle_relationships:
            dt_client.upsert_relationship(
            relationship["$sourceId"],
            relationship["$relationshipId"],
            relationship)

        vehicleCount += 1

        # Create the digital twins for stops
        stopCount = 1
        passedTime = 0

        nextStops = traci.vehicle.getNextStops(vehicle_config["name"])

        for stop in nextStops:
            stopDuration = stop[4];
            passedTime += travelTimeToStops[stopCount-1]
            lon, lat = traci.simulation.convertGeo(traci.lane.getShape(stop[0])[0][0], traci.lane.getShape(stop[0])[0][1])
            stop_twin_id = f'stop-dt-{stopCount}'
            stop_twin = {
            "$metadata": {
                "$model": "dtmi:ness:Destination;1"
            },
            "$dtId": stop_twin_id,
            "Latitude": lat,
            "Longitude": lon,
            "StopDuration_Planned": stopDuration,
            "ETA_Planned" :  simulationStartTime + timedelta(seconds=passedTime),
            "Address" : "Kosice " + random.randint(0, 1000).__str__(),
            "CustomerStatus" : vehicle_config["destinationMetadata"][stopCount-1]["CustomerStatus"],
            "DelaySolverProcessId": "",
            }

            passedTime += stopDuration

            dt_client.upsert_digital_twin(stop_twin_id, stop_twin)

            # Create stop relationships
            stop_relationships = [
                {
                    "$relationshipId": trip_twin_id + f'-{stopCount}',
                    "$sourceId": trip_twin_id,
                    "$relationshipName": "HasDestination",
                    "$targetId": stop_twin_id,
                    "Order": stopCount
                }
            ]

            for relationship in stop_relationships:
                dt_client.upsert_relationship(
                relationship["$sourceId"],
                relationship["$relationshipId"],
                relationship)

            stopCount += 1



# this is the main entry point of this script
if __name__ == "__main__":
    options = get_options()

    # this script has been called from the command line. It will start sumo as a
    # server, then connect and run
    if options.nogui:
        sumoBinary = checkBinary('sumo')
    else:
        sumoBinary = checkBinary('sumo-gui')

    # this is the normal way of using traci. sumo is started as a
    # subprocess and then the python script connects and runs
    sumo_config_path = os.path.abspath("../Data/SumoUseCases/OneVehicleFourStops/2023-02-23-12-40-56/osm.sumocfg")
    traci.start([sumoBinary, "-c", sumo_config_path,
                             "--tripinfo-output", "tripinfo.xml"])
    run()
