{"version": 3, "file": "msRestUserAgentPolicy.native.js", "sourceRoot": "", "sources": ["../../../src/policies/msRestUserAgentPolicy.native.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAOlC,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,+FAA+F;AAE7I,MAAM,UAAU,sBAAsB;IACpC,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED,MAAM,UAAU,uBAAuB;IACrC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,QAAQ,CAAC,SAAS,CAAC,kBAAkB,CAAC;IACtE,MAAM,WAAW,GAAG;QAClB,GAAG,EAAE,cAAc;QACnB,KAAK,EAAE,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;KACpC,CAAC;IAEF,MAAM,MAAM,GAAG;QACb,GAAG,EAAE,IAAI;QACT,KAAK,EAAE,GAAG,QAAQ,CAAC,EAAE,IAAI,QAAQ,CAAC,OAAO,EAAE;KAC5C,CAAC;IAEF,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;AAC/B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/*\n * NOTE: When moving this file, please update \"react-native\" section in package.json.\n */\n\nimport { TelemetryInfo } from \"./userAgentPolicy\";\nconst { Platform } = require(\"react-native\"); // eslint-disable-line import/no-extraneous-dependencies, @typescript-eslint/no-require-imports\n\nexport function getDefaultUserAgentKey(): string {\n  return \"x-ms-useragent\";\n}\n\nexport function getPlatformSpecificData(): TelemetryInfo[] {\n  const { major, minor, patch } = Platform.constants.reactNativeVersion;\n  const runtimeInfo = {\n    key: \"react-native\",\n    value: `${major}.${minor}.${patch}`,\n  };\n\n  const osInfo = {\n    key: \"OS\",\n    value: `${Platform.OS}-${Platform.Version}`,\n  };\n\n  return [runtimeInfo, osInfo];\n}\n"]}