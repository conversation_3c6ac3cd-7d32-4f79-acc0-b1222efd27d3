{"version": 3, "file": "interactiveBrowserCredential.js", "sourceRoot": "", "sources": ["../../../src/credentials/interactiveBrowserCredential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AASlC,OAAO,EACL,yBAAyB,EACzB,iCAAiC,GAClC,MAAM,uBAAuB,CAAC;AAG/B,OAAO,EAAE,eAAe,EAAE,MAAM,mCAAmC,CAAC;AACpE,OAAO,EAAE,gBAAgB,EAAE,MAAM,iBAAiB,CAAC;AACnD,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,MAAM,MAAM,GAAG,gBAAgB,CAAC,8BAA8B,CAAC,CAAC;AAEhE;;;GAGG;AACH,MAAM,OAAO,4BAA4B;IAMvC;;;;;;;;;;;OAWG;IACH,YACE,UAEmD,EAAE;QAErD,MAAM,WAAW,GACf,OAAO,OAAO,CAAC,WAAW,KAAK,UAAU;YACvC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE;YACvB,CAAC,CAAC,OAAO,CAAC,WAAW,IAAI,kBAAkB,CAAC;QAEhD,IAAI,CAAC,QAAQ,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,CAAC;QAClC,IAAI,CAAC,4BAA4B,GAAG,iCAAiC,CACnE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,0BAA0B,CACpC,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,IAAI,eAAe,iCAC9B,OAAO,KACV,sBAAsB,EAAE,OAAO,EAC/B,MAAM;YACN,WAAW,IACX,CAAC;QACH,IAAI,CAAC,8BAA8B,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,8BAA8B,CAAC;IAChF,CAAC;IAED;;;;;;;;;;;OAWG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAyB,EAAE,UAA2B,EAAE;QACrE,OAAO,aAAa,CAAC,QAAQ,CAC3B,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,WAAW,EACnC,OAAO,EACP,KAAK,EAAE,UAAU,EAAE,EAAE;YACnB,UAAU,CAAC,QAAQ,GAAG,yBAAyB,CAC7C,IAAI,CAAC,QAAQ,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,CAClC,CAAC;YAEF,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,kCACpC,UAAU,KACb,8BAA8B,EAAE,IAAI,CAAC,8BAA8B,IACnE,CAAC;QACL,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,KAAK,CAAC,YAAY,CAChB,MAAyB,EACzB,UAA2B,EAAE;QAE7B,OAAO,aAAa,CAAC,QAAQ,CAC3B,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,eAAe,EACvC,OAAO,EACP,KAAK,EAAE,UAAU,EAAE,EAAE;YACnB,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;YACzC,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YACtD,OAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;QAC1C,CAAC,CACF,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport {\n  InteractiveBrowserCredentialInBrowserOptions,\n  InteractiveBrowserCredentialNodeOptions,\n} from \"./interactiveBrowserCredentialOptions\";\nimport {\n  processMultiTenantRequest,\n  resolveAddionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { AuthenticationRecord } from \"../msal/types\";\nimport { MsalFlow } from \"../msal/flows\";\nimport { MsalOpenBrowser } from \"../msal/nodeFlows/msalOpenBrowser\";\nimport { credentialLogger } from \"../util/logging\";\nimport { ensureScopes } from \"../util/scopeUtils\";\nimport { tracingClient } from \"../util/tracing\";\n\nconst logger = credentialLogger(\"InteractiveBrowserCredential\");\n\n/**\n * Enables authentication to Azure Active Directory inside of the web browser\n * using the interactive login flow.\n */\nexport class InteractiveBrowserCredential implements TokenCredential {\n  private tenantId?: string;\n  private additionallyAllowedTenantIds: string[];\n  private msalFlow: MsalFlow;\n  private disableAutomaticAuthentication?: boolean;\n\n  /**\n   * Creates an instance of InteractiveBrowserCredential with the details needed.\n   *\n   * This credential uses the [Authorization Code Flow](https://docs.microsoft.com/azure/active-directory/develop/v2-oauth2-auth-code-flow).\n   * On Node.js, it will open a browser window while it listens for a redirect response from the authentication service.\n   * On browsers, it authenticates via popups. The `loginStyle` optional parameter can be set to `redirect` to authenticate by redirecting the user to an Azure secure login page, which then will redirect the user back to the web application where the authentication started.\n   *\n   * For Node.js, if a `clientId` is provided, the Azure Active Directory application will need to be configured to have a \"Mobile and desktop applications\" redirect endpoint.\n   * Follow our guide on [setting up Redirect URIs for Desktop apps that calls to web APIs](https://docs.microsoft.com/azure/active-directory/develop/scenario-desktop-app-registration#redirect-uris).\n   *\n   * @param options - Options for configuring the client which makes the authentication requests.\n   */\n  constructor(\n    options:\n      | InteractiveBrowserCredentialNodeOptions\n      | InteractiveBrowserCredentialInBrowserOptions = {}\n  ) {\n    const redirectUri =\n      typeof options.redirectUri === \"function\"\n        ? options.redirectUri()\n        : options.redirectUri || \"http://localhost\";\n\n    this.tenantId = options?.tenantId;\n    this.additionallyAllowedTenantIds = resolveAddionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants\n    );\n\n    this.msalFlow = new MsalOpenBrowser({\n      ...options,\n      tokenCredentialOptions: options,\n      logger,\n      redirectUri,\n    });\n    this.disableAutomaticAuthentication = options?.disableAutomaticAuthentication;\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * If the user provided the option `disableAutomaticAuthentication`,\n   * once the token can't be retrieved silently,\n   * this method won't attempt to request user interaction to retrieve the token.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  async getToken(scopes: string | string[], options: GetTokenOptions = {}): Promise<AccessToken> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.getToken`,\n      options,\n      async (newOptions) => {\n        newOptions.tenantId = processMultiTenantRequest(\n          this.tenantId,\n          newOptions,\n          this.additionallyAllowedTenantIds\n        );\n\n        const arrayScopes = ensureScopes(scopes);\n        return this.msalFlow.getToken(arrayScopes, {\n          ...newOptions,\n          disableAutomaticAuthentication: this.disableAutomaticAuthentication,\n        });\n      }\n    );\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * If the token can't be retrieved silently, this method will require user interaction to retrieve the token.\n   *\n   * On Node.js, this credential has [Proof Key for Code Exchange (PKCE)](https://datatracker.ietf.org/doc/html/rfc7636) enabled by default.\n   * PKCE is a security feature that mitigates authentication code interception attacks.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                  TokenCredential implementation might make.\n   */\n  async authenticate(\n    scopes: string | string[],\n    options: GetTokenOptions = {}\n  ): Promise<AuthenticationRecord | undefined> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.authenticate`,\n      options,\n      async (newOptions) => {\n        const arrayScopes = ensureScopes(scopes);\n        await this.msalFlow.getToken(arrayScopes, newOptions);\n        return this.msalFlow.getActiveAccount();\n      }\n    );\n  }\n}\n"]}