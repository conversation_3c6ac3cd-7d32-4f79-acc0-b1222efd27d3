{"version": 3, "file": "parameters.js", "sourceRoot": "", "sources": ["../../../../src/generated/models/parameters.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,OAAO,EAIL,qBAAqB,EACtB,MAAM,kBAAkB,CAAC;AAC1B,OAAO,EACL,kBAAkB,IAAI,wBAAwB,EAC9C,UAAU,IAAI,gBAAgB,EAC/B,MAAM,mBAAmB,CAAC;AAE3B,MAAM,CAAC,MAAM,WAAW,GAAuB;IAC7C,aAAa,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;IACzC,MAAM,EAAE;QACN,YAAY,EAAE,kBAAkB;QAChC,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,cAAc;QAC9B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAAuB;IACxC,aAAa,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC;IACpC,MAAM,EAAE;QACN,WAAW,EAAE;YACX,QAAQ,EAAE,CAAC;YACX,WAAW,EAAE,IAAI;SAClB;QACD,cAAc,EAAE,QAAQ;QACxB,IAAI,EAAE;YACJ,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;SACnC;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAA0B;IAC1C,aAAa,EAAE,OAAO;IACtB,MAAM,EAAE;QACN,cAAc,EAAE,OAAO;QACvB,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;IACD,YAAY,EAAE,IAAI;CACnB,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAAuB;IAC7C,aAAa,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;IACzC,MAAM,EAAE;QACN,cAAc,EAAE,aAAa;QAC7B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAAuB;IAC5C,aAAa,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;IACxC,MAAM,EAAE;QACN,cAAc,EAAE,YAAY;QAC5B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAA4B;IACjD,aAAa,EAAE,YAAY;IAC3B,MAAM,EAAE;QACN,YAAY,EAAE,YAAY;QAC1B,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,aAAa;QAC7B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAA4B;IACtD,aAAa,EAAE,CAAC,SAAS,EAAE,iBAAiB,CAAC;IAC7C,MAAM,EAAE;QACN,cAAc,EAAE,iBAAiB;QACjC,IAAI,EAAE;YACJ,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;SACtC;KACF;IACD,gBAAgB,EAAE,qBAAqB,CAAC,GAAG;CAC5C,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAA4B;IAC7D,aAAa,EAAE,CAAC,SAAS,EAAE,wBAAwB,CAAC;IACpD,MAAM,EAAE;QACN,cAAc,EAAE,wBAAwB;QACxC,IAAI,EAAE;YACJ,IAAI,EAAE,SAAS;SAChB;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAuB;IACjD,aAAa,EAAE,CAAC,SAAS,EAAE,iBAAiB,CAAC;IAC7C,MAAM,EAAE;QACN,cAAc,EAAE,oBAAoB;QACpC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,EAAE,GAA0B;IACvC,aAAa,EAAE,IAAI;IACnB,MAAM,EAAE;QACN,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAuB;IAC9C,aAAa,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;IACzC,MAAM,EAAE;QACN,YAAY,EAAE,6BAA6B;QAC3C,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,cAAc;QAC9B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAAuB;IAC7C,aAAa,EAAE,aAAa;IAC5B,MAAM,EAAE;QACN,cAAc,EAAE,aAAa;QAC7B,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE;YACJ,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;SACnC;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAA0B;IAC7C,aAAa,EAAE,UAAU;IACzB,MAAM,EAAE;QACN,cAAc,EAAE,UAAU;QAC1B,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;IACD,YAAY,EAAE,IAAI;CACnB,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAAuB;IACpD,aAAa,EAAE,oBAAoB;IACnC,MAAM,EAAE,wBAAwB;CACjC,CAAC;AAEF,MAAM,CAAC,MAAM,IAAI,GAAuB;IACtC,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,cAAc,EAAE,MAAM;QACtB,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE;YACJ,IAAI,EAAE,KAAK;SACZ;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAAuB;IAC7C,aAAa,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;IACzC,MAAM,EAAE;QACN,cAAc,EAAE,eAAe;QAC/B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAuB;IACzC,aAAa,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;IACrC,MAAM,EAAE;QACN,cAAc,EAAE,UAAU;QAC1B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAAuB;IAC/C,aAAa,EAAE,eAAe;IAC9B,MAAM,EAAE;QACN,cAAc,EAAE,eAAe;QAC/B,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE;YACJ,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;SACnC;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,cAAc,GAA0B;IACnD,aAAa,EAAE,gBAAgB;IAC/B,MAAM,EAAE;QACN,cAAc,EAAE,gBAAgB;QAChC,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAuB;IAC9C,aAAa,EAAE,cAAc;IAC7B,MAAM,EAAE;QACN,cAAc,EAAE,cAAc;QAC9B,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE;YACJ,IAAI,EAAE,KAAK;SACZ;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAA4B;IACvD,aAAa,EAAE,CAAC,SAAS,EAAE,kBAAkB,CAAC;IAC9C,MAAM,EAAE;QACN,cAAc,EAAE,kBAAkB;QAClC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAuB;IAC3C,aAAa,EAAE,WAAW;IAC1B,MAAM,EAAE;QACN,cAAc,EAAE,WAAW;QAC3B,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE;YACJ,IAAI,EAAE,KAAK;SACZ;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAuB;IAC3C,aAAa,EAAE,WAAW;IAC1B,MAAM,EAAE;QACN,cAAc,EAAE,YAAY;QAC5B,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,mBAAmB,GAAuB;IACrD,aAAa,EAAE,CAAC,SAAS,EAAE,qBAAqB,CAAC;IACjD,MAAM,EAAE;QACN,cAAc,EAAE,uBAAuB;QACvC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAA0B;IAClD,aAAa,EAAE,eAAe;IAC9B,MAAM,EAAE;QACN,cAAc,EAAE,eAAe;QAC/B,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAAuB;IAC5C,aAAa,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;IACxC,MAAM,EAAE,gBAAgB;CACzB,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport {\n  OperationParameter,\n  OperationURLParameter,\n  OperationQueryParameter,\n  QueryCollectionFormat\n} from \"@azure/core-http\";\nimport {\n  QuerySpecification as QuerySpecificationMapper,\n  EventRoute as EventRouteMapper\n} from \"../models/mappers\";\n\nexport const contentType: OperationParameter = {\n  parameterPath: [\"options\", \"contentType\"],\n  mapper: {\n    defaultValue: \"application/json\",\n    isConstant: true,\n    serializedName: \"Content-Type\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const models: OperationParameter = {\n  parameterPath: [\"options\", \"models\"],\n  mapper: {\n    constraints: {\n      MinItems: 1,\n      UniqueItems: true\n    },\n    serializedName: \"models\",\n    type: {\n      name: \"Sequence\",\n      element: { type: { name: \"any\" } }\n    }\n  }\n};\n\nexport const $host: OperationURLParameter = {\n  parameterPath: \"$host\",\n  mapper: {\n    serializedName: \"$host\",\n    required: true,\n    type: {\n      name: \"String\"\n    }\n  },\n  skipEncoding: true\n};\n\nexport const traceparent: OperationParameter = {\n  parameterPath: [\"options\", \"traceparent\"],\n  mapper: {\n    serializedName: \"traceparent\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const tracestate: OperationParameter = {\n  parameterPath: [\"options\", \"tracestate\"],\n  mapper: {\n    serializedName: \"tracestate\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const apiVersion: OperationQueryParameter = {\n  parameterPath: \"apiVersion\",\n  mapper: {\n    defaultValue: \"2022-05-31\",\n    isConstant: true,\n    serializedName: \"api-version\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const dependenciesFor: OperationQueryParameter = {\n  parameterPath: [\"options\", \"dependenciesFor\"],\n  mapper: {\n    serializedName: \"dependenciesFor\",\n    type: {\n      name: \"Sequence\",\n      element: { type: { name: \"String\" } }\n    }\n  },\n  collectionFormat: QueryCollectionFormat.Csv\n};\n\nexport const includeModelDefinition: OperationQueryParameter = {\n  parameterPath: [\"options\", \"includeModelDefinition\"],\n  mapper: {\n    serializedName: \"includeModelDefinition\",\n    type: {\n      name: \"Boolean\"\n    }\n  }\n};\n\nexport const maxItemsPerPage: OperationParameter = {\n  parameterPath: [\"options\", \"maxItemsPerPage\"],\n  mapper: {\n    serializedName: \"max-items-per-page\",\n    type: {\n      name: \"Number\"\n    }\n  }\n};\n\nexport const id: OperationURLParameter = {\n  parameterPath: \"id\",\n  mapper: {\n    serializedName: \"id\",\n    required: true,\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const contentType1: OperationParameter = {\n  parameterPath: [\"options\", \"contentType\"],\n  mapper: {\n    defaultValue: \"application/json-patch+json\",\n    isConstant: true,\n    serializedName: \"Content-Type\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const updateModel: OperationParameter = {\n  parameterPath: \"updateModel\",\n  mapper: {\n    serializedName: \"updateModel\",\n    required: true,\n    type: {\n      name: \"Sequence\",\n      element: { type: { name: \"any\" } }\n    }\n  }\n};\n\nexport const nextLink: OperationURLParameter = {\n  parameterPath: \"nextLink\",\n  mapper: {\n    serializedName: \"nextLink\",\n    required: true,\n    type: {\n      name: \"String\"\n    }\n  },\n  skipEncoding: true\n};\n\nexport const querySpecification: OperationParameter = {\n  parameterPath: \"querySpecification\",\n  mapper: QuerySpecificationMapper\n};\n\nexport const twin: OperationParameter = {\n  parameterPath: \"twin\",\n  mapper: {\n    serializedName: \"twin\",\n    required: true,\n    type: {\n      name: \"any\"\n    }\n  }\n};\n\nexport const ifNoneMatch: OperationParameter = {\n  parameterPath: [\"options\", \"ifNoneMatch\"],\n  mapper: {\n    serializedName: \"If-None-Match\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const ifMatch: OperationParameter = {\n  parameterPath: [\"options\", \"ifMatch\"],\n  mapper: {\n    serializedName: \"If-Match\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const patchDocument: OperationParameter = {\n  parameterPath: \"patchDocument\",\n  mapper: {\n    serializedName: \"patchDocument\",\n    required: true,\n    type: {\n      name: \"Sequence\",\n      element: { type: { name: \"any\" } }\n    }\n  }\n};\n\nexport const relationshipId: OperationURLParameter = {\n  parameterPath: \"relationshipId\",\n  mapper: {\n    serializedName: \"relationshipId\",\n    required: true,\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const relationship: OperationParameter = {\n  parameterPath: \"relationship\",\n  mapper: {\n    serializedName: \"relationship\",\n    required: true,\n    type: {\n      name: \"any\"\n    }\n  }\n};\n\nexport const relationshipName: OperationQueryParameter = {\n  parameterPath: [\"options\", \"relationshipName\"],\n  mapper: {\n    serializedName: \"relationshipName\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const telemetry: OperationParameter = {\n  parameterPath: \"telemetry\",\n  mapper: {\n    serializedName: \"telemetry\",\n    required: true,\n    type: {\n      name: \"any\"\n    }\n  }\n};\n\nexport const messageId: OperationParameter = {\n  parameterPath: \"messageId\",\n  mapper: {\n    serializedName: \"Message-Id\",\n    required: true,\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const telemetrySourceTime: OperationParameter = {\n  parameterPath: [\"options\", \"telemetrySourceTime\"],\n  mapper: {\n    serializedName: \"Telemetry-Source-Time\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const componentPath: OperationURLParameter = {\n  parameterPath: \"componentPath\",\n  mapper: {\n    serializedName: \"componentPath\",\n    required: true,\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const eventRoute: OperationParameter = {\n  parameterPath: [\"options\", \"eventRoute\"],\n  mapper: EventRouteMapper\n};\n"]}