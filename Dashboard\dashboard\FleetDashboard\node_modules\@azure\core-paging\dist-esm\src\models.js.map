{"version": 3, "file": "models.js", "sourceRoot": "", "sources": ["../../src/models.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * An interface that tracks the settings for paged iteration\n */\nexport interface PageSettings {\n  /**\n   * The token that keeps track of where to continue the iterator\n   */\n  continuationToken?: string;\n  /**\n   * The size of the page during paged iteration\n   */\n  maxPageSize?: number;\n}\n/**\n * An interface that allows async iterable iteration both to completion and by page.\n */\nexport interface PagedAsyncIterableIterator<\n  TElement,\n  TPage = TElement[],\n  TPageSettings = PageSettings\n> {\n  /**\n   * The next method, part of the iteration protocol\n   */\n  next(): Promise<IteratorResult<TElement>>;\n  /**\n   * The connection to the async iterator, part of the iteration protocol\n   */\n  [Symbol.asyncIterator](): PagedAsyncIterableIterator<TElement, TPage, TPageSettings>;\n  /**\n   * Return an AsyncIterableIterator that works a page at a time\n   */\n  byPage: (settings?: TPageSettings) => AsyncIterableIterator<TPage>;\n}\n\n/**\n * An interface that describes how to communicate with the service.\n */\nexport interface PagedResult<TPage, TPageSettings = PageSettings, TLink = string> {\n  /**\n   * Link to the first page of results.\n   */\n  firstPageLink: TLink;\n  /**\n   * A method that returns a page of results.\n   */\n  getPage: (\n    pageLink: TLink,\n    maxPageSize?: number\n  ) => Promise<{ page: TPage; nextPageLink?: TLink } | undefined>;\n  /**\n   * a function to implement the `byPage` method on the paged async iterator. The default is\n   * one that sets the `maxPageSizeParam` from `settings.maxPageSize`.\n   */\n  byPage?: (settings?: TPageSettings) => AsyncIterableIterator<TPage>;\n\n  /**\n   * A function to extract elements from a page.\n   */\n  toElements?: (page: TPage) => unknown[];\n}\n\n/**\n * Paged collection of T items\n */\nexport type Paged<T> = {\n  /**\n   * The T items on this page\n   */\n  value: T[];\n  /**\n   * The link to the next page of items\n   */\n  nextLink?: string;\n};\n"]}