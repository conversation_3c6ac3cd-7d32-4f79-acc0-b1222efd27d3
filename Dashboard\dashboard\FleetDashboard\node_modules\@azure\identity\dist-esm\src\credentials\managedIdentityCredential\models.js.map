{"version": 3, "file": "models.js", "sourceRoot": "", "sources": ["../../../../src/credentials/managedIdentityCredential/models.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions } from \"@azure/core-auth\";\nimport { IdentityClient } from \"../../client/identityClient\";\n\n/**\n * @internal\n */\nexport interface MSIConfiguration {\n  identityClient: IdentityClient;\n  scopes: string | string[];\n  clientId?: string;\n  resourceId?: string;\n}\n\n/**\n * @internal\n */\nexport interface MSI {\n  isAvailable(options: {\n    scopes: string | string[];\n    identityClient?: IdentityClient;\n    clientId?: string;\n    resourceId?: string;\n    getTokenOptions?: GetTokenOptions;\n  }): Promise<boolean>;\n  getToken(\n    configuration: MSIConfiguration,\n    getTokenOptions?: GetTokenOptions\n  ): Promise<AccessToken | null>;\n}\n"]}