{"version": 3, "file": "index.js", "sources": ["../src/getPagedAsyncIterator.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PageSettings, PagedAsyncIterableIterator, PagedResult } from \"./models\";\n\n/**\n * returns an async iterator that iterates over results. It also has a `byPage`\n * method that returns pages of items at once.\n *\n * @param pagedResult - an object that specifies how to get pages.\n * @returns a paged async iterator that iterates over results.\n */\n\nexport function getPagedAsyncIterator<\n  TElement,\n  TPage = TElement[],\n  TPageSettings = PageSettings,\n  TLink = string\n>(\n  pagedResult: PagedResult<TPage, TPageSettings, TLink>\n): PagedAsyncIterableIterator<TElement, TPage, TPageSettings> {\n  const iter = getItemAsyncIterator<TElement, TPage, TLink, TPageSettings>(pagedResult);\n  return {\n    next() {\n      return iter.next();\n    },\n    [Symbol.asyncIterator]() {\n      return this;\n    },\n    byPage:\n      pagedResult?.byPage ??\n      (((settings?: PageSettings) => {\n        const { continuationToken, maxPageSize } = settings ?? {};\n        return getPageAsyncIterator(pagedResult, {\n          pageLink: continuationToken as unknown as TLink | undefined,\n          maxPageSize,\n        });\n      }) as unknown as (settings?: TPageSettings) => AsyncIterableIterator<TPage>),\n  };\n}\n\nasync function* getItemAsyncIterator<TElement, TPage, TLink, TPageSettings>(\n  pagedResult: PagedResult<TPage, TPageSettings, TLink>\n): AsyncIterableIterator<TElement> {\n  const pages = getPageAsyncIterator(pagedResult);\n  const firstVal = await pages.next();\n  // if the result does not have an array shape, i.e. TPage = TElement, then we return it as is\n  if (!Array.isArray(firstVal.value)) {\n    // can extract elements from this page\n    const { toElements } = pagedResult;\n    if (toElements) {\n      yield* toElements(firstVal.value) as TElement[];\n      for await (const page of pages) {\n        yield* toElements(page) as TElement[];\n      }\n    } else {\n      yield firstVal.value;\n      // `pages` is of type `AsyncIterableIterator<TPage>` but TPage = TElement in this case\n      yield* pages as unknown as AsyncIterableIterator<TElement>;\n    }\n  } else {\n    yield* firstVal.value;\n    for await (const page of pages) {\n      // pages is of type `AsyncIterableIterator<TPage>` so `page` is of type `TPage`. In this branch,\n      // it must be the case that `TPage = TElement[]`\n      yield* page as unknown as TElement[];\n    }\n  }\n}\n\nasync function* getPageAsyncIterator<TPage, TLink, TPageSettings>(\n  pagedResult: PagedResult<TPage, TPageSettings, TLink>,\n  options: {\n    maxPageSize?: number;\n    pageLink?: TLink;\n  } = {}\n): AsyncIterableIterator<TPage> {\n  const { pageLink, maxPageSize } = options;\n  let response = await pagedResult.getPage(pageLink ?? pagedResult.firstPageLink, maxPageSize);\n  if (!response) {\n    return;\n  }\n  yield response.page;\n  while (response.nextPageLink) {\n    response = await pagedResult.getPage(response.nextPageLink, maxPageSize);\n    if (!response) {\n      return;\n    }\n    yield response.page;\n  }\n}\n"], "names": ["__await", "__asyncDelegator", "__asyncValues"], "mappings": ";;;;;;AAAA;AAKA;;;;;;AAMG;AAEG,SAAU,qBAAqB,CAMnC,WAAqD,EAAA;;AAErD,IAAA,MAAM,IAAI,GAAG,oBAAoB,CAAwC,WAAW,CAAC,CAAC;IACtF,OAAO;QACL,IAAI,GAAA;AACF,YAAA,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;SACpB;QACD,CAAC,MAAM,CAAC,aAAa,CAAC,GAAA;AACpB,YAAA,OAAO,IAAI,CAAC;SACb;AACD,QAAA,MAAM,EACJ,CAAA,EAAA,GAAA,WAAW,KAAA,IAAA,IAAX,WAAW,KAAX,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,WAAW,CAAE,MAAM,MAClB,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,IAAC,CAAC,QAAuB,KAAI;AAC5B,YAAA,MAAM,EAAE,iBAAiB,EAAE,WAAW,EAAE,GAAG,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAA,KAAA,CAAA,GAAR,QAAQ,GAAI,EAAE,CAAC;YAC1D,OAAO,oBAAoB,CAAC,WAAW,EAAE;AACvC,gBAAA,QAAQ,EAAE,iBAAiD;gBAC3D,WAAW;AACZ,aAAA,CAAC,CAAC;AACL,SAAC,CAA2E;KAC/E,CAAC;AACJ,CAAC;AAED,SAAgB,oBAAoB,CAClC,WAAqD,EAAA;;;AAErD,QAAA,MAAM,KAAK,GAAG,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAChD,MAAM,QAAQ,GAAG,MAAMA,aAAA,CAAA,KAAK,CAAC,IAAI,EAAE,CAAA,CAAC;;QAEpC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;;AAElC,YAAA,MAAM,EAAE,UAAU,EAAE,GAAG,WAAW,CAAC;AACnC,YAAA,IAAI,UAAU,EAAE;AACd,gBAAA,MAAAA,aAAA,CAAA,OAAOC,sBAAA,CAAAC,oBAAA,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAe,CAAA,CAAA,CAAA,CAAC;;AAChD,oBAAA,KAAyB,IAAA,OAAA,GAAAA,mBAAA,CAAA,KAAK,CAAA,EAAA,SAAA,EAAA,SAAA,GAAA,MAAAF,aAAA,CAAA,OAAA,CAAA,IAAA,EAAA,CAAA,EAAA,CAAA,SAAA,CAAA,IAAA,GAAA;wBAAnB,MAAM,IAAI,kBAAA,CAAA;AACnB,wBAAA,MAAAA,aAAA,CAAA,OAAOC,sBAAA,CAAAC,mBAAA,CAAA,UAAU,CAAC,IAAI,CAAe,CAAA,CAAA,CAAA,CAAC;AACvC,qBAAA;;;;;;;;;AACF,aAAA;AAAM,iBAAA;AACL,gBAAA,MAAA,MAAAF,aAAA,CAAM,QAAQ,CAAC,KAAK,CAAA,CAAC;;gBAErB,MAAAA,aAAA,CAAA,OAAOC,sBAAA,CAAAC,oBAAA,KAAmD,CAAA,CAAA,CAAA,CAAC;AAC5D,aAAA;AACF,SAAA;AAAM,aAAA;YACL,MAAAF,aAAA,CAAA,OAAOC,sBAAA,CAAAC,mBAAA,CAAA,QAAQ,CAAC,KAAK,CAAA,CAAA,CAAA,CAAC;;AACtB,gBAAA,KAAyB,IAAA,OAAA,GAAAA,mBAAA,CAAA,KAAK,CAAA,EAAA,SAAA,EAAA,SAAA,GAAA,MAAAF,aAAA,CAAA,OAAA,CAAA,IAAA,EAAA,CAAA,EAAA,CAAA,SAAA,CAAA,IAAA,GAAA;oBAAnB,MAAM,IAAI,kBAAA,CAAA;;;oBAGnB,MAAAA,aAAA,CAAA,OAAOC,sBAAA,CAAAC,oBAAA,IAA6B,CAAA,CAAA,CAAA,CAAC;AACtC,iBAAA;;;;;;;;;AACF,SAAA;KACF,CAAA,CAAA;AAAA,CAAA;AAED,SAAgB,oBAAoB,CAClC,WAAqD,EACrD,UAGI,EAAE,EAAA;;AAEN,QAAA,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;QAC1C,IAAI,QAAQ,GAAG,MAAMF,aAAA,CAAA,WAAW,CAAC,OAAO,CAAC,QAAQ,KAAR,IAAA,IAAA,QAAQ,cAAR,QAAQ,GAAI,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,CAAA,CAAC;QAC7F,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,MAAAA,aAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACR,SAAA;AACD,QAAA,MAAA,MAAAA,aAAA,CAAM,QAAQ,CAAC,IAAI,CAAA,CAAC;QACpB,OAAO,QAAQ,CAAC,YAAY,EAAE;AAC5B,YAAA,QAAQ,GAAG,MAAAA,aAAA,CAAM,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,WAAW,CAAC,CAAA,CAAC;YACzE,IAAI,CAAC,QAAQ,EAAE;gBACb,OAAO,MAAAA,aAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACR,aAAA;AACD,YAAA,MAAA,MAAAA,aAAA,CAAM,QAAQ,CAAC,IAAI,CAAA,CAAC;AACrB,SAAA;KACF,CAAA,CAAA;AAAA;;;;"}