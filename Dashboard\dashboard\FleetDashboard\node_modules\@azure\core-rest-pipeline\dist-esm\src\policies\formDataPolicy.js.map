{"version": 3, "file": "formDataPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/formDataPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,QAAQ,MAAM,WAAW,CAAC;AAIjC;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAEnD;;GAEG;AACH,MAAM,UAAU,cAAc;IAC5B,OAAO;QACL,IAAI,EAAE,kBAAkB;QACxB,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;YAC3D,IAAI,OAAO,CAAC,QAAQ,EAAE;gBACpB,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;gBACxD,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,CAAC,mCAAmC,CAAC,KAAK,CAAC,CAAC,EAAE;oBAClF,OAAO,CAAC,IAAI,GAAG,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBAClD,OAAO,CAAC,QAAQ,GAAG,SAAS,CAAC;iBAC9B;qBAAM;oBACL,MAAM,eAAe,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;iBAClD;aACF;YACD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CAAC,QAAqB;IAC7C,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;IAC9C,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;QACnD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACxB,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE;gBAC5B,eAAe,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;aAClD;SACF;aAAM;YACL,eAAe,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;SAC/C;KACF;IACD,OAAO,eAAe,CAAC,QAAQ,EAAE,CAAC;AACpC,CAAC;AAED,KAAK,UAAU,eAAe,CAAC,QAAqB,EAAE,OAAwB;IAC5E,MAAM,WAAW,GAAG,IAAI,QAAQ,EAAE,CAAC;IACnC,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;QAC3C,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;QACpC,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YAC5B,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;gBAChC,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;aACvC;SACF;aAAM;YACL,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;SACxC;KACF;IAED,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC;IAC3B,OAAO,CAAC,QAAQ,GAAG,SAAS,CAAC;IAC7B,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IACxD,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,EAAE;QACpE,OAAO,CAAC,OAAO,CAAC,GAAG,CACjB,cAAc,EACd,iCAAiC,WAAW,CAAC,WAAW,EAAE,EAAE,CAC7D,CAAC;KACH;IACD,IAAI;QACF,MAAM,aAAa,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAClE,WAAW,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBACpC,IAAI,GAAG,EAAE;oBACP,MAAM,CAAC,GAAG,CAAC,CAAC;iBACb;qBAAM;oBACL,OAAO,CAAC,MAAM,CAAC,CAAC;iBACjB;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;KACtD;IAAC,OAAO,CAAM,EAAE;QACf,0CAA0C;KAC3C;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport FormData from \"form-data\";\nimport { FormDataMap, PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces\";\nimport { PipelinePolicy } from \"../pipeline\";\n\n/**\n * The programmatic identifier of the formDataPolicy.\n */\nexport const formDataPolicyName = \"formDataPolicy\";\n\n/**\n * A policy that encodes FormData on the request into the body.\n */\nexport function formDataPolicy(): PipelinePolicy {\n  return {\n    name: formDataPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      if (request.formData) {\n        const contentType = request.headers.get(\"Content-Type\");\n        if (contentType && contentType.indexOf(\"application/x-www-form-urlencoded\") !== -1) {\n          request.body = wwwFormUrlEncode(request.formData);\n          request.formData = undefined;\n        } else {\n          await prepareFormData(request.formData, request);\n        }\n      }\n      return next(request);\n    },\n  };\n}\n\nfunction wwwFormUrlEncode(formData: FormDataMap): string {\n  const urlSearchParams = new URLSearchParams();\n  for (const [key, value] of Object.entries(formData)) {\n    if (Array.isArray(value)) {\n      for (const subValue of value) {\n        urlSearchParams.append(key, subValue.toString());\n      }\n    } else {\n      urlSearchParams.append(key, value.toString());\n    }\n  }\n  return urlSearchParams.toString();\n}\n\nasync function prepareFormData(formData: FormDataMap, request: PipelineRequest): Promise<void> {\n  const requestForm = new FormData();\n  for (const formKey of Object.keys(formData)) {\n    const formValue = formData[formKey];\n    if (Array.isArray(formValue)) {\n      for (const subValue of formValue) {\n        requestForm.append(formKey, subValue);\n      }\n    } else {\n      requestForm.append(formKey, formValue);\n    }\n  }\n\n  request.body = requestForm;\n  request.formData = undefined;\n  const contentType = request.headers.get(\"Content-Type\");\n  if (contentType && contentType.indexOf(\"multipart/form-data\") !== -1) {\n    request.headers.set(\n      \"Content-Type\",\n      `multipart/form-data; boundary=${requestForm.getBoundary()}`\n    );\n  }\n  try {\n    const contentLength = await new Promise<number>((resolve, reject) => {\n      requestForm.getLength((err, length) => {\n        if (err) {\n          reject(err);\n        } else {\n          resolve(length);\n        }\n      });\n    });\n    request.headers.set(\"Content-Length\", contentLength);\n  } catch (e: any) {\n    // ignore setting the length if this fails\n  }\n}\n"]}