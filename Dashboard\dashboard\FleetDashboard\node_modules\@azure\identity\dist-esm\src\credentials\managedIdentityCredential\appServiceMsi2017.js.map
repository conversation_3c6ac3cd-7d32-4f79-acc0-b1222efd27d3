{"version": 3, "file": "appServiceMsi2017.js", "sourceRoot": "", "sources": ["../../../../src/credentials/managedIdentityCredential/appServiceMsi2017.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAEL,iBAAiB,EACjB,qBAAqB,GACtB,MAAM,2BAA2B,CAAC;AAEnC,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAEtD,OAAO,EAAE,mBAAmB,EAAE,MAAM,SAAS,CAAC;AAE9C,MAAM,OAAO,GAAG,gDAAgD,CAAC;AACjE,MAAM,MAAM,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAEzC;;GAEG;AACH,SAAS,qBAAqB,CAC5B,MAAyB,EACzB,QAAiB;IAEjB,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAC7C,IAAI,CAAC,QAAQ,EAAE;QACb,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,sCAAsC,CAAC,CAAC;KACnE;IAED,MAAM,eAAe,GAA2B;QAC9C,QAAQ;QACR,aAAa,EAAE,YAAY;KAC5B,CAAC;IAEF,IAAI,QAAQ,EAAE;QACZ,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;KACrC;IAED,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,eAAe,CAAC,CAAC;IAEnD,wIAAwI;IACxI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE;QAC7B,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,8CAA8C,CAAC,CAAC;KAC3E;IACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;QAC3B,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,4CAA4C,CAAC,CAAC;KACzE;IAED,OAAO;QACL,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,KAAK,CAAC,QAAQ,EAAE,EAAE;QACtD,MAAM,EAAE,KAAK;QACb,OAAO,EAAE,iBAAiB,CAAC;YACzB,MAAM,EAAE,kBAAkB;YAC1B,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU;SAC/B,CAAC;KACH,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAQ;IACpC,KAAK,CAAC,WAAW,CAAC,EAAE,MAAM,EAAE;QAC1B,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,EAAE;YACb,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,mDAAmD,CAAC,CAAC;YAC3E,OAAO,KAAK,CAAC;SACd;QACD,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;QACxB,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;QAC3D,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,CAAC,IAAI,CACT,GAAG,OAAO,mFAAmF,CAC9F,CAAC;SACH;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,KAAK,CAAC,QAAQ,CACZ,aAA+B,EAC/B,kBAAmC,EAAE;QAErC,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,aAAa,CAAC;QAEvE,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,OAAO,CACZ,GAAG,OAAO,0GAA0G,CACrH,CAAC;SACH;QAED,MAAM,CAAC,IAAI,CACT,GAAG,OAAO,2FAA2F,OAAO,CAAC,GAAG,CAAC,YAAY,6BAA6B,CAC3J,CAAC;QAEF,MAAM,OAAO,GAAG,qBAAqB,+BACnC,WAAW,EAAE,eAAe,CAAC,WAAW,IACrC,qBAAqB,CAAC,MAAM,EAAE,QAAQ,CAAC;YAC1C,0FAA0F;YAC1F,uBAAuB,EAAE,IAAI,IAC7B,CAAC;QACH,MAAM,aAAa,GAAG,MAAM,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACrE,OAAO,CAAC,aAAa,IAAI,aAAa,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC;IAC9D,CAAC;CACF,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  PipelineRequestOptions,\n  createHttpHeaders,\n  createPipelineRequest,\n} from \"@azure/core-rest-pipeline\";\nimport { AccessToken, GetTokenOptions } from \"@azure/core-auth\";\nimport { credentialLogger } from \"../../util/logging\";\nimport { MSI, MSIConfiguration } from \"./models\";\nimport { mapScopesToResource } from \"./utils\";\n\nconst msiName = \"ManagedIdentityCredential - AppServiceMSI 2017\";\nconst logger = credentialLogger(msiName);\n\n/**\n * Generates the options used on the request for an access token.\n */\nfunction prepareRequestOptions(\n  scopes: string | string[],\n  clientId?: string\n): PipelineRequestOptions {\n  const resource = mapScopesToResource(scopes);\n  if (!resource) {\n    throw new Error(`${msiName}: Multiple scopes are not supported.`);\n  }\n\n  const queryParameters: Record<string, string> = {\n    resource,\n    \"api-version\": \"2017-09-01\",\n  };\n\n  if (clientId) {\n    queryParameters.clientid = clientId;\n  }\n\n  const query = new URLSearchParams(queryParameters);\n\n  // This error should not bubble up, since we verify that this environment variable is defined in the isAvailable() method defined below.\n  if (!process.env.MSI_ENDPOINT) {\n    throw new Error(`${msiName}: Missing environment variable: MSI_ENDPOINT`);\n  }\n  if (!process.env.MSI_SECRET) {\n    throw new Error(`${msiName}: Missing environment variable: MSI_SECRET`);\n  }\n\n  return {\n    url: `${process.env.MSI_ENDPOINT}?${query.toString()}`,\n    method: \"GET\",\n    headers: createHttpHeaders({\n      Accept: \"application/json\",\n      secret: process.env.MSI_SECRET,\n    }),\n  };\n}\n\n/**\n * Defines how to determine whether the Azure App Service MSI is available, and also how to retrieve a token from the Azure App Service MSI.\n */\nexport const appServiceMsi2017: MSI = {\n  async isAvailable({ scopes }): Promise<boolean> {\n    const resource = mapScopesToResource(scopes);\n    if (!resource) {\n      logger.info(`${msiName}: Unavailable. Multiple scopes are not supported.`);\n      return false;\n    }\n    const env = process.env;\n    const result = Boolean(env.MSI_ENDPOINT && env.MSI_SECRET);\n    if (!result) {\n      logger.info(\n        `${msiName}: Unavailable. The environment variables needed are: MSI_ENDPOINT and MSI_SECRET.`\n      );\n    }\n    return result;\n  },\n  async getToken(\n    configuration: MSIConfiguration,\n    getTokenOptions: GetTokenOptions = {}\n  ): Promise<AccessToken | null> {\n    const { identityClient, scopes, clientId, resourceId } = configuration;\n\n    if (resourceId) {\n      logger.warning(\n        `${msiName}: managed Identity by resource Id is not supported. Argument resourceId might be ignored by the service.`\n      );\n    }\n\n    logger.info(\n      `${msiName}: Using the endpoint and the secret coming form the environment variables: MSI_ENDPOINT=${process.env.MSI_ENDPOINT} and MSI_SECRET=[REDACTED].`\n    );\n\n    const request = createPipelineRequest({\n      abortSignal: getTokenOptions.abortSignal,\n      ...prepareRequestOptions(scopes, clientId),\n      // Generally, MSI endpoints use the HTTP protocol, without transport layer security (TLS).\n      allowInsecureConnection: true,\n    });\n    const tokenResponse = await identityClient.sendTokenRequest(request);\n    return (tokenResponse && tokenResponse.accessToken) || null;\n  },\n};\n"]}