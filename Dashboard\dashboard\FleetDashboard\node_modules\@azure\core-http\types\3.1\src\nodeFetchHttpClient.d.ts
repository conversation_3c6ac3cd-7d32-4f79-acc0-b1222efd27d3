/// <reference types="node" />
import { HttpHeadersLike } from "./httpHeaders";
import { Transform } from "stream";
import { TransferProgressEvent, WebResourceLike } from "./webResource";
import { HttpClient } from "./httpClient";
import { HttpOperationResponse } from "./httpOperationResponse";
/**
 * String URLs used when calling to `fetch()`.
 */
export declare type CommonRequestInfo = string;
/**
 * An object containing information about the outgoing HTTP request.
 */
export declare type CommonRequestInit = Pick<RequestInit, Exclude<keyof RequestInit, "body" | "headers" | "signal">> & {
    body?: any;
    headers?: any;
    signal?: any;
};
/**
 * An object containing information about the incoming HTTP response.
 */
export declare type CommonResponse = Pick<Response, Exclude<keyof Response, "body" | "trailer" | "formData">> & {
    body: any;
    trailer: any;
    formData: any;
};
export declare class ReportTransform extends Transform {
    private progressCallback;
    private loadedBytes;
    _transform(chunk: string | Buffer, _encoding: string, callback: (arg: any) => void): void;
    constructor(progressCallback: (progress: TransferProgressEvent) => void);
}
/**
 * Transforms a set of headers into the key/value pair defined by {@link HttpHeadersLike}
 */
export declare function parseHeaders(headers: Headers): HttpHeadersLike;
/**
 * An HTTP client that uses `node-fetch`.
 */
export declare class NodeFetchHttpClient implements HttpClient {
    /**
     * Provides minimum viable error handling and the logic that executes the abstract methods.
     * @param httpRequest - Object representing the outgoing HTTP request.
     * @returns An object representing the incoming HTTP response.
     */
    sendRequest(httpRequest: WebResourceLike): Promise<HttpOperationResponse>;
    private proxyAgentMap;
    private keepAliveAgents;
    private readonly cookieJar;
    private getOrCreateAgent;
    /**
     * Uses `node-fetch` to perform the request.
     */
    fetch(input: CommonRequestInfo, init?: CommonRequestInit): Promise<CommonResponse>;
    /**
     * Prepares a request based on the provided web resource.
     */
    prepareRequest(httpRequest: WebResourceLike): Promise<Partial<RequestInit>>;
    /**
     * Process an HTTP response. Handles persisting a cookie for subsequent requests if the response has a "Set-Cookie" header.
     */
    processRequest(operationResponse: HttpOperationResponse): Promise<void>;
}
//# sourceMappingURL=nodeFetchHttpClient.d.ts.map
