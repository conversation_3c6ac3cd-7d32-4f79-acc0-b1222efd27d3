{"version": 3, "file": "throttlingRetryStrategy.js", "sourceRoot": "", "sources": ["../../../src/util/throttlingRetryStrategy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;GAEG;AACH,MAAM,CAAC,MAAM,8BAA8B,GAAG,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Maximum number of retries for the throttling retry policy\n */\nexport const DEFAULT_CLIENT_MAX_RETRY_COUNT = 3;\n"]}