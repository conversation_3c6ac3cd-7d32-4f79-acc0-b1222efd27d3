{"version": 3, "file": "defaultHttpClient.browser.js", "sourceRoot": "", "sources": ["../../src/defaultHttpClient.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,qBAAqB,EAAE,MAAM,mBAAmB,CAAC;AAE1D;;GAEG;AACH,MAAM,UAAU,uBAAuB;IACrC,OAAO,qBAAqB,EAAE,CAAC;AACjC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpClient } from \"./interfaces\";\nimport { createFetchHttpClient } from \"./fetchHttpClient\";\n\n/**\n * Create the correct HttpClient for the current environment.\n */\nexport function createDefaultHttpClient(): HttpClient {\n  return createFetchHttpClient();\n}\n"]}