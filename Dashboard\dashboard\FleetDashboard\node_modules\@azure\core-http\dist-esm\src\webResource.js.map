{"version": 3, "file": "webResource.js", "sourceRoot": "", "sources": ["../../src/webResource.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,WAAW,EAAmB,iBAAiB,EAAE,MAAM,eAAe,CAAC;AAChF,OAAO,EAAU,UAAU,EAAE,MAAM,cAAc,CAAC;AAOlD,OAAO,EAAE,YAAY,EAAE,MAAM,cAAc,CAAC;AAuJ5C,MAAM,UAAU,iBAAiB,CAAC,MAAe;IAC/C,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QACxC,MAAM,UAAU,GAAG,MAOlB,CAAC;QACF,IACE,OAAO,UAAU,CAAC,GAAG,KAAK,QAAQ;YAClC,OAAO,UAAU,CAAC,MAAM,KAAK,QAAQ;YACrC,OAAO,UAAU,CAAC,OAAO,KAAK,QAAQ;YACtC,iBAAiB,CAAC,UAAU,CAAC,OAAO,CAAC;YACrC,OAAO,UAAU,CAAC,yBAAyB,KAAK,UAAU;YAC1D,OAAO,UAAU,CAAC,OAAO,KAAK,UAAU;YACxC,OAAO,UAAU,CAAC,KAAK,KAAK,UAAU,EACtC;YACA,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;GAKG;AACH,MAAM,OAAO,WAAW;IAsGtB,YACE,GAAY,EACZ,MAAoB,EACpB,IAAc,EACd,KAA8B,EAC9B,OAAkD,EAClD,kBAA4B,EAC5B,eAAyB,EACzB,WAA6B,EAC7B,OAAgB,EAChB,gBAA4D,EAC5D,kBAA8D,EAC9D,aAA6B,EAC7B,SAAmB,EACnB,kBAA4B,EAC5B,yBAAuC;QAEvC,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,yBAAyB,GAAG,yBAAyB,CAAC;QAC3D,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,KAAK,CAAC;QAC9B,IAAI,CAAC,OAAO,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;QAC/E,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;QAC1B,IAAI,CAAC,eAAe,GAAG,eAAe,IAAI,KAAK,CAAC;QAChD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,CAAC,CAAC;QAC5B,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,YAAY,EAAE,CAAC;IAChF,CAAC;IAED;;;;OAIG;IACH,yBAAyB;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;SACpD;QACD,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YACb,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;SACjD;IACH,CAAC;IAED;;;;OAIG;IACH,OAAO,CAAC,OAA8B;QACpC,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;SAC/C;QAED,IACE,OAAO,CAAC,MAAM,KAAK,SAAS;YAC5B,OAAO,CAAC,MAAM,KAAK,IAAI;YACvB,OAAO,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,QAAQ,EAC5C;YACA,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;SACrD;QAED,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,YAAY,EAAE;YACvC,MAAM,IAAI,KAAK,CACb,kGAAkG,CACnG,CAAC;SACH;QAED,IACE,CAAC,OAAO,CAAC,YAAY,KAAK,SAAS;YACjC,OAAO,CAAC,YAAY,KAAK,IAAI;YAC7B,OAAO,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,QAAQ,CAAC;YACrD,CAAC,OAAO,CAAC,GAAG,KAAK,SAAS;gBACxB,OAAO,CAAC,GAAG,KAAK,IAAI;gBACpB,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,QAAQ,CAAC,EAC5C;YACA,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;SACvF;QAED,iCAAiC;QACjC,IAAI,OAAO,CAAC,GAAG,EAAE;YACf,IAAI,OAAO,OAAO,CAAC,GAAG,KAAK,QAAQ,EAAE;gBACnC,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;aAC1D;YACD,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;SACxB;QAED,iBAAiB;QACjB,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,MAAM,YAAY,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YAC3F,IAAI,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC7D,MAAM,IAAI,KAAK,CACb,uBAAuB;oBACrB,OAAO,CAAC,MAAM;oBACd,4CAA4C;oBAC5C,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAC/B,CAAC;aACH;SACF;QACD,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,EAAiB,CAAC;QAE1D,iDAAiD;QACjD,IAAI,OAAO,CAAC,YAAY,EAAE;YACxB,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;YACjD,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;gBACpC,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;aACnE;YACD,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;gBACpB,OAAO,CAAC,OAAO,GAAG,8BAA8B,CAAC;aAClD;YACD,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YAChC,IAAI,GAAG,GACL,OAAO;gBACP,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;gBAClC,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;YACxE,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;YACpD,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,EAAE;gBAC/B,IAAI,CAAC,cAAc,EAAE;oBACnB,MAAM,IAAI,KAAK,CACb,iBAAiB,YAAY,0EAA0E,CACxG,CAAC;iBACH;gBACD,QAAQ,CAAC,OAAO,CAAC,UAAU,IAAI;oBAC7B,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBACxC,MAAM,SAAS,GAAI,cAAyC,CAAC,aAAa,CAAC,CAAC;oBAC5E,IACE,SAAS,KAAK,IAAI;wBAClB,SAAS,KAAK,SAAS;wBACvB,CAAC,CAAC,OAAO,SAAS,KAAK,QAAQ,IAAI,OAAO,SAAS,KAAK,QAAQ,CAAC,EACjE;wBACA,MAAM,yBAAyB,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;wBAC/E,MAAM,IAAI,KAAK,CACb,iBAAiB,YAAY,gCAAgC,aAAa,EAAE;4BAC1E,8CAA8C,yBAAyB,GAAG;4BAC1E,0EAA0E,aAAa,6BAA6B;4BACpH,wCAAwC,aAAa,6DAA6D,CACrH,CAAC;qBACH;oBAED,IAAI,OAAO,SAAS,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;wBAC3C,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC;qBACxD;oBAED,IAAI,OAAO,SAAS,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;wBAC3C,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;4BACpB,MAAM,IAAI,KAAK,CACb,0BAA0B,aAAa,mEAAmE,CAC3G,CAAC;yBACH;wBACD,IAAI,SAAS,CAAC,eAAe,EAAE;4BAC7B,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;yBAC1C;6BAAM;4BACL,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,kBAAkB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;yBAC9D;qBACF;gBACH,CAAC,CAAC,CAAC;aACJ;YACD,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;SAChB;QAED,iHAAiH;QACjH,IAAI,OAAO,CAAC,eAAe,EAAE;YAC3B,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;YAChD,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;gBACvC,MAAM,IAAI,KAAK,CACb,6EAA6E;oBAC3E,qFAAqF;oBACrF,2IAA2I,CAC9I,CAAC;aACH;YACD,uDAAuD;YACvD,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC5C,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC;aACjB;YACD,wBAAwB;YACxB,MAAM,WAAW,GAAG,EAAE,CAAC;YACvB,4GAA4G;YAC5G,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;YAChB,KAAK,MAAM,cAAc,IAAI,eAAe,EAAE;gBAC5C,MAAM,UAAU,GAAQ,eAAe,CAAC,cAAc,CAAC,CAAC;gBACxD,IAAI,UAAU,EAAE;oBACd,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;wBAClC,WAAW,CAAC,IAAI,CAAC,cAAc,GAAG,GAAG,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;wBACxE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;qBAC7D;yBAAM,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;wBACzC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;4BACrB,MAAM,IAAI,KAAK,CACb,2BAA2B,cAAc,mEAAmE,CAC7G,CAAC;yBACH;wBACD,IAAI,UAAU,CAAC,eAAe,EAAE;4BAC9B,WAAW,CAAC,IAAI,CAAC,cAAc,GAAG,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;4BAC1D,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC;yBAC/C;6BAAM;4BACL,WAAW,CAAC,IAAI,CAAC,cAAc,GAAG,GAAG,GAAG,kBAAkB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;4BAC9E,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;yBACnE;qBACF;iBACF;aACF,CAAC,aAAa;YACf,yBAAyB;YACzB,IAAI,CAAC,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACnC;QAED,kDAAkD;QAClD,IAAI,OAAO,CAAC,OAAO,EAAE;YACnB,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YAChC,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACrD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;aACnD;SACF;QACD,0CAA0C;QAC1C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE;YACxC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;SAC9C;QACD,yCAAyC;QACzC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;YAClF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;SAC5D;QAED,UAAU;QACV,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;YACrC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,iCAAiC,CAAC,CAAC;SACrE;QAED,0HAA0H;QAC1H,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACzB,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,EAAE;YACvD,6HAA6H;YAC7H,IAAI,OAAO,CAAC,YAAY,EAAE;gBACxB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE;oBAC1C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC;iBAClD;gBACD,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,0BAA0B,EAAE;oBACnE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,0BAA0B,CAAC,CAAC;iBAC9D;aACF;iBAAM;gBACL,IAAI,OAAO,CAAC,mBAAmB,EAAE;oBAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,SAAS,CACnD,OAAO,CAAC,mBAAmB,EAC3B,OAAO,CAAC,IAAI,EACZ,aAAa,CACd,CAAC;iBACH;gBACD,IAAI,CAAC,OAAO,CAAC,0BAA0B,EAAE;oBACvC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;iBAC1C;aACF;SACF;QAED,IAAI,OAAO,CAAC,WAAW,EAAE;YACvB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;SACxC;QAED,IAAI,OAAO,CAAC,cAAc,EAAE;YAC1B,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;SAC9C;QAED,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;QACrD,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;QAEjD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,KAAK;QACH,MAAM,MAAM,GAAG,IAAI,WAAW,CAC5B,IAAI,CAAC,GAAG,EACR,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EACpC,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,yBAAyB,CAC/B,CAAC;QAEF,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;SACjC;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;SAC3C;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;SACnD;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAChC,MAAM,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC;SAC/D;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { Context, SpanOptions } from \"@azure/core-tracing\";\nimport { HttpHeaders, HttpHeadersLike, isHttpHeadersLike } from \"./httpHeaders\";\nimport { Mapper, Serializer } from \"./serializer\";\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { HttpOperationResponse } from \"./httpOperationResponse\";\nimport { OperationResponse } from \"./operationResponse\";\nimport { OperationSpec } from \"./operationSpec\";\nimport { ProxySettings } from \"./serviceClient\";\nimport { SerializerOptions } from \"./util/serializer.common\";\nimport { generateUuid } from \"./util/utils\";\n\n/**\n * List of supported HTTP methods.\n */\nexport type HttpMethods =\n  | \"GET\"\n  | \"PUT\"\n  | \"POST\"\n  | \"DELETE\"\n  | \"PATCH\"\n  | \"HEAD\"\n  | \"OPTIONS\"\n  | \"TRACE\";\n\n/**\n * Possible HTTP request body types\n */\nexport type HttpRequestBody =\n  | Blob\n  | string\n  | ArrayBuffer\n  | ArrayBufferView\n  | (() => NodeJS.ReadableStream);\n\n/**\n * Fired in response to upload or download progress.\n */\nexport type TransferProgressEvent = {\n  /**\n   * The number of bytes loaded so far.\n   */\n  loadedBytes: number;\n};\n\n/**\n * A description of a HTTP request to be made to a remote server.\n */\nexport interface WebResourceLike {\n  /**\n   * The URL being accessed by the request.\n   */\n  url: string;\n  /**\n   * The HTTP method to use when making the request.\n   */\n  method: HttpMethods;\n  /**\n   * The HTTP body contents of the request.\n   */\n  body?: any;\n  /**\n   * The HTTP headers to use when making the request.\n   */\n  headers: HttpHeadersLike;\n  /**\n   * Whether or not the body of the HttpOperationResponse should be treated as a stream.\n   * @deprecated Use streamResponseStatusCodes property instead.\n   */\n  streamResponseBody?: boolean;\n  /**\n   * A list of response status codes whose corresponding HttpOperationResponse body should be treated as a stream.\n   */\n  streamResponseStatusCodes?: Set<number>;\n  /**\n   * Whether or not the HttpOperationResponse should be deserialized. If this is undefined, then the\n   * HttpOperationResponse should be deserialized.\n   */\n  shouldDeserialize?: boolean | ((response: HttpOperationResponse) => boolean);\n  /**\n   * A function that returns the proper OperationResponse for the given OperationSpec and\n   * HttpOperationResponse combination. If this is undefined, then a simple status code lookup will\n   * be used.\n   */\n  operationResponseGetter?: (\n    operationSpec: OperationSpec,\n    response: HttpOperationResponse\n  ) => undefined | OperationResponse;\n  /**\n   * Form data, used to build the request body.\n   */\n  formData?: any;\n  /**\n   * A query string represented as an object.\n   */\n  query?: { [key: string]: any };\n  /**\n   * Used to parse the response.\n   */\n  operationSpec?: OperationSpec;\n  /**\n   * If credentials (cookies) should be sent along during an XHR.\n   */\n  withCredentials: boolean;\n  /**\n   * The number of milliseconds a request can take before automatically being terminated.\n   * If the request is terminated, an `AbortError` is thrown.\n   */\n  timeout: number;\n  /**\n   * Proxy configuration.\n   */\n  proxySettings?: ProxySettings;\n  /**\n   * If the connection should be reused.\n   */\n  keepAlive?: boolean;\n  /**\n   * Whether or not to decompress response according to Accept-Encoding header (node-fetch only)\n   */\n  decompressResponse?: boolean;\n  /**\n   * A unique identifier for the request. Used for logging and tracing.\n   */\n  requestId: string;\n\n  /**\n   * Signal of an abort controller. Can be used to abort both sending a network request and waiting for a response.\n   */\n  abortSignal?: AbortSignalLike;\n\n  /**\n   * Callback which fires upon upload progress.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /** Callback which fires upon download progress. */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Tracing: Context used when creating spans.\n   */\n  tracingContext?: Context;\n\n  /**\n   * Validates that the required properties such as method, url, headers[\"Content-Type\"],\n   * headers[\"accept-language\"] are defined. It will throw an error if one of the above\n   * mentioned properties are not defined.\n   */\n  validateRequestProperties(): void;\n\n  /**\n   * Sets options on the request.\n   */\n  prepare(options: RequestPrepareOptions): WebResourceLike;\n  /**\n   * Clone this request object.\n   */\n  clone(): WebResourceLike;\n}\n\nexport function isWebResourceLike(object: unknown): object is WebResourceLike {\n  if (object && typeof object === \"object\") {\n    const castObject = object as {\n      url: unknown;\n      method: unknown;\n      headers: unknown;\n      validateRequestProperties: unknown;\n      prepare: unknown;\n      clone: unknown;\n    };\n    if (\n      typeof castObject.url === \"string\" &&\n      typeof castObject.method === \"string\" &&\n      typeof castObject.headers === \"object\" &&\n      isHttpHeadersLike(castObject.headers) &&\n      typeof castObject.validateRequestProperties === \"function\" &&\n      typeof castObject.prepare === \"function\" &&\n      typeof castObject.clone === \"function\"\n    ) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * Creates a new WebResource object.\n *\n * This class provides an abstraction over a REST call by being library / implementation agnostic and wrapping the necessary\n * properties to initiate a request.\n */\nexport class WebResource implements WebResourceLike {\n  /**\n   * URL of the outgoing request.\n   */\n  url: string;\n  /**\n   * HTTP method to use.\n   */\n  method: HttpMethods;\n  /**\n   * Request body.\n   */\n  body?: any;\n  /**\n   * HTTP headers.\n   */\n  headers: HttpHeadersLike;\n  /**\n   * Whether or not the body of the HttpOperationResponse should be treated as a stream.\n   * @deprecated Use streamResponseStatusCodes property instead.\n   */\n  streamResponseBody?: boolean;\n  /**\n   * A list of status codes whose corresponding HttpOperationResponse body should be treated as a stream.\n   */\n  streamResponseStatusCodes?: Set<number>;\n  /**\n   * Whether or not the HttpOperationResponse should be deserialized. If this is undefined, then the\n   * HttpOperationResponse should be deserialized.\n   */\n  shouldDeserialize?: boolean | ((response: HttpOperationResponse) => boolean);\n  /**\n   * A function that returns the proper OperationResponse for the given OperationSpec and\n   * HttpOperationResponse combination. If this is undefined, then a simple status code lookup will\n   * be used.\n   */\n  operationResponseGetter?: (\n    operationSpec: OperationSpec,\n    response: HttpOperationResponse\n  ) => undefined | OperationResponse;\n  /**\n   * Form data, used to build the request body.\n   */\n  formData?: any;\n  /**\n   * Query added to the URL.\n   */\n  query?: { [key: string]: any };\n  /**\n   * Specification of the HTTP request.\n   */\n  operationSpec?: OperationSpec;\n  /**\n   * Whether to send credentials (via cookies, authorization headers, or TLS client certificates) when making a request in the browser to a cross-site destination.\n   */\n  withCredentials: boolean;\n  /**\n   * How long to wait in milliseconds before aborting the request.\n   */\n  timeout: number;\n  /**\n   * What proxy to use, if necessary.\n   */\n  proxySettings?: ProxySettings;\n  /**\n   * Whether to keep the HTTP connections alive throughout requests.\n   */\n  keepAlive?: boolean;\n  /**\n   * Whether or not to decompress response according to Accept-Encoding header (node-fetch only)\n   */\n  decompressResponse?: boolean;\n  /**\n   * Unique identifier of the outgoing request.\n   */\n  requestId: string;\n\n  /**\n   * Signal of an abort controller. Can be used to abort both sending a network request and waiting for a response.\n   */\n  abortSignal?: AbortSignalLike;\n\n  /**\n   * Callback which fires upon upload progress.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Callback which fires upon download progress.\n   */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Tracing: Options used to create a span when tracing is enabled.\n   */\n  spanOptions?: SpanOptions;\n\n  /**\n   * Tracing: Context used when creating Spans.\n   */\n  tracingContext?: Context;\n\n  constructor(\n    url?: string,\n    method?: HttpMethods,\n    body?: unknown,\n    query?: { [key: string]: any },\n    headers?: { [key: string]: any } | HttpHeadersLike,\n    streamResponseBody?: boolean,\n    withCredentials?: boolean,\n    abortSignal?: AbortSignalLike,\n    timeout?: number,\n    onUploadProgress?: (progress: TransferProgressEvent) => void,\n    onDownloadProgress?: (progress: TransferProgressEvent) => void,\n    proxySettings?: ProxySettings,\n    keepAlive?: boolean,\n    decompressResponse?: boolean,\n    streamResponseStatusCodes?: Set<number>\n  ) {\n    this.streamResponseBody = streamResponseBody;\n    this.streamResponseStatusCodes = streamResponseStatusCodes;\n    this.url = url || \"\";\n    this.method = method || \"GET\";\n    this.headers = isHttpHeadersLike(headers) ? headers : new HttpHeaders(headers);\n    this.body = body;\n    this.query = query;\n    this.formData = undefined;\n    this.withCredentials = withCredentials || false;\n    this.abortSignal = abortSignal;\n    this.timeout = timeout || 0;\n    this.onUploadProgress = onUploadProgress;\n    this.onDownloadProgress = onDownloadProgress;\n    this.proxySettings = proxySettings;\n    this.keepAlive = keepAlive;\n    this.decompressResponse = decompressResponse;\n    this.requestId = this.headers.get(\"x-ms-client-request-id\") || generateUuid();\n  }\n\n  /**\n   * Validates that the required properties such as method, url, headers[\"Content-Type\"],\n   * headers[\"accept-language\"] are defined. It will throw an error if one of the above\n   * mentioned properties are not defined.\n   */\n  validateRequestProperties(): void {\n    if (!this.method) {\n      throw new Error(\"WebResource.method is required.\");\n    }\n    if (!this.url) {\n      throw new Error(\"WebResource.url is required.\");\n    }\n  }\n\n  /**\n   * Prepares the request.\n   * @param options - Options to provide for preparing the request.\n   * @returns Returns the prepared WebResource (HTTP Request) object that needs to be given to the request pipeline.\n   */\n  prepare(options: RequestPrepareOptions): WebResource {\n    if (!options) {\n      throw new Error(\"options object is required\");\n    }\n\n    if (\n      options.method === undefined ||\n      options.method === null ||\n      typeof options.method.valueOf() !== \"string\"\n    ) {\n      throw new Error(\"options.method must be a string.\");\n    }\n\n    if (options.url && options.pathTemplate) {\n      throw new Error(\n        \"options.url and options.pathTemplate are mutually exclusive. Please provide exactly one of them.\"\n      );\n    }\n\n    if (\n      (options.pathTemplate === undefined ||\n        options.pathTemplate === null ||\n        typeof options.pathTemplate.valueOf() !== \"string\") &&\n      (options.url === undefined ||\n        options.url === null ||\n        typeof options.url.valueOf() !== \"string\")\n    ) {\n      throw new Error(\"Please provide exactly one of options.pathTemplate or options.url.\");\n    }\n\n    // set the url if it is provided.\n    if (options.url) {\n      if (typeof options.url !== \"string\") {\n        throw new Error('options.url must be of type \"string\".');\n      }\n      this.url = options.url;\n    }\n\n    // set the method\n    if (options.method) {\n      const validMethods = [\"GET\", \"PUT\", \"HEAD\", \"DELETE\", \"OPTIONS\", \"POST\", \"PATCH\", \"TRACE\"];\n      if (validMethods.indexOf(options.method.toUpperCase()) === -1) {\n        throw new Error(\n          'The provided method \"' +\n            options.method +\n            '\" is invalid. Supported HTTP methods are: ' +\n            JSON.stringify(validMethods)\n        );\n      }\n    }\n    this.method = options.method.toUpperCase() as HttpMethods;\n\n    // construct the url if path template is provided\n    if (options.pathTemplate) {\n      const { pathTemplate, pathParameters } = options;\n      if (typeof pathTemplate !== \"string\") {\n        throw new Error('options.pathTemplate must be of type \"string\".');\n      }\n      if (!options.baseUrl) {\n        options.baseUrl = \"https://management.azure.com\";\n      }\n      const baseUrl = options.baseUrl;\n      let url =\n        baseUrl +\n        (baseUrl.endsWith(\"/\") ? \"\" : \"/\") +\n        (pathTemplate.startsWith(\"/\") ? pathTemplate.slice(1) : pathTemplate);\n      const segments = url.match(/({[\\w-]*\\s*[\\w-]*})/gi);\n      if (segments && segments.length) {\n        if (!pathParameters) {\n          throw new Error(\n            `pathTemplate: ${pathTemplate} has been provided. Hence, options.pathParameters must also be provided.`\n          );\n        }\n        segments.forEach(function (item) {\n          const pathParamName = item.slice(1, -1);\n          const pathParam = (pathParameters as { [key: string]: any })[pathParamName];\n          if (\n            pathParam === null ||\n            pathParam === undefined ||\n            !(typeof pathParam === \"string\" || typeof pathParam === \"object\")\n          ) {\n            const stringifiedPathParameters = JSON.stringify(pathParameters, undefined, 2);\n            throw new Error(\n              `pathTemplate: ${pathTemplate} contains the path parameter ${pathParamName}` +\n                ` however, it is not present in parameters: ${stringifiedPathParameters}.` +\n                `The value of the path parameter can either be a \"string\" of the form { ${pathParamName}: \"some sample value\" } or ` +\n                `it can be an \"object\" of the form { \"${pathParamName}\": { value: \"some sample value\", skipUrlEncoding: true } }.`\n            );\n          }\n\n          if (typeof pathParam.valueOf() === \"string\") {\n            url = url.replace(item, encodeURIComponent(pathParam));\n          }\n\n          if (typeof pathParam.valueOf() === \"object\") {\n            if (!pathParam.value) {\n              throw new Error(\n                `options.pathParameters[${pathParamName}] is of type \"object\" but it does not contain a \"value\" property.`\n              );\n            }\n            if (pathParam.skipUrlEncoding) {\n              url = url.replace(item, pathParam.value);\n            } else {\n              url = url.replace(item, encodeURIComponent(pathParam.value));\n            }\n          }\n        });\n      }\n      this.url = url;\n    }\n\n    // append query parameters to the url if they are provided. They can be provided with pathTemplate or url option.\n    if (options.queryParameters) {\n      const queryParameters = options.queryParameters;\n      if (typeof queryParameters !== \"object\") {\n        throw new Error(\n          `options.queryParameters must be of type object. It should be a JSON object ` +\n            `of \"query-parameter-name\" as the key and the \"query-parameter-value\" as the value. ` +\n            `The \"query-parameter-value\" may be fo type \"string\" or an \"object\" of the form { value: \"query-parameter-value\", skipUrlEncoding: true }.`\n        );\n      }\n      // append question mark if it is not present in the url\n      if (this.url && this.url.indexOf(\"?\") === -1) {\n        this.url += \"?\";\n      }\n      // construct queryString\n      const queryParams = [];\n      // We need to populate this.query as a dictionary if the request is being used for Sway's validateRequest().\n      this.query = {};\n      for (const queryParamName in queryParameters) {\n        const queryParam: any = queryParameters[queryParamName];\n        if (queryParam) {\n          if (typeof queryParam === \"string\") {\n            queryParams.push(queryParamName + \"=\" + encodeURIComponent(queryParam));\n            this.query[queryParamName] = encodeURIComponent(queryParam);\n          } else if (typeof queryParam === \"object\") {\n            if (!queryParam.value) {\n              throw new Error(\n                `options.queryParameters[${queryParamName}] is of type \"object\" but it does not contain a \"value\" property.`\n              );\n            }\n            if (queryParam.skipUrlEncoding) {\n              queryParams.push(queryParamName + \"=\" + queryParam.value);\n              this.query[queryParamName] = queryParam.value;\n            } else {\n              queryParams.push(queryParamName + \"=\" + encodeURIComponent(queryParam.value));\n              this.query[queryParamName] = encodeURIComponent(queryParam.value);\n            }\n          }\n        }\n      } // end-of-for\n      // append the queryString\n      this.url += queryParams.join(\"&\");\n    }\n\n    // add headers to the request if they are provided\n    if (options.headers) {\n      const headers = options.headers;\n      for (const headerName of Object.keys(options.headers)) {\n        this.headers.set(headerName, headers[headerName]);\n      }\n    }\n    // ensure accept-language is set correctly\n    if (!this.headers.get(\"accept-language\")) {\n      this.headers.set(\"accept-language\", \"en-US\");\n    }\n    // ensure the request-id is set correctly\n    if (!this.headers.get(\"x-ms-client-request-id\") && !options.disableClientRequestId) {\n      this.headers.set(\"x-ms-client-request-id\", this.requestId);\n    }\n\n    // default\n    if (!this.headers.get(\"Content-Type\")) {\n      this.headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n    }\n\n    // set the request body. request.js automatically sets the Content-Length request header, so we need not set it explicitly\n    this.body = options.body;\n    if (options.body !== undefined && options.body !== null) {\n      // body as a stream special case. set the body as-is and check for some special request headers specific to sending a stream.\n      if (options.bodyIsStream) {\n        if (!this.headers.get(\"Transfer-Encoding\")) {\n          this.headers.set(\"Transfer-Encoding\", \"chunked\");\n        }\n        if (this.headers.get(\"Content-Type\") !== \"application/octet-stream\") {\n          this.headers.set(\"Content-Type\", \"application/octet-stream\");\n        }\n      } else {\n        if (options.serializationMapper) {\n          this.body = new Serializer(options.mappers).serialize(\n            options.serializationMapper,\n            options.body,\n            \"requestBody\"\n          );\n        }\n        if (!options.disableJsonStringifyOnBody) {\n          this.body = JSON.stringify(options.body);\n        }\n      }\n    }\n\n    if (options.spanOptions) {\n      this.spanOptions = options.spanOptions;\n    }\n\n    if (options.tracingContext) {\n      this.tracingContext = options.tracingContext;\n    }\n\n    this.abortSignal = options.abortSignal;\n    this.onDownloadProgress = options.onDownloadProgress;\n    this.onUploadProgress = options.onUploadProgress;\n\n    return this;\n  }\n\n  /**\n   * Clone this WebResource HTTP request object.\n   * @returns The clone of this WebResource HTTP request object.\n   */\n  clone(): WebResource {\n    const result = new WebResource(\n      this.url,\n      this.method,\n      this.body,\n      this.query,\n      this.headers && this.headers.clone(),\n      this.streamResponseBody,\n      this.withCredentials,\n      this.abortSignal,\n      this.timeout,\n      this.onUploadProgress,\n      this.onDownloadProgress,\n      this.proxySettings,\n      this.keepAlive,\n      this.decompressResponse,\n      this.streamResponseStatusCodes\n    );\n\n    if (this.formData) {\n      result.formData = this.formData;\n    }\n\n    if (this.operationSpec) {\n      result.operationSpec = this.operationSpec;\n    }\n\n    if (this.shouldDeserialize) {\n      result.shouldDeserialize = this.shouldDeserialize;\n    }\n\n    if (this.operationResponseGetter) {\n      result.operationResponseGetter = this.operationResponseGetter;\n    }\n\n    return result;\n  }\n}\n\n/**\n * Options to prepare an outgoing HTTP request.\n */\nexport interface RequestPrepareOptions {\n  /**\n   * The HTTP request method. Valid values are \"GET\", \"PUT\", \"HEAD\", \"DELETE\", \"OPTIONS\", \"POST\",\n   * or \"PATCH\".\n   */\n  method: HttpMethods;\n  /**\n   * The request url. It may or may not have query parameters in it. Either provide the \"url\" or\n   * provide the \"pathTemplate\" in the options object. Both the options are mutually exclusive.\n   */\n  url?: string;\n  /**\n   * A dictionary of query parameters to be appended to the url, where\n   * the \"key\" is the \"query-parameter-name\" and the \"value\" is the \"query-parameter-value\".\n   * The \"query-parameter-value\" can be of type \"string\" or it can be of type \"object\".\n   * The \"object\" format should be used when you want to skip url encoding. While using the object format,\n   * the object must have a property named value which provides the \"query-parameter-value\".\n   * Example:\n   *    - query-parameter-value in \"object\" format: `{ \"query-parameter-name\": { value: \"query-parameter-value\", skipUrlEncoding: true } }`\n   *    - query-parameter-value in \"string\" format: `{ \"query-parameter-name\": \"query-parameter-value\"}`.\n   * Note: \"If options.url already has some query parameters, then the value provided in options.queryParameters will be appended to the url.\n   */\n  queryParameters?: { [key: string]: any | ParameterValue };\n  /**\n   * The path template of the request url. Either provide the \"url\" or provide the \"pathTemplate\" in\n   * the options object. Both the options are mutually exclusive.\n   * Example: `/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Storage/storageAccounts/{accountName}`\n   */\n  pathTemplate?: string;\n  /**\n   * The base url of the request. Default value is: \"https://management.azure.com\". This is\n   * applicable only with pathTemplate. If you are providing options.url then it is expected that\n   * you provide the complete url.\n   */\n  baseUrl?: string;\n  /**\n   * A dictionary of path parameters that need to be replaced with actual values in the pathTemplate.\n   * Here the key is the \"path-parameter-name\" and the value is the \"path-parameter-value\".\n   * The \"path-parameter-value\" can be of type \"string\"  or it can be of type \"object\".\n   * The \"object\" format should be used when you want to skip url encoding. While using the object format,\n   * the object must have a property named value which provides the \"path-parameter-value\".\n   * Example:\n   *    - path-parameter-value in \"object\" format: `{ \"path-parameter-name\": { value: \"path-parameter-value\", skipUrlEncoding: true } }`\n   *    - path-parameter-value in \"string\" format: `{ \"path-parameter-name\": \"path-parameter-value\" }`.\n   */\n  pathParameters?: { [key: string]: any | ParameterValue };\n  /**\n   * Form data, used to build the request body.\n   */\n  formData?: { [key: string]: any };\n  /**\n   * A dictionary of request headers that need to be applied to the request.\n   * Here the key is the \"header-name\" and the value is the \"header-value\". The header-value MUST be of type string.\n   *  - ContentType must be provided with the key name as \"Content-Type\". Default value \"application/json; charset=utf-8\".\n   *  - \"Transfer-Encoding\" is set to \"chunked\" by default if \"options.bodyIsStream\" is set to true.\n   *  - \"Content-Type\" is set to \"application/octet-stream\" by default if \"options.bodyIsStream\" is set to true.\n   *  - \"accept-language\" by default is set to \"en-US\"\n   *  - \"x-ms-client-request-id\" by default is set to a new Guid. To not generate a guid for the request, please set options.disableClientRequestId to true\n   */\n  headers?: { [key: string]: any };\n  /**\n   * When set to true, instructs the client to not set \"x-ms-client-request-id\" header to a new Guid().\n   */\n  disableClientRequestId?: boolean;\n  /**\n   * The request body. It can be of any type. This value will be serialized if it is not a stream.\n   */\n  body?: any;\n  /**\n   * Provides information on how to serialize the request body.\n   */\n  serializationMapper?: Mapper;\n  /**\n   * A dictionary of mappers that may be used while [de]serialization.\n   */\n  mappers?: { [x: string]: any };\n  /**\n   * Provides information on how to deserialize the response body.\n   */\n  deserializationMapper?: Record<string, unknown>;\n  /**\n   * Indicates whether this method should JSON.stringify() the request body. Default value: false.\n   */\n  disableJsonStringifyOnBody?: boolean;\n  /**\n   * Indicates whether the request body is a stream (useful for file upload scenarios).\n   */\n  bodyIsStream?: boolean;\n  /**\n   * Signal of an abort controller. Can be used to abort both sending a network request and waiting for a response.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Allows keeping track of the progress of uploading the outgoing request.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n  /**\n   * Allows keeping track of the progress of downloading the incoming response.\n   */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n  /**\n   * Tracing: Options used to create a span when tracing is enabled.\n   */\n  spanOptions?: SpanOptions;\n  /**\n   * Tracing: Context used when creating spans.\n   */\n  tracingContext?: Context;\n}\n\n/**\n * The Parameter value provided for path or query parameters in RequestPrepareOptions\n */\nexport interface ParameterValue {\n  /**\n   * Value of the parameter.\n   */\n  value: any;\n  /**\n   * Disables URL encoding if set to true.\n   */\n  skipUrlEncoding: boolean;\n  /**\n   * Parameter values may contain any other property.\n   */\n  [key: string]: any;\n}\n\n/**\n * Describes the base structure of the options object that will be used in every operation.\n */\nexport interface RequestOptionsBase {\n  /**\n   * will be applied before the request is sent.\n   */\n  customHeaders?: { [key: string]: string };\n\n  /**\n   * Signal of an abort controller. Can be used to abort both sending a network request and waiting for a response.\n   */\n  abortSignal?: AbortSignalLike;\n\n  /**\n   * The number of milliseconds a request can take before automatically being terminated.\n   * If the request is terminated, an `AbortError` is thrown.\n   */\n  timeout?: number;\n\n  /**\n   * Callback which fires upon upload progress.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Callback which fires upon download progress.\n   */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Whether or not the HttpOperationResponse should be deserialized. If this is undefined, then the\n   * HttpOperationResponse should be deserialized.\n   */\n  shouldDeserialize?: boolean | ((response: HttpOperationResponse) => boolean);\n\n  /**\n   * Tracing: Context used when creating spans.\n   */\n  tracingContext?: Context;\n\n  /**\n   * May contain other properties.\n   */\n  [key: string]: any;\n\n  /**\n   * Options to override XML parsing/building behavior.\n   */\n  serializerOptions?: SerializerOptions;\n}\n"]}