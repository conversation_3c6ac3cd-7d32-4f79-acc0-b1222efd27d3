// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
export { createSerializer, MapperTypeNames } from "./serializer";
export { ServiceClient } from "./serviceClient";
export { createClientPipeline } from "./pipeline";
export { XML_ATTRKEY, XML_CHARKEY, } from "./interfaces";
export { deserializationPolicy, deserializationPolicyName, } from "./deserializationPolicy";
export { serializationPolicy, serializationPolicyName, } from "./serializationPolicy";
export { authorizeRequestOnClaimChallenge } from "./authorizeRequestOnClaimChallenge";
export { authorizeRequestOnTenantChallenge } from "./authorizeRequestOnTenantChallenge";
//# sourceMappingURL=index.js.map