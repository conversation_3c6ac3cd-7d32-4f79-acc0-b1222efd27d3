{"version": 3, "file": "defaultHttpClient.js", "sourceRoot": "", "sources": ["../../src/defaultHttpClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,oBAAoB,EAAE,MAAM,kBAAkB,CAAC;AAExD;;GAEG;AACH,MAAM,UAAU,uBAAuB;IACrC,OAAO,oBAAoB,EAAE,CAAC;AAChC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpClient } from \"./interfaces\";\nimport { createNodeHttpClient } from \"./nodeHttpClient\";\n\n/**\n * Create the correct HttpClient for the current environment.\n */\nexport function createDefaultHttpClient(): HttpClient {\n  return createNodeHttpClient();\n}\n"]}