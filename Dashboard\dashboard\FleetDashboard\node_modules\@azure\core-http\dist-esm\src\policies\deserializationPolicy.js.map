{"version": 3, "file": "deserializationPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/deserializationPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EACL,iBAAiB,GAIlB,MAAM,iBAAiB,CAAC;AACzB,OAAO,EAAqB,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAE3E,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAG3C,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAEzC,OAAO,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;AA+BvC;;;GAGG;AACH,MAAM,UAAU,qBAAqB,CACnC,2BAAyD,EACzD,cAAkC;IAElC,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,EAAE,EAAE;YACnE,OAAO,IAAI,qBAAqB,CAC9B,UAAU,EACV,OAAO,EACP,2BAA2B,EAC3B,cAAc,CACf,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,MAAM,uBAAuB,GAAG,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;AACzE,MAAM,CAAC,MAAM,sBAAsB,GAAG,CAAC,iBAAiB,EAAE,sBAAsB,CAAC,CAAC;AAElF,MAAM,CAAC,MAAM,6BAA6B,GAA2B;IACnE,oBAAoB,EAAE;QACpB,IAAI,EAAE,uBAAuB;QAC7B,GAAG,EAAE,sBAAsB;KAC5B;CACF,CAAC;AAEF;;;GAGG;AACH,MAAM,OAAO,qBAAsB,SAAQ,iBAAiB;IAK1D,YACE,UAAyB,EACzB,oBAA0C,EAC1C,2BAAyD,EACzD,iBAAoC,EAAE;;QAEtC,KAAK,CAAC,UAAU,EAAE,oBAAoB,CAAC,CAAC;QAExC,IAAI,CAAC,gBAAgB;YACnB,CAAC,2BAA2B,IAAI,2BAA2B,CAAC,IAAI,CAAC,IAAI,uBAAuB,CAAC;QAC/F,IAAI,CAAC,eAAe;YAClB,CAAC,2BAA2B,IAAI,2BAA2B,CAAC,GAAG,CAAC,IAAI,sBAAsB,CAAC;QAC7F,IAAI,CAAC,UAAU,GAAG,MAAA,cAAc,CAAC,UAAU,mCAAI,WAAW,CAAC;IAC7D,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,OAAwB;QAC/C,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,QAA+B,EAAE,EAAE,CACpF,uBAAuB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,EAAE,QAAQ,EAAE;YAC7E,UAAU,EAAE,IAAI,CAAC,UAAU;SAC5B,CAAC,CACH,CAAC;IACJ,CAAC;CACF;AAED,SAAS,oBAAoB,CAC3B,cAAqC;IAErC,IAAI,MAAqC,CAAC;IAC1C,MAAM,OAAO,GAAoB,cAAc,CAAC,OAAO,CAAC;IACxD,MAAM,aAAa,GAA8B,OAAO,CAAC,aAAa,CAAC;IACvE,IAAI,aAAa,EAAE;QACjB,MAAM,uBAAuB,GAKa,OAAO,CAAC,uBAAuB,CAAC;QAC1E,IAAI,CAAC,uBAAuB,EAAE;YAC5B,MAAM,GAAG,aAAa,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;SACzD;aAAM;YACL,MAAM,GAAG,uBAAuB,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;SACjE;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,yBAAyB,CAAC,cAAqC;IACtE,MAAM,iBAAiB,GACrB,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC;IAC3C,IAAI,MAAe,CAAC;IACpB,IAAI,iBAAiB,KAAK,SAAS,EAAE;QACnC,MAAM,GAAG,IAAI,CAAC;KACf;SAAM,IAAI,OAAO,iBAAiB,KAAK,SAAS,EAAE;QACjD,MAAM,GAAG,iBAAiB,CAAC;KAC5B;SAAM;QACL,MAAM,GAAG,iBAAiB,CAAC,cAAc,CAAC,CAAC;KAC5C;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,uBAAuB,CACrC,gBAA0B,EAC1B,eAAyB,EACzB,QAA+B,EAC/B,UAA6B,EAAE;;IAE/B,MAAM,cAAc,GAAgC;QAClD,QAAQ,EAAE,MAAA,OAAO,CAAC,QAAQ,mCAAI,EAAE;QAChC,WAAW,EAAE,MAAA,OAAO,CAAC,WAAW,mCAAI,KAAK;QACzC,UAAU,EAAE,MAAA,OAAO,CAAC,UAAU,mCAAI,WAAW;KAC9C,CAAC;IACF,OAAO,KAAK,CAAC,gBAAgB,EAAE,eAAe,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC,IAAI,CAC5E,CAAC,cAAc,EAAE,EAAE;QACjB,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,EAAE;YAC9C,OAAO,cAAc,CAAC;SACvB;QAED,MAAM,aAAa,GAAG,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC;QAC3D,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE;YAC9C,OAAO,cAAc,CAAC;SACvB;QAED,MAAM,YAAY,GAAG,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAE1D,MAAM,EAAE,KAAK,EAAE,oBAAoB,EAAE,GAAG,mBAAmB,CACzD,cAAc,EACd,aAAa,EACb,YAAY,CACb,CAAC;QACF,IAAI,KAAK,EAAE;YACT,MAAM,KAAK,CAAC;SACb;aAAM,IAAI,oBAAoB,EAAE;YAC/B,OAAO,cAAc,CAAC;SACvB;QAED,oEAAoE;QACpE,sCAAsC;QACtC,IAAI,YAAY,EAAE;YAChB,IAAI,YAAY,CAAC,UAAU,EAAE;gBAC3B,IAAI,kBAAkB,GAAQ,cAAc,CAAC,UAAU,CAAC;gBACxD,IAAI,aAAa,CAAC,KAAK,IAAI,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,QAAQ,EAAE;oBACpF,kBAAkB;wBAChB,OAAO,kBAAkB,KAAK,QAAQ;4BACpC,CAAC,CAAC,kBAAkB,CAAC,YAAY,CAAC,UAAU,CAAC,cAAe,CAAC;4BAC7D,CAAC,CAAC,EAAE,CAAC;iBACV;gBACD,IAAI;oBACF,cAAc,CAAC,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC,WAAW,CAC9D,YAAY,CAAC,UAAU,EACvB,kBAAkB,EAClB,yBAAyB,EACzB,OAAO,CACR,CAAC;iBACH;gBAAC,OAAO,UAAe,EAAE;oBACxB,MAAM,SAAS,GAAG,IAAI,SAAS,CAC7B,SAAS,UAAU,iDAAiD,cAAc,CAAC,UAAU,EAAE,EAC/F,SAAS,EACT,cAAc,CAAC,MAAM,EACrB,cAAc,CAAC,OAAO,EACtB,cAAc,CACf,CAAC;oBACF,MAAM,SAAS,CAAC;iBACjB;aACF;iBAAM,IAAI,aAAa,CAAC,UAAU,KAAK,MAAM,EAAE;gBAC9C,uGAAuG;gBACvG,cAAc,CAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG,CAAC;aAC7E;YAED,IAAI,YAAY,CAAC,aAAa,EAAE;gBAC9B,cAAc,CAAC,aAAa,GAAG,aAAa,CAAC,UAAU,CAAC,WAAW,CACjE,YAAY,CAAC,aAAa,EAC1B,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,EAC/B,4BAA4B,EAC5B,OAAO,CACR,CAAC;aACH;SACF;QAED,OAAO,cAAc,CAAC;IACxB,CAAC,CACF,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAAC,aAA4B;IACxD,MAAM,mBAAmB,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;IACjE,OAAO,CACL,mBAAmB,CAAC,MAAM,KAAK,CAAC;QAChC,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,IAAI,mBAAmB,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAC3E,CAAC;AACJ,CAAC;AAED,SAAS,mBAAmB,CAC1B,cAAqC,EACrC,aAA4B,EAC5B,YAA2C;;IAE3C,MAAM,iBAAiB,GAAG,GAAG,IAAI,cAAc,CAAC,MAAM,IAAI,cAAc,CAAC,MAAM,GAAG,GAAG,CAAC;IACtF,MAAM,oBAAoB,GAAY,oBAAoB,CAAC,aAAa,CAAC;QACvE,CAAC,CAAC,iBAAiB;QACnB,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;IAEnB,IAAI,oBAAoB,EAAE;QACxB,IAAI,YAAY,EAAE;YAChB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;gBACzB,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,oBAAoB,EAAE,KAAK,EAAE,CAAC;aACrD;SACF;aAAM;YACL,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,oBAAoB,EAAE,KAAK,EAAE,CAAC;SACrD;KACF;IAED,MAAM,iBAAiB,GAAG,YAAY,aAAZ,YAAY,cAAZ,YAAY,GAAI,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC;IAC1E,MAAM,SAAS,GACb,CAAA,MAAA,cAAc,CAAC,OAAO,CAAC,yBAAyB,0CAAE,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC;QAC5E,cAAc,CAAC,OAAO,CAAC,kBAAkB,CAAC;IAC5C,MAAM,mBAAmB,GAAG,SAAS;QACnC,CAAC,CAAC,2BAA2B,cAAc,CAAC,MAAM,EAAE;QACpD,CAAC,CAAE,cAAc,CAAC,UAAqB,CAAC;IAE1C,MAAM,KAAK,GAAG,IAAI,SAAS,CACzB,mBAAmB,EACnB,SAAS,EACT,cAAc,CAAC,MAAM,EACrB,cAAc,CAAC,OAAO,EACtB,cAAc,CACf,CAAC;IAEF,yFAAyF;IACzF,sDAAsD;IACtD,IAAI,CAAC,iBAAiB,EAAE;QACtB,MAAM,KAAK,CAAC;KACb;IAED,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,UAAU,CAAC;IACvD,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,aAAa,CAAC;IAE7D,IAAI;QACF,iFAAiF;QACjF,mDAAmD;QACnD,IAAI,cAAc,CAAC,UAAU,EAAE;YAC7B,MAAM,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;YAC7C,IAAI,WAAW,CAAC;YAChB,IAAI,iBAAiB,EAAE;gBACrB,IAAI,kBAAkB,GAAQ,UAAU,CAAC;gBACzC,IAAI,aAAa,CAAC,KAAK,IAAI,iBAAiB,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,QAAQ,EAAE;oBAC9E,kBAAkB;wBAChB,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,iBAAiB,CAAC,cAAe,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;iBACvF;gBACD,WAAW,GAAG,aAAa,CAAC,UAAU,CAAC,WAAW,CAChD,iBAAiB,EACjB,kBAAkB,EAClB,2BAA2B,CAC5B,CAAC;aACH;YAED,MAAM,aAAa,GAAQ,UAAU,CAAC,KAAK,IAAI,WAAW,IAAI,UAAU,CAAC;YACzE,KAAK,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC;YAChC,IAAI,aAAa,CAAC,OAAO,EAAE;gBACzB,KAAK,CAAC,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC;aACvC;YAED,IAAI,iBAAiB,EAAE;gBACrB,KAAK,CAAC,QAAS,CAAC,UAAU,GAAG,WAAW,CAAC;aAC1C;SACF;QAED,mFAAmF;QACnF,IAAI,cAAc,CAAC,OAAO,IAAI,oBAAoB,EAAE;YAClD,KAAK,CAAC,QAAS,CAAC,aAAa,GAAG,aAAa,CAAC,UAAU,CAAC,WAAW,CAClE,oBAAoB,EACpB,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,EAC/B,4BAA4B,CAC7B,CAAC;SACH;KACF;IAAC,OAAO,YAAiB,EAAE;QAC1B,KAAK,CAAC,OAAO,GAAG,UAAU,YAAY,CAAC,OAAO,mDAAmD,cAAc,CAAC,UAAU,6BAA6B,CAAC;KACzJ;IAED,OAAO,EAAE,KAAK,EAAE,oBAAoB,EAAE,KAAK,EAAE,CAAC;AAChD,CAAC;AAED,SAAS,KAAK,CACZ,gBAA0B,EAC1B,eAAyB,EACzB,iBAAwC,EACxC,IAAiC;;IAEjC,MAAM,YAAY,GAAG,CAAC,GAA6B,EAAkB,EAAE;QACrE,MAAM,GAAG,GAAG,UAAU,GAAG,gDAAgD,iBAAiB,CAAC,UAAU,GAAG,CAAC;QACzG,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,IAAI,SAAS,CAAC,WAAW,CAAC;QAClD,MAAM,CAAC,GAAG,IAAI,SAAS,CACrB,GAAG,EACH,OAAO,EACP,iBAAiB,CAAC,MAAM,EACxB,iBAAiB,CAAC,OAAO,EACzB,iBAAiB,CAClB,CAAC;QACF,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC;IAEF,MAAM,SAAS,GACb,CAAA,MAAA,iBAAiB,CAAC,OAAO,CAAC,yBAAyB,0CAAE,GAAG,CAAC,iBAAiB,CAAC,MAAM,CAAC;QAClF,iBAAiB,CAAC,OAAO,CAAC,kBAAkB,CAAC;IAC/C,IAAI,CAAC,SAAS,IAAI,iBAAiB,CAAC,UAAU,EAAE;QAC9C,MAAM,IAAI,GAAG,iBAAiB,CAAC,UAAU,CAAC;QAC1C,MAAM,WAAW,GAAW,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;QAChF,MAAM,iBAAiB,GAAa,CAAC,WAAW;YAC9C,CAAC,CAAC,EAAE;YACJ,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;QACvE,IACE,iBAAiB,CAAC,MAAM,KAAK,CAAC;YAC9B,iBAAiB,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EACjF;YACA,OAAO,IAAI,OAAO,CAAwB,CAAC,OAAO,EAAE,EAAE;gBACpD,iBAAiB,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAChD,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;SACxB;aAAM,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;YAC3F,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC;iBACxB,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;gBACb,iBAAiB,CAAC,UAAU,GAAG,IAAI,CAAC;gBACpC,OAAO,iBAAiB,CAAC;YAC3B,CAAC,CAAC;iBACD,KAAK,CAAC,YAAY,CAAC,CAAC;SACxB;KACF;IAED,OAAO,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAC5C,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"./requestPolicy\";\nimport { SerializerOptions, XML_CHARKEY } from \"../util/serializer.common\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { MapperType } from \"../serializer\";\nimport { OperationResponse } from \"../operationResponse\";\nimport { OperationSpec } from \"../operationSpec\";\nimport { RestError } from \"../restError\";\nimport { WebResourceLike } from \"../webResource\";\nimport { parseXML } from \"../util/xml\";\n\n/**\n * Options to configure API response deserialization.\n */\nexport interface DeserializationOptions {\n  /**\n   * Configures the expected content types for the deserialization of\n   * JSON and XML response bodies.\n   */\n  expectedContentTypes: DeserializationContentTypes;\n}\n\n/**\n * The content-types that will indicate that an operation response should be deserialized in a\n * particular way.\n */\nexport interface DeserializationContentTypes {\n  /**\n   * The content-types that indicate that an operation response should be deserialized as JSON.\n   * Defaults to [ \"application/json\", \"text/json\" ].\n   */\n  json?: string[];\n\n  /**\n   * The content-types that indicate that an operation response should be deserialized as XML.\n   * Defaults to [ \"application/xml\", \"application/atom+xml\" ].\n   */\n  xml?: string[];\n}\n\n/**\n * Create a new serialization RequestPolicyCreator that will serialized HTTP request bodies as they\n * pass through the HTTP pipeline.\n */\nexport function deserializationPolicy(\n  deserializationContentTypes?: DeserializationContentTypes,\n  parsingOptions?: SerializerOptions\n): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new DeserializationPolicy(\n        nextPolicy,\n        options,\n        deserializationContentTypes,\n        parsingOptions\n      );\n    },\n  };\n}\n\nexport const defaultJsonContentTypes = [\"application/json\", \"text/json\"];\nexport const defaultXmlContentTypes = [\"application/xml\", \"application/atom+xml\"];\n\nexport const DefaultDeserializationOptions: DeserializationOptions = {\n  expectedContentTypes: {\n    json: defaultJsonContentTypes,\n    xml: defaultXmlContentTypes,\n  },\n};\n\n/**\n * A RequestPolicy that will deserialize HTTP response bodies and headers as they pass through the\n * HTTP pipeline.\n */\nexport class DeserializationPolicy extends BaseRequestPolicy {\n  public readonly jsonContentTypes: string[];\n  public readonly xmlContentTypes: string[];\n  public readonly xmlCharKey: string;\n\n  constructor(\n    nextPolicy: RequestPolicy,\n    requestPolicyOptions: RequestPolicyOptions,\n    deserializationContentTypes?: DeserializationContentTypes,\n    parsingOptions: SerializerOptions = {}\n  ) {\n    super(nextPolicy, requestPolicyOptions);\n\n    this.jsonContentTypes =\n      (deserializationContentTypes && deserializationContentTypes.json) || defaultJsonContentTypes;\n    this.xmlContentTypes =\n      (deserializationContentTypes && deserializationContentTypes.xml) || defaultXmlContentTypes;\n    this.xmlCharKey = parsingOptions.xmlCharKey ?? XML_CHARKEY;\n  }\n\n  public async sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    return this._nextPolicy.sendRequest(request).then((response: HttpOperationResponse) =>\n      deserializeResponseBody(this.jsonContentTypes, this.xmlContentTypes, response, {\n        xmlCharKey: this.xmlCharKey,\n      })\n    );\n  }\n}\n\nfunction getOperationResponse(\n  parsedResponse: HttpOperationResponse\n): undefined | OperationResponse {\n  let result: OperationResponse | undefined;\n  const request: WebResourceLike = parsedResponse.request;\n  const operationSpec: OperationSpec | undefined = request.operationSpec;\n  if (operationSpec) {\n    const operationResponseGetter:\n      | undefined\n      | ((\n          operationSpec: OperationSpec,\n          response: HttpOperationResponse\n        ) => undefined | OperationResponse) = request.operationResponseGetter;\n    if (!operationResponseGetter) {\n      result = operationSpec.responses[parsedResponse.status];\n    } else {\n      result = operationResponseGetter(operationSpec, parsedResponse);\n    }\n  }\n  return result;\n}\n\nfunction shouldDeserializeResponse(parsedResponse: HttpOperationResponse): boolean {\n  const shouldDeserialize: undefined | boolean | ((response: HttpOperationResponse) => boolean) =\n    parsedResponse.request.shouldDeserialize;\n  let result: boolean;\n  if (shouldDeserialize === undefined) {\n    result = true;\n  } else if (typeof shouldDeserialize === \"boolean\") {\n    result = shouldDeserialize;\n  } else {\n    result = shouldDeserialize(parsedResponse);\n  }\n  return result;\n}\n\n/**\n * Given a particular set of content types to parse as either JSON or XML, consumes the HTTP response to produce the result object defined by the request's {@link OperationSpec}.\n * @param jsonContentTypes - Response content types to parse the body as JSON.\n * @param xmlContentTypes  - Response content types to parse the body as XML.\n * @param response - HTTP Response from the pipeline.\n * @param options  - Options to the serializer, mostly for configuring the XML parser if needed.\n * @returns A parsed {@link HttpOperationResponse} object that can be returned by the {@link ServiceClient}.\n */\nexport function deserializeResponseBody(\n  jsonContentTypes: string[],\n  xmlContentTypes: string[],\n  response: HttpOperationResponse,\n  options: SerializerOptions = {}\n): Promise<HttpOperationResponse> {\n  const updatedOptions: Required<SerializerOptions> = {\n    rootName: options.rootName ?? \"\",\n    includeRoot: options.includeRoot ?? false,\n    xmlCharKey: options.xmlCharKey ?? XML_CHARKEY,\n  };\n  return parse(jsonContentTypes, xmlContentTypes, response, updatedOptions).then(\n    (parsedResponse) => {\n      if (!shouldDeserializeResponse(parsedResponse)) {\n        return parsedResponse;\n      }\n\n      const operationSpec = parsedResponse.request.operationSpec;\n      if (!operationSpec || !operationSpec.responses) {\n        return parsedResponse;\n      }\n\n      const responseSpec = getOperationResponse(parsedResponse);\n\n      const { error, shouldReturnResponse } = handleErrorResponse(\n        parsedResponse,\n        operationSpec,\n        responseSpec\n      );\n      if (error) {\n        throw error;\n      } else if (shouldReturnResponse) {\n        return parsedResponse;\n      }\n\n      // An operation response spec does exist for current status code, so\n      // use it to deserialize the response.\n      if (responseSpec) {\n        if (responseSpec.bodyMapper) {\n          let valueToDeserialize: any = parsedResponse.parsedBody;\n          if (operationSpec.isXML && responseSpec.bodyMapper.type.name === MapperType.Sequence) {\n            valueToDeserialize =\n              typeof valueToDeserialize === \"object\"\n                ? valueToDeserialize[responseSpec.bodyMapper.xmlElementName!]\n                : [];\n          }\n          try {\n            parsedResponse.parsedBody = operationSpec.serializer.deserialize(\n              responseSpec.bodyMapper,\n              valueToDeserialize,\n              \"operationRes.parsedBody\",\n              options\n            );\n          } catch (innerError: any) {\n            const restError = new RestError(\n              `Error ${innerError} occurred in deserializing the responseBody - ${parsedResponse.bodyAsText}`,\n              undefined,\n              parsedResponse.status,\n              parsedResponse.request,\n              parsedResponse\n            );\n            throw restError;\n          }\n        } else if (operationSpec.httpMethod === \"HEAD\") {\n          // head methods never have a body, but we return a boolean to indicate presence/absence of the resource\n          parsedResponse.parsedBody = response.status >= 200 && response.status < 300;\n        }\n\n        if (responseSpec.headersMapper) {\n          parsedResponse.parsedHeaders = operationSpec.serializer.deserialize(\n            responseSpec.headersMapper,\n            parsedResponse.headers.toJson(),\n            \"operationRes.parsedHeaders\",\n            options\n          );\n        }\n      }\n\n      return parsedResponse;\n    }\n  );\n}\n\nfunction isOperationSpecEmpty(operationSpec: OperationSpec): boolean {\n  const expectedStatusCodes = Object.keys(operationSpec.responses);\n  return (\n    expectedStatusCodes.length === 0 ||\n    (expectedStatusCodes.length === 1 && expectedStatusCodes[0] === \"default\")\n  );\n}\n\nfunction handleErrorResponse(\n  parsedResponse: HttpOperationResponse,\n  operationSpec: OperationSpec,\n  responseSpec: OperationResponse | undefined\n): { error: RestError | null; shouldReturnResponse: boolean } {\n  const isSuccessByStatus = 200 <= parsedResponse.status && parsedResponse.status < 300;\n  const isExpectedStatusCode: boolean = isOperationSpecEmpty(operationSpec)\n    ? isSuccessByStatus\n    : !!responseSpec;\n\n  if (isExpectedStatusCode) {\n    if (responseSpec) {\n      if (!responseSpec.isError) {\n        return { error: null, shouldReturnResponse: false };\n      }\n    } else {\n      return { error: null, shouldReturnResponse: false };\n    }\n  }\n\n  const errorResponseSpec = responseSpec ?? operationSpec.responses.default;\n  const streaming =\n    parsedResponse.request.streamResponseStatusCodes?.has(parsedResponse.status) ||\n    parsedResponse.request.streamResponseBody;\n  const initialErrorMessage = streaming\n    ? `Unexpected status code: ${parsedResponse.status}`\n    : (parsedResponse.bodyAsText as string);\n\n  const error = new RestError(\n    initialErrorMessage,\n    undefined,\n    parsedResponse.status,\n    parsedResponse.request,\n    parsedResponse\n  );\n\n  // If the item failed but there's no error spec or default spec to deserialize the error,\n  // we should fail so we just throw the parsed response\n  if (!errorResponseSpec) {\n    throw error;\n  }\n\n  const defaultBodyMapper = errorResponseSpec.bodyMapper;\n  const defaultHeadersMapper = errorResponseSpec.headersMapper;\n\n  try {\n    // If error response has a body, try to deserialize it using default body mapper.\n    // Then try to extract error code & message from it\n    if (parsedResponse.parsedBody) {\n      const parsedBody = parsedResponse.parsedBody;\n      let parsedError;\n      if (defaultBodyMapper) {\n        let valueToDeserialize: any = parsedBody;\n        if (operationSpec.isXML && defaultBodyMapper.type.name === MapperType.Sequence) {\n          valueToDeserialize =\n            typeof parsedBody === \"object\" ? parsedBody[defaultBodyMapper.xmlElementName!] : [];\n        }\n        parsedError = operationSpec.serializer.deserialize(\n          defaultBodyMapper,\n          valueToDeserialize,\n          \"error.response.parsedBody\"\n        );\n      }\n\n      const internalError: any = parsedBody.error || parsedError || parsedBody;\n      error.code = internalError.code;\n      if (internalError.message) {\n        error.message = internalError.message;\n      }\n\n      if (defaultBodyMapper) {\n        error.response!.parsedBody = parsedError;\n      }\n    }\n\n    // If error response has headers, try to deserialize it using default header mapper\n    if (parsedResponse.headers && defaultHeadersMapper) {\n      error.response!.parsedHeaders = operationSpec.serializer.deserialize(\n        defaultHeadersMapper,\n        parsedResponse.headers.toJson(),\n        \"operationRes.parsedHeaders\"\n      );\n    }\n  } catch (defaultError: any) {\n    error.message = `Error \"${defaultError.message}\" occurred in deserializing the responseBody - \"${parsedResponse.bodyAsText}\" for the default response.`;\n  }\n\n  return { error, shouldReturnResponse: false };\n}\n\nfunction parse(\n  jsonContentTypes: string[],\n  xmlContentTypes: string[],\n  operationResponse: HttpOperationResponse,\n  opts: Required<SerializerOptions>\n): Promise<HttpOperationResponse> {\n  const errorHandler = (err: Error & { code: string }): Promise<never> => {\n    const msg = `Error \"${err}\" occurred while parsing the response body - ${operationResponse.bodyAsText}.`;\n    const errCode = err.code || RestError.PARSE_ERROR;\n    const e = new RestError(\n      msg,\n      errCode,\n      operationResponse.status,\n      operationResponse.request,\n      operationResponse\n    );\n    return Promise.reject(e);\n  };\n\n  const streaming =\n    operationResponse.request.streamResponseStatusCodes?.has(operationResponse.status) ||\n    operationResponse.request.streamResponseBody;\n  if (!streaming && operationResponse.bodyAsText) {\n    const text = operationResponse.bodyAsText;\n    const contentType: string = operationResponse.headers.get(\"Content-Type\") || \"\";\n    const contentComponents: string[] = !contentType\n      ? []\n      : contentType.split(\";\").map((component) => component.toLowerCase());\n    if (\n      contentComponents.length === 0 ||\n      contentComponents.some((component) => jsonContentTypes.indexOf(component) !== -1)\n    ) {\n      return new Promise<HttpOperationResponse>((resolve) => {\n        operationResponse.parsedBody = JSON.parse(text);\n        resolve(operationResponse);\n      }).catch(errorHandler);\n    } else if (contentComponents.some((component) => xmlContentTypes.indexOf(component) !== -1)) {\n      return parseXML(text, opts)\n        .then((body) => {\n          operationResponse.parsedBody = body;\n          return operationResponse;\n        })\n        .catch(errorHandler);\n    }\n  }\n\n  return Promise.resolve(operationResponse);\n}\n"]}