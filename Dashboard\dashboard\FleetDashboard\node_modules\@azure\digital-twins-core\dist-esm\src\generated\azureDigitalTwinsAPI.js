/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import * as operations from "./operations";
import * as Models from "./models";
import * as Mappers from "./models/mappers";
import { AzureDigitalTwinsAPIContext } from "./azureDigitalTwinsAPIContext";
class AzureDigitalTwinsAPI extends AzureDigitalTwinsAPIContext {
    /**
     * Initializes a new instance of the AzureDigitalTwinsAPI class.
     * @param options The parameter options
     */
    constructor(options) {
        super(options);
        this.digitalTwinModels = new operations.DigitalTwinModels(this);
        this.query = new operations.Query(this);
        this.digitalTwins = new operations.DigitalTwins(this);
        this.eventRoutes = new operations.EventRoutes(this);
    }
}
// Operation Specifications
export { AzureDigitalTwinsAPI, AzureDigitalTwinsAPIContext, Models as AzureDigitalTwinsAPIModels, Mappers as AzureDigitalTwinsAPIMappers };
export * from "./operations";
//# sourceMappingURL=azureDigitalTwinsAPI.js.map