{"version": 3, "file": "sha256.browser.js", "sourceRoot": "", "sources": ["../../src/sha256.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,aAAa,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AACjE,OAAO,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AACpC,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AA6C7C,IAAI,YAAsC,CAAC;AAE3C;;;GAGG;AACH,SAAS,SAAS;IAChB,IAAI,YAAY,EAAE;QAChB,OAAO,YAAY,CAAC;KACrB;IAED,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;QACvC,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;KACtF;IAED,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAClC,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,GAAW,EACX,YAAoB,EACpB,QAA0B;IAE1B,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;IAC3B,MAAM,QAAQ,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;IACpC,MAAM,iBAAiB,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC;IAEpD,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,SAAS,CACtC,KAAK,EACL,QAAQ,EACR;QACE,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;KAC1B,EACD,KAAK,EACL,CAAC,MAAM,CAAC,CACT,CAAC;IACF,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,IAAI,CACjC;QACE,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;KAC1B,EACD,SAAS,EACT,iBAAiB,CAClB,CAAC;IAEF,QAAQ,QAAQ,EAAE;QAChB,KAAK,QAAQ;YACX,OAAO,cAAc,CAAC,SAAS,CAAC,CAAC;QACnC,KAAK,KAAK;YACR,OAAO,WAAW,CAAC,SAAS,CAAC,CAAC;KACjC;AACH,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,OAAe,EACf,QAA0B;IAE1B,MAAM,YAAY,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;IAC1C,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,YAAY,CAAC,CAAC;IAE3E,QAAQ,QAAQ,EAAE;QAChB,KAAK,QAAQ;YACX,OAAO,cAAc,CAAC,MAAM,CAAC,CAAC;QAChC,KAAK,KAAK;YACR,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC;KAC9B;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { base64ToBytes, bufferToBase64 } from \"./base64.browser\";\nimport { bufferToHex } from \"./hex\";\nimport { utf8ToBytes } from \"./utf8.browser\";\n\n// stubs for browser self.crypto\ninterface JsonWebKey {}\ninterface CryptoKey {}\ntype KeyUsage =\n  | \"decrypt\"\n  | \"deriveBits\"\n  | \"deriveKey\"\n  | \"encrypt\"\n  | \"sign\"\n  | \"unwrapKey\"\n  | \"verify\"\n  | \"wrapKey\";\ninterface Algorithm {\n  name: string;\n}\ninterface SubtleCrypto {\n  importKey(\n    format: string,\n    keyData: Json<PERSON>eb<PERSON><PERSON>,\n    algorithm: HmacImportParams,\n    extractable: boolean,\n    usage: KeyUsage[]\n  ): Promise<CryptoKey>;\n  sign(\n    algorithm: HmacImportParams,\n    key: CryptoKey,\n    data: ArrayBufferView | ArrayBuffer\n  ): Promise<ArrayBuffer>;\n  digest(algorithm: Algorithm, data: ArrayBufferView | ArrayBuffer): Promise<ArrayBuffer>;\n}\ninterface Crypto {\n  readonly subtle: SubtleCrypto;\n  getRandomValues<T extends ArrayBufferView | null>(array: T): T;\n}\ndeclare const self: {\n  crypto: Crypto;\n};\ninterface HmacImportParams {\n  name: string;\n  hash: Algorithm;\n  length?: number;\n}\n\nlet subtleCrypto: SubtleCrypto | undefined;\n\n/**\n * Returns a cached reference to the Web API crypto.subtle object.\n * @internal\n */\nfunction getCrypto(): SubtleCrypto {\n  if (subtleCrypto) {\n    return subtleCrypto;\n  }\n\n  if (!self.crypto || !self.crypto.subtle) {\n    throw new Error(\"Your browser environment does not support cryptography functions.\");\n  }\n\n  subtleCrypto = self.crypto.subtle;\n  return subtleCrypto;\n}\n\n/**\n * Generates a SHA-256 HMAC signature.\n * @param key - The HMAC key represented as a base64 string, used to generate the cryptographic HMAC hash.\n * @param stringToSign - The data to be signed.\n * @param encoding - The textual encoding to use for the returned HMAC digest.\n */\nexport async function computeSha256Hmac(\n  key: string,\n  stringToSign: string,\n  encoding: \"base64\" | \"hex\"\n): Promise<string> {\n  const crypto = getCrypto();\n  const keyBytes = base64ToBytes(key);\n  const stringToSignBytes = utf8ToBytes(stringToSign);\n\n  const cryptoKey = await crypto.importKey(\n    \"raw\",\n    keyBytes,\n    {\n      name: \"HMAC\",\n      hash: { name: \"SHA-256\" },\n    },\n    false,\n    [\"sign\"]\n  );\n  const signature = await crypto.sign(\n    {\n      name: \"HMAC\",\n      hash: { name: \"SHA-256\" },\n    },\n    cryptoKey,\n    stringToSignBytes\n  );\n\n  switch (encoding) {\n    case \"base64\":\n      return bufferToBase64(signature);\n    case \"hex\":\n      return bufferToHex(signature);\n  }\n}\n\n/**\n * Generates a SHA-256 hash.\n * @param content - The data to be included in the hash.\n * @param encoding - The textual encoding to use for the returned hash.\n */\nexport async function computeSha256Hash(\n  content: string,\n  encoding: \"base64\" | \"hex\"\n): Promise<string> {\n  const contentBytes = utf8ToBytes(content);\n  const digest = await getCrypto().digest({ name: \"SHA-256\" }, contentBytes);\n\n  switch (encoding) {\n    case \"base64\":\n      return bufferToBase64(digest);\n    case \"hex\":\n      return bufferToHex(digest);\n  }\n}\n"]}