{"version": 3, "file": "sanitizer.js", "sourceRoot": "", "sources": ["../../../src/util/sanitizer.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC;AAC9C,OAAO,EAAiB,QAAQ,EAAE,MAAM,SAAS,CAAC;AAkBlD,MAAM,cAAc,GAAG,UAAU,CAAC;AAElC,MAAM,yBAAyB,GAAG;IAChC,wBAAwB;IACxB,+BAA+B;IAC/B,gBAAgB;IAChB,6BAA6B;IAC7B,iBAAiB;IACjB,mBAAmB;IACnB,OAAO;IACP,0BAA0B;IAC1B,aAAa;IAEb,kCAAkC;IAClC,8BAA8B;IAC9B,8BAA8B;IAC9B,6BAA6B;IAC7B,+BAA+B;IAC/B,wBAAwB;IACxB,gCAAgC;IAChC,+BAA+B;IAC/B,QAAQ;IAER,QAAQ;IACR,iBAAiB;IACjB,eAAe;IACf,YAAY;IACZ,gBAAgB;IAChB,cAAc;IACd,MAAM;IACN,MAAM;IACN,SAAS;IACT,UAAU;IACV,mBAAmB;IACnB,eAAe;IACf,qBAAqB;IACrB,eAAe;IACf,QAAQ;IACR,YAAY;IACZ,aAAa;IACb,QAAQ;IACR,mBAAmB;IACnB,YAAY;IACZ,kBAAkB;CACnB,CAAC;AAEF,MAAM,6BAA6B,GAAa,CAAC,aAAa,CAAC,CAAC;AAEhE,MAAM,OAAO,SAAS;IAIpB,YAAY,EAAE,kBAAkB,GAAG,EAAE,EAAE,sBAAsB,GAAG,EAAE,KAAuB,EAAE;QACzF,kBAAkB,GAAG,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC;YACpD,CAAC,CAAC,yBAAyB,CAAC,MAAM,CAAC,kBAAkB,CAAC;YACtD,CAAC,CAAC,yBAAyB,CAAC;QAE9B,sBAAsB,GAAG,KAAK,CAAC,OAAO,CAAC,sBAAsB,CAAC;YAC5D,CAAC,CAAC,6BAA6B,CAAC,MAAM,CAAC,sBAAsB,CAAC;YAC9D,CAAC,CAAC,6BAA6B,CAAC;QAElC,IAAI,CAAC,kBAAkB,GAAG,IAAI,GAAG,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAClF,IAAI,CAAC,sBAAsB,GAAG,IAAI,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC5F,CAAC;IAEM,QAAQ,CAAC,GAAY;QAC1B,MAAM,IAAI,GAAG,IAAI,GAAG,EAAW,CAAC;QAChC,OAAO,IAAI,CAAC,SAAS,CACnB,GAAG,EACH,CAAC,GAAW,EAAE,KAAc,EAAE,EAAE;YAC9B,iEAAiE;YACjE,IAAI,KAAK,YAAY,KAAK,EAAE;gBAC1B,uCACK,KAAK,KACR,IAAI,EAAE,KAAK,CAAC,IAAI,EAChB,OAAO,EAAE,KAAK,CAAC,OAAO,IACtB;aACH;YAED,IAAI,GAAG,KAAK,aAAa,EAAE;gBACzB,OAAO,IAAI,CAAC,eAAe,CAAC,KAAsB,CAAC,CAAC;aACrD;iBAAM,IAAI,GAAG,KAAK,KAAK,EAAE;gBACxB,OAAO,IAAI,CAAC,WAAW,CAAC,KAAe,CAAC,CAAC;aAC1C;iBAAM,IAAI,GAAG,KAAK,OAAO,EAAE;gBAC1B,OAAO,IAAI,CAAC,aAAa,CAAC,KAAsB,CAAC,CAAC;aACnD;iBAAM,IAAI,GAAG,KAAK,MAAM,EAAE;gBACzB,6BAA6B;gBAC7B,OAAO,SAAS,CAAC;aAClB;iBAAM,IAAI,GAAG,KAAK,UAAU,EAAE;gBAC7B,2BAA2B;gBAC3B,OAAO,SAAS,CAAC;aAClB;iBAAM,IAAI,GAAG,KAAK,eAAe,EAAE;gBAClC,iEAAiE;gBACjE,mDAAmD;gBACnD,OAAO,SAAS,CAAC;aAClB;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;gBAClD,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;oBACnB,OAAO,YAAY,CAAC;iBACrB;gBACD,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;aACjB;YAED,OAAO,KAAK,CAAC;QACf,CAAC,EACD,CAAC,CACF,CAAC;IACJ,CAAC;IAEO,eAAe,CAAC,KAAoB;QAC1C,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACnF,CAAC;IAEO,aAAa,CAAC,KAAoB;QACxC,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjF,CAAC;IAEO,cAAc,CACpB,KAAoB,EACpB,WAAwB,EACxB,QAA0C;QAE1C,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE;YAC/C,OAAO,KAAK,CAAC;SACd;QAED,MAAM,SAAS,GAAkB,EAAE,CAAC;QAEpC,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAClC,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,EAAE;gBACpC,SAAS,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aACnC;iBAAM;gBACL,SAAS,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC;aAC/B;SACF;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,WAAW,CAAC,KAAa;QAC/B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE;YAC/C,OAAO,KAAK,CAAC;SACd;QAED,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC3C,MAAM,WAAW,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;QAE1C,IAAI,CAAC,WAAW,EAAE;YAChB,OAAO,KAAK,CAAC;SACd;QAED,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QAC1C,KAAK,MAAM,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,EAAE;gBACrD,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;aAC9B;SACF;QAED,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;QACtC,OAAO,UAAU,CAAC,QAAQ,EAAE,CAAC;IAC/B,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { U<PERSON><PERSON><PERSON><PERSON>, URLQuery } from \"../url\";\nimport { UnknownObject, isObject } from \"./utils\";\n\nexport interface SanitizerOptions {\n  /**\n   * Header names whose values will be logged when logging is enabled. Defaults to\n   * Date, traceparent, x-ms-client-request-id, and x-ms-request id.  Any headers\n   * specified in this field will be added to that list.  Any other values will\n   * be written to logs as \"REDACTED\".\n   */\n  allowedHeaderNames?: string[];\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   */\n  allowedQueryParameters?: string[];\n}\n\nconst RedactedString = \"REDACTED\";\n\nconst defaultAllowedHeaderNames = [\n  \"x-ms-client-request-id\",\n  \"x-ms-return-client-request-id\",\n  \"x-ms-useragent\",\n  \"x-ms-correlation-request-id\",\n  \"x-ms-request-id\",\n  \"client-request-id\",\n  \"ms-cv\",\n  \"return-client-request-id\",\n  \"traceparent\",\n\n  \"Access-Control-Allow-Credentials\",\n  \"Access-Control-Allow-Headers\",\n  \"Access-Control-Allow-Methods\",\n  \"Access-Control-Allow-Origin\",\n  \"Access-Control-Expose-Headers\",\n  \"Access-Control-Max-Age\",\n  \"Access-Control-Request-Headers\",\n  \"Access-Control-Request-Method\",\n  \"Origin\",\n\n  \"Accept\",\n  \"Accept-Encoding\",\n  \"Cache-Control\",\n  \"Connection\",\n  \"Content-Length\",\n  \"Content-Type\",\n  \"Date\",\n  \"ETag\",\n  \"Expires\",\n  \"If-Match\",\n  \"If-Modified-Since\",\n  \"If-None-Match\",\n  \"If-Unmodified-Since\",\n  \"Last-Modified\",\n  \"Pragma\",\n  \"Request-Id\",\n  \"Retry-After\",\n  \"Server\",\n  \"Transfer-Encoding\",\n  \"User-Agent\",\n  \"WWW-Authenticate\",\n];\n\nconst defaultAllowedQueryParameters: string[] = [\"api-version\"];\n\nexport class Sanitizer {\n  public allowedHeaderNames: Set<string>;\n  public allowedQueryParameters: Set<string>;\n\n  constructor({ allowedHeaderNames = [], allowedQueryParameters = [] }: SanitizerOptions = {}) {\n    allowedHeaderNames = Array.isArray(allowedHeaderNames)\n      ? defaultAllowedHeaderNames.concat(allowedHeaderNames)\n      : defaultAllowedHeaderNames;\n\n    allowedQueryParameters = Array.isArray(allowedQueryParameters)\n      ? defaultAllowedQueryParameters.concat(allowedQueryParameters)\n      : defaultAllowedQueryParameters;\n\n    this.allowedHeaderNames = new Set(allowedHeaderNames.map((n) => n.toLowerCase()));\n    this.allowedQueryParameters = new Set(allowedQueryParameters.map((p) => p.toLowerCase()));\n  }\n\n  public sanitize(obj: unknown): string {\n    const seen = new Set<unknown>();\n    return JSON.stringify(\n      obj,\n      (key: string, value: unknown) => {\n        // Ensure Errors include their interesting non-enumerable members\n        if (value instanceof Error) {\n          return {\n            ...value,\n            name: value.name,\n            message: value.message,\n          };\n        }\n\n        if (key === \"_headersMap\") {\n          return this.sanitizeHeaders(value as UnknownObject);\n        } else if (key === \"url\") {\n          return this.sanitizeUrl(value as string);\n        } else if (key === \"query\") {\n          return this.sanitizeQuery(value as UnknownObject);\n        } else if (key === \"body\") {\n          // Don't log the request body\n          return undefined;\n        } else if (key === \"response\") {\n          // Don't log response again\n          return undefined;\n        } else if (key === \"operationSpec\") {\n          // When using sendOperationRequest, the request carries a massive\n          // field with the autorest spec. No need to log it.\n          return undefined;\n        } else if (Array.isArray(value) || isObject(value)) {\n          if (seen.has(value)) {\n            return \"[Circular]\";\n          }\n          seen.add(value);\n        }\n\n        return value;\n      },\n      2\n    );\n  }\n\n  private sanitizeHeaders(value: UnknownObject): UnknownObject {\n    return this.sanitizeObject(value, this.allowedHeaderNames, (v, k) => v[k].value);\n  }\n\n  private sanitizeQuery(value: UnknownObject): UnknownObject {\n    return this.sanitizeObject(value, this.allowedQueryParameters, (v, k) => v[k]);\n  }\n\n  private sanitizeObject(\n    value: UnknownObject,\n    allowedKeys: Set<string>,\n    accessor: (value: any, key: string) => any\n  ): UnknownObject {\n    if (typeof value !== \"object\" || value === null) {\n      return value;\n    }\n\n    const sanitized: UnknownObject = {};\n\n    for (const k of Object.keys(value)) {\n      if (allowedKeys.has(k.toLowerCase())) {\n        sanitized[k] = accessor(value, k);\n      } else {\n        sanitized[k] = RedactedString;\n      }\n    }\n\n    return sanitized;\n  }\n\n  private sanitizeUrl(value: string): string {\n    if (typeof value !== \"string\" || value === null) {\n      return value;\n    }\n\n    const urlBuilder = URLBuilder.parse(value);\n    const queryString = urlBuilder.getQuery();\n\n    if (!queryString) {\n      return value;\n    }\n\n    const query = URLQuery.parse(queryString);\n    for (const k of query.keys()) {\n      if (!this.allowedQueryParameters.has(k.toLowerCase())) {\n        query.set(k, RedactedString);\n      }\n    }\n\n    urlBuilder.setQuery(query.toString());\n    return urlBuilder.toString();\n  }\n}\n"]}