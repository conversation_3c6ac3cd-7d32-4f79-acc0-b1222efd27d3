{"version": 3, "file": "defaultHttpClient.js", "sourceRoot": "", "sources": ["../../src/defaultHttpClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,mBAAmB,IAAI,iBAAiB,EAAE,MAAM,uBAAuB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nexport { NodeFetchHttpClient as DefaultHttpClient } from \"./nodeFetchHttpClient\";\n"]}