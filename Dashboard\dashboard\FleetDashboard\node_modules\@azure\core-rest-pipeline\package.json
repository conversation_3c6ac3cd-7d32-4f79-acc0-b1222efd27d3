{"name": "@azure/core-rest-pipeline", "version": "1.10.3", "description": "Isomorphic client library for making HTTP requests in node.js and browser.", "sdk-type": "client", "main": "dist/index.js", "module": "dist-esm/src/index.js", "browser": {"./dist-esm/src/defaultHttpClient.js": "./dist-esm/src/defaultHttpClient.browser.js", "./dist-esm/src/policies/decompressResponsePolicy.js": "./dist-esm/src/policies/decompressResponsePolicy.browser.js", "./dist-esm/src/policies/formDataPolicy.js": "./dist-esm/src/policies/formDataPolicy.browser.js", "./dist-esm/src/policies/proxyPolicy.js": "./dist-esm/src/policies/proxyPolicy.browser.js", "./dist-esm/src/util/inspect.js": "./dist-esm/src/util/inspect.browser.js", "./dist-esm/src/util/userAgentPlatform.js": "./dist-esm/src/util/userAgentPlatform.browser.js"}, "react-native": {"./dist/index.js": "./dist-esm/src/index.js", "./dist-esm/src/defaultHttpClient.js": "./dist-esm/src/defaultHttpClient.native.js", "./dist-esm/src/util/userAgentPlatform.js": "./dist-esm/src/util/userAgentPlatform.native.js"}, "types": "core-rest-pipeline.shims.d.ts", "typesVersions": {"<3.6": {"core-rest-pipeline.shims.d.ts": ["core-rest-pipeline.shims-3_1.d.ts"]}}, "scripts": {"audit": "node ../../../common/scripts/rush-audit.js && rimraf node_modules package-lock.json && npm i --package-lock-only 2>&1 && npm audit", "build:samples": "echo Obsolete", "build:test": "tsc -p . && dev-tool run bundle", "build:types": "downlevel-dts types/latest/ types/3.1/", "build": "npm run clean && tsc -p . && dev-tool run bundle && api-extractor run --local && npm run build:types", "check-format": "prettier --list-different --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.ts\" \"test/**/*.ts\" \"*.{js,json}\"", "clean": "rimraf dist dist-* temp types *.tgz *.log", "execute:samples": "echo skipped", "extract-api": "tsc -p . && api-extractor run --local", "format": "prettier --write --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.ts\" \"test/**/*.ts\" \"samples-dev/**/*.ts\" \"*.{js,json}\"", "integration-test:browser": "echo skipped", "integration-test:node": "echo skipped", "integration-test": "npm run integration-test:node && npm run integration-test:browser", "lint:fix": "eslint package.json api-extractor.json src test --ext .ts --fix --fix-type [problem,suggestion]", "lint": "eslint package.json api-extractor.json src test --ext .ts", "pack": "npm pack 2>&1", "test:browser": "npm run clean && npm run build:test && npm run unit-test:browser && npm run integration-test:browser", "test:node": "npm run clean && tsc -p . && npm run unit-test:node && npm run integration-test:node", "test": "npm run clean && tsc -p . && npm run unit-test:node && dev-tool run bundle && npm run unit-test:browser && npm run integration-test", "unit-test:browser": "karma start --single-run", "unit-test:node": "mocha -r esm -r ts-node/register --reporter ../../../common/tools/mocha-multi-reporter.js --timeout 1200000 --full-trace  --exclude \"test/**/browser/*.spec.ts\" \"test/**/*.spec.ts\"", "unit-test": "npm run unit-test:node && npm run unit-test:browser"}, "files": ["dist/", "dist-esm/src/", "types/3.1/core-rest-pipeline.d.ts", "types/latest/core-rest-pipeline.d.ts", "core-rest-pipeline.shims.d.ts", "core-rest-pipeline.shims-3_1.d.ts", "LICENSE", "README.md"], "repository": "github:Azure/azure-sdk-for-js", "keywords": ["azure", "cloud"], "author": "Microsoft Corporation", "license": "MIT", "bugs": {"url": "https://github.com/Azure/azure-sdk-for-js/issues"}, "engines": {"node": ">=14.0.0"}, "homepage": "https://github.com/Azure/azure-sdk-for-js/blob/main/sdk/core/core-rest-pipeline/", "sideEffects": false, "prettier": "@azure/eslint-plugin-azure-sdk/prettier.json", "//metadata": {"constantPaths": [{"path": "src/constants.ts", "prefix": "SDK_VERSION"}]}, "dependencies": {"@azure/abort-controller": "^1.0.0", "@azure/core-auth": "^1.4.0", "@azure/core-tracing": "^1.0.1", "@azure/core-util": "^1.3.0", "@azure/logger": "^1.0.0", "form-data": "^4.0.0", "tslib": "^2.2.0", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.0"}, "devDependencies": {"@azure/dev-tool": "^1.0.0", "@azure/eslint-plugin-azure-sdk": "^3.0.0", "@microsoft/api-extractor": "^7.31.1", "@opentelemetry/api": "^1.4.0", "@types/chai": "^4.1.6", "@types/chai-as-promised": "^7.1.0", "@types/mocha": "^7.0.2", "@types/node": "^14.0.0", "@types/sinon": "^9.0.4", "@types/uuid": "^8.0.0", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "downlevel-dts": "^0.10.0", "cross-env": "^7.0.2", "eslint": "^8.0.0", "inherits": "^2.0.3", "karma-chrome-launcher": "^3.1.0", "karma-coverage": "^2.0.0", "karma-env-preprocessor": "^0.1.1", "karma-firefox-launcher": "^1.1.0", "karma-junit-reporter": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-mocha": "^2.0.1", "karma-sourcemap-loader": "^0.3.8", "karma": "^6.3.0", "mocha-junit-reporter": "^2.0.0", "mocha": "^7.1.1", "prettier": "^2.5.1", "puppeteer": "^19.2.2", "rimraf": "^3.0.0", "sinon": "^9.0.2", "source-map-support": "^0.5.9", "typescript": "~4.8.0", "util": "^0.12.1"}, "//sampleConfiguration": {"skipFolder": true, "disableDocsMs": true, "productName": "Azure SDK Core", "productSlugs": ["azure"]}}