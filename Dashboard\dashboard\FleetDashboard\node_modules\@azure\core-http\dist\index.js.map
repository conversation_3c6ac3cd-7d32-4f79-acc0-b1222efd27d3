{"version": 3, "file": "index.js", "sources": ["../src/httpHeaders.ts", "../src/util/base64.ts", "../src/util/constants.ts", "../src/util/serializer.common.ts", "../src/util/utils.ts", "../src/serializer.ts", "../src/webResource.ts", "../src/url.ts", "../src/proxyAgent.ts", "../src/util/sanitizer.ts", "../src/util/inspect.ts", "../src/restError.ts", "../src/log.ts", "../src/nodeFetchHttpClient.ts", "../src/httpPipelineLogLevel.ts", "../src/operationOptions.ts", "../src/policies/requestPolicy.ts", "../src/util/xml.ts", "../src/policies/deserializationPolicy.ts", "../src/policies/keepAlivePolicy.ts", "../src/policies/redirectPolicy.ts", "../src/util/exponentialBackoffStrategy.ts", "../src/policies/exponentialRetryPolicy.ts", "../src/policies/logPolicy.ts", "../src/operationParameter.ts", "../src/operationSpec.ts", "../src/policies/msRestUserAgentPolicy.ts", "../src/policies/userAgentPolicy.ts", "../src/queryCollectionFormat.ts", "../src/policies/bearerTokenAuthenticationPolicy.ts", "../src/policies/disableResponseDecompressionPolicy.ts", "../src/policies/generateClientRequestIdPolicy.ts", "../src/httpClientCache.ts", "../src/policies/ndJsonPolicy.ts", "../src/policies/proxyPolicy.ts", "../src/policies/rpRegistrationPolicy.ts", "../src/policies/signingPolicy.ts", "../src/policies/systemErrorRetryPolicy.ts", "../src/util/throttlingRetryStrategy.ts", "../src/policies/throttlingRetryPolicy.ts", "../src/policies/tracingPolicy.ts", "../src/serviceClient.ts", "../src/createSpanLegacy.ts", "../src/credentials/accessTokenCache.ts", "../src/credentials/accessTokenRefresher.ts", "../src/credentials/basicAuthenticationCredentials.ts", "../src/credentials/apiKeyCredentials.ts", "../src/credentials/topicCredentials.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * A collection of HttpHeaders that can be sent with a HTTP request.\n */\nfunction getHeaderKey(headerName: string): string {\n  return headerName.toLowerCase();\n}\n\n/**\n * An individual header within a HttpHeaders collection.\n */\nexport interface HttpHeader {\n  /**\n   * The name of the header.\n   */\n  name: string;\n\n  /**\n   * The value of the header.\n   */\n  value: string;\n}\n\n/**\n * A HttpHeaders collection represented as a simple JSON object.\n */\nexport type RawHttpHeaders = { [headerName: string]: string };\n\n/**\n * A collection of HTTP header key/value pairs.\n */\nexport interface HttpHeadersLike {\n  /**\n   * Set a header in this collection with the provided name and value. The name is\n   * case-insensitive.\n   * @param headerName - The name of the header to set. This value is case-insensitive.\n   * @param headerValue - The value of the header to set.\n   */\n  set(headerName: string, headerValue: string | number): void;\n  /**\n   * Get the header value for the provided header name, or undefined if no header exists in this\n   * collection with the provided name.\n   * @param headerName - The name of the header.\n   */\n  get(headerName: string): string | undefined;\n  /**\n   * Get whether or not this header collection contains a header entry for the provided header name.\n   */\n  contains(headerName: string): boolean;\n  /**\n   * Remove the header with the provided headerName. Return whether or not the header existed and\n   * was removed.\n   * @param headerName - The name of the header to remove.\n   */\n  remove(headerName: string): boolean;\n  /**\n   * Get the headers that are contained this collection as an object.\n   */\n  rawHeaders(): RawHttpHeaders;\n  /**\n   * Get the headers that are contained in this collection as an array.\n   */\n  headersArray(): HttpHeader[];\n  /**\n   * Get the header names that are contained in this collection.\n   */\n  headerNames(): string[];\n  /**\n   * Get the header values that are contained in this collection.\n   */\n  headerValues(): string[];\n  /**\n   * Create a deep clone/copy of this HttpHeaders collection.\n   */\n  clone(): HttpHeadersLike;\n  /**\n   * Get the JSON object representation of this HTTP header collection.\n   * The result is the same as `rawHeaders()`.\n   */\n  toJson(options?: { preserveCase?: boolean }): RawHttpHeaders;\n}\n\nexport function isHttpHeadersLike(object?: unknown): object is HttpHeadersLike {\n  if (object && typeof object === \"object\") {\n    const castObject = object as {\n      rawHeaders: unknown;\n      clone: unknown;\n      get: unknown;\n      set: unknown;\n      contains: unknown;\n      remove: unknown;\n      headersArray: unknown;\n      headerValues: unknown;\n      headerNames: unknown;\n      toJson: unknown;\n    };\n    if (\n      typeof castObject.rawHeaders === \"function\" &&\n      typeof castObject.clone === \"function\" &&\n      typeof castObject.get === \"function\" &&\n      typeof castObject.set === \"function\" &&\n      typeof castObject.contains === \"function\" &&\n      typeof castObject.remove === \"function\" &&\n      typeof castObject.headersArray === \"function\" &&\n      typeof castObject.headerValues === \"function\" &&\n      typeof castObject.headerNames === \"function\" &&\n      typeof castObject.toJson === \"function\"\n    ) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/**\n * A collection of HTTP header key/value pairs.\n */\nexport class HttpHeaders implements HttpHeadersLike {\n  private readonly _headersMap: { [headerKey: string]: HttpHeader };\n\n  constructor(rawHeaders?: RawHttpHeaders) {\n    this._headersMap = {};\n    if (rawHeaders) {\n      for (const headerName in rawHeaders) {\n        this.set(headerName, rawHeaders[headerName]);\n      }\n    }\n  }\n\n  /**\n   * Set a header in this collection with the provided name and value. The name is\n   * case-insensitive.\n   * @param headerName - The name of the header to set. This value is case-insensitive.\n   * @param headerValue - The value of the header to set.\n   */\n  public set(headerName: string, headerValue: string | number): void {\n    this._headersMap[getHeaderKey(headerName)] = {\n      name: headerName,\n      value: headerValue.toString(),\n    };\n  }\n\n  /**\n   * Get the header value for the provided header name, or undefined if no header exists in this\n   * collection with the provided name.\n   * @param headerName - The name of the header.\n   */\n  public get(headerName: string): string | undefined {\n    const header: HttpHeader = this._headersMap[getHeaderKey(headerName)];\n    return !header ? undefined : header.value;\n  }\n\n  /**\n   * Get whether or not this header collection contains a header entry for the provided header name.\n   */\n  public contains(headerName: string): boolean {\n    return !!this._headersMap[getHeaderKey(headerName)];\n  }\n\n  /**\n   * Remove the header with the provided headerName. Return whether or not the header existed and\n   * was removed.\n   * @param headerName - The name of the header to remove.\n   */\n  public remove(headerName: string): boolean {\n    const result: boolean = this.contains(headerName);\n    delete this._headersMap[getHeaderKey(headerName)];\n    return result;\n  }\n\n  /**\n   * Get the headers that are contained this collection as an object.\n   */\n  public rawHeaders(): RawHttpHeaders {\n    return this.toJson({ preserveCase: true });\n  }\n\n  /**\n   * Get the headers that are contained in this collection as an array.\n   */\n  public headersArray(): HttpHeader[] {\n    const headers: HttpHeader[] = [];\n    for (const headerKey in this._headersMap) {\n      headers.push(this._headersMap[headerKey]);\n    }\n    return headers;\n  }\n\n  /**\n   * Get the header names that are contained in this collection.\n   */\n  public headerNames(): string[] {\n    const headerNames: string[] = [];\n    const headers: HttpHeader[] = this.headersArray();\n    for (let i = 0; i < headers.length; ++i) {\n      headerNames.push(headers[i].name);\n    }\n    return headerNames;\n  }\n\n  /**\n   * Get the header values that are contained in this collection.\n   */\n  public headerValues(): string[] {\n    const headerValues: string[] = [];\n    const headers: HttpHeader[] = this.headersArray();\n    for (let i = 0; i < headers.length; ++i) {\n      headerValues.push(headers[i].value);\n    }\n    return headerValues;\n  }\n\n  /**\n   * Get the JSON object representation of this HTTP header collection.\n   */\n  public toJson(options: { preserveCase?: boolean } = {}): RawHttpHeaders {\n    const result: RawHttpHeaders = {};\n    if (options.preserveCase) {\n      for (const headerKey in this._headersMap) {\n        const header: HttpHeader = this._headersMap[headerKey];\n        result[header.name] = header.value;\n      }\n    } else {\n      for (const headerKey in this._headersMap) {\n        const header: HttpHeader = this._headersMap[headerKey];\n        result[getHeaderKey(header.name)] = header.value;\n      }\n    }\n    return result;\n  }\n\n  /**\n   * Get the string representation of this HTTP header collection.\n   */\n  public toString(): string {\n    return JSON.stringify(this.toJson({ preserveCase: true }));\n  }\n\n  /**\n   * Create a deep clone/copy of this HttpHeaders collection.\n   */\n  public clone(): HttpHeaders {\n    const resultPreservingCasing: RawHttpHeaders = {};\n    for (const headerKey in this._headersMap) {\n      const header: HttpHeader = this._headersMap[headerKey];\n      resultPreservingCasing[header.name] = header.value;\n    }\n    return new HttpHeaders(resultPreservingCasing);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Encodes a string in base64 format.\n * @param value - The string to encode\n */\nexport function encodeString(value: string): string {\n  return Buffer.from(value).toString(\"base64\");\n}\n\n/**\n * Encodes a byte array in base64 format.\n * @param value - The Uint8Aray to encode\n */\nexport function encodeByteArray(value: Uint8Array): string {\n  // Buffer.from accepts <ArrayBuffer> | <SharedArrayBuffer>-- the TypeScript definition is off here\n  // https://nodejs.org/api/buffer.html#buffer_class_method_buffer_from_arraybuffer_byteoffset_length\n  const bufferValue = value instanceof Buffer ? value : Buffer.from(value.buffer as ArrayBuffer);\n  return bufferValue.toString(\"base64\");\n}\n\n/**\n * Decodes a base64 string into a byte array.\n * @param value - The base64 string to decode\n */\nexport function decodeString(value: string): Uint8Array {\n  return Buffer.from(value, \"base64\");\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n/**\n * A set of constants used internally when processing requests.\n */\nexport const Constants = {\n  /**\n   * The core-http version\n   */\n  coreHttpVersion: \"2.3.2\",\n\n  /**\n   * Specifies HTTP.\n   */\n  HTTP: \"http:\",\n\n  /**\n   * Specifies HTTPS.\n   */\n  HTTPS: \"https:\",\n\n  /**\n   * Specifies HTTP Proxy.\n   */\n  HTTP_PROXY: \"HTTP_PROXY\",\n\n  /**\n   * Specifies HTTPS Proxy.\n   */\n  HTTPS_PROXY: \"HTTPS_PROXY\",\n\n  /**\n   * Specifies NO Proxy.\n   */\n  NO_PROXY: \"NO_PROXY\",\n\n  /**\n   * Specifies ALL Proxy.\n   */\n  ALL_PROXY: \"ALL_PROXY\",\n\n  HttpConstants: {\n    /**\n     * Http Verbs\n     */\n    HttpVerbs: {\n      PUT: \"PUT\",\n      GET: \"GET\",\n      DELETE: \"DELETE\",\n      POST: \"POST\",\n      MERGE: \"MERGE\",\n      HEAD: \"HEAD\",\n      PATCH: \"PATCH\",\n    },\n\n    StatusCodes: {\n      TooManyRequests: 429,\n      ServiceUnavailable: 503,\n    },\n  },\n\n  /**\n   * Defines constants for use with HTTP headers.\n   */\n  HeaderConstants: {\n    /**\n     * The Authorization header.\n     */\n    AUTHORIZATION: \"authorization\",\n\n    AUTHORIZATION_SCHEME: \"Bearer\",\n\n    /**\n     * The Retry-After response-header field can be used with a 503 (Service\n     * Unavailable) or 349 (Too Many Requests) responses to indicate how long\n     * the service is expected to be unavailable to the requesting client.\n     */\n    RETRY_AFTER: \"Retry-After\",\n\n    /**\n     * The UserAgent header.\n     */\n    USER_AGENT: \"User-Agent\",\n  },\n};\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Default key used to access the XML attributes.\n */\nexport const XML_ATTRKEY = \"$\";\n/**\n * Default key used to access the XML value content.\n */\nexport const XML_CHARKEY = \"_\";\n\n/**\n * Options to govern behavior of xml parser and builder.\n */\nexport interface SerializerOptions {\n  /**\n   * indicates the name of the root element in the resulting XML when building XML.\n   */\n  rootName?: string;\n  /**\n   * indicates whether the root element is to be included or not in the output when parsing XML.\n   */\n  includeRoot?: boolean;\n  /**\n   * key used to access the XML value content when parsing XML.\n   */\n  xmlCharKey?: string;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { Constants } from \"./constants\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { RestError } from \"../restError\";\nimport { WebResourceLike } from \"../webResource\";\nimport { XML_ATTRKEY } from \"./serializer.common\";\nimport { v4 as uuidv4 } from \"uuid\";\n\nconst validUuidRegex =\n  /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/i;\n\n/**\n * A constant that indicates whether the environment is node.js or browser based.\n */\nexport const isNode =\n  typeof process !== \"undefined\" &&\n  !!process.version &&\n  !!process.versions &&\n  !!process.versions.node;\n\n/**\n * Checks if a parsed URL is HTTPS\n *\n * @param urlToCheck - The url to check\n * @returns True if the URL is HTTPS; false otherwise.\n */\nexport function urlIsHTTPS(urlToCheck: { protocol: string }): boolean {\n  return urlToCheck.protocol.toLowerCase() === Constants.HTTPS;\n}\n\n/**\n * Encodes an URI.\n *\n * @param uri - The URI to be encoded.\n * @returns The encoded URI.\n */\nexport function encodeUri(uri: string): string {\n  return encodeURIComponent(uri)\n    .replace(/!/g, \"%21\")\n    .replace(/\"/g, \"%27\")\n    .replace(/\\(/g, \"%28\")\n    .replace(/\\)/g, \"%29\")\n    .replace(/\\*/g, \"%2A\");\n}\n\n/**\n * Returns a stripped version of the Http Response which only contains body,\n * headers and the status.\n *\n * @param response - The Http Response\n * @returns The stripped version of Http Response.\n */\nexport function stripResponse(response: HttpOperationResponse): any {\n  const strippedResponse: any = {};\n  strippedResponse.body = response.bodyAsText;\n  strippedResponse.headers = response.headers;\n  strippedResponse.status = response.status;\n  return strippedResponse;\n}\n\n/**\n * Returns a stripped version of the Http Request that does not contain the\n * Authorization header.\n *\n * @param request - The Http Request object\n * @returns The stripped version of Http Request.\n */\nexport function stripRequest(request: WebResourceLike): WebResourceLike {\n  const strippedRequest = request.clone();\n  if (strippedRequest.headers) {\n    strippedRequest.headers.remove(\"authorization\");\n  }\n  return strippedRequest;\n}\n\n/**\n * Validates the given uuid as a string\n *\n * @param uuid - The uuid as a string that needs to be validated\n * @returns True if the uuid is valid; false otherwise.\n */\nexport function isValidUuid(uuid: string): boolean {\n  return validUuidRegex.test(uuid);\n}\n\n/**\n * Generated UUID\n *\n * @returns RFC4122 v4 UUID.\n */\nexport function generateUuid(): string {\n  return uuidv4();\n}\n\n/**\n * Executes an array of promises sequentially. Inspiration of this method is here:\n * https://pouchdb.com/2015/05/18/we-have-a-problem-with-promises.html. An awesome blog on promises!\n *\n * @param promiseFactories - An array of promise factories(A function that return a promise)\n * @param kickstart - Input to the first promise that is used to kickstart the promise chain.\n * If not provided then the promise chain starts with undefined.\n * @returns A chain of resolved or rejected promises\n */\nexport function executePromisesSequentially(\n  promiseFactories: Array<any>,\n  kickstart: unknown\n): Promise<any> {\n  let result = Promise.resolve(kickstart);\n  promiseFactories.forEach((promiseFactory) => {\n    result = result.then(promiseFactory);\n  });\n  return result;\n}\n\n/**\n * Service callback that is returned for REST requests initiated by the service client.\n */\nexport interface ServiceCallback<TResult> {\n  /**\n   * A method that will be invoked as a callback to a service function.\n   * @param err - The error occurred if any, while executing the request; otherwise null.\n   * @param result - The deserialized response body if an error did not occur.\n   * @param request - The raw/actual request sent to the server if an error did not occur.\n   * @param response - The raw/actual response from the server if an error did not occur.\n   */\n  (\n    err: Error | RestError | null,\n    result?: TResult,\n    request?: WebResourceLike,\n    response?: HttpOperationResponse\n  ): void;\n}\n\n/**\n * Converts a Promise to a callback.\n * @param promise - The Promise to be converted to a callback\n * @returns A function that takes the callback `(cb: Function) => void`\n * @deprecated generated code should instead depend on responseToBody\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function promiseToCallback(promise: Promise<any>): (cb: Function) => void {\n  if (typeof promise.then !== \"function\") {\n    throw new Error(\"The provided input is not a Promise.\");\n  }\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  return (cb: Function): void => {\n    promise\n      .then((data: any) => {\n        // eslint-disable-next-line promise/no-callback-in-promise\n        return cb(undefined, data);\n      })\n      .catch((err: Error) => {\n        // eslint-disable-next-line promise/no-callback-in-promise\n        cb(err);\n      });\n  };\n}\n\n/**\n * Converts a Promise to a service callback.\n * @param promise - The Promise of HttpOperationResponse to be converted to a service callback\n * @returns A function that takes the service callback (cb: ServiceCallback<T>): void\n */\nexport function promiseToServiceCallback<T>(\n  promise: Promise<HttpOperationResponse>\n): (cb: ServiceCallback<T>) => void {\n  if (typeof promise.then !== \"function\") {\n    throw new Error(\"The provided input is not a Promise.\");\n  }\n  return (cb: ServiceCallback<T>): void => {\n    promise\n      .then((data: HttpOperationResponse) => {\n        return process.nextTick(cb, undefined, data.parsedBody as T, data.request, data);\n      })\n      .catch((err: Error) => {\n        process.nextTick(cb, err);\n      });\n  };\n}\n\nexport function prepareXMLRootList(\n  obj: unknown,\n  elementName: string,\n  xmlNamespaceKey?: string,\n  xmlNamespace?: string\n): { [s: string]: any } {\n  if (!Array.isArray(obj)) {\n    obj = [obj];\n  }\n\n  if (!xmlNamespaceKey || !xmlNamespace) {\n    return { [elementName]: obj };\n  }\n\n  const result = { [elementName]: obj };\n  result[XML_ATTRKEY] = { [xmlNamespaceKey]: xmlNamespace };\n  return result;\n}\n\n/**\n * Applies the properties on the prototype of sourceCtors to the prototype of targetCtor\n * @param targetCtor - The target object on which the properties need to be applied.\n * @param sourceCtors - An array of source objects from which the properties need to be taken.\n */\nexport function applyMixins(targetCtorParam: unknown, sourceCtors: any[]): void {\n  const castTargetCtorParam = targetCtorParam as {\n    prototype: Record<string, unknown>;\n  };\n  sourceCtors.forEach((sourceCtor) => {\n    Object.getOwnPropertyNames(sourceCtor.prototype).forEach((name) => {\n      castTargetCtorParam.prototype[name] = sourceCtor.prototype[name];\n    });\n  });\n}\n\nconst validateISODuration =\n  /^(-|\\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;\n\n/**\n * Indicates whether the given string is in ISO 8601 format.\n * @param value - The value to be validated for ISO 8601 duration format.\n * @returns `true` if valid, `false` otherwise.\n */\nexport function isDuration(value: string): boolean {\n  return validateISODuration.test(value);\n}\n\n/**\n * Replace all of the instances of searchValue in value with the provided replaceValue.\n * @param value - The value to search and replace in.\n * @param searchValue - The value to search for in the value argument.\n * @param replaceValue - The value to replace searchValue with in the value argument.\n * @returns The value where each instance of searchValue was replaced with replacedValue.\n */\nexport function replaceAll(\n  value: string | undefined,\n  searchValue: string,\n  replaceValue: string\n): string | undefined {\n  return !value || !searchValue ? value : value.split(searchValue).join(replaceValue || \"\");\n}\n\n/**\n * Determines whether the given entity is a basic/primitive type\n * (string, number, boolean, null, undefined).\n * @param value - Any entity\n * @returns true is it is primitive type, false otherwise.\n */\nexport function isPrimitiveType(value: unknown): boolean {\n  return (typeof value !== \"object\" && typeof value !== \"function\") || value === null;\n}\n\nexport function getEnvironmentValue(name: string): string | undefined {\n  if (process.env[name]) {\n    return process.env[name];\n  } else if (process.env[name.toLowerCase()]) {\n    return process.env[name.toLowerCase()];\n  }\n  return undefined;\n}\n\n/**\n * @internal\n */\nexport type UnknownObject = { [s: string]: unknown };\n\n/**\n * @internal\n * @returns true when input is an object type that is not null, Array, RegExp, or Date.\n */\nexport function isObject(input: unknown): input is UnknownObject {\n  return (\n    typeof input === \"object\" &&\n    input !== null &&\n    !Array.isArray(input) &&\n    !(input instanceof RegExp) &&\n    !(input instanceof Date)\n  );\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n/* eslint-disable eqeqeq */\n\nimport * as base64 from \"./util/base64\";\nimport * as utils from \"./util/utils\";\nimport { SerializerOptions, XML_ATTRKEY, XML_CHARKEY } from \"./util/serializer.common\";\n\n// This file contains utility code to serialize and deserialize network operations according to `OperationSpec` objects generated by AutoRest.TypeScript from OpenAPI specifications.\n\n/**\n * Used to map raw response objects to final shapes.\n * Helps packing and unpacking Dates and other encoded types that are not intrinsic to JSON.\n * Also allows pulling values from headers, as well as inserting default values and constants.\n */\nexport class Serializer {\n  constructor(\n    /**\n     * The provided model mapper.\n     */\n    public readonly modelMappers: { [key: string]: any } = {},\n    /**\n     * Whether the contents are XML or not.\n     */\n    public readonly isXML?: boolean\n  ) {}\n\n  /**\n   * Validates constraints, if any. This function will throw if the provided value does not respect those constraints.\n   * @param mapper - The definition of data models.\n   * @param value - The value.\n   * @param objectName - Name of the object. Used in the error messages.\n   * @deprecated Removing the constraints validation on client side.\n   */\n  validateConstraints(mapper: Mapper, value: unknown, objectName: string): void {\n    const failValidation = (\n      constraintName: keyof MapperConstraints,\n      constraintValue: any\n    ): Error => {\n      throw new Error(\n        `\"${objectName}\" with value \"${value}\" should satisfy the constraint \"${constraintName}\": ${constraintValue}.`\n      );\n    };\n    if (mapper.constraints && value != undefined) {\n      const valueAsNumber = value as number;\n      const {\n        ExclusiveMaximum,\n        ExclusiveMinimum,\n        InclusiveMaximum,\n        InclusiveMinimum,\n        MaxItems,\n        MaxLength,\n        MinItems,\n        MinLength,\n        MultipleOf,\n        Pattern,\n        UniqueItems,\n      } = mapper.constraints;\n      if (ExclusiveMaximum != undefined && valueAsNumber >= ExclusiveMaximum) {\n        failValidation(\"ExclusiveMaximum\", ExclusiveMaximum);\n      }\n      if (ExclusiveMinimum != undefined && valueAsNumber <= ExclusiveMinimum) {\n        failValidation(\"ExclusiveMinimum\", ExclusiveMinimum);\n      }\n      if (InclusiveMaximum != undefined && valueAsNumber > InclusiveMaximum) {\n        failValidation(\"InclusiveMaximum\", InclusiveMaximum);\n      }\n      if (InclusiveMinimum != undefined && valueAsNumber < InclusiveMinimum) {\n        failValidation(\"InclusiveMinimum\", InclusiveMinimum);\n      }\n      const valueAsArray = value as any[];\n      if (MaxItems != undefined && valueAsArray.length > MaxItems) {\n        failValidation(\"MaxItems\", MaxItems);\n      }\n      if (MaxLength != undefined && valueAsArray.length > MaxLength) {\n        failValidation(\"MaxLength\", MaxLength);\n      }\n      if (MinItems != undefined && valueAsArray.length < MinItems) {\n        failValidation(\"MinItems\", MinItems);\n      }\n      if (MinLength != undefined && valueAsArray.length < MinLength) {\n        failValidation(\"MinLength\", MinLength);\n      }\n      if (MultipleOf != undefined && valueAsNumber % MultipleOf !== 0) {\n        failValidation(\"MultipleOf\", MultipleOf);\n      }\n      if (Pattern) {\n        const pattern: RegExp = typeof Pattern === \"string\" ? new RegExp(Pattern) : Pattern;\n        if (typeof value !== \"string\" || value.match(pattern) === null) {\n          failValidation(\"Pattern\", Pattern);\n        }\n      }\n      if (\n        UniqueItems &&\n        valueAsArray.some((item: any, i: number, ar: Array<any>) => ar.indexOf(item) !== i)\n      ) {\n        failValidation(\"UniqueItems\", UniqueItems);\n      }\n    }\n  }\n\n  /**\n   * Serialize the given object based on its metadata defined in the mapper.\n   *\n   * @param mapper - The mapper which defines the metadata of the serializable object.\n   * @param object - A valid Javascript object to be serialized.\n   * @param objectName - Name of the serialized object.\n   * @param options - additional options to deserialization.\n   * @returns A valid serialized Javascript object.\n   */\n  serialize(\n    mapper: Mapper,\n    object: unknown,\n    objectName?: string,\n    options: SerializerOptions = {}\n  ): any {\n    const updatedOptions: Required<SerializerOptions> = {\n      rootName: options.rootName ?? \"\",\n      includeRoot: options.includeRoot ?? false,\n      xmlCharKey: options.xmlCharKey ?? XML_CHARKEY,\n    };\n    let payload: any = {};\n    const mapperType = mapper.type.name as string;\n    if (!objectName) {\n      objectName = mapper.serializedName!;\n    }\n    if (mapperType.match(/^Sequence$/i) !== null) {\n      payload = [];\n    }\n\n    if (mapper.isConstant) {\n      object = mapper.defaultValue;\n    }\n\n    // This table of allowed values should help explain\n    // the mapper.required and mapper.nullable properties.\n    // X means \"neither undefined or null are allowed\".\n    //           || required\n    //           || true      | false\n    //  nullable || ==========================\n    //      true || null      | undefined/null\n    //     false || X         | undefined\n    // undefined || X         | undefined/null\n\n    const { required, nullable } = mapper;\n\n    if (required && nullable && object === undefined) {\n      throw new Error(`${objectName} cannot be undefined.`);\n    }\n    if (required && !nullable && object == undefined) {\n      throw new Error(`${objectName} cannot be null or undefined.`);\n    }\n    if (!required && nullable === false && object === null) {\n      throw new Error(`${objectName} cannot be null.`);\n    }\n\n    if (object == undefined) {\n      payload = object;\n    } else {\n      if (mapperType.match(/^any$/i) !== null) {\n        payload = object;\n      } else if (mapperType.match(/^(Number|String|Boolean|Object|Stream|Uuid)$/i) !== null) {\n        payload = serializeBasicTypes(mapperType, objectName, object);\n      } else if (mapperType.match(/^Enum$/i) !== null) {\n        const enumMapper: EnumMapper = mapper as EnumMapper;\n        payload = serializeEnumType(objectName, enumMapper.type.allowedValues, object);\n      } else if (\n        mapperType.match(/^(Date|DateTime|TimeSpan|DateTimeRfc1123|UnixTime)$/i) !== null\n      ) {\n        payload = serializeDateTypes(mapperType, object, objectName);\n      } else if (mapperType.match(/^ByteArray$/i) !== null) {\n        payload = serializeByteArrayType(objectName, object as Uint8Array);\n      } else if (mapperType.match(/^Base64Url$/i) !== null) {\n        payload = serializeBase64UrlType(objectName, object as Uint8Array);\n      } else if (mapperType.match(/^Sequence$/i) !== null) {\n        payload = serializeSequenceType(\n          this,\n          mapper as SequenceMapper,\n          object,\n          objectName,\n          Boolean(this.isXML),\n          updatedOptions\n        );\n      } else if (mapperType.match(/^Dictionary$/i) !== null) {\n        payload = serializeDictionaryType(\n          this,\n          mapper as DictionaryMapper,\n          object,\n          objectName,\n          Boolean(this.isXML),\n          updatedOptions\n        );\n      } else if (mapperType.match(/^Composite$/i) !== null) {\n        payload = serializeCompositeType(\n          this,\n          mapper as CompositeMapper,\n          object,\n          objectName,\n          Boolean(this.isXML),\n          updatedOptions\n        );\n      }\n    }\n    return payload;\n  }\n\n  /**\n   * Deserialize the given object based on its metadata defined in the mapper.\n   *\n   * @param mapper - The mapper which defines the metadata of the serializable object.\n   * @param responseBody - A valid Javascript entity to be deserialized.\n   * @param objectName - Name of the deserialized object.\n   * @param options - Controls behavior of XML parser and builder.\n   * @returns A valid deserialized Javascript object.\n   */\n  deserialize(\n    mapper: Mapper,\n    responseBody: unknown,\n    objectName: string,\n    options: SerializerOptions = {}\n  ): any {\n    const updatedOptions: Required<SerializerOptions> = {\n      rootName: options.rootName ?? \"\",\n      includeRoot: options.includeRoot ?? false,\n      xmlCharKey: options.xmlCharKey ?? XML_CHARKEY,\n    };\n    if (responseBody == undefined) {\n      if (this.isXML && mapper.type.name === \"Sequence\" && !mapper.xmlIsWrapped) {\n        // Edge case for empty XML non-wrapped lists. xml2js can't distinguish\n        // between the list being empty versus being missing,\n        // so let's do the more user-friendly thing and return an empty list.\n        responseBody = [];\n      }\n      // specifically check for undefined as default value can be a falsey value `0, \"\", false, null`\n      if (mapper.defaultValue !== undefined) {\n        responseBody = mapper.defaultValue;\n      }\n      return responseBody;\n    }\n\n    let payload: any;\n    const mapperType = mapper.type.name;\n    if (!objectName) {\n      objectName = mapper.serializedName!;\n    }\n\n    if (mapperType.match(/^Composite$/i) !== null) {\n      payload = deserializeCompositeType(\n        this,\n        mapper as CompositeMapper,\n        responseBody,\n        objectName,\n        updatedOptions\n      );\n    } else {\n      if (this.isXML) {\n        const xmlCharKey = updatedOptions.xmlCharKey;\n        const castResponseBody = responseBody as Record<string, unknown>;\n        /**\n         * If the mapper specifies this as a non-composite type value but the responseBody contains\n         * both header (\"$\" i.e., XML_ATTRKEY) and body (\"#\" i.e., XML_CHARKEY) properties,\n         * then just reduce the responseBody value to the body (\"#\" i.e., XML_CHARKEY) property.\n         */\n        if (\n          castResponseBody[XML_ATTRKEY] != undefined &&\n          castResponseBody[xmlCharKey] != undefined\n        ) {\n          responseBody = castResponseBody[xmlCharKey];\n        }\n      }\n\n      if (mapperType.match(/^Number$/i) !== null) {\n        payload = parseFloat(responseBody as string);\n        if (isNaN(payload)) {\n          payload = responseBody;\n        }\n      } else if (mapperType.match(/^Boolean$/i) !== null) {\n        if (responseBody === \"true\") {\n          payload = true;\n        } else if (responseBody === \"false\") {\n          payload = false;\n        } else {\n          payload = responseBody;\n        }\n      } else if (mapperType.match(/^(String|Enum|Object|Stream|Uuid|TimeSpan|any)$/i) !== null) {\n        payload = responseBody;\n      } else if (mapperType.match(/^(Date|DateTime|DateTimeRfc1123)$/i) !== null) {\n        payload = new Date(responseBody as string);\n      } else if (mapperType.match(/^UnixTime$/i) !== null) {\n        payload = unixTimeToDate(responseBody as number);\n      } else if (mapperType.match(/^ByteArray$/i) !== null) {\n        payload = base64.decodeString(responseBody as string);\n      } else if (mapperType.match(/^Base64Url$/i) !== null) {\n        payload = base64UrlToByteArray(responseBody as string);\n      } else if (mapperType.match(/^Sequence$/i) !== null) {\n        payload = deserializeSequenceType(\n          this,\n          mapper as SequenceMapper,\n          responseBody,\n          objectName,\n          updatedOptions\n        );\n      } else if (mapperType.match(/^Dictionary$/i) !== null) {\n        payload = deserializeDictionaryType(\n          this,\n          mapper as DictionaryMapper,\n          responseBody,\n          objectName,\n          updatedOptions\n        );\n      }\n    }\n\n    if (mapper.isConstant) {\n      payload = mapper.defaultValue;\n    }\n\n    return payload;\n  }\n}\n\nfunction trimEnd(str: string, ch: string): string {\n  let len = str.length;\n  while (len - 1 >= 0 && str[len - 1] === ch) {\n    --len;\n  }\n  return str.substr(0, len);\n}\n\nfunction bufferToBase64Url(buffer: any): string | undefined {\n  if (!buffer) {\n    return undefined;\n  }\n  if (!(buffer instanceof Uint8Array)) {\n    throw new Error(`Please provide an input of type Uint8Array for converting to Base64Url.`);\n  }\n  // Uint8Array to Base64.\n  const str = base64.encodeByteArray(buffer);\n  // Base64 to Base64Url.\n  return trimEnd(str, \"=\").replace(/\\+/g, \"-\").replace(/\\//g, \"_\");\n}\n\nfunction base64UrlToByteArray(str: string): Uint8Array | undefined {\n  if (!str) {\n    return undefined;\n  }\n  if (str && typeof str.valueOf() !== \"string\") {\n    throw new Error(\"Please provide an input of type string for converting to Uint8Array\");\n  }\n  // Base64Url to Base64.\n  str = str.replace(/-/g, \"+\").replace(/_/g, \"/\");\n  // Base64 to Uint8Array.\n  return base64.decodeString(str);\n}\n\nfunction splitSerializeName(prop: string | undefined): string[] {\n  const classes: string[] = [];\n  let partialclass = \"\";\n  if (prop) {\n    const subwords = prop.split(\".\");\n\n    for (const item of subwords) {\n      if (item.charAt(item.length - 1) === \"\\\\\") {\n        partialclass += item.substr(0, item.length - 1) + \".\";\n      } else {\n        partialclass += item;\n        classes.push(partialclass);\n        partialclass = \"\";\n      }\n    }\n  }\n\n  return classes;\n}\n\nfunction dateToUnixTime(d: string | Date): number | undefined {\n  if (!d) {\n    return undefined;\n  }\n\n  if (typeof d.valueOf() === \"string\") {\n    d = new Date(d as string);\n  }\n  return Math.floor((d as Date).getTime() / 1000);\n}\n\nfunction unixTimeToDate(n: number): Date | undefined {\n  if (!n) {\n    return undefined;\n  }\n  return new Date(n * 1000);\n}\n\nfunction serializeBasicTypes(typeName: string, objectName: string, value: any): any {\n  if (value !== null && value !== undefined) {\n    if (typeName.match(/^Number$/i) !== null) {\n      if (typeof value !== \"number\") {\n        throw new Error(`${objectName} with value ${value} must be of type number.`);\n      }\n    } else if (typeName.match(/^String$/i) !== null) {\n      if (typeof value.valueOf() !== \"string\") {\n        throw new Error(`${objectName} with value \"${value}\" must be of type string.`);\n      }\n    } else if (typeName.match(/^Uuid$/i) !== null) {\n      if (!(typeof value.valueOf() === \"string\" && utils.isValidUuid(value))) {\n        throw new Error(\n          `${objectName} with value \"${value}\" must be of type string and a valid uuid.`\n        );\n      }\n    } else if (typeName.match(/^Boolean$/i) !== null) {\n      if (typeof value !== \"boolean\") {\n        throw new Error(`${objectName} with value ${value} must be of type boolean.`);\n      }\n    } else if (typeName.match(/^Stream$/i) !== null) {\n      const objectType = typeof value;\n      if (\n        objectType !== \"string\" &&\n        objectType !== \"function\" &&\n        !(value instanceof ArrayBuffer) &&\n        !ArrayBuffer.isView(value) &&\n        !((typeof Blob === \"function\" || typeof Blob === \"object\") && value instanceof Blob)\n      ) {\n        throw new Error(\n          `${objectName} must be a string, Blob, ArrayBuffer, ArrayBufferView, or a function returning NodeJS.ReadableStream.`\n        );\n      }\n    }\n  }\n\n  return value;\n}\n\nfunction serializeEnumType(objectName: string, allowedValues: Array<any>, value: any): any {\n  if (!allowedValues) {\n    throw new Error(\n      `Please provide a set of allowedValues to validate ${objectName} as an Enum Type.`\n    );\n  }\n  const isPresent = allowedValues.some((item) => {\n    if (typeof item.valueOf() === \"string\") {\n      return item.toLowerCase() === value.toLowerCase();\n    }\n    return item === value;\n  });\n  if (!isPresent) {\n    throw new Error(\n      `${value} is not a valid value for ${objectName}. The valid values are: ${JSON.stringify(\n        allowedValues\n      )}.`\n    );\n  }\n  return value;\n}\n\nfunction serializeByteArrayType(objectName: string, value: Uint8Array): string {\n  let returnValue: string = \"\";\n  if (value != undefined) {\n    if (!(value instanceof Uint8Array)) {\n      throw new Error(`${objectName} must be of type Uint8Array.`);\n    }\n    returnValue = base64.encodeByteArray(value);\n  }\n  return returnValue;\n}\n\nfunction serializeBase64UrlType(objectName: string, value: Uint8Array): string {\n  let returnValue: string = \"\";\n  if (value != undefined) {\n    if (!(value instanceof Uint8Array)) {\n      throw new Error(`${objectName} must be of type Uint8Array.`);\n    }\n    returnValue = bufferToBase64Url(value) || \"\";\n  }\n  return returnValue;\n}\n\nfunction serializeDateTypes(typeName: string, value: any, objectName: string): any {\n  if (value != undefined) {\n    if (typeName.match(/^Date$/i) !== null) {\n      if (\n        !(\n          value instanceof Date ||\n          (typeof value.valueOf() === \"string\" && !isNaN(Date.parse(value)))\n        )\n      ) {\n        throw new Error(`${objectName} must be an instanceof Date or a string in ISO8601 format.`);\n      }\n      value =\n        value instanceof Date\n          ? value.toISOString().substring(0, 10)\n          : new Date(value).toISOString().substring(0, 10);\n    } else if (typeName.match(/^DateTime$/i) !== null) {\n      if (\n        !(\n          value instanceof Date ||\n          (typeof value.valueOf() === \"string\" && !isNaN(Date.parse(value)))\n        )\n      ) {\n        throw new Error(`${objectName} must be an instanceof Date or a string in ISO8601 format.`);\n      }\n      value = value instanceof Date ? value.toISOString() : new Date(value).toISOString();\n    } else if (typeName.match(/^DateTimeRfc1123$/i) !== null) {\n      if (\n        !(\n          value instanceof Date ||\n          (typeof value.valueOf() === \"string\" && !isNaN(Date.parse(value)))\n        )\n      ) {\n        throw new Error(`${objectName} must be an instanceof Date or a string in RFC-1123 format.`);\n      }\n      value = value instanceof Date ? value.toUTCString() : new Date(value).toUTCString();\n    } else if (typeName.match(/^UnixTime$/i) !== null) {\n      if (\n        !(\n          value instanceof Date ||\n          (typeof value.valueOf() === \"string\" && !isNaN(Date.parse(value)))\n        )\n      ) {\n        throw new Error(\n          `${objectName} must be an instanceof Date or a string in RFC-1123/ISO8601 format ` +\n            `for it to be serialized in UnixTime/Epoch format.`\n        );\n      }\n      value = dateToUnixTime(value);\n    } else if (typeName.match(/^TimeSpan$/i) !== null) {\n      if (!utils.isDuration(value)) {\n        throw new Error(\n          `${objectName} must be a string in ISO 8601 format. Instead was \"${value}\".`\n        );\n      }\n    }\n  }\n  return value;\n}\n\nfunction serializeSequenceType(\n  serializer: Serializer,\n  mapper: SequenceMapper,\n  object: any,\n  objectName: string,\n  isXml: boolean,\n  options: Required<SerializerOptions>\n): any[] {\n  if (!Array.isArray(object)) {\n    throw new Error(`${objectName} must be of type Array.`);\n  }\n  const elementType = mapper.type.element;\n  if (!elementType || typeof elementType !== \"object\") {\n    throw new Error(\n      `element\" metadata for an Array must be defined in the ` +\n        `mapper and it must of type \"object\" in ${objectName}.`\n    );\n  }\n  const tempArray = [];\n  for (let i = 0; i < object.length; i++) {\n    const serializedValue = serializer.serialize(elementType, object[i], objectName, options);\n\n    if (isXml && elementType.xmlNamespace) {\n      const xmlnsKey = elementType.xmlNamespacePrefix\n        ? `xmlns:${elementType.xmlNamespacePrefix}`\n        : \"xmlns\";\n      if (elementType.type.name === \"Composite\") {\n        tempArray[i] = { ...serializedValue };\n        tempArray[i][XML_ATTRKEY] = { [xmlnsKey]: elementType.xmlNamespace };\n      } else {\n        tempArray[i] = {};\n        tempArray[i][options.xmlCharKey] = serializedValue;\n        tempArray[i][XML_ATTRKEY] = { [xmlnsKey]: elementType.xmlNamespace };\n      }\n    } else {\n      tempArray[i] = serializedValue;\n    }\n  }\n  return tempArray;\n}\n\nfunction serializeDictionaryType(\n  serializer: Serializer,\n  mapper: DictionaryMapper,\n  object: any,\n  objectName: string,\n  isXml: boolean,\n  options: Required<SerializerOptions>\n): { [key: string]: any } {\n  if (typeof object !== \"object\") {\n    throw new Error(`${objectName} must be of type object.`);\n  }\n  const valueType = mapper.type.value;\n  if (!valueType || typeof valueType !== \"object\") {\n    throw new Error(\n      `\"value\" metadata for a Dictionary must be defined in the ` +\n        `mapper and it must of type \"object\" in ${objectName}.`\n    );\n  }\n  const tempDictionary: { [key: string]: any } = {};\n  for (const key of Object.keys(object)) {\n    const serializedValue = serializer.serialize(valueType, object[key], objectName, options);\n    // If the element needs an XML namespace we need to add it within the $ property\n    tempDictionary[key] = getXmlObjectValue(valueType, serializedValue, isXml, options);\n  }\n\n  // Add the namespace to the root element if needed\n  if (isXml && mapper.xmlNamespace) {\n    const xmlnsKey = mapper.xmlNamespacePrefix ? `xmlns:${mapper.xmlNamespacePrefix}` : \"xmlns\";\n\n    const result = tempDictionary;\n    result[XML_ATTRKEY] = { [xmlnsKey]: mapper.xmlNamespace };\n    return result;\n  }\n\n  return tempDictionary;\n}\n\n/**\n * Resolves the additionalProperties property from a referenced mapper.\n * @param serializer - The serializer containing the entire set of mappers.\n * @param mapper - The composite mapper to resolve.\n * @param objectName - Name of the object being serialized.\n */\nfunction resolveAdditionalProperties(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  objectName: string\n): SequenceMapper | BaseMapper | CompositeMapper | DictionaryMapper | EnumMapper | undefined {\n  const additionalProperties = mapper.type.additionalProperties;\n\n  if (!additionalProperties && mapper.type.className) {\n    const modelMapper = resolveReferencedMapper(serializer, mapper, objectName);\n    return modelMapper?.type.additionalProperties;\n  }\n\n  return additionalProperties;\n}\n\n/**\n * Finds the mapper referenced by `className`.\n * @param serializer - The serializer containing the entire set of mappers\n * @param mapper - The composite mapper to resolve\n * @param objectName - Name of the object being serialized\n */\nfunction resolveReferencedMapper(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  objectName: string\n): CompositeMapper | undefined {\n  const className = mapper.type.className;\n  if (!className) {\n    throw new Error(\n      `Class name for model \"${objectName}\" is not provided in the mapper \"${JSON.stringify(\n        mapper,\n        undefined,\n        2\n      )}\".`\n    );\n  }\n\n  return serializer.modelMappers[className];\n}\n\n/**\n * Resolves a composite mapper's modelProperties.\n * @param serializer - The serializer containing the entire set of mappers\n * @param mapper - The composite mapper to resolve\n */\nfunction resolveModelProperties(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  objectName: string\n): { [propertyName: string]: Mapper } {\n  let modelProps = mapper.type.modelProperties;\n  if (!modelProps) {\n    const modelMapper = resolveReferencedMapper(serializer, mapper, objectName);\n    if (!modelMapper) {\n      throw new Error(`mapper() cannot be null or undefined for model \"${mapper.type.className}\".`);\n    }\n    modelProps = modelMapper?.type.modelProperties;\n    if (!modelProps) {\n      throw new Error(\n        `modelProperties cannot be null or undefined in the ` +\n          `mapper \"${JSON.stringify(modelMapper)}\" of type \"${\n            mapper.type.className\n          }\" for object \"${objectName}\".`\n      );\n    }\n  }\n\n  return modelProps;\n}\n\nfunction serializeCompositeType(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  object: any,\n  objectName: string,\n  isXml: boolean,\n  options: Required<SerializerOptions>\n): any {\n  if (getPolymorphicDiscriminatorRecursively(serializer, mapper)) {\n    mapper = getPolymorphicMapper(serializer, mapper, object, \"clientName\");\n  }\n\n  if (object != undefined) {\n    const payload: any = {};\n    const modelProps = resolveModelProperties(serializer, mapper, objectName);\n    for (const key of Object.keys(modelProps)) {\n      const propertyMapper = modelProps[key];\n      if (propertyMapper.readOnly) {\n        continue;\n      }\n\n      let propName: string | undefined;\n      let parentObject: any = payload;\n      if (serializer.isXML) {\n        if (propertyMapper.xmlIsWrapped) {\n          propName = propertyMapper.xmlName;\n        } else {\n          propName = propertyMapper.xmlElementName || propertyMapper.xmlName;\n        }\n      } else {\n        const paths = splitSerializeName(propertyMapper.serializedName!);\n        propName = paths.pop();\n\n        for (const pathName of paths) {\n          const childObject = parentObject[pathName];\n          if (\n            childObject == undefined &&\n            (object[key] != undefined || propertyMapper.defaultValue !== undefined)\n          ) {\n            parentObject[pathName] = {};\n          }\n          parentObject = parentObject[pathName];\n        }\n      }\n\n      if (parentObject != undefined) {\n        if (isXml && mapper.xmlNamespace) {\n          const xmlnsKey = mapper.xmlNamespacePrefix\n            ? `xmlns:${mapper.xmlNamespacePrefix}`\n            : \"xmlns\";\n          parentObject[XML_ATTRKEY] = {\n            ...parentObject[XML_ATTRKEY],\n            [xmlnsKey]: mapper.xmlNamespace,\n          };\n        }\n        const propertyObjectName =\n          propertyMapper.serializedName !== \"\"\n            ? objectName + \".\" + propertyMapper.serializedName\n            : objectName;\n\n        let toSerialize = object[key];\n        const polymorphicDiscriminator = getPolymorphicDiscriminatorRecursively(serializer, mapper);\n        if (\n          polymorphicDiscriminator &&\n          polymorphicDiscriminator.clientName === key &&\n          toSerialize == undefined\n        ) {\n          toSerialize = mapper.serializedName;\n        }\n\n        const serializedValue = serializer.serialize(\n          propertyMapper,\n          toSerialize,\n          propertyObjectName,\n          options\n        );\n\n        if (serializedValue !== undefined && propName != undefined) {\n          const value = getXmlObjectValue(propertyMapper, serializedValue, isXml, options);\n          if (isXml && propertyMapper.xmlIsAttribute) {\n            // XML_ATTRKEY, i.e., $ is the key attributes are kept under in xml2js.\n            // This keeps things simple while preventing name collision\n            // with names in user documents.\n            parentObject[XML_ATTRKEY] = parentObject[XML_ATTRKEY] || {};\n            parentObject[XML_ATTRKEY][propName] = serializedValue;\n          } else if (isXml && propertyMapper.xmlIsWrapped) {\n            parentObject[propName] = { [propertyMapper.xmlElementName!]: value };\n          } else {\n            parentObject[propName] = value;\n          }\n        }\n      }\n    }\n\n    const additionalPropertiesMapper = resolveAdditionalProperties(serializer, mapper, objectName);\n    if (additionalPropertiesMapper) {\n      const propNames = Object.keys(modelProps);\n      for (const clientPropName in object) {\n        const isAdditionalProperty = propNames.every((pn) => pn !== clientPropName);\n        if (isAdditionalProperty) {\n          payload[clientPropName] = serializer.serialize(\n            additionalPropertiesMapper,\n            object[clientPropName],\n            objectName + '[\"' + clientPropName + '\"]',\n            options\n          );\n        }\n      }\n    }\n\n    return payload;\n  }\n  return object;\n}\n\nfunction getXmlObjectValue(\n  propertyMapper: Mapper,\n  serializedValue: any,\n  isXml: boolean,\n  options: Required<SerializerOptions>\n): any {\n  if (!isXml || !propertyMapper.xmlNamespace) {\n    return serializedValue;\n  }\n\n  const xmlnsKey = propertyMapper.xmlNamespacePrefix\n    ? `xmlns:${propertyMapper.xmlNamespacePrefix}`\n    : \"xmlns\";\n  const xmlNamespace = { [xmlnsKey]: propertyMapper.xmlNamespace };\n\n  if ([\"Composite\"].includes(propertyMapper.type.name)) {\n    if (serializedValue[XML_ATTRKEY]) {\n      return serializedValue;\n    } else {\n      const result: any = { ...serializedValue };\n      result[XML_ATTRKEY] = xmlNamespace;\n      return result;\n    }\n  }\n  const result: any = {};\n  result[options.xmlCharKey] = serializedValue;\n  result[XML_ATTRKEY] = xmlNamespace;\n  return result;\n}\n\nfunction isSpecialXmlProperty(propertyName: string, options: Required<SerializerOptions>): boolean {\n  return [XML_ATTRKEY, options.xmlCharKey].includes(propertyName);\n}\n\nfunction deserializeCompositeType(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  responseBody: any,\n  objectName: string,\n  options: Required<SerializerOptions>\n): any {\n  const xmlCharKey = options.xmlCharKey ?? XML_CHARKEY;\n  if (getPolymorphicDiscriminatorRecursively(serializer, mapper)) {\n    mapper = getPolymorphicMapper(serializer, mapper, responseBody, \"serializedName\");\n  }\n\n  const modelProps = resolveModelProperties(serializer, mapper, objectName);\n  let instance: { [key: string]: any } = {};\n  const handledPropertyNames: string[] = [];\n\n  for (const key of Object.keys(modelProps)) {\n    const propertyMapper = modelProps[key];\n    const paths = splitSerializeName(modelProps[key].serializedName!);\n    handledPropertyNames.push(paths[0]);\n    const { serializedName, xmlName, xmlElementName } = propertyMapper;\n    let propertyObjectName = objectName;\n    if (serializedName !== \"\" && serializedName !== undefined) {\n      propertyObjectName = objectName + \".\" + serializedName;\n    }\n\n    const headerCollectionPrefix = (propertyMapper as DictionaryMapper).headerCollectionPrefix;\n    if (headerCollectionPrefix) {\n      const dictionary: any = {};\n      for (const headerKey of Object.keys(responseBody)) {\n        if (headerKey.startsWith(headerCollectionPrefix)) {\n          dictionary[headerKey.substring(headerCollectionPrefix.length)] = serializer.deserialize(\n            (propertyMapper as DictionaryMapper).type.value,\n            responseBody[headerKey],\n            propertyObjectName,\n            options\n          );\n        }\n\n        handledPropertyNames.push(headerKey);\n      }\n      instance[key] = dictionary;\n    } else if (serializer.isXML) {\n      if (propertyMapper.xmlIsAttribute && responseBody[XML_ATTRKEY]) {\n        instance[key] = serializer.deserialize(\n          propertyMapper,\n          responseBody[XML_ATTRKEY][xmlName!],\n          propertyObjectName,\n          options\n        );\n      } else if (propertyMapper.xmlIsMsText) {\n        if (responseBody[xmlCharKey] !== undefined) {\n          instance[key] = responseBody[xmlCharKey];\n        } else if (typeof responseBody === \"string\") {\n          // The special case where xml parser parses \"<Name>content</Name>\" into JSON of\n          //   `{ name: \"content\"}` instead of `{ name: { \"_\": \"content\" }}`\n          instance[key] = responseBody;\n        }\n      } else {\n        const propertyName = xmlElementName || xmlName || serializedName;\n        if (propertyMapper.xmlIsWrapped) {\n          /* a list of <xmlElementName> wrapped by <xmlName>\n            For the xml example below\n              <Cors>\n                <CorsRule>...</CorsRule>\n                <CorsRule>...</CorsRule>\n              </Cors>\n            the responseBody has\n              {\n                Cors: {\n                  CorsRule: [{...}, {...}]\n                }\n              }\n            xmlName is \"Cors\" and xmlElementName is\"CorsRule\".\n          */\n          const wrapped = responseBody[xmlName!];\n          const elementList = wrapped?.[xmlElementName!] ?? [];\n          instance[key] = serializer.deserialize(\n            propertyMapper,\n            elementList,\n            propertyObjectName,\n            options\n          );\n        } else {\n          const property = responseBody[propertyName!];\n          instance[key] = serializer.deserialize(\n            propertyMapper,\n            property,\n            propertyObjectName,\n            options\n          );\n        }\n      }\n    } else {\n      // deserialize the property if it is present in the provided responseBody instance\n      let propertyInstance;\n      let res = responseBody;\n      // traversing the object step by step.\n      for (const item of paths) {\n        if (!res) break;\n        res = res[item];\n      }\n      propertyInstance = res;\n      const polymorphicDiscriminator = mapper.type.polymorphicDiscriminator;\n      // checking that the model property name (key)(ex: \"fishtype\") and the\n      // clientName of the polymorphicDiscriminator {metadata} (ex: \"fishtype\")\n      // instead of the serializedName of the polymorphicDiscriminator (ex: \"fish.type\")\n      // is a better approach. The generator is not consistent with escaping '\\.' in the\n      // serializedName of the property (ex: \"fish\\.type\") that is marked as polymorphic discriminator\n      // and the serializedName of the metadata polymorphicDiscriminator (ex: \"fish.type\"). However,\n      // the clientName transformation of the polymorphicDiscriminator (ex: \"fishtype\") and\n      // the transformation of model property name (ex: \"fishtype\") is done consistently.\n      // Hence, it is a safer bet to rely on the clientName of the polymorphicDiscriminator.\n      if (\n        polymorphicDiscriminator &&\n        key === polymorphicDiscriminator.clientName &&\n        propertyInstance == undefined\n      ) {\n        propertyInstance = mapper.serializedName;\n      }\n\n      let serializedValue;\n      // paging\n      if (Array.isArray(responseBody[key]) && modelProps[key].serializedName === \"\") {\n        propertyInstance = responseBody[key];\n        const arrayInstance = serializer.deserialize(\n          propertyMapper,\n          propertyInstance,\n          propertyObjectName,\n          options\n        );\n        // Copy over any properties that have already been added into the instance, where they do\n        // not exist on the newly de-serialized array\n        for (const [k, v] of Object.entries(instance)) {\n          if (!Object.prototype.hasOwnProperty.call(arrayInstance, k)) {\n            arrayInstance[k] = v;\n          }\n        }\n        instance = arrayInstance;\n      } else if (propertyInstance !== undefined || propertyMapper.defaultValue !== undefined) {\n        serializedValue = serializer.deserialize(\n          propertyMapper,\n          propertyInstance,\n          propertyObjectName,\n          options\n        );\n        instance[key] = serializedValue;\n      }\n    }\n  }\n\n  const additionalPropertiesMapper = mapper.type.additionalProperties;\n  if (additionalPropertiesMapper) {\n    const isAdditionalProperty = (responsePropName: string): boolean => {\n      for (const clientPropName in modelProps) {\n        const paths = splitSerializeName(modelProps[clientPropName].serializedName);\n        if (paths[0] === responsePropName) {\n          return false;\n        }\n      }\n      return true;\n    };\n\n    for (const responsePropName in responseBody) {\n      if (isAdditionalProperty(responsePropName)) {\n        instance[responsePropName] = serializer.deserialize(\n          additionalPropertiesMapper,\n          responseBody[responsePropName],\n          objectName + '[\"' + responsePropName + '\"]',\n          options\n        );\n      }\n    }\n  } else if (responseBody) {\n    for (const key of Object.keys(responseBody)) {\n      if (\n        instance[key] === undefined &&\n        !handledPropertyNames.includes(key) &&\n        !isSpecialXmlProperty(key, options)\n      ) {\n        instance[key] = responseBody[key];\n      }\n    }\n  }\n\n  return instance;\n}\n\nfunction deserializeDictionaryType(\n  serializer: Serializer,\n  mapper: DictionaryMapper,\n  responseBody: any,\n  objectName: string,\n  options: Required<SerializerOptions>\n): { [key: string]: any } {\n  const value = mapper.type.value;\n  if (!value || typeof value !== \"object\") {\n    throw new Error(\n      `\"value\" metadata for a Dictionary must be defined in the ` +\n        `mapper and it must of type \"object\" in ${objectName}`\n    );\n  }\n  if (responseBody) {\n    const tempDictionary: { [key: string]: any } = {};\n    for (const key of Object.keys(responseBody)) {\n      tempDictionary[key] = serializer.deserialize(value, responseBody[key], objectName, options);\n    }\n    return tempDictionary;\n  }\n  return responseBody;\n}\n\nfunction deserializeSequenceType(\n  serializer: Serializer,\n  mapper: SequenceMapper,\n  responseBody: any,\n  objectName: string,\n  options: Required<SerializerOptions>\n): any[] {\n  const element = mapper.type.element;\n  if (!element || typeof element !== \"object\") {\n    throw new Error(\n      `element\" metadata for an Array must be defined in the ` +\n        `mapper and it must of type \"object\" in ${objectName}`\n    );\n  }\n  if (responseBody) {\n    if (!Array.isArray(responseBody)) {\n      // xml2js will interpret a single element array as just the element, so force it to be an array\n      responseBody = [responseBody];\n    }\n\n    const tempArray = [];\n    for (let i = 0; i < responseBody.length; i++) {\n      tempArray[i] = serializer.deserialize(\n        element,\n        responseBody[i],\n        `${objectName}[${i}]`,\n        options\n      );\n    }\n    return tempArray;\n  }\n  return responseBody;\n}\n\nfunction getPolymorphicMapper(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  object: any,\n  polymorphicPropertyName: \"clientName\" | \"serializedName\"\n): CompositeMapper {\n  const polymorphicDiscriminator = getPolymorphicDiscriminatorRecursively(serializer, mapper);\n  if (polymorphicDiscriminator) {\n    const discriminatorName = polymorphicDiscriminator[polymorphicPropertyName];\n    if (discriminatorName != undefined) {\n      const discriminatorValue = object[discriminatorName];\n      if (discriminatorValue != undefined) {\n        const typeName = mapper.type.uberParent || mapper.type.className;\n        const indexDiscriminator =\n          discriminatorValue === typeName\n            ? discriminatorValue\n            : typeName + \".\" + discriminatorValue;\n        const polymorphicMapper = serializer.modelMappers.discriminators[indexDiscriminator];\n        if (polymorphicMapper) {\n          mapper = polymorphicMapper;\n        }\n      }\n    }\n  }\n  return mapper;\n}\n\nfunction getPolymorphicDiscriminatorRecursively(\n  serializer: Serializer,\n  mapper: CompositeMapper\n): PolymorphicDiscriminator | undefined {\n  return (\n    mapper.type.polymorphicDiscriminator ||\n    getPolymorphicDiscriminatorSafely(serializer, mapper.type.uberParent) ||\n    getPolymorphicDiscriminatorSafely(serializer, mapper.type.className)\n  );\n}\n\nfunction getPolymorphicDiscriminatorSafely(serializer: Serializer, typeName?: string): any {\n  return (\n    typeName &&\n    serializer.modelMappers[typeName] &&\n    serializer.modelMappers[typeName].type.polymorphicDiscriminator\n  );\n}\n\n/**\n * Description of various value constraints such as integer ranges and string regex.\n */\nexport interface MapperConstraints {\n  /**\n   * The value should be less than or equal to the `InclusiveMaximum` value.\n   */\n  InclusiveMaximum?: number;\n  /**\n   * The value should be less than the `ExclusiveMaximum` value.\n   */\n  ExclusiveMaximum?: number;\n  /**\n   * The value should be greater than or equal to the `InclusiveMinimum` value.\n   */\n  InclusiveMinimum?: number;\n  /**\n   * The value should be greater than the `InclusiveMinimum` value.\n   */\n  ExclusiveMinimum?: number;\n  /**\n   * The length should be smaller than the `MaxLength`.\n   */\n  MaxLength?: number;\n  /**\n   * The length should be bigger than the `MinLength`.\n   */\n  MinLength?: number;\n  /**\n   * The value must match the pattern.\n   */\n  Pattern?: RegExp;\n  /**\n   * The value must contain fewer items than the MaxItems value.\n   */\n  MaxItems?: number;\n  /**\n   * The value must contain more items than the `MinItems` value.\n   */\n  MinItems?: number;\n  /**\n   * The value must contain only unique items.\n   */\n  UniqueItems?: true;\n  /**\n   * The value should be exactly divisible by the `MultipleOf` value.\n   */\n  MultipleOf?: number;\n}\n\n/**\n * Type of the mapper. Includes known mappers.\n */\nexport type MapperType =\n  | SimpleMapperType\n  | CompositeMapperType\n  | SequenceMapperType\n  | DictionaryMapperType\n  | EnumMapperType;\n\n/**\n * The type of a simple mapper.\n */\nexport interface SimpleMapperType {\n  /**\n   * Name of the type of the property.\n   */\n  name:\n    | \"Base64Url\"\n    | \"Boolean\"\n    | \"ByteArray\"\n    | \"Date\"\n    | \"DateTime\"\n    | \"DateTimeRfc1123\"\n    | \"Object\"\n    | \"Stream\"\n    | \"String\"\n    | \"TimeSpan\"\n    | \"UnixTime\"\n    | \"Uuid\"\n    | \"Number\"\n    | \"any\";\n}\n\n/**\n * Helps build a mapper that describes how to map a set of properties of an object based on other mappers.\n *\n * Only one of the following properties should be present: `className`, `modelProperties` and `additionalProperties`.\n */\nexport interface CompositeMapperType {\n  /**\n   * Name of the composite mapper type.\n   */\n  name: \"Composite\";\n\n  /**\n   * Use `className` to reference another type definition.\n   */\n  className?: string;\n\n  /**\n   * Use `modelProperties` when the reference to the other type has been resolved.\n   */\n  modelProperties?: { [propertyName: string]: Mapper };\n\n  /**\n   * Used when a model has `additionalProperties: true`. Allows the generic processing of unnamed model properties on the response object.\n   */\n  additionalProperties?: Mapper;\n\n  /**\n   * The name of the top-most parent scheme, the one that has no parents.\n   */\n  uberParent?: string;\n\n  /**\n   * A polymorphic discriminator.\n   */\n  polymorphicDiscriminator?: PolymorphicDiscriminator;\n}\n\n/**\n * Helps build a mapper that describes how to parse a sequence of mapped values.\n */\nexport interface SequenceMapperType {\n  /**\n   * Name of the sequence type mapper.\n   */\n  name: \"Sequence\";\n  /**\n   * The mapper to use to map each one of the properties of the sequence.\n   */\n  element: Mapper;\n}\n\n/**\n * Helps build a mapper that describes how to parse a dictionary of mapped values.\n */\nexport interface DictionaryMapperType {\n  /**\n   * Name of the sequence type mapper.\n   */\n  name: \"Dictionary\";\n  /**\n   * The mapper to use to map the value of each property in the dictionary.\n   */\n  value: Mapper;\n}\n\n/**\n * Helps build a mapper that describes how to parse an enum value.\n */\nexport interface EnumMapperType {\n  /**\n   * Name of the enum type mapper.\n   */\n  name: \"Enum\";\n  /**\n   * Values allowed by this mapper.\n   */\n  allowedValues: any[];\n}\n\n/**\n * The base definition of a mapper. Can be used for XML and plain JavaScript objects.\n */\nexport interface BaseMapper {\n  /**\n   * Name for the xml element\n   */\n  xmlName?: string;\n  /**\n   * Xml element namespace\n   */\n  xmlNamespace?: string;\n  /**\n   * Xml element namespace prefix\n   */\n  xmlNamespacePrefix?: string;\n  /**\n   * Determines if the current property should be serialized as an attribute of the parent xml element\n   */\n  xmlIsAttribute?: boolean;\n  /**\n   * Determines if the current property should be serialized as the inner content of the xml element\n   */\n  xmlIsMsText?: boolean;\n  /**\n   * Name for the xml elements when serializing an array\n   */\n  xmlElementName?: string;\n  /**\n   * Whether or not the current property should have a wrapping XML element\n   */\n  xmlIsWrapped?: boolean;\n  /**\n   * Whether or not the current property is readonly\n   */\n  readOnly?: boolean;\n  /**\n   * Whether or not the current property is a constant\n   */\n  isConstant?: boolean;\n  /**\n   * Whether or not the current property is required\n   */\n  required?: boolean;\n  /**\n   * Whether or not the current property allows mull as a value\n   */\n  nullable?: boolean;\n  /**\n   * The name to use when serializing\n   */\n  serializedName?: string;\n  /**\n   * Type of the mapper\n   */\n  type: MapperType;\n  /**\n   * Default value when one is not explicitly provided\n   */\n  defaultValue?: any;\n  /**\n   * Constraints to test the current value against\n   */\n  constraints?: MapperConstraints;\n}\n\n/**\n * Mappers are definitions of the data models used in the library.\n * These data models are part of the Operation or Client definitions in the responses or parameters.\n */\nexport type Mapper = BaseMapper | CompositeMapper | SequenceMapper | DictionaryMapper | EnumMapper;\n\n/**\n * Used to disambiguate discriminated type unions.\n * For example, if response can have many shapes but also includes a 'kind' field (or similar),\n * that field can be used to determine how to deserialize the response to the correct type.\n */\nexport interface PolymorphicDiscriminator {\n  /**\n   * Name of the discriminant property in the original JSON payload, e.g. `@odata.kind`.\n   */\n  serializedName: string;\n  /**\n   * Name to use on the resulting object instead of the original property name.\n   * Useful since the JSON property could be difficult to work with.\n   * For example: For a field received as `@odata.kind`, the final object could instead include a property simply named `kind`.\n   */\n  clientName: string;\n  /**\n   * It may contain any other property.\n   */\n  [key: string]: string;\n}\n\n/**\n * A mapper composed of other mappers.\n */\nexport interface CompositeMapper extends BaseMapper {\n  /**\n   * The type descriptor of the `CompositeMapper`.\n   */\n  type: CompositeMapperType;\n}\n\n/**\n * A mapper describing arrays.\n */\nexport interface SequenceMapper extends BaseMapper {\n  /**\n   * The type descriptor of the `SequenceMapper`.\n   */\n  type: SequenceMapperType;\n}\n\n/**\n * A mapper describing plain JavaScript objects used as key/value pairs.\n */\nexport interface DictionaryMapper extends BaseMapper {\n  /**\n   * The type descriptor of the `DictionaryMapper`.\n   */\n  type: DictionaryMapperType;\n  /**\n   * Optionally, a prefix to add to the header collection.\n   */\n  headerCollectionPrefix?: string;\n}\n\n/**\n * A mapper describing an enum value.\n */\nexport interface EnumMapper extends BaseMapper {\n  /**\n   * The type descriptor of the `EnumMapper`.\n   */\n  type: EnumMapperType;\n}\n\n/**\n * An interface representing an URL parameter value.\n */\nexport interface UrlParameterValue {\n  /**\n   * The URL value.\n   */\n  value: string;\n  /**\n   * Whether to keep or skip URL encoding.\n   */\n  skipUrlEncoding: boolean;\n}\n\n/**\n * Utility function that serializes an object that might contain binary information into a plain object, array or a string.\n */\nexport function serializeObject(toSerialize: unknown): any {\n  const castToSerialize = toSerialize as Record<string, unknown>;\n  if (toSerialize == undefined) return undefined;\n  if (toSerialize instanceof Uint8Array) {\n    toSerialize = base64.encodeByteArray(toSerialize);\n    return toSerialize;\n  } else if (toSerialize instanceof Date) {\n    return toSerialize.toISOString();\n  } else if (Array.isArray(toSerialize)) {\n    const array = [];\n    for (let i = 0; i < toSerialize.length; i++) {\n      array.push(serializeObject(toSerialize[i]));\n    }\n    return array;\n  } else if (typeof toSerialize === \"object\") {\n    const dictionary: { [key: string]: any } = {};\n    for (const property in toSerialize) {\n      dictionary[property] = serializeObject(castToSerialize[property]);\n    }\n    return dictionary;\n  }\n  return toSerialize;\n}\n\n/**\n * Utility function to create a K:V from a list of strings\n */\nfunction strEnum<T extends string>(o: Array<T>): { [K in T]: K } {\n  const result: any = {};\n  for (const key of o) {\n    result[key] = key;\n  }\n  return result;\n}\n\n/**\n * String enum containing the string types of property mappers.\n */\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport const MapperType = strEnum([\n  \"Base64Url\",\n  \"Boolean\",\n  \"ByteArray\",\n  \"Composite\",\n  \"Date\",\n  \"DateTime\",\n  \"DateTimeRfc1123\",\n  \"Dictionary\",\n  \"Enum\",\n  \"Number\",\n  \"Object\",\n  \"Sequence\",\n  \"String\",\n  \"Stream\",\n  \"TimeSpan\",\n  \"UnixTime\",\n]);\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { Context, SpanOptions } from \"@azure/core-tracing\";\nimport { HttpHeaders, HttpHeadersLike, isHttpHeadersLike } from \"./httpHeaders\";\nimport { Mapper, Serializer } from \"./serializer\";\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { HttpOperationResponse } from \"./httpOperationResponse\";\nimport { OperationResponse } from \"./operationResponse\";\nimport { OperationSpec } from \"./operationSpec\";\nimport { ProxySettings } from \"./serviceClient\";\nimport { SerializerOptions } from \"./util/serializer.common\";\nimport { generateUuid } from \"./util/utils\";\n\n/**\n * List of supported HTTP methods.\n */\nexport type HttpMethods =\n  | \"GET\"\n  | \"PUT\"\n  | \"POST\"\n  | \"DELETE\"\n  | \"PATCH\"\n  | \"HEAD\"\n  | \"OPTIONS\"\n  | \"TRACE\";\n\n/**\n * Possible HTTP request body types\n */\nexport type HttpRequestBody =\n  | Blob\n  | string\n  | ArrayBuffer\n  | ArrayBufferView\n  | (() => NodeJS.ReadableStream);\n\n/**\n * Fired in response to upload or download progress.\n */\nexport type TransferProgressEvent = {\n  /**\n   * The number of bytes loaded so far.\n   */\n  loadedBytes: number;\n};\n\n/**\n * A description of a HTTP request to be made to a remote server.\n */\nexport interface WebResourceLike {\n  /**\n   * The URL being accessed by the request.\n   */\n  url: string;\n  /**\n   * The HTTP method to use when making the request.\n   */\n  method: HttpMethods;\n  /**\n   * The HTTP body contents of the request.\n   */\n  body?: any;\n  /**\n   * The HTTP headers to use when making the request.\n   */\n  headers: HttpHeadersLike;\n  /**\n   * Whether or not the body of the HttpOperationResponse should be treated as a stream.\n   * @deprecated Use streamResponseStatusCodes property instead.\n   */\n  streamResponseBody?: boolean;\n  /**\n   * A list of response status codes whose corresponding HttpOperationResponse body should be treated as a stream.\n   */\n  streamResponseStatusCodes?: Set<number>;\n  /**\n   * Whether or not the HttpOperationResponse should be deserialized. If this is undefined, then the\n   * HttpOperationResponse should be deserialized.\n   */\n  shouldDeserialize?: boolean | ((response: HttpOperationResponse) => boolean);\n  /**\n   * A function that returns the proper OperationResponse for the given OperationSpec and\n   * HttpOperationResponse combination. If this is undefined, then a simple status code lookup will\n   * be used.\n   */\n  operationResponseGetter?: (\n    operationSpec: OperationSpec,\n    response: HttpOperationResponse\n  ) => undefined | OperationResponse;\n  /**\n   * Form data, used to build the request body.\n   */\n  formData?: any;\n  /**\n   * A query string represented as an object.\n   */\n  query?: { [key: string]: any };\n  /**\n   * Used to parse the response.\n   */\n  operationSpec?: OperationSpec;\n  /**\n   * If credentials (cookies) should be sent along during an XHR.\n   */\n  withCredentials: boolean;\n  /**\n   * The number of milliseconds a request can take before automatically being terminated.\n   * If the request is terminated, an `AbortError` is thrown.\n   */\n  timeout: number;\n  /**\n   * Proxy configuration.\n   */\n  proxySettings?: ProxySettings;\n  /**\n   * If the connection should be reused.\n   */\n  keepAlive?: boolean;\n  /**\n   * Whether or not to decompress response according to Accept-Encoding header (node-fetch only)\n   */\n  decompressResponse?: boolean;\n  /**\n   * A unique identifier for the request. Used for logging and tracing.\n   */\n  requestId: string;\n\n  /**\n   * Signal of an abort controller. Can be used to abort both sending a network request and waiting for a response.\n   */\n  abortSignal?: AbortSignalLike;\n\n  /**\n   * Callback which fires upon upload progress.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /** Callback which fires upon download progress. */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Tracing: Context used when creating spans.\n   */\n  tracingContext?: Context;\n\n  /**\n   * Validates that the required properties such as method, url, headers[\"Content-Type\"],\n   * headers[\"accept-language\"] are defined. It will throw an error if one of the above\n   * mentioned properties are not defined.\n   */\n  validateRequestProperties(): void;\n\n  /**\n   * Sets options on the request.\n   */\n  prepare(options: RequestPrepareOptions): WebResourceLike;\n  /**\n   * Clone this request object.\n   */\n  clone(): WebResourceLike;\n}\n\nexport function isWebResourceLike(object: unknown): object is WebResourceLike {\n  if (object && typeof object === \"object\") {\n    const castObject = object as {\n      url: unknown;\n      method: unknown;\n      headers: unknown;\n      validateRequestProperties: unknown;\n      prepare: unknown;\n      clone: unknown;\n    };\n    if (\n      typeof castObject.url === \"string\" &&\n      typeof castObject.method === \"string\" &&\n      typeof castObject.headers === \"object\" &&\n      isHttpHeadersLike(castObject.headers) &&\n      typeof castObject.validateRequestProperties === \"function\" &&\n      typeof castObject.prepare === \"function\" &&\n      typeof castObject.clone === \"function\"\n    ) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * Creates a new WebResource object.\n *\n * This class provides an abstraction over a REST call by being library / implementation agnostic and wrapping the necessary\n * properties to initiate a request.\n */\nexport class WebResource implements WebResourceLike {\n  /**\n   * URL of the outgoing request.\n   */\n  url: string;\n  /**\n   * HTTP method to use.\n   */\n  method: HttpMethods;\n  /**\n   * Request body.\n   */\n  body?: any;\n  /**\n   * HTTP headers.\n   */\n  headers: HttpHeadersLike;\n  /**\n   * Whether or not the body of the HttpOperationResponse should be treated as a stream.\n   * @deprecated Use streamResponseStatusCodes property instead.\n   */\n  streamResponseBody?: boolean;\n  /**\n   * A list of status codes whose corresponding HttpOperationResponse body should be treated as a stream.\n   */\n  streamResponseStatusCodes?: Set<number>;\n  /**\n   * Whether or not the HttpOperationResponse should be deserialized. If this is undefined, then the\n   * HttpOperationResponse should be deserialized.\n   */\n  shouldDeserialize?: boolean | ((response: HttpOperationResponse) => boolean);\n  /**\n   * A function that returns the proper OperationResponse for the given OperationSpec and\n   * HttpOperationResponse combination. If this is undefined, then a simple status code lookup will\n   * be used.\n   */\n  operationResponseGetter?: (\n    operationSpec: OperationSpec,\n    response: HttpOperationResponse\n  ) => undefined | OperationResponse;\n  /**\n   * Form data, used to build the request body.\n   */\n  formData?: any;\n  /**\n   * Query added to the URL.\n   */\n  query?: { [key: string]: any };\n  /**\n   * Specification of the HTTP request.\n   */\n  operationSpec?: OperationSpec;\n  /**\n   * Whether to send credentials (via cookies, authorization headers, or TLS client certificates) when making a request in the browser to a cross-site destination.\n   */\n  withCredentials: boolean;\n  /**\n   * How long to wait in milliseconds before aborting the request.\n   */\n  timeout: number;\n  /**\n   * What proxy to use, if necessary.\n   */\n  proxySettings?: ProxySettings;\n  /**\n   * Whether to keep the HTTP connections alive throughout requests.\n   */\n  keepAlive?: boolean;\n  /**\n   * Whether or not to decompress response according to Accept-Encoding header (node-fetch only)\n   */\n  decompressResponse?: boolean;\n  /**\n   * Unique identifier of the outgoing request.\n   */\n  requestId: string;\n\n  /**\n   * Signal of an abort controller. Can be used to abort both sending a network request and waiting for a response.\n   */\n  abortSignal?: AbortSignalLike;\n\n  /**\n   * Callback which fires upon upload progress.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Callback which fires upon download progress.\n   */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Tracing: Options used to create a span when tracing is enabled.\n   */\n  spanOptions?: SpanOptions;\n\n  /**\n   * Tracing: Context used when creating Spans.\n   */\n  tracingContext?: Context;\n\n  constructor(\n    url?: string,\n    method?: HttpMethods,\n    body?: unknown,\n    query?: { [key: string]: any },\n    headers?: { [key: string]: any } | HttpHeadersLike,\n    streamResponseBody?: boolean,\n    withCredentials?: boolean,\n    abortSignal?: AbortSignalLike,\n    timeout?: number,\n    onUploadProgress?: (progress: TransferProgressEvent) => void,\n    onDownloadProgress?: (progress: TransferProgressEvent) => void,\n    proxySettings?: ProxySettings,\n    keepAlive?: boolean,\n    decompressResponse?: boolean,\n    streamResponseStatusCodes?: Set<number>\n  ) {\n    this.streamResponseBody = streamResponseBody;\n    this.streamResponseStatusCodes = streamResponseStatusCodes;\n    this.url = url || \"\";\n    this.method = method || \"GET\";\n    this.headers = isHttpHeadersLike(headers) ? headers : new HttpHeaders(headers);\n    this.body = body;\n    this.query = query;\n    this.formData = undefined;\n    this.withCredentials = withCredentials || false;\n    this.abortSignal = abortSignal;\n    this.timeout = timeout || 0;\n    this.onUploadProgress = onUploadProgress;\n    this.onDownloadProgress = onDownloadProgress;\n    this.proxySettings = proxySettings;\n    this.keepAlive = keepAlive;\n    this.decompressResponse = decompressResponse;\n    this.requestId = this.headers.get(\"x-ms-client-request-id\") || generateUuid();\n  }\n\n  /**\n   * Validates that the required properties such as method, url, headers[\"Content-Type\"],\n   * headers[\"accept-language\"] are defined. It will throw an error if one of the above\n   * mentioned properties are not defined.\n   */\n  validateRequestProperties(): void {\n    if (!this.method) {\n      throw new Error(\"WebResource.method is required.\");\n    }\n    if (!this.url) {\n      throw new Error(\"WebResource.url is required.\");\n    }\n  }\n\n  /**\n   * Prepares the request.\n   * @param options - Options to provide for preparing the request.\n   * @returns Returns the prepared WebResource (HTTP Request) object that needs to be given to the request pipeline.\n   */\n  prepare(options: RequestPrepareOptions): WebResource {\n    if (!options) {\n      throw new Error(\"options object is required\");\n    }\n\n    if (\n      options.method === undefined ||\n      options.method === null ||\n      typeof options.method.valueOf() !== \"string\"\n    ) {\n      throw new Error(\"options.method must be a string.\");\n    }\n\n    if (options.url && options.pathTemplate) {\n      throw new Error(\n        \"options.url and options.pathTemplate are mutually exclusive. Please provide exactly one of them.\"\n      );\n    }\n\n    if (\n      (options.pathTemplate === undefined ||\n        options.pathTemplate === null ||\n        typeof options.pathTemplate.valueOf() !== \"string\") &&\n      (options.url === undefined ||\n        options.url === null ||\n        typeof options.url.valueOf() !== \"string\")\n    ) {\n      throw new Error(\"Please provide exactly one of options.pathTemplate or options.url.\");\n    }\n\n    // set the url if it is provided.\n    if (options.url) {\n      if (typeof options.url !== \"string\") {\n        throw new Error('options.url must be of type \"string\".');\n      }\n      this.url = options.url;\n    }\n\n    // set the method\n    if (options.method) {\n      const validMethods = [\"GET\", \"PUT\", \"HEAD\", \"DELETE\", \"OPTIONS\", \"POST\", \"PATCH\", \"TRACE\"];\n      if (validMethods.indexOf(options.method.toUpperCase()) === -1) {\n        throw new Error(\n          'The provided method \"' +\n            options.method +\n            '\" is invalid. Supported HTTP methods are: ' +\n            JSON.stringify(validMethods)\n        );\n      }\n    }\n    this.method = options.method.toUpperCase() as HttpMethods;\n\n    // construct the url if path template is provided\n    if (options.pathTemplate) {\n      const { pathTemplate, pathParameters } = options;\n      if (typeof pathTemplate !== \"string\") {\n        throw new Error('options.pathTemplate must be of type \"string\".');\n      }\n      if (!options.baseUrl) {\n        options.baseUrl = \"https://management.azure.com\";\n      }\n      const baseUrl = options.baseUrl;\n      let url =\n        baseUrl +\n        (baseUrl.endsWith(\"/\") ? \"\" : \"/\") +\n        (pathTemplate.startsWith(\"/\") ? pathTemplate.slice(1) : pathTemplate);\n      const segments = url.match(/({[\\w-]*\\s*[\\w-]*})/gi);\n      if (segments && segments.length) {\n        if (!pathParameters) {\n          throw new Error(\n            `pathTemplate: ${pathTemplate} has been provided. Hence, options.pathParameters must also be provided.`\n          );\n        }\n        segments.forEach(function (item) {\n          const pathParamName = item.slice(1, -1);\n          const pathParam = (pathParameters as { [key: string]: any })[pathParamName];\n          if (\n            pathParam === null ||\n            pathParam === undefined ||\n            !(typeof pathParam === \"string\" || typeof pathParam === \"object\")\n          ) {\n            const stringifiedPathParameters = JSON.stringify(pathParameters, undefined, 2);\n            throw new Error(\n              `pathTemplate: ${pathTemplate} contains the path parameter ${pathParamName}` +\n                ` however, it is not present in parameters: ${stringifiedPathParameters}.` +\n                `The value of the path parameter can either be a \"string\" of the form { ${pathParamName}: \"some sample value\" } or ` +\n                `it can be an \"object\" of the form { \"${pathParamName}\": { value: \"some sample value\", skipUrlEncoding: true } }.`\n            );\n          }\n\n          if (typeof pathParam.valueOf() === \"string\") {\n            url = url.replace(item, encodeURIComponent(pathParam));\n          }\n\n          if (typeof pathParam.valueOf() === \"object\") {\n            if (!pathParam.value) {\n              throw new Error(\n                `options.pathParameters[${pathParamName}] is of type \"object\" but it does not contain a \"value\" property.`\n              );\n            }\n            if (pathParam.skipUrlEncoding) {\n              url = url.replace(item, pathParam.value);\n            } else {\n              url = url.replace(item, encodeURIComponent(pathParam.value));\n            }\n          }\n        });\n      }\n      this.url = url;\n    }\n\n    // append query parameters to the url if they are provided. They can be provided with pathTemplate or url option.\n    if (options.queryParameters) {\n      const queryParameters = options.queryParameters;\n      if (typeof queryParameters !== \"object\") {\n        throw new Error(\n          `options.queryParameters must be of type object. It should be a JSON object ` +\n            `of \"query-parameter-name\" as the key and the \"query-parameter-value\" as the value. ` +\n            `The \"query-parameter-value\" may be fo type \"string\" or an \"object\" of the form { value: \"query-parameter-value\", skipUrlEncoding: true }.`\n        );\n      }\n      // append question mark if it is not present in the url\n      if (this.url && this.url.indexOf(\"?\") === -1) {\n        this.url += \"?\";\n      }\n      // construct queryString\n      const queryParams = [];\n      // We need to populate this.query as a dictionary if the request is being used for Sway's validateRequest().\n      this.query = {};\n      for (const queryParamName in queryParameters) {\n        const queryParam: any = queryParameters[queryParamName];\n        if (queryParam) {\n          if (typeof queryParam === \"string\") {\n            queryParams.push(queryParamName + \"=\" + encodeURIComponent(queryParam));\n            this.query[queryParamName] = encodeURIComponent(queryParam);\n          } else if (typeof queryParam === \"object\") {\n            if (!queryParam.value) {\n              throw new Error(\n                `options.queryParameters[${queryParamName}] is of type \"object\" but it does not contain a \"value\" property.`\n              );\n            }\n            if (queryParam.skipUrlEncoding) {\n              queryParams.push(queryParamName + \"=\" + queryParam.value);\n              this.query[queryParamName] = queryParam.value;\n            } else {\n              queryParams.push(queryParamName + \"=\" + encodeURIComponent(queryParam.value));\n              this.query[queryParamName] = encodeURIComponent(queryParam.value);\n            }\n          }\n        }\n      } // end-of-for\n      // append the queryString\n      this.url += queryParams.join(\"&\");\n    }\n\n    // add headers to the request if they are provided\n    if (options.headers) {\n      const headers = options.headers;\n      for (const headerName of Object.keys(options.headers)) {\n        this.headers.set(headerName, headers[headerName]);\n      }\n    }\n    // ensure accept-language is set correctly\n    if (!this.headers.get(\"accept-language\")) {\n      this.headers.set(\"accept-language\", \"en-US\");\n    }\n    // ensure the request-id is set correctly\n    if (!this.headers.get(\"x-ms-client-request-id\") && !options.disableClientRequestId) {\n      this.headers.set(\"x-ms-client-request-id\", this.requestId);\n    }\n\n    // default\n    if (!this.headers.get(\"Content-Type\")) {\n      this.headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n    }\n\n    // set the request body. request.js automatically sets the Content-Length request header, so we need not set it explicitly\n    this.body = options.body;\n    if (options.body !== undefined && options.body !== null) {\n      // body as a stream special case. set the body as-is and check for some special request headers specific to sending a stream.\n      if (options.bodyIsStream) {\n        if (!this.headers.get(\"Transfer-Encoding\")) {\n          this.headers.set(\"Transfer-Encoding\", \"chunked\");\n        }\n        if (this.headers.get(\"Content-Type\") !== \"application/octet-stream\") {\n          this.headers.set(\"Content-Type\", \"application/octet-stream\");\n        }\n      } else {\n        if (options.serializationMapper) {\n          this.body = new Serializer(options.mappers).serialize(\n            options.serializationMapper,\n            options.body,\n            \"requestBody\"\n          );\n        }\n        if (!options.disableJsonStringifyOnBody) {\n          this.body = JSON.stringify(options.body);\n        }\n      }\n    }\n\n    if (options.spanOptions) {\n      this.spanOptions = options.spanOptions;\n    }\n\n    if (options.tracingContext) {\n      this.tracingContext = options.tracingContext;\n    }\n\n    this.abortSignal = options.abortSignal;\n    this.onDownloadProgress = options.onDownloadProgress;\n    this.onUploadProgress = options.onUploadProgress;\n\n    return this;\n  }\n\n  /**\n   * Clone this WebResource HTTP request object.\n   * @returns The clone of this WebResource HTTP request object.\n   */\n  clone(): WebResource {\n    const result = new WebResource(\n      this.url,\n      this.method,\n      this.body,\n      this.query,\n      this.headers && this.headers.clone(),\n      this.streamResponseBody,\n      this.withCredentials,\n      this.abortSignal,\n      this.timeout,\n      this.onUploadProgress,\n      this.onDownloadProgress,\n      this.proxySettings,\n      this.keepAlive,\n      this.decompressResponse,\n      this.streamResponseStatusCodes\n    );\n\n    if (this.formData) {\n      result.formData = this.formData;\n    }\n\n    if (this.operationSpec) {\n      result.operationSpec = this.operationSpec;\n    }\n\n    if (this.shouldDeserialize) {\n      result.shouldDeserialize = this.shouldDeserialize;\n    }\n\n    if (this.operationResponseGetter) {\n      result.operationResponseGetter = this.operationResponseGetter;\n    }\n\n    return result;\n  }\n}\n\n/**\n * Options to prepare an outgoing HTTP request.\n */\nexport interface RequestPrepareOptions {\n  /**\n   * The HTTP request method. Valid values are \"GET\", \"PUT\", \"HEAD\", \"DELETE\", \"OPTIONS\", \"POST\",\n   * or \"PATCH\".\n   */\n  method: HttpMethods;\n  /**\n   * The request url. It may or may not have query parameters in it. Either provide the \"url\" or\n   * provide the \"pathTemplate\" in the options object. Both the options are mutually exclusive.\n   */\n  url?: string;\n  /**\n   * A dictionary of query parameters to be appended to the url, where\n   * the \"key\" is the \"query-parameter-name\" and the \"value\" is the \"query-parameter-value\".\n   * The \"query-parameter-value\" can be of type \"string\" or it can be of type \"object\".\n   * The \"object\" format should be used when you want to skip url encoding. While using the object format,\n   * the object must have a property named value which provides the \"query-parameter-value\".\n   * Example:\n   *    - query-parameter-value in \"object\" format: `{ \"query-parameter-name\": { value: \"query-parameter-value\", skipUrlEncoding: true } }`\n   *    - query-parameter-value in \"string\" format: `{ \"query-parameter-name\": \"query-parameter-value\"}`.\n   * Note: \"If options.url already has some query parameters, then the value provided in options.queryParameters will be appended to the url.\n   */\n  queryParameters?: { [key: string]: any | ParameterValue };\n  /**\n   * The path template of the request url. Either provide the \"url\" or provide the \"pathTemplate\" in\n   * the options object. Both the options are mutually exclusive.\n   * Example: `/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Storage/storageAccounts/{accountName}`\n   */\n  pathTemplate?: string;\n  /**\n   * The base url of the request. Default value is: \"https://management.azure.com\". This is\n   * applicable only with pathTemplate. If you are providing options.url then it is expected that\n   * you provide the complete url.\n   */\n  baseUrl?: string;\n  /**\n   * A dictionary of path parameters that need to be replaced with actual values in the pathTemplate.\n   * Here the key is the \"path-parameter-name\" and the value is the \"path-parameter-value\".\n   * The \"path-parameter-value\" can be of type \"string\"  or it can be of type \"object\".\n   * The \"object\" format should be used when you want to skip url encoding. While using the object format,\n   * the object must have a property named value which provides the \"path-parameter-value\".\n   * Example:\n   *    - path-parameter-value in \"object\" format: `{ \"path-parameter-name\": { value: \"path-parameter-value\", skipUrlEncoding: true } }`\n   *    - path-parameter-value in \"string\" format: `{ \"path-parameter-name\": \"path-parameter-value\" }`.\n   */\n  pathParameters?: { [key: string]: any | ParameterValue };\n  /**\n   * Form data, used to build the request body.\n   */\n  formData?: { [key: string]: any };\n  /**\n   * A dictionary of request headers that need to be applied to the request.\n   * Here the key is the \"header-name\" and the value is the \"header-value\". The header-value MUST be of type string.\n   *  - ContentType must be provided with the key name as \"Content-Type\". Default value \"application/json; charset=utf-8\".\n   *  - \"Transfer-Encoding\" is set to \"chunked\" by default if \"options.bodyIsStream\" is set to true.\n   *  - \"Content-Type\" is set to \"application/octet-stream\" by default if \"options.bodyIsStream\" is set to true.\n   *  - \"accept-language\" by default is set to \"en-US\"\n   *  - \"x-ms-client-request-id\" by default is set to a new Guid. To not generate a guid for the request, please set options.disableClientRequestId to true\n   */\n  headers?: { [key: string]: any };\n  /**\n   * When set to true, instructs the client to not set \"x-ms-client-request-id\" header to a new Guid().\n   */\n  disableClientRequestId?: boolean;\n  /**\n   * The request body. It can be of any type. This value will be serialized if it is not a stream.\n   */\n  body?: any;\n  /**\n   * Provides information on how to serialize the request body.\n   */\n  serializationMapper?: Mapper;\n  /**\n   * A dictionary of mappers that may be used while [de]serialization.\n   */\n  mappers?: { [x: string]: any };\n  /**\n   * Provides information on how to deserialize the response body.\n   */\n  deserializationMapper?: Record<string, unknown>;\n  /**\n   * Indicates whether this method should JSON.stringify() the request body. Default value: false.\n   */\n  disableJsonStringifyOnBody?: boolean;\n  /**\n   * Indicates whether the request body is a stream (useful for file upload scenarios).\n   */\n  bodyIsStream?: boolean;\n  /**\n   * Signal of an abort controller. Can be used to abort both sending a network request and waiting for a response.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Allows keeping track of the progress of uploading the outgoing request.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n  /**\n   * Allows keeping track of the progress of downloading the incoming response.\n   */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n  /**\n   * Tracing: Options used to create a span when tracing is enabled.\n   */\n  spanOptions?: SpanOptions;\n  /**\n   * Tracing: Context used when creating spans.\n   */\n  tracingContext?: Context;\n}\n\n/**\n * The Parameter value provided for path or query parameters in RequestPrepareOptions\n */\nexport interface ParameterValue {\n  /**\n   * Value of the parameter.\n   */\n  value: any;\n  /**\n   * Disables URL encoding if set to true.\n   */\n  skipUrlEncoding: boolean;\n  /**\n   * Parameter values may contain any other property.\n   */\n  [key: string]: any;\n}\n\n/**\n * Describes the base structure of the options object that will be used in every operation.\n */\nexport interface RequestOptionsBase {\n  /**\n   * will be applied before the request is sent.\n   */\n  customHeaders?: { [key: string]: string };\n\n  /**\n   * Signal of an abort controller. Can be used to abort both sending a network request and waiting for a response.\n   */\n  abortSignal?: AbortSignalLike;\n\n  /**\n   * The number of milliseconds a request can take before automatically being terminated.\n   * If the request is terminated, an `AbortError` is thrown.\n   */\n  timeout?: number;\n\n  /**\n   * Callback which fires upon upload progress.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Callback which fires upon download progress.\n   */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Whether or not the HttpOperationResponse should be deserialized. If this is undefined, then the\n   * HttpOperationResponse should be deserialized.\n   */\n  shouldDeserialize?: boolean | ((response: HttpOperationResponse) => boolean);\n\n  /**\n   * Tracing: Context used when creating spans.\n   */\n  tracingContext?: Context;\n\n  /**\n   * May contain other properties.\n   */\n  [key: string]: any;\n\n  /**\n   * Options to override XML parsing/building behavior.\n   */\n  serializerOptions?: SerializerOptions;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { replaceAll } from \"./util/utils\";\n\ntype URLQueryParseState = \"ParameterName\" | \"ParameterValue\";\n\n/**\n * A class that handles the query portion of a URLBuilder.\n */\nexport class URLQuery {\n  private readonly _rawQuery: { [queryParameterName: string]: string | string[] } = {};\n\n  /**\n   * Get whether or not there any query parameters in this URLQuery.\n   */\n  public any(): boolean {\n    return Object.keys(this._rawQuery).length > 0;\n  }\n\n  /**\n   * Get the keys of the query string.\n   */\n  public keys(): string[] {\n    return Object.keys(this._rawQuery);\n  }\n\n  /**\n   * Set a query parameter with the provided name and value. If the parameterValue is undefined or\n   * empty, then this will attempt to remove an existing query parameter with the provided\n   * parameterName.\n   */\n  public set(parameterName: string, parameterValue: unknown): void {\n    const caseParameterValue = parameterValue as {\n      toString: () => string;\n    };\n    if (parameterName) {\n      if (caseParameterValue !== undefined && caseParameterValue !== null) {\n        const newValue = Array.isArray(caseParameterValue)\n          ? caseParameterValue\n          : caseParameterValue.toString();\n        this._rawQuery[parameterName] = newValue;\n      } else {\n        delete this._rawQuery[parameterName];\n      }\n    }\n  }\n\n  /**\n   * Get the value of the query parameter with the provided name. If no parameter exists with the\n   * provided parameter name, then undefined will be returned.\n   */\n  public get(parameterName: string): string | string[] | undefined {\n    return parameterName ? this._rawQuery[parameterName] : undefined;\n  }\n\n  /**\n   * Get the string representation of this query. The return value will not start with a \"?\".\n   */\n  public toString(): string {\n    let result = \"\";\n    for (const parameterName in this._rawQuery) {\n      if (result) {\n        result += \"&\";\n      }\n      const parameterValue = this._rawQuery[parameterName];\n      if (Array.isArray(parameterValue)) {\n        const parameterStrings = [];\n        for (const parameterValueElement of parameterValue) {\n          parameterStrings.push(`${parameterName}=${parameterValueElement}`);\n        }\n        result += parameterStrings.join(\"&\");\n      } else {\n        result += `${parameterName}=${parameterValue}`;\n      }\n    }\n    return result;\n  }\n\n  /**\n   * Parse a URLQuery from the provided text.\n   */\n  public static parse(text: string): URLQuery {\n    const result = new URLQuery();\n\n    if (text) {\n      if (text.startsWith(\"?\")) {\n        text = text.substring(1);\n      }\n\n      let currentState: URLQueryParseState = \"ParameterName\";\n\n      let parameterName = \"\";\n      let parameterValue = \"\";\n      for (let i = 0; i < text.length; ++i) {\n        const currentCharacter: string = text[i];\n        switch (currentState) {\n          case \"ParameterName\":\n            switch (currentCharacter) {\n              case \"=\":\n                currentState = \"ParameterValue\";\n                break;\n\n              case \"&\":\n                parameterName = \"\";\n                parameterValue = \"\";\n                break;\n\n              default:\n                parameterName += currentCharacter;\n                break;\n            }\n            break;\n\n          case \"ParameterValue\":\n            switch (currentCharacter) {\n              case \"&\":\n                result.set(parameterName, parameterValue);\n                parameterName = \"\";\n                parameterValue = \"\";\n                currentState = \"ParameterName\";\n                break;\n\n              default:\n                parameterValue += currentCharacter;\n                break;\n            }\n            break;\n\n          default:\n            throw new Error(\"Unrecognized URLQuery parse state: \" + currentState);\n        }\n      }\n      if (currentState === \"ParameterValue\") {\n        result.set(parameterName, parameterValue);\n      }\n    }\n\n    return result;\n  }\n}\n\n/**\n * A class that handles creating, modifying, and parsing URLs.\n */\nexport class URLBuilder {\n  private _scheme: string | undefined;\n  private _host: string | undefined;\n  private _port: string | undefined;\n  private _path: string | undefined;\n  private _query: URLQuery | undefined;\n\n  /**\n   * Set the scheme/protocol for this URL. If the provided scheme contains other parts of a URL\n   * (such as a host, port, path, or query), those parts will be added to this URL as well.\n   */\n  public setScheme(scheme: string | undefined): void {\n    if (!scheme) {\n      this._scheme = undefined;\n    } else {\n      this.set(scheme, \"SCHEME\");\n    }\n  }\n\n  /**\n   * Get the scheme that has been set in this URL.\n   */\n  public getScheme(): string | undefined {\n    return this._scheme;\n  }\n\n  /**\n   * Set the host for this URL. If the provided host contains other parts of a URL (such as a\n   * port, path, or query), those parts will be added to this URL as well.\n   */\n  public setHost(host: string | undefined): void {\n    if (!host) {\n      this._host = undefined;\n    } else {\n      this.set(host, \"SCHEME_OR_HOST\");\n    }\n  }\n\n  /**\n   * Get the host that has been set in this URL.\n   */\n  public getHost(): string | undefined {\n    return this._host;\n  }\n\n  /**\n   * Set the port for this URL. If the provided port contains other parts of a URL (such as a\n   * path or query), those parts will be added to this URL as well.\n   */\n  public setPort(port: number | string | undefined): void {\n    if (port === undefined || port === null || port === \"\") {\n      this._port = undefined;\n    } else {\n      this.set(port.toString(), \"PORT\");\n    }\n  }\n\n  /**\n   * Get the port that has been set in this URL.\n   */\n  public getPort(): string | undefined {\n    return this._port;\n  }\n\n  /**\n   * Set the path for this URL. If the provided path contains a query, then it will be added to\n   * this URL as well.\n   */\n  public setPath(path: string | undefined): void {\n    if (!path) {\n      this._path = undefined;\n    } else {\n      const schemeIndex = path.indexOf(\"://\");\n      if (schemeIndex !== -1) {\n        const schemeStart = path.lastIndexOf(\"/\", schemeIndex);\n        // Make sure to only grab the URL part of the path before setting the state back to SCHEME\n        // this will handle cases such as \"/a/b/c/https://microsoft.com\" => \"https://microsoft.com\"\n        this.set(schemeStart === -1 ? path : path.substr(schemeStart + 1), \"SCHEME\");\n      } else {\n        this.set(path, \"PATH\");\n      }\n    }\n  }\n\n  /**\n   * Append the provided path to this URL's existing path. If the provided path contains a query,\n   * then it will be added to this URL as well.\n   */\n  public appendPath(path: string | undefined): void {\n    if (path) {\n      let currentPath: string | undefined = this.getPath();\n      if (currentPath) {\n        if (!currentPath.endsWith(\"/\")) {\n          currentPath += \"/\";\n        }\n\n        if (path.startsWith(\"/\")) {\n          path = path.substring(1);\n        }\n\n        path = currentPath + path;\n      }\n      this.set(path, \"PATH\");\n    }\n  }\n\n  /**\n   * Get the path that has been set in this URL.\n   */\n  public getPath(): string | undefined {\n    return this._path;\n  }\n\n  /**\n   * Set the query in this URL.\n   */\n  public setQuery(query: string | undefined): void {\n    if (!query) {\n      this._query = undefined;\n    } else {\n      this._query = URLQuery.parse(query);\n    }\n  }\n\n  /**\n   * Set a query parameter with the provided name and value in this URL's query. If the provided\n   * query parameter value is undefined or empty, then the query parameter will be removed if it\n   * existed.\n   */\n  public setQueryParameter(queryParameterName: string, queryParameterValue: unknown): void {\n    if (queryParameterName) {\n      if (!this._query) {\n        this._query = new URLQuery();\n      }\n      this._query.set(queryParameterName, queryParameterValue);\n    }\n  }\n\n  /**\n   * Get the value of the query parameter with the provided query parameter name. If no query\n   * parameter exists with the provided name, then undefined will be returned.\n   */\n  public getQueryParameterValue(queryParameterName: string): string | string[] | undefined {\n    return this._query ? this._query.get(queryParameterName) : undefined;\n  }\n\n  /**\n   * Get the query in this URL.\n   */\n  public getQuery(): string | undefined {\n    return this._query ? this._query.toString() : undefined;\n  }\n\n  /**\n   * Set the parts of this URL by parsing the provided text using the provided startState.\n   */\n  private set(text: string, startState: URLTokenizerState): void {\n    const tokenizer = new URLTokenizer(text, startState);\n\n    while (tokenizer.next()) {\n      const token: URLToken | undefined = tokenizer.current();\n      let tokenPath: string | undefined;\n      if (token) {\n        switch (token.type) {\n          case \"SCHEME\":\n            this._scheme = token.text || undefined;\n            break;\n\n          case \"HOST\":\n            this._host = token.text || undefined;\n            break;\n\n          case \"PORT\":\n            this._port = token.text || undefined;\n            break;\n\n          case \"PATH\":\n            tokenPath = token.text || undefined;\n            if (!this._path || this._path === \"/\" || tokenPath !== \"/\") {\n              this._path = tokenPath;\n            }\n            break;\n\n          case \"QUERY\":\n            this._query = URLQuery.parse(token.text);\n            break;\n\n          default:\n            throw new Error(`Unrecognized URLTokenType: ${token.type}`);\n        }\n      }\n    }\n  }\n\n  /**\n   * Serializes the URL as a string.\n   * @returns the URL as a string.\n   */\n  public toString(): string {\n    let result = \"\";\n\n    if (this._scheme) {\n      result += `${this._scheme}://`;\n    }\n\n    if (this._host) {\n      result += this._host;\n    }\n\n    if (this._port) {\n      result += `:${this._port}`;\n    }\n\n    if (this._path) {\n      if (!this._path.startsWith(\"/\")) {\n        result += \"/\";\n      }\n      result += this._path;\n    }\n\n    if (this._query && this._query.any()) {\n      result += `?${this._query.toString()}`;\n    }\n\n    return result;\n  }\n\n  /**\n   * If the provided searchValue is found in this URLBuilder, then replace it with the provided\n   * replaceValue.\n   */\n  public replaceAll(searchValue: string, replaceValue: string): void {\n    if (searchValue) {\n      this.setScheme(replaceAll(this.getScheme(), searchValue, replaceValue));\n      this.setHost(replaceAll(this.getHost(), searchValue, replaceValue));\n      this.setPort(replaceAll(this.getPort(), searchValue, replaceValue));\n      this.setPath(replaceAll(this.getPath(), searchValue, replaceValue));\n      this.setQuery(replaceAll(this.getQuery(), searchValue, replaceValue));\n    }\n  }\n\n  /**\n   * Parses a given string URL into a new {@link URLBuilder}.\n   */\n  public static parse(text: string): URLBuilder {\n    const result = new URLBuilder();\n    result.set(text, \"SCHEME_OR_HOST\");\n    return result;\n  }\n}\n\ntype URLTokenizerState = \"SCHEME\" | \"SCHEME_OR_HOST\" | \"HOST\" | \"PORT\" | \"PATH\" | \"QUERY\" | \"DONE\";\n\ntype URLTokenType = \"SCHEME\" | \"HOST\" | \"PORT\" | \"PATH\" | \"QUERY\";\n\nexport class URLToken {\n  public constructor(public readonly text: string, public readonly type: URLTokenType) {}\n\n  public static scheme(text: string): URLToken {\n    return new URLToken(text, \"SCHEME\");\n  }\n\n  public static host(text: string): URLToken {\n    return new URLToken(text, \"HOST\");\n  }\n\n  public static port(text: string): URLToken {\n    return new URLToken(text, \"PORT\");\n  }\n\n  public static path(text: string): URLToken {\n    return new URLToken(text, \"PATH\");\n  }\n\n  public static query(text: string): URLToken {\n    return new URLToken(text, \"QUERY\");\n  }\n}\n\n/**\n * Get whether or not the provided character (single character string) is an alphanumeric (letter or\n * digit) character.\n */\nexport function isAlphaNumericCharacter(character: string): boolean {\n  const characterCode: number = character.charCodeAt(0);\n  return (\n    (48 /* '0' */ <= characterCode && characterCode <= 57) /* '9' */ ||\n    (65 /* 'A' */ <= characterCode && characterCode <= 90) /* 'Z' */ ||\n    (97 /* 'a' */ <= characterCode && characterCode <= 122) /* 'z' */\n  );\n}\n\n/**\n * A class that tokenizes URL strings.\n */\nexport class URLTokenizer {\n  readonly _textLength: number;\n  _currentState: URLTokenizerState;\n  _currentIndex: number;\n  _currentToken: URLToken | undefined;\n\n  public constructor(readonly _text: string, state?: URLTokenizerState) {\n    this._textLength = _text ? _text.length : 0;\n    this._currentState = state !== undefined && state !== null ? state : \"SCHEME_OR_HOST\";\n    this._currentIndex = 0;\n  }\n\n  /**\n   * Get the current URLToken this URLTokenizer is pointing at, or undefined if the URLTokenizer\n   * hasn't started or has finished tokenizing.\n   */\n  public current(): URLToken | undefined {\n    return this._currentToken;\n  }\n\n  /**\n   * Advance to the next URLToken and return whether or not a URLToken was found.\n   */\n  public next(): boolean {\n    if (!hasCurrentCharacter(this)) {\n      this._currentToken = undefined;\n    } else {\n      switch (this._currentState) {\n        case \"SCHEME\":\n          nextScheme(this);\n          break;\n\n        case \"SCHEME_OR_HOST\":\n          nextSchemeOrHost(this);\n          break;\n\n        case \"HOST\":\n          nextHost(this);\n          break;\n\n        case \"PORT\":\n          nextPort(this);\n          break;\n\n        case \"PATH\":\n          nextPath(this);\n          break;\n\n        case \"QUERY\":\n          nextQuery(this);\n          break;\n\n        default:\n          throw new Error(`Unrecognized URLTokenizerState: ${this._currentState}`);\n      }\n    }\n    return !!this._currentToken;\n  }\n}\n\n/**\n * Read the remaining characters from this Tokenizer's character stream.\n */\nfunction readRemaining(tokenizer: URLTokenizer): string {\n  let result = \"\";\n  if (tokenizer._currentIndex < tokenizer._textLength) {\n    result = tokenizer._text.substring(tokenizer._currentIndex);\n    tokenizer._currentIndex = tokenizer._textLength;\n  }\n  return result;\n}\n\n/**\n * Whether or not this URLTokenizer has a current character.\n */\nfunction hasCurrentCharacter(tokenizer: URLTokenizer): boolean {\n  return tokenizer._currentIndex < tokenizer._textLength;\n}\n\n/**\n * Get the character in the text string at the current index.\n */\nfunction getCurrentCharacter(tokenizer: URLTokenizer): string {\n  return tokenizer._text[tokenizer._currentIndex];\n}\n\n/**\n * Advance to the character in text that is \"step\" characters ahead. If no step value is provided,\n * then step will default to 1.\n */\nfunction nextCharacter(tokenizer: URLTokenizer, step?: number): void {\n  if (hasCurrentCharacter(tokenizer)) {\n    if (!step) {\n      step = 1;\n    }\n    tokenizer._currentIndex += step;\n  }\n}\n\n/**\n * Starting with the current character, peek \"charactersToPeek\" number of characters ahead in this\n * Tokenizer's stream of characters.\n */\nfunction peekCharacters(tokenizer: URLTokenizer, charactersToPeek: number): string {\n  let endIndex: number = tokenizer._currentIndex + charactersToPeek;\n  if (tokenizer._textLength < endIndex) {\n    endIndex = tokenizer._textLength;\n  }\n  return tokenizer._text.substring(tokenizer._currentIndex, endIndex);\n}\n\n/**\n * Read characters from this Tokenizer until the end of the stream or until the provided condition\n * is false when provided the current character.\n */\nfunction readWhile(tokenizer: URLTokenizer, condition: (character: string) => boolean): string {\n  let result = \"\";\n\n  while (hasCurrentCharacter(tokenizer)) {\n    const currentCharacter: string = getCurrentCharacter(tokenizer);\n    if (!condition(currentCharacter)) {\n      break;\n    } else {\n      result += currentCharacter;\n      nextCharacter(tokenizer);\n    }\n  }\n\n  return result;\n}\n\n/**\n * Read characters from this Tokenizer until a non-alphanumeric character or the end of the\n * character stream is reached.\n */\nfunction readWhileLetterOrDigit(tokenizer: URLTokenizer): string {\n  return readWhile(tokenizer, (character: string) => isAlphaNumericCharacter(character));\n}\n\n/**\n * Read characters from this Tokenizer until one of the provided terminating characters is read or\n * the end of the character stream is reached.\n */\nfunction readUntilCharacter(tokenizer: URLTokenizer, ...terminatingCharacters: string[]): string {\n  return readWhile(\n    tokenizer,\n    (character: string) => terminatingCharacters.indexOf(character) === -1\n  );\n}\n\nfunction nextScheme(tokenizer: URLTokenizer): void {\n  const scheme: string = readWhileLetterOrDigit(tokenizer);\n  tokenizer._currentToken = URLToken.scheme(scheme);\n  if (!hasCurrentCharacter(tokenizer)) {\n    tokenizer._currentState = \"DONE\";\n  } else {\n    tokenizer._currentState = \"HOST\";\n  }\n}\n\nfunction nextSchemeOrHost(tokenizer: URLTokenizer): void {\n  const schemeOrHost: string = readUntilCharacter(tokenizer, \":\", \"/\", \"?\");\n  if (!hasCurrentCharacter(tokenizer)) {\n    tokenizer._currentToken = URLToken.host(schemeOrHost);\n    tokenizer._currentState = \"DONE\";\n  } else if (getCurrentCharacter(tokenizer) === \":\") {\n    if (peekCharacters(tokenizer, 3) === \"://\") {\n      tokenizer._currentToken = URLToken.scheme(schemeOrHost);\n      tokenizer._currentState = \"HOST\";\n    } else {\n      tokenizer._currentToken = URLToken.host(schemeOrHost);\n      tokenizer._currentState = \"PORT\";\n    }\n  } else {\n    tokenizer._currentToken = URLToken.host(schemeOrHost);\n    if (getCurrentCharacter(tokenizer) === \"/\") {\n      tokenizer._currentState = \"PATH\";\n    } else {\n      tokenizer._currentState = \"QUERY\";\n    }\n  }\n}\n\nfunction nextHost(tokenizer: URLTokenizer): void {\n  if (peekCharacters(tokenizer, 3) === \"://\") {\n    nextCharacter(tokenizer, 3);\n  }\n\n  const host: string = readUntilCharacter(tokenizer, \":\", \"/\", \"?\");\n  tokenizer._currentToken = URLToken.host(host);\n\n  if (!hasCurrentCharacter(tokenizer)) {\n    tokenizer._currentState = \"DONE\";\n  } else if (getCurrentCharacter(tokenizer) === \":\") {\n    tokenizer._currentState = \"PORT\";\n  } else if (getCurrentCharacter(tokenizer) === \"/\") {\n    tokenizer._currentState = \"PATH\";\n  } else {\n    tokenizer._currentState = \"QUERY\";\n  }\n}\n\nfunction nextPort(tokenizer: URLTokenizer): void {\n  if (getCurrentCharacter(tokenizer) === \":\") {\n    nextCharacter(tokenizer);\n  }\n\n  const port: string = readUntilCharacter(tokenizer, \"/\", \"?\");\n  tokenizer._currentToken = URLToken.port(port);\n\n  if (!hasCurrentCharacter(tokenizer)) {\n    tokenizer._currentState = \"DONE\";\n  } else if (getCurrentCharacter(tokenizer) === \"/\") {\n    tokenizer._currentState = \"PATH\";\n  } else {\n    tokenizer._currentState = \"QUERY\";\n  }\n}\n\nfunction nextPath(tokenizer: URLTokenizer): void {\n  const path: string = readUntilCharacter(tokenizer, \"?\");\n  tokenizer._currentToken = URLToken.path(path);\n\n  if (!hasCurrentCharacter(tokenizer)) {\n    tokenizer._currentState = \"DONE\";\n  } else {\n    tokenizer._currentState = \"QUERY\";\n  }\n}\n\nfunction nextQuery(tokenizer: URLTokenizer): void {\n  if (getCurrentCharacter(tokenizer) === \"?\") {\n    nextCharacter(tokenizer);\n  }\n\n  const query: string = readRemaining(tokenizer);\n  tokenizer._currentToken = URLToken.query(query);\n  tokenizer._currentState = \"DONE\";\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as http from \"http\";\nimport * as https from \"https\";\nimport * as tunnel from \"tunnel\";\nimport { HttpHeadersLike } from \"./httpHeaders\";\nimport { ProxySettings } from \"./serviceClient\";\nimport { URLBuilder } from \"./url\";\n\nexport type ProxyAgent = { isHttps: boolean; agent: http.Agent | https.Agent };\nexport function createProxyAgent(\n  requestUrl: string,\n  proxySettings: ProxySettings,\n  headers?: HttpHeadersLike\n): ProxyAgent {\n  const host = URLBuilder.parse(proxySettings.host).getHost() as string;\n  if (!host) {\n    throw new Error(\"Expecting a non-empty host in proxy settings.\");\n  }\n  if (!isValidPort(proxySettings.port)) {\n    throw new Error(\"Expecting a valid port number in the range of [0, 65535] in proxy settings.\");\n  }\n  const tunnelOptions: tunnel.HttpsOverHttpsOptions = {\n    proxy: {\n      host: host,\n      port: proxySettings.port,\n      headers: (headers && headers.rawHeaders()) || {},\n    },\n  };\n\n  if (proxySettings.username && proxySettings.password) {\n    tunnelOptions.proxy!.proxyAuth = `${proxySettings.username}:${proxySettings.password}`;\n  } else if (proxySettings.username) {\n    tunnelOptions.proxy!.proxyAuth = `${proxySettings.username}`;\n  }\n\n  const isRequestHttps = isUrlHttps(requestUrl);\n  const isProxyHttps = isUrlHttps(proxySettings.host);\n\n  const proxyAgent = {\n    isHttps: isRequestHttps,\n    agent: createTunnel(isRequestHttps, isProxyHttps, tunnelOptions),\n  };\n\n  return proxyAgent;\n}\n\nexport function isUrlHttps(url: string): boolean {\n  const urlScheme = URLBuilder.parse(url).getScheme() || \"\";\n  return urlScheme.toLowerCase() === \"https\";\n}\n\nexport function createTunnel(\n  isRequestHttps: boolean,\n  isProxyHttps: boolean,\n  tunnelOptions: tunnel.HttpsOverHttpsOptions\n): http.Agent | https.Agent {\n  if (isRequestHttps && isProxyHttps) {\n    return tunnel.httpsOverHttps(tunnelOptions);\n  } else if (isRequestHttps && !isProxyHttps) {\n    return tunnel.httpsOverHttp(tunnelOptions);\n  } else if (!isRequestHttps && isProxyHttps) {\n    return tunnel.httpOverHttps(tunnelOptions);\n  } else {\n    return tunnel.httpOverHttp(tunnelOptions);\n  }\n}\n\nfunction isValidPort(port: number): boolean {\n  // any port in 0-65535 range is valid (RFC 793) even though almost all implementations\n  // will reserve 0 for a specific purpose, and a range of numbers for ephemeral ports\n  return 0 <= port && port <= 65535;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { U<PERSON><PERSON><PERSON><PERSON>, URLQuery } from \"../url\";\nimport { UnknownObject, isObject } from \"./utils\";\n\nexport interface SanitizerOptions {\n  /**\n   * Header names whose values will be logged when logging is enabled. Defaults to\n   * Date, traceparent, x-ms-client-request-id, and x-ms-request id.  Any headers\n   * specified in this field will be added to that list.  Any other values will\n   * be written to logs as \"REDACTED\".\n   */\n  allowedHeaderNames?: string[];\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   */\n  allowedQueryParameters?: string[];\n}\n\nconst RedactedString = \"REDACTED\";\n\nconst defaultAllowedHeaderNames = [\n  \"x-ms-client-request-id\",\n  \"x-ms-return-client-request-id\",\n  \"x-ms-useragent\",\n  \"x-ms-correlation-request-id\",\n  \"x-ms-request-id\",\n  \"client-request-id\",\n  \"ms-cv\",\n  \"return-client-request-id\",\n  \"traceparent\",\n\n  \"Access-Control-Allow-Credentials\",\n  \"Access-Control-Allow-Headers\",\n  \"Access-Control-Allow-Methods\",\n  \"Access-Control-Allow-Origin\",\n  \"Access-Control-Expose-Headers\",\n  \"Access-Control-Max-Age\",\n  \"Access-Control-Request-Headers\",\n  \"Access-Control-Request-Method\",\n  \"Origin\",\n\n  \"Accept\",\n  \"Accept-Encoding\",\n  \"Cache-Control\",\n  \"Connection\",\n  \"Content-Length\",\n  \"Content-Type\",\n  \"Date\",\n  \"ETag\",\n  \"Expires\",\n  \"If-Match\",\n  \"If-Modified-Since\",\n  \"If-None-Match\",\n  \"If-Unmodified-Since\",\n  \"Last-Modified\",\n  \"Pragma\",\n  \"Request-Id\",\n  \"Retry-After\",\n  \"Server\",\n  \"Transfer-Encoding\",\n  \"User-Agent\",\n  \"WWW-Authenticate\",\n];\n\nconst defaultAllowedQueryParameters: string[] = [\"api-version\"];\n\nexport class Sanitizer {\n  public allowedHeaderNames: Set<string>;\n  public allowedQueryParameters: Set<string>;\n\n  constructor({ allowedHeaderNames = [], allowedQueryParameters = [] }: SanitizerOptions = {}) {\n    allowedHeaderNames = Array.isArray(allowedHeaderNames)\n      ? defaultAllowedHeaderNames.concat(allowedHeaderNames)\n      : defaultAllowedHeaderNames;\n\n    allowedQueryParameters = Array.isArray(allowedQueryParameters)\n      ? defaultAllowedQueryParameters.concat(allowedQueryParameters)\n      : defaultAllowedQueryParameters;\n\n    this.allowedHeaderNames = new Set(allowedHeaderNames.map((n) => n.toLowerCase()));\n    this.allowedQueryParameters = new Set(allowedQueryParameters.map((p) => p.toLowerCase()));\n  }\n\n  public sanitize(obj: unknown): string {\n    const seen = new Set<unknown>();\n    return JSON.stringify(\n      obj,\n      (key: string, value: unknown) => {\n        // Ensure Errors include their interesting non-enumerable members\n        if (value instanceof Error) {\n          return {\n            ...value,\n            name: value.name,\n            message: value.message,\n          };\n        }\n\n        if (key === \"_headersMap\") {\n          return this.sanitizeHeaders(value as UnknownObject);\n        } else if (key === \"url\") {\n          return this.sanitizeUrl(value as string);\n        } else if (key === \"query\") {\n          return this.sanitizeQuery(value as UnknownObject);\n        } else if (key === \"body\") {\n          // Don't log the request body\n          return undefined;\n        } else if (key === \"response\") {\n          // Don't log response again\n          return undefined;\n        } else if (key === \"operationSpec\") {\n          // When using sendOperationRequest, the request carries a massive\n          // field with the autorest spec. No need to log it.\n          return undefined;\n        } else if (Array.isArray(value) || isObject(value)) {\n          if (seen.has(value)) {\n            return \"[Circular]\";\n          }\n          seen.add(value);\n        }\n\n        return value;\n      },\n      2\n    );\n  }\n\n  private sanitizeHeaders(value: UnknownObject): UnknownObject {\n    return this.sanitizeObject(value, this.allowedHeaderNames, (v, k) => v[k].value);\n  }\n\n  private sanitizeQuery(value: UnknownObject): UnknownObject {\n    return this.sanitizeObject(value, this.allowedQueryParameters, (v, k) => v[k]);\n  }\n\n  private sanitizeObject(\n    value: UnknownObject,\n    allowedKeys: Set<string>,\n    accessor: (value: any, key: string) => any\n  ): UnknownObject {\n    if (typeof value !== \"object\" || value === null) {\n      return value;\n    }\n\n    const sanitized: UnknownObject = {};\n\n    for (const k of Object.keys(value)) {\n      if (allowedKeys.has(k.toLowerCase())) {\n        sanitized[k] = accessor(value, k);\n      } else {\n        sanitized[k] = RedactedString;\n      }\n    }\n\n    return sanitized;\n  }\n\n  private sanitizeUrl(value: string): string {\n    if (typeof value !== \"string\" || value === null) {\n      return value;\n    }\n\n    const urlBuilder = URLBuilder.parse(value);\n    const queryString = urlBuilder.getQuery();\n\n    if (!queryString) {\n      return value;\n    }\n\n    const query = URLQuery.parse(queryString);\n    for (const k of query.keys()) {\n      if (!this.allowedQueryParameters.has(k.toLowerCase())) {\n        query.set(k, RedactedString);\n      }\n    }\n\n    urlBuilder.setQuery(query.toString());\n    return urlBuilder.toString();\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { inspect } from \"util\";\n\nexport const custom = inspect.custom;\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpOperationResponse } from \"./httpOperationResponse\";\nimport { Sanitizer } from \"./util/sanitizer\";\nimport { WebResourceLike } from \"./webResource\";\nimport { custom } from \"./util/inspect\";\n\nconst errorSanitizer = new Sanitizer();\n\n/**\n * An error resulting from an HTTP request to a service endpoint.\n */\nexport class RestError extends Error {\n  /**\n   * A constant string to identify errors that may arise when making an HTTP request that indicates an issue with the transport layer (e.g. the hostname of the URL cannot be resolved via DNS.)\n   */\n  static readonly REQUEST_SEND_ERROR: string = \"REQUEST_SEND_ERROR\";\n  /**\n   * A constant string to identify errors that may arise from parsing an incoming HTTP response. Usually indicates a malformed HTTP body, such as an encoded JSON payload that is incomplete.\n   */\n  static readonly PARSE_ERROR: string = \"PARSE_ERROR\";\n\n  /**\n   * The error code, if any. Can be one of the static error code properties (REQUEST_SEND_ERROR / PARSE_ERROR) or can be a string code from an underlying system call (E_NOENT).\n   */\n  code?: string;\n  /**\n   * The HTTP status code of the response, if one was returned.\n   */\n  statusCode?: number;\n  /**\n   * Outgoing request.\n   */\n  request?: WebResourceLike;\n  /**\n   * Incoming response.\n   */\n  response?: HttpOperationResponse;\n  /**\n   * Any additional details. In the case of deserialization errors, can be the processed response.\n   */\n  details?: unknown;\n  constructor(\n    message: string,\n    code?: string,\n    statusCode?: number,\n    request?: WebResourceLike,\n    response?: HttpOperationResponse\n  ) {\n    super(message);\n    this.name = \"RestError\";\n    this.code = code;\n    this.statusCode = statusCode;\n    this.request = request;\n    this.response = response;\n\n    Object.setPrototypeOf(this, RestError.prototype);\n  }\n\n  /**\n   * Logging method for util.inspect in Node\n   */\n  [custom](): string {\n    return `RestError: ${this.message} \\n ${errorSanitizer.sanitize(this)}`;\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\nimport { createClientLogger } from \"@azure/logger\";\nexport const logger = createClientLogger(\"core-http\");\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as http from \"http\";\nimport * as https from \"https\";\nimport * as tough from \"tough-cookie\";\nimport { AbortController, AbortError } from \"@azure/abort-controller\";\nimport { HttpHeaders, HttpHeadersLike } from \"./httpHeaders\";\nimport { ProxyAgent, createProxyAgent, isUrlHttps } from \"./proxyAgent\";\nimport { Readable, Transform } from \"stream\";\nimport { TransferProgressEvent, WebResourceLike } from \"./webResource\";\nimport FormData from \"form-data\";\nimport { HttpClient } from \"./httpClient\";\nimport { HttpOperationResponse } from \"./httpOperationResponse\";\nimport { RestError } from \"./restError\";\nimport { logger } from \"./log\";\nimport node_fetch from \"node-fetch\";\n\ninterface AgentCache {\n  httpAgent?: http.Agent;\n  httpsAgent?: https.Agent;\n}\n\nfunction getCachedAgent(\n  isHttps: boolean,\n  agentCache: AgentCache\n): http.Agent | https.Agent | undefined {\n  return isHttps ? agentCache.httpsAgent : agentCache.httpAgent;\n}\n\ninterface FetchError extends Error {\n  code?: string;\n  errno?: string;\n  type?: string;\n}\n\n/**\n * String URLs used when calling to `fetch()`.\n */\nexport type CommonRequestInfo = string;\n\n/**\n * An object containing information about the outgoing HTTP request.\n */\nexport type CommonRequestInit = Omit<RequestInit, \"body\" | \"headers\" | \"signal\"> & {\n  body?: any;\n  headers?: any;\n  signal?: any;\n};\n\n/**\n * An object containing information about the incoming HTTP response.\n */\nexport type CommonResponse = Omit<Response, \"body\" | \"trailer\" | \"formData\"> & {\n  body: any;\n  trailer: any;\n  formData: any;\n};\n\nexport class ReportTransform extends Transform {\n  private loadedBytes: number = 0;\n  _transform(chunk: string | Buffer, _encoding: string, callback: (arg: any) => void): void {\n    this.push(chunk);\n    this.loadedBytes += chunk.length;\n    this.progressCallback!({ loadedBytes: this.loadedBytes });\n    callback(undefined);\n  }\n\n  constructor(private progressCallback: (progress: TransferProgressEvent) => void) {\n    super();\n  }\n}\n\nfunction isReadableStream(body: any): body is Readable {\n  return body && typeof body.pipe === \"function\";\n}\n\nfunction isStreamComplete(stream: Readable, aborter?: AbortController): Promise<void> {\n  return new Promise((resolve) => {\n    stream.once(\"close\", () => {\n      aborter?.abort();\n      resolve();\n    });\n    stream.once(\"end\", resolve);\n    stream.once(\"error\", resolve);\n  });\n}\n\n/**\n * Transforms a set of headers into the key/value pair defined by {@link HttpHeadersLike}\n */\nexport function parseHeaders(headers: Headers): HttpHeadersLike {\n  const httpHeaders = new HttpHeaders();\n\n  headers.forEach((value, key) => {\n    httpHeaders.set(key, value);\n  });\n\n  return httpHeaders;\n}\n\n/**\n * An HTTP client that uses `node-fetch`.\n */\nexport class NodeFetchHttpClient implements HttpClient {\n  /**\n   * Provides minimum viable error handling and the logic that executes the abstract methods.\n   * @param httpRequest - Object representing the outgoing HTTP request.\n   * @returns An object representing the incoming HTTP response.\n   */\n  async sendRequest(httpRequest: WebResourceLike): Promise<HttpOperationResponse> {\n    if (!httpRequest && typeof httpRequest !== \"object\") {\n      throw new Error(\n        \"'httpRequest' (WebResourceLike) cannot be null or undefined and must be of type object.\"\n      );\n    }\n\n    const abortController = new AbortController();\n    let abortListener: ((event: any) => void) | undefined;\n    if (httpRequest.abortSignal) {\n      if (httpRequest.abortSignal.aborted) {\n        throw new AbortError(\"The operation was aborted.\");\n      }\n\n      abortListener = (event: Event) => {\n        if (event.type === \"abort\") {\n          abortController.abort();\n        }\n      };\n      httpRequest.abortSignal.addEventListener(\"abort\", abortListener);\n    }\n\n    if (httpRequest.timeout) {\n      setTimeout(() => {\n        abortController.abort();\n      }, httpRequest.timeout);\n    }\n\n    if (httpRequest.formData) {\n      const formData: any = httpRequest.formData;\n      const requestForm = new FormData();\n      const appendFormValue = (key: string, value: any): void => {\n        // value function probably returns a stream so we can provide a fresh stream on each retry\n        if (typeof value === \"function\") {\n          value = value();\n        }\n        if (\n          value &&\n          Object.prototype.hasOwnProperty.call(value, \"value\") &&\n          Object.prototype.hasOwnProperty.call(value, \"options\")\n        ) {\n          requestForm.append(key, value.value, value.options);\n        } else {\n          requestForm.append(key, value);\n        }\n      };\n      for (const formKey of Object.keys(formData)) {\n        const formValue = formData[formKey];\n        if (Array.isArray(formValue)) {\n          for (let j = 0; j < formValue.length; j++) {\n            appendFormValue(formKey, formValue[j]);\n          }\n        } else {\n          appendFormValue(formKey, formValue);\n        }\n      }\n\n      httpRequest.body = requestForm;\n      httpRequest.formData = undefined;\n      const contentType = httpRequest.headers.get(\"Content-Type\");\n      if (contentType && contentType.indexOf(\"multipart/form-data\") !== -1) {\n        if (typeof requestForm.getBoundary === \"function\") {\n          httpRequest.headers.set(\n            \"Content-Type\",\n            `multipart/form-data; boundary=${requestForm.getBoundary()}`\n          );\n        } else {\n          // browser will automatically apply a suitable content-type header\n          httpRequest.headers.remove(\"Content-Type\");\n        }\n      }\n    }\n\n    let body = httpRequest.body\n      ? typeof httpRequest.body === \"function\"\n        ? httpRequest.body()\n        : httpRequest.body\n      : undefined;\n    if (httpRequest.onUploadProgress && httpRequest.body) {\n      const onUploadProgress = httpRequest.onUploadProgress;\n      const uploadReportStream = new ReportTransform(onUploadProgress);\n      if (isReadableStream(body)) {\n        body.pipe(uploadReportStream);\n      } else {\n        uploadReportStream.end(body);\n      }\n\n      body = uploadReportStream;\n    }\n\n    const platformSpecificRequestInit: Partial<RequestInit> = await this.prepareRequest(\n      httpRequest\n    );\n\n    const requestInit: RequestInit = {\n      body: body,\n      headers: httpRequest.headers.rawHeaders(),\n      method: httpRequest.method,\n      // the types for RequestInit are from the browser, which expects AbortSignal to\n      // have `reason` and `throwIfAborted`, but these don't exist on our polyfill\n      // for Node.\n      signal: abortController.signal as any,\n      redirect: \"manual\",\n      ...platformSpecificRequestInit,\n    };\n\n    let operationResponse: HttpOperationResponse | undefined;\n    try {\n      const response: CommonResponse = await this.fetch(httpRequest.url, requestInit);\n\n      const headers = parseHeaders(response.headers);\n\n      const streaming =\n        httpRequest.streamResponseStatusCodes?.has(response.status) ||\n        httpRequest.streamResponseBody;\n\n      operationResponse = {\n        headers: headers,\n        request: httpRequest,\n        status: response.status,\n        readableStreamBody: streaming\n          ? (response.body as unknown as NodeJS.ReadableStream)\n          : undefined,\n        bodyAsText: !streaming ? await response.text() : undefined,\n      };\n\n      const onDownloadProgress = httpRequest.onDownloadProgress;\n      if (onDownloadProgress) {\n        const responseBody: ReadableStream<Uint8Array> | undefined = response.body || undefined;\n\n        if (isReadableStream(responseBody)) {\n          const downloadReportStream = new ReportTransform(onDownloadProgress);\n          responseBody.pipe(downloadReportStream);\n          operationResponse.readableStreamBody = downloadReportStream;\n        } else {\n          const length = parseInt(headers.get(\"Content-Length\")!) || undefined;\n          if (length) {\n            // Calling callback for non-stream response for consistency with browser\n            onDownloadProgress({ loadedBytes: length });\n          }\n        }\n      }\n\n      await this.processRequest(operationResponse);\n\n      return operationResponse;\n    } catch (error: any) {\n      const fetchError: FetchError = error;\n      if (fetchError.code === \"ENOTFOUND\") {\n        throw new RestError(\n          fetchError.message,\n          RestError.REQUEST_SEND_ERROR,\n          undefined,\n          httpRequest\n        );\n      } else if (fetchError.type === \"aborted\") {\n        throw new AbortError(\"The operation was aborted.\");\n      }\n\n      throw fetchError;\n    } finally {\n      // clean up event listener\n      if (httpRequest.abortSignal && abortListener) {\n        let uploadStreamDone = Promise.resolve();\n        if (isReadableStream(body)) {\n          uploadStreamDone = isStreamComplete(body);\n        }\n        let downloadStreamDone = Promise.resolve();\n        if (isReadableStream(operationResponse?.readableStreamBody)) {\n          downloadStreamDone = isStreamComplete(\n            operationResponse!.readableStreamBody,\n            abortController\n          );\n        }\n\n        Promise.all([uploadStreamDone, downloadStreamDone])\n          .then(() => {\n            httpRequest.abortSignal?.removeEventListener(\"abort\", abortListener!);\n            return;\n          })\n          .catch((e) => {\n            logger.warning(\"Error when cleaning up abortListener on httpRequest\", e);\n          });\n      }\n    }\n  }\n\n  // a mapping of proxy settings string `${host}:${port}:${username}:${password}` to agent\n  private proxyAgentMap: Map<string, AgentCache> = new Map();\n  private keepAliveAgents: AgentCache = {};\n\n  private readonly cookieJar = new tough.CookieJar(undefined, { looseMode: true });\n\n  private getOrCreateAgent(httpRequest: WebResourceLike): http.Agent | https.Agent {\n    const isHttps = isUrlHttps(httpRequest.url);\n\n    // At the moment, proxy settings and keepAlive are mutually\n    // exclusive because the 'tunnel' library currently lacks the\n    // ability to create a proxy with keepAlive turned on.\n    if (httpRequest.proxySettings) {\n      const { host, port, username, password } = httpRequest.proxySettings;\n      const key = `${host}:${port}:${username}:${password}`;\n      const proxyAgents = this.proxyAgentMap.get(key) ?? {};\n\n      let agent = getCachedAgent(isHttps, proxyAgents);\n      if (agent) {\n        return agent;\n      }\n\n      const tunnel: ProxyAgent = createProxyAgent(\n        httpRequest.url,\n        httpRequest.proxySettings,\n        httpRequest.headers\n      );\n\n      agent = tunnel.agent;\n      if (tunnel.isHttps) {\n        proxyAgents.httpsAgent = tunnel.agent as https.Agent;\n      } else {\n        proxyAgents.httpAgent = tunnel.agent;\n      }\n      this.proxyAgentMap.set(key, proxyAgents);\n\n      return agent;\n    } else if (httpRequest.keepAlive) {\n      let agent = getCachedAgent(isHttps, this.keepAliveAgents);\n      if (agent) {\n        return agent;\n      }\n\n      const agentOptions: http.AgentOptions | https.AgentOptions = {\n        keepAlive: httpRequest.keepAlive,\n      };\n\n      if (isHttps) {\n        agent = this.keepAliveAgents.httpsAgent = new https.Agent(agentOptions);\n      } else {\n        agent = this.keepAliveAgents.httpAgent = new http.Agent(agentOptions);\n      }\n\n      return agent;\n    } else {\n      return isHttps ? https.globalAgent : http.globalAgent;\n    }\n  }\n\n  /**\n   * Uses `node-fetch` to perform the request.\n   */\n  // eslint-disable-next-line @azure/azure-sdk/ts-apisurface-standardized-verbs\n  async fetch(input: CommonRequestInfo, init?: CommonRequestInit): Promise<CommonResponse> {\n    return node_fetch(input, init) as unknown as Promise<CommonResponse>;\n  }\n\n  /**\n   * Prepares a request based on the provided web resource.\n   */\n  async prepareRequest(httpRequest: WebResourceLike): Promise<Partial<RequestInit>> {\n    const requestInit: Partial<RequestInit & { agent?: any; compress?: boolean }> = {};\n\n    if (this.cookieJar && !httpRequest.headers.get(\"Cookie\")) {\n      const cookieString = await new Promise<string>((resolve, reject) => {\n        this.cookieJar!.getCookieString(httpRequest.url, (err, cookie) => {\n          if (err) {\n            reject(err);\n          } else {\n            resolve(cookie);\n          }\n        });\n      });\n\n      httpRequest.headers.set(\"Cookie\", cookieString);\n    }\n\n    // Set the http(s) agent\n    requestInit.agent = this.getOrCreateAgent(httpRequest);\n\n    requestInit.compress = httpRequest.decompressResponse;\n\n    return requestInit;\n  }\n\n  /**\n   * Process an HTTP response. Handles persisting a cookie for subsequent requests if the response has a \"Set-Cookie\" header.\n   */\n  async processRequest(operationResponse: HttpOperationResponse): Promise<void> {\n    if (this.cookieJar) {\n      const setCookieHeader = operationResponse.headers.get(\"Set-Cookie\");\n      if (setCookieHeader !== undefined) {\n        await new Promise<void>((resolve, reject) => {\n          this.cookieJar!.setCookie(\n            setCookieHeader,\n            operationResponse.request.url,\n            { ignoreError: true },\n            (err) => {\n              if (err) {\n                reject(err);\n              } else {\n                resolve();\n              }\n            }\n          );\n        });\n      }\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * The different levels of logs that can be used with the HttpPipelineLogger.\n */\nexport enum HttpPipelineLogLevel {\n  /**\n   * A log level that indicates that no logs will be logged.\n   */\n  OFF,\n\n  /**\n   * An error log.\n   */\n  ERROR,\n\n  /**\n   * A warning log.\n   */\n  WARNING,\n\n  /**\n   * An information log.\n   */\n  INFO,\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { RequestOptionsBase, TransferProgressEvent } from \"./webResource\";\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { HttpOperationResponse } from \"./httpOperationResponse\";\nimport { OperationTracingOptions } from \"@azure/core-tracing\";\n\n/**\n * The base options type for all operations.\n */\nexport interface OperationOptions {\n  /**\n   * The signal which can be used to abort requests.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Options used when creating and sending HTTP requests for this operation.\n   */\n  requestOptions?: OperationRequestOptions;\n  /**\n   * Options used when tracing is enabled.\n   */\n  tracingOptions?: OperationTracingOptions;\n}\n\n/**\n * Options that allow configuring the handling of HTTP requests made by an SDK operation.\n */\nexport interface OperationRequestOptions {\n  /**\n   * User defined custom request headers that will be applied before the request is sent.\n   */\n  customHeaders?: { [key: string]: string };\n\n  /**\n   * The number of milliseconds a request can take before automatically being terminated.\n   */\n  timeout?: number;\n\n  /**\n   * Callback which fires upon upload progress.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Callback which fires upon download progress.\n   */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n  /**\n   * Whether or not the HttpOperationResponse should be deserialized. If this is undefined, then the\n   * HttpOperationResponse should be deserialized.\n   */\n  shouldDeserialize?: boolean | ((response: HttpOperationResponse) => boolean);\n}\n\n/**\n * Converts an OperationOptions to a RequestOptionsBase\n *\n * @param opts - OperationOptions object to convert to RequestOptionsBase\n */\nexport function operationOptionsToRequestOptionsBase<T extends OperationOptions>(\n  opts: T\n): RequestOptionsBase {\n  const { requestOptions, tracingOptions, ...additionalOptions } = opts;\n\n  let result: RequestOptionsBase = additionalOptions;\n\n  if (requestOptions) {\n    result = { ...result, ...requestOptions };\n  }\n\n  if (tracingOptions) {\n    result.tracingContext = tracingOptions.tracingContext;\n    // By passing spanOptions if they exist at runtime, we're backwards compatible with @azure/core-tracing@preview.13 and earlier.\n    result.spanOptions = (tracingOptions as any)?.spanOptions;\n  }\n\n  return result;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { HttpPipelineLogLevel } from \"../httpPipelineLogLevel\";\nimport { HttpPipelineLogger } from \"../httpPipelineLogger\";\nimport { WebResourceLike } from \"../webResource\";\n\n/**\n * Creates a new RequestPolicy per-request that uses the provided nextPolicy.\n */\nexport type RequestPolicyFactory = {\n  create(nextPolicy: RequestPolicy, options: RequestPolicyOptionsLike): RequestPolicy;\n};\n\n/**\n * The underlying structure of a request policy.\n */\nexport interface RequestPolicy {\n  /**\n   * A method that retrieves an {@link HttpOperationResponse} given a {@link WebResourceLike} describing the request to be made.\n   * @param httpRequest - {@link WebResourceLike} describing the request to be made.\n   */\n  sendRequest(httpRequest: WebResourceLike): Promise<HttpOperationResponse>;\n}\n\n/**\n * The base class from which all request policies derive.\n */\nexport abstract class BaseRequestPolicy implements RequestPolicy {\n  /**\n   * The main method to implement that manipulates a request/response.\n   */\n  protected constructor(\n    /**\n     * The next policy in the pipeline. Each policy is responsible for executing the next one if the request is to continue through the pipeline.\n     */\n    readonly _nextPolicy: RequestPolicy,\n    /**\n     * The options that can be passed to a given request policy.\n     */\n    readonly _options: RequestPolicyOptionsLike\n  ) {}\n\n  /**\n   * Sends a network request based on the given web resource.\n   * @param webResource - A {@link WebResourceLike} that describes a HTTP request to be made.\n   */\n  public abstract sendRequest(webResource: WebResourceLike): Promise<HttpOperationResponse>;\n\n  /**\n   * Get whether or not a log with the provided log level should be logged.\n   * @param logLevel - The log level of the log that will be logged.\n   * @returns Whether or not a log with the provided log level should be logged.\n   */\n  public shouldLog(logLevel: HttpPipelineLogLevel): boolean {\n    return this._options.shouldLog(logLevel);\n  }\n\n  /**\n   * Attempt to log the provided message to the provided logger. If no logger was provided or if\n   * the log level does not meat the logger's threshold, then nothing will be logged.\n   * @param logLevel - The log level of this log.\n   * @param message - The message of this log.\n   */\n  public log(logLevel: HttpPipelineLogLevel, message: string): void {\n    this._options.log(logLevel, message);\n  }\n}\n\n/**\n * Optional properties that can be used when creating a RequestPolicy.\n */\nexport interface RequestPolicyOptionsLike {\n  /**\n   * Get whether or not a log with the provided log level should be logged.\n   * @param logLevel - The log level of the log that will be logged.\n   * @returns Whether or not a log with the provided log level should be logged.\n   */\n  shouldLog(logLevel: HttpPipelineLogLevel): boolean;\n\n  /**\n   * Attempt to log the provided message to the provided logger. If no logger was provided or if\n   * the log level does not meet the logger's threshold, then nothing will be logged.\n   * @param logLevel - The log level of this log.\n   * @param message - The message of this log.\n   */\n  log(logLevel: HttpPipelineLogLevel, message: string): void;\n}\n\n/**\n * Optional properties that can be used when creating a RequestPolicy.\n */\nexport class RequestPolicyOptions {\n  constructor(private _logger?: HttpPipelineLogger) {}\n\n  /**\n   * Get whether or not a log with the provided log level should be logged.\n   * @param logLevel - The log level of the log that will be logged.\n   * @returns Whether or not a log with the provided log level should be logged.\n   */\n  public shouldLog(logLevel: HttpPipelineLogLevel): boolean {\n    return (\n      !!this._logger &&\n      logLevel !== HttpPipelineLogLevel.OFF &&\n      logLevel <= this._logger.minimumLogLevel\n    );\n  }\n\n  /**\n   * Attempt to log the provided message to the provided logger. If no logger was provided or if\n   * the log level does not meet the logger's threshold, then nothing will be logged.\n   * @param logLevel - The log level of this log.\n   * @param message - The message of this log.\n   */\n  public log(logLevel: HttpPipelineLogLevel, message: string): void {\n    if (this._logger && this.shouldLog(logLevel)) {\n      this._logger.log(logLevel, message);\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as xml2js from \"xml2js\";\nimport { SerializerOptions, XML_ATTRKEY, XML_CHARKEY } from \"./serializer.common\";\n\n// Note: The reason we re-define all of the xml2js default settings (version 2.0) here is because the default settings object exposed\n// by the xm2js library is mutable. See https://github.com/Leon<PERSON>-from-XIV/node-xml2js/issues/536\n// By creating a new copy of the settings each time we instantiate the parser,\n// we are safeguarding against the possibility of the default settings being mutated elsewhere unintentionally.\nconst xml2jsDefaultOptionsV2: xml2js.OptionsV2 = {\n  explicitCharkey: false,\n  trim: false,\n  normalize: false,\n  normalizeTags: false,\n  attrkey: XML_ATTRKEY,\n  explicitArray: true,\n  ignoreAttrs: false,\n  mergeAttrs: false,\n  explicitRoot: true,\n  validator: undefined,\n  xmlns: false,\n  explicitChildren: false,\n  preserveChildrenOrder: false,\n  childkey: \"$$\",\n  charsAsChildren: false,\n  includeWhiteChars: false,\n  async: false,\n  strict: true,\n  attrNameProcessors: undefined,\n  attrValueProcessors: undefined,\n  tagNameProcessors: undefined,\n  valueProcessors: undefined,\n  rootName: \"root\",\n  xmldec: {\n    version: \"1.0\",\n    encoding: \"UTF-8\",\n    standalone: true,\n  },\n  doctype: undefined,\n  renderOpts: {\n    pretty: true,\n    indent: \"  \",\n    newline: \"\\n\",\n  },\n  headless: false,\n  chunkSize: 10000,\n  emptyTag: \"\",\n  cdata: false,\n};\n\n// The xml2js settings for general XML parsing operations.\nconst xml2jsParserSettings: any = Object.assign({}, xml2jsDefaultOptionsV2);\nxml2jsParserSettings.explicitArray = false;\n\n// The xml2js settings for general XML building operations.\nconst xml2jsBuilderSettings: any = Object.assign({}, xml2jsDefaultOptionsV2);\nxml2jsBuilderSettings.explicitArray = false;\nxml2jsBuilderSettings.renderOpts = {\n  pretty: false,\n};\n\n/**\n * Converts given JSON object to XML string\n * @param obj - JSON object to be converted into XML string\n * @param opts - Options that govern the parsing of given JSON object\n */\nexport function stringifyXML(obj: unknown, opts: SerializerOptions = {}): string {\n  xml2jsBuilderSettings.rootName = opts.rootName;\n  xml2jsBuilderSettings.charkey = opts.xmlCharKey ?? XML_CHARKEY;\n  const builder = new xml2js.Builder(xml2jsBuilderSettings);\n  return builder.buildObject(obj);\n}\n\n/**\n * Converts given XML string into JSON\n * @param str - String containing the XML content to be parsed into JSON\n * @param opts - Options that govern the parsing of given xml string\n */\nexport function parseXML(str: string, opts: SerializerOptions = {}): Promise<any> {\n  xml2jsParserSettings.explicitRoot = !!opts.includeRoot;\n  xml2jsParserSettings.charkey = opts.xmlCharKey ?? XML_CHARKEY;\n  const xmlParser = new xml2js.Parser(xml2jsParserSettings);\n  return new Promise((resolve, reject) => {\n    if (!str) {\n      reject(new Error(\"Document is empty\"));\n    } else {\n      xmlParser.parseString(str, (err: any, res: any) => {\n        if (err) {\n          reject(err);\n        } else {\n          resolve(res);\n        }\n      });\n    }\n  });\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"./requestPolicy\";\nimport { SerializerOptions, XML_CHARKEY } from \"../util/serializer.common\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { MapperType } from \"../serializer\";\nimport { OperationResponse } from \"../operationResponse\";\nimport { OperationSpec } from \"../operationSpec\";\nimport { RestError } from \"../restError\";\nimport { WebResourceLike } from \"../webResource\";\nimport { parseXML } from \"../util/xml\";\n\n/**\n * Options to configure API response deserialization.\n */\nexport interface DeserializationOptions {\n  /**\n   * Configures the expected content types for the deserialization of\n   * JSON and XML response bodies.\n   */\n  expectedContentTypes: DeserializationContentTypes;\n}\n\n/**\n * The content-types that will indicate that an operation response should be deserialized in a\n * particular way.\n */\nexport interface DeserializationContentTypes {\n  /**\n   * The content-types that indicate that an operation response should be deserialized as JSON.\n   * Defaults to [ \"application/json\", \"text/json\" ].\n   */\n  json?: string[];\n\n  /**\n   * The content-types that indicate that an operation response should be deserialized as XML.\n   * Defaults to [ \"application/xml\", \"application/atom+xml\" ].\n   */\n  xml?: string[];\n}\n\n/**\n * Create a new serialization RequestPolicyCreator that will serialized HTTP request bodies as they\n * pass through the HTTP pipeline.\n */\nexport function deserializationPolicy(\n  deserializationContentTypes?: DeserializationContentTypes,\n  parsingOptions?: SerializerOptions\n): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new DeserializationPolicy(\n        nextPolicy,\n        options,\n        deserializationContentTypes,\n        parsingOptions\n      );\n    },\n  };\n}\n\nexport const defaultJsonContentTypes = [\"application/json\", \"text/json\"];\nexport const defaultXmlContentTypes = [\"application/xml\", \"application/atom+xml\"];\n\nexport const DefaultDeserializationOptions: DeserializationOptions = {\n  expectedContentTypes: {\n    json: defaultJsonContentTypes,\n    xml: defaultXmlContentTypes,\n  },\n};\n\n/**\n * A RequestPolicy that will deserialize HTTP response bodies and headers as they pass through the\n * HTTP pipeline.\n */\nexport class DeserializationPolicy extends BaseRequestPolicy {\n  public readonly jsonContentTypes: string[];\n  public readonly xmlContentTypes: string[];\n  public readonly xmlCharKey: string;\n\n  constructor(\n    nextPolicy: RequestPolicy,\n    requestPolicyOptions: RequestPolicyOptions,\n    deserializationContentTypes?: DeserializationContentTypes,\n    parsingOptions: SerializerOptions = {}\n  ) {\n    super(nextPolicy, requestPolicyOptions);\n\n    this.jsonContentTypes =\n      (deserializationContentTypes && deserializationContentTypes.json) || defaultJsonContentTypes;\n    this.xmlContentTypes =\n      (deserializationContentTypes && deserializationContentTypes.xml) || defaultXmlContentTypes;\n    this.xmlCharKey = parsingOptions.xmlCharKey ?? XML_CHARKEY;\n  }\n\n  public async sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    return this._nextPolicy.sendRequest(request).then((response: HttpOperationResponse) =>\n      deserializeResponseBody(this.jsonContentTypes, this.xmlContentTypes, response, {\n        xmlCharKey: this.xmlCharKey,\n      })\n    );\n  }\n}\n\nfunction getOperationResponse(\n  parsedResponse: HttpOperationResponse\n): undefined | OperationResponse {\n  let result: OperationResponse | undefined;\n  const request: WebResourceLike = parsedResponse.request;\n  const operationSpec: OperationSpec | undefined = request.operationSpec;\n  if (operationSpec) {\n    const operationResponseGetter:\n      | undefined\n      | ((\n          operationSpec: OperationSpec,\n          response: HttpOperationResponse\n        ) => undefined | OperationResponse) = request.operationResponseGetter;\n    if (!operationResponseGetter) {\n      result = operationSpec.responses[parsedResponse.status];\n    } else {\n      result = operationResponseGetter(operationSpec, parsedResponse);\n    }\n  }\n  return result;\n}\n\nfunction shouldDeserializeResponse(parsedResponse: HttpOperationResponse): boolean {\n  const shouldDeserialize: undefined | boolean | ((response: HttpOperationResponse) => boolean) =\n    parsedResponse.request.shouldDeserialize;\n  let result: boolean;\n  if (shouldDeserialize === undefined) {\n    result = true;\n  } else if (typeof shouldDeserialize === \"boolean\") {\n    result = shouldDeserialize;\n  } else {\n    result = shouldDeserialize(parsedResponse);\n  }\n  return result;\n}\n\n/**\n * Given a particular set of content types to parse as either JSON or XML, consumes the HTTP response to produce the result object defined by the request's {@link OperationSpec}.\n * @param jsonContentTypes - Response content types to parse the body as JSON.\n * @param xmlContentTypes  - Response content types to parse the body as XML.\n * @param response - HTTP Response from the pipeline.\n * @param options  - Options to the serializer, mostly for configuring the XML parser if needed.\n * @returns A parsed {@link HttpOperationResponse} object that can be returned by the {@link ServiceClient}.\n */\nexport function deserializeResponseBody(\n  jsonContentTypes: string[],\n  xmlContentTypes: string[],\n  response: HttpOperationResponse,\n  options: SerializerOptions = {}\n): Promise<HttpOperationResponse> {\n  const updatedOptions: Required<SerializerOptions> = {\n    rootName: options.rootName ?? \"\",\n    includeRoot: options.includeRoot ?? false,\n    xmlCharKey: options.xmlCharKey ?? XML_CHARKEY,\n  };\n  return parse(jsonContentTypes, xmlContentTypes, response, updatedOptions).then(\n    (parsedResponse) => {\n      if (!shouldDeserializeResponse(parsedResponse)) {\n        return parsedResponse;\n      }\n\n      const operationSpec = parsedResponse.request.operationSpec;\n      if (!operationSpec || !operationSpec.responses) {\n        return parsedResponse;\n      }\n\n      const responseSpec = getOperationResponse(parsedResponse);\n\n      const { error, shouldReturnResponse } = handleErrorResponse(\n        parsedResponse,\n        operationSpec,\n        responseSpec\n      );\n      if (error) {\n        throw error;\n      } else if (shouldReturnResponse) {\n        return parsedResponse;\n      }\n\n      // An operation response spec does exist for current status code, so\n      // use it to deserialize the response.\n      if (responseSpec) {\n        if (responseSpec.bodyMapper) {\n          let valueToDeserialize: any = parsedResponse.parsedBody;\n          if (operationSpec.isXML && responseSpec.bodyMapper.type.name === MapperType.Sequence) {\n            valueToDeserialize =\n              typeof valueToDeserialize === \"object\"\n                ? valueToDeserialize[responseSpec.bodyMapper.xmlElementName!]\n                : [];\n          }\n          try {\n            parsedResponse.parsedBody = operationSpec.serializer.deserialize(\n              responseSpec.bodyMapper,\n              valueToDeserialize,\n              \"operationRes.parsedBody\",\n              options\n            );\n          } catch (innerError: any) {\n            const restError = new RestError(\n              `Error ${innerError} occurred in deserializing the responseBody - ${parsedResponse.bodyAsText}`,\n              undefined,\n              parsedResponse.status,\n              parsedResponse.request,\n              parsedResponse\n            );\n            throw restError;\n          }\n        } else if (operationSpec.httpMethod === \"HEAD\") {\n          // head methods never have a body, but we return a boolean to indicate presence/absence of the resource\n          parsedResponse.parsedBody = response.status >= 200 && response.status < 300;\n        }\n\n        if (responseSpec.headersMapper) {\n          parsedResponse.parsedHeaders = operationSpec.serializer.deserialize(\n            responseSpec.headersMapper,\n            parsedResponse.headers.toJson(),\n            \"operationRes.parsedHeaders\",\n            options\n          );\n        }\n      }\n\n      return parsedResponse;\n    }\n  );\n}\n\nfunction isOperationSpecEmpty(operationSpec: OperationSpec): boolean {\n  const expectedStatusCodes = Object.keys(operationSpec.responses);\n  return (\n    expectedStatusCodes.length === 0 ||\n    (expectedStatusCodes.length === 1 && expectedStatusCodes[0] === \"default\")\n  );\n}\n\nfunction handleErrorResponse(\n  parsedResponse: HttpOperationResponse,\n  operationSpec: OperationSpec,\n  responseSpec: OperationResponse | undefined\n): { error: RestError | null; shouldReturnResponse: boolean } {\n  const isSuccessByStatus = 200 <= parsedResponse.status && parsedResponse.status < 300;\n  const isExpectedStatusCode: boolean = isOperationSpecEmpty(operationSpec)\n    ? isSuccessByStatus\n    : !!responseSpec;\n\n  if (isExpectedStatusCode) {\n    if (responseSpec) {\n      if (!responseSpec.isError) {\n        return { error: null, shouldReturnResponse: false };\n      }\n    } else {\n      return { error: null, shouldReturnResponse: false };\n    }\n  }\n\n  const errorResponseSpec = responseSpec ?? operationSpec.responses.default;\n  const streaming =\n    parsedResponse.request.streamResponseStatusCodes?.has(parsedResponse.status) ||\n    parsedResponse.request.streamResponseBody;\n  const initialErrorMessage = streaming\n    ? `Unexpected status code: ${parsedResponse.status}`\n    : (parsedResponse.bodyAsText as string);\n\n  const error = new RestError(\n    initialErrorMessage,\n    undefined,\n    parsedResponse.status,\n    parsedResponse.request,\n    parsedResponse\n  );\n\n  // If the item failed but there's no error spec or default spec to deserialize the error,\n  // we should fail so we just throw the parsed response\n  if (!errorResponseSpec) {\n    throw error;\n  }\n\n  const defaultBodyMapper = errorResponseSpec.bodyMapper;\n  const defaultHeadersMapper = errorResponseSpec.headersMapper;\n\n  try {\n    // If error response has a body, try to deserialize it using default body mapper.\n    // Then try to extract error code & message from it\n    if (parsedResponse.parsedBody) {\n      const parsedBody = parsedResponse.parsedBody;\n      let parsedError;\n      if (defaultBodyMapper) {\n        let valueToDeserialize: any = parsedBody;\n        if (operationSpec.isXML && defaultBodyMapper.type.name === MapperType.Sequence) {\n          valueToDeserialize =\n            typeof parsedBody === \"object\" ? parsedBody[defaultBodyMapper.xmlElementName!] : [];\n        }\n        parsedError = operationSpec.serializer.deserialize(\n          defaultBodyMapper,\n          valueToDeserialize,\n          \"error.response.parsedBody\"\n        );\n      }\n\n      const internalError: any = parsedBody.error || parsedError || parsedBody;\n      error.code = internalError.code;\n      if (internalError.message) {\n        error.message = internalError.message;\n      }\n\n      if (defaultBodyMapper) {\n        error.response!.parsedBody = parsedError;\n      }\n    }\n\n    // If error response has headers, try to deserialize it using default header mapper\n    if (parsedResponse.headers && defaultHeadersMapper) {\n      error.response!.parsedHeaders = operationSpec.serializer.deserialize(\n        defaultHeadersMapper,\n        parsedResponse.headers.toJson(),\n        \"operationRes.parsedHeaders\"\n      );\n    }\n  } catch (defaultError: any) {\n    error.message = `Error \"${defaultError.message}\" occurred in deserializing the responseBody - \"${parsedResponse.bodyAsText}\" for the default response.`;\n  }\n\n  return { error, shouldReturnResponse: false };\n}\n\nfunction parse(\n  jsonContentTypes: string[],\n  xmlContentTypes: string[],\n  operationResponse: HttpOperationResponse,\n  opts: Required<SerializerOptions>\n): Promise<HttpOperationResponse> {\n  const errorHandler = (err: Error & { code: string }): Promise<never> => {\n    const msg = `Error \"${err}\" occurred while parsing the response body - ${operationResponse.bodyAsText}.`;\n    const errCode = err.code || RestError.PARSE_ERROR;\n    const e = new RestError(\n      msg,\n      errCode,\n      operationResponse.status,\n      operationResponse.request,\n      operationResponse\n    );\n    return Promise.reject(e);\n  };\n\n  const streaming =\n    operationResponse.request.streamResponseStatusCodes?.has(operationResponse.status) ||\n    operationResponse.request.streamResponseBody;\n  if (!streaming && operationResponse.bodyAsText) {\n    const text = operationResponse.bodyAsText;\n    const contentType: string = operationResponse.headers.get(\"Content-Type\") || \"\";\n    const contentComponents: string[] = !contentType\n      ? []\n      : contentType.split(\";\").map((component) => component.toLowerCase());\n    if (\n      contentComponents.length === 0 ||\n      contentComponents.some((component) => jsonContentTypes.indexOf(component) !== -1)\n    ) {\n      return new Promise<HttpOperationResponse>((resolve) => {\n        operationResponse.parsedBody = JSON.parse(text);\n        resolve(operationResponse);\n      }).catch(errorHandler);\n    } else if (contentComponents.some((component) => xmlContentTypes.indexOf(component) !== -1)) {\n      return parseXML(text, opts)\n        .then((body) => {\n          operationResponse.parsedBody = body;\n          return operationResponse;\n        })\n        .catch(errorHandler);\n    }\n  }\n\n  return Promise.resolve(operationResponse);\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"./requestPolicy\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\n\n/**\n * Options for how HTTP connections should be maintained for future\n * requests.\n */\nexport interface KeepAliveOptions {\n  /**\n   * When true, connections will be kept alive for multiple requests.\n   * Defaults to true.\n   */\n  enable: boolean;\n}\n\n/**\n * By default, HTTP connections are maintained for future requests.\n */\nexport const DefaultKeepAliveOptions: KeepAliveOptions = {\n  enable: true,\n};\n\n/**\n * Creates a policy that controls whether HTTP connections are maintained on future requests.\n * @param keepAliveOptions - Keep alive options. By default, HTTP connections are maintained for future requests.\n * @returns An instance of the {@link KeepAlivePolicy}\n */\nexport function keepAlivePolicy(keepAliveOptions?: KeepAliveOptions): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new KeepAlivePolicy(nextPolicy, options, keepAliveOptions || DefaultKeepAliveOptions);\n    },\n  };\n}\n\n/**\n * KeepAlivePolicy is a policy used to control keep alive settings for every request.\n */\nexport class KeepAlivePolicy extends BaseRequestPolicy {\n  /**\n   * Creates an instance of KeepAlivePolicy.\n   *\n   * @param nextPolicy -\n   * @param options -\n   * @param keepAliveOptions -\n   */\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    private readonly keepAliveOptions: KeepAliveOptions\n  ) {\n    super(nextPolicy, options);\n  }\n\n  /**\n   * Sends out request.\n   *\n   * @param request -\n   * @returns\n   */\n  public async sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    request.keepAlive = this.keepAliveOptions.enable;\n    return this._nextPolicy.sendRequest(request);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"./requestPolicy\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { URLBuilder } from \"../url\";\nimport { WebResourceLike } from \"../webResource\";\n\n/**\n * Methods that are allowed to follow redirects 301 and 302\n */\nconst allowedRedirect = [\"GET\", \"HEAD\"];\n\n/**\n * Options for how redirect responses are handled.\n */\nexport interface RedirectOptions {\n  /**\n   * When true, redirect responses are followed.  Defaults to true.\n   */\n  handleRedirects: boolean;\n\n  /**\n   * The maximum number of times the redirect URL will be tried before\n   * failing.  Defaults to 20.\n   */\n  maxRetries?: number;\n}\n\nexport const DefaultRedirectOptions: RedirectOptions = {\n  handleRedirects: true,\n  maxRetries: 20,\n};\n\n/**\n * Creates a redirect policy, which sends a repeats the request to a new destination if a response arrives with a \"location\" header, and a status code between 300 and 307.\n * @param maximumRetries - Maximum number of redirects to follow.\n * @returns An instance of the {@link RedirectPolicy}\n */\nexport function redirectPolicy(maximumRetries = 20): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new RedirectPolicy(nextPolicy, options, maximumRetries);\n    },\n  };\n}\n\n/**\n * Resends the request to a new destination if a response arrives with a \"location\" header, and a status code between 300 and 307.\n */\nexport class RedirectPolicy extends BaseRequestPolicy {\n  constructor(nextPolicy: RequestPolicy, options: RequestPolicyOptions, readonly maxRetries = 20) {\n    super(nextPolicy, options);\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    return this._nextPolicy\n      .sendRequest(request)\n      .then((response) => handleRedirect(this, response, 0));\n  }\n}\n\nfunction handleRedirect(\n  policy: RedirectPolicy,\n  response: HttpOperationResponse,\n  currentRetries: number\n): Promise<HttpOperationResponse> {\n  const { request, status } = response;\n  const locationHeader = response.headers.get(\"location\");\n  if (\n    locationHeader &&\n    (status === 300 ||\n      (status === 301 && allowedRedirect.includes(request.method)) ||\n      (status === 302 && allowedRedirect.includes(request.method)) ||\n      (status === 303 && request.method === \"POST\") ||\n      status === 307) &&\n    (!policy.maxRetries || currentRetries < policy.maxRetries)\n  ) {\n    const builder = URLBuilder.parse(request.url);\n    builder.setPath(locationHeader);\n    request.url = builder.toString();\n\n    // POST request with Status code 303 should be converted into a\n    // redirected GET request if the redirect url is present in the location header\n    if (status === 303) {\n      request.method = \"GET\";\n      delete request.body;\n    }\n\n    return policy._nextPolicy\n      .sendRequest(request)\n      .then((res) => handleRedirect(policy, res, currentRetries + 1));\n  }\n\n  return Promise.resolve(response);\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpOperationResponse } from \"..\";\n\nexport const DEFAULT_CLIENT_RETRY_COUNT = 3;\n// intervals are in ms\nexport const DEFAULT_CLIENT_RETRY_INTERVAL = 1000 * 30;\nexport const DEFAULT_CLIENT_MAX_RETRY_INTERVAL = 1000 * 90;\nexport const DEFAULT_CLIENT_MIN_RETRY_INTERVAL = 1000 * 3;\n\nexport function isNumber(n: unknown): n is number {\n  return typeof n === \"number\";\n}\nexport interface RetryData {\n  retryCount: number;\n  retryInterval: number;\n  error?: RetryError;\n}\n\nexport interface RetryError extends Error {\n  message: string;\n  code?: string;\n  innerError?: RetryError;\n}\n\n/**\n * @internal\n * Determines if the operation should be retried.\n *\n * @param retryLimit - Specifies the max number of retries.\n * @param predicate - Initial chekck on whether to retry based on given responses or errors\n * @param retryData -  The retry data.\n * @returns True if the operation qualifies for a retry; false otherwise.\n */\nexport function shouldRetry(\n  retryLimit: number,\n  predicate: (response?: HttpOperationResponse, error?: RetryError) => boolean,\n  retryData: RetryData,\n  response?: HttpOperationResponse,\n  error?: RetryError\n): boolean {\n  if (!predicate(response, error)) {\n    return false;\n  }\n\n  return retryData.retryCount < retryLimit;\n}\n\n/**\n * @internal\n * Updates the retry data for the next attempt.\n *\n * @param retryOptions - specifies retry interval, and its lower bound and upper bound.\n * @param retryData -  The retry data.\n * @param err - The operation\"s error, if any.\n */\nexport function updateRetryData(\n  retryOptions: { retryInterval: number; minRetryInterval: number; maxRetryInterval: number },\n  retryData: RetryData = { retryCount: 0, retryInterval: 0 },\n  err?: RetryError\n): RetryData {\n  if (err) {\n    if (retryData.error) {\n      err.innerError = retryData.error;\n    }\n\n    retryData.error = err;\n  }\n\n  // Adjust retry count\n  retryData.retryCount++;\n\n  // Adjust retry interval\n  let incrementDelta = Math.pow(2, retryData.retryCount - 1) - 1;\n  const boundedRandDelta =\n    retryOptions.retryInterval * 0.8 +\n    Math.floor(Math.random() * (retryOptions.retryInterval * 0.4));\n  incrementDelta *= boundedRandDelta;\n\n  retryData.retryInterval = Math.min(\n    retryOptions.minRetryInterval + incrementDelta,\n    retryOptions.maxRetryInterval\n  );\n\n  return retryData;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"./requestPolicy\";\nimport {\n  DEFAULT_CLIENT_MAX_RETRY_INTERVAL,\n  DEFAULT_CLIENT_RETRY_COUNT,\n  DEFAULT_CLIENT_RETRY_INTERVAL,\n  RetryData,\n  RetryError,\n  isNumber,\n  shouldRetry,\n  updateRetryData,\n} from \"../util/exponentialBackoffStrategy\";\nimport { Constants } from \"../util/constants\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { RestError } from \"../restError\";\nimport { WebResourceLike } from \"../webResource\";\nimport { delay } from \"@azure/core-util\";\nimport { logger } from \"../log\";\n\n/**\n * Policy that retries the request as many times as configured for as long as the max retry time interval specified, each retry waiting longer to begin than the last time.\n * @param retryCount - Maximum number of retries.\n * @param retryInterval - Base time between retries.\n * @param maxRetryInterval - Maximum time to wait between retries.\n */\nexport function exponentialRetryPolicy(\n  retryCount?: number,\n  retryInterval?: number,\n  maxRetryInterval?: number\n): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new ExponentialRetryPolicy(\n        nextPolicy,\n        options,\n        retryCount,\n        retryInterval,\n        maxRetryInterval\n      );\n    },\n  };\n}\n\n/**\n * Describes the Retry Mode type. Currently supporting only Exponential.\n */\nexport enum RetryMode {\n  /**\n   * Currently supported retry mode.\n   * Each time a retry happens, it will take exponentially more time than the last time.\n   */\n  Exponential,\n}\n\n/**\n * Options that control how to retry failed requests.\n */\nexport interface RetryOptions {\n  /**\n   * The maximum number of retry attempts.  Defaults to 3.\n   */\n  maxRetries?: number;\n\n  /**\n   * The amount of delay in milliseconds between retry attempts. Defaults to 30000\n   * (30 seconds). The delay increases exponentially with each retry up to a maximum\n   * specified by maxRetryDelayInMs.\n   */\n  retryDelayInMs?: number;\n\n  /**\n   * The maximum delay in milliseconds allowed before retrying an operation. Defaults\n   * to 90000 (90 seconds).\n   */\n  maxRetryDelayInMs?: number;\n\n  /**\n   * Currently supporting only Exponential mode.\n   */\n  mode?: RetryMode;\n}\n\nexport const DefaultRetryOptions: RetryOptions = {\n  maxRetries: DEFAULT_CLIENT_RETRY_COUNT,\n  retryDelayInMs: DEFAULT_CLIENT_RETRY_INTERVAL,\n  maxRetryDelayInMs: DEFAULT_CLIENT_MAX_RETRY_INTERVAL,\n};\n\n/**\n * Instantiates a new \"ExponentialRetryPolicyFilter\" instance.\n */\nexport class ExponentialRetryPolicy extends BaseRequestPolicy {\n  /**\n   * The client retry count.\n   */\n  retryCount: number;\n  /**\n   * The client retry interval in milliseconds.\n   */\n  retryInterval: number;\n  /**\n   * The maximum retry interval in milliseconds.\n   */\n  maxRetryInterval: number;\n\n  /**\n   * @param nextPolicy - The next RequestPolicy in the pipeline chain.\n   * @param options - The options for this RequestPolicy.\n   * @param retryCount - The client retry count.\n   * @param retryInterval - The client retry interval, in milliseconds.\n   * @param minRetryInterval - The minimum retry interval, in milliseconds.\n   * @param maxRetryInterval - The maximum retry interval, in milliseconds.\n   */\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    retryCount?: number,\n    retryInterval?: number,\n    maxRetryInterval?: number\n  ) {\n    super(nextPolicy, options);\n    this.retryCount = isNumber(retryCount) ? retryCount : DEFAULT_CLIENT_RETRY_COUNT;\n    this.retryInterval = isNumber(retryInterval) ? retryInterval : DEFAULT_CLIENT_RETRY_INTERVAL;\n    this.maxRetryInterval = isNumber(maxRetryInterval)\n      ? maxRetryInterval\n      : DEFAULT_CLIENT_MAX_RETRY_INTERVAL;\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    return this._nextPolicy\n      .sendRequest(request.clone())\n      .then((response) => retry(this, request, response))\n      .catch((error) => retry(this, request, error.response, undefined, error));\n  }\n}\n\nasync function retry(\n  policy: ExponentialRetryPolicy,\n  request: WebResourceLike,\n  response?: HttpOperationResponse,\n  retryData?: RetryData,\n  requestError?: RetryError\n): Promise<HttpOperationResponse> {\n  function shouldPolicyRetry(responseParam?: HttpOperationResponse): boolean {\n    const statusCode = responseParam?.status;\n    if (statusCode === 503 && response?.headers.get(Constants.HeaderConstants.RETRY_AFTER)) {\n      return false;\n    }\n\n    if (\n      statusCode === undefined ||\n      (statusCode < 500 && statusCode !== 408) ||\n      statusCode === 501 ||\n      statusCode === 505\n    ) {\n      return false;\n    }\n    return true;\n  }\n\n  retryData = updateRetryData(\n    {\n      retryInterval: policy.retryInterval,\n      minRetryInterval: 0,\n      maxRetryInterval: policy.maxRetryInterval,\n    },\n    retryData,\n    requestError\n  );\n\n  const isAborted: boolean | undefined = request.abortSignal && request.abortSignal.aborted;\n  if (!isAborted && shouldRetry(policy.retryCount, shouldPolicyRetry, retryData, response)) {\n    logger.info(`Retrying request in ${retryData.retryInterval}`);\n    try {\n      await delay(retryData.retryInterval);\n      const res = await policy._nextPolicy.sendRequest(request.clone());\n      return retry(policy, request, res, retryData);\n    } catch (err: any) {\n      return retry(policy, request, response, retryData, err);\n    }\n  } else if (isAborted || requestError || !response) {\n    // If the operation failed in the end, return all errors instead of just the last one\n    const err =\n      retryData.error ||\n      new RestError(\n        \"Failed to send the request.\",\n        RestError.REQUEST_SEND_ERROR,\n        response && response.status,\n        response && response.request,\n        response\n      );\n    throw err;\n  } else {\n    return response;\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"./requestPolicy\";\nimport { Debugger } from \"@azure/logger\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { Sanitizer } from \"../util/sanitizer\";\nimport { WebResourceLike } from \"../webResource\";\nimport { logger as coreLogger } from \"../log\";\n\n/**\n * Options to pass to the {@link logPolicy}.\n * By default only a set list of headers are logged, though this can be configured. Request and response bodies are never logged.\n */\nexport interface LogPolicyOptions {\n  /**\n   * Header names whose values will be logged when logging is enabled. Defaults to:\n   * x-ms-client-request-id, x-ms-return-client-request-id, x-ms-useragent, x-ms-correlation-request-id,\n   * x-ms-request-id, client-request-id, ms-cv, return-client-request-id, traceparent, Access-Control-Allow-Credentials,\n   * Access-Control-Allow-Headers, Access-Control-Allow-Methods, Access-Control-Allow-Origin, Access-Control-Expose-Headers,\n   * Access-Control-Max-Age, Access-Control-Request-Headers, Access-Control-Request-Method, Origin, Accept, Accept-Encoding,\n   * Cache-Control, Connection, Content-Length, Content-Type, Date, ETag, Expires, If-Match, If-Modified-Since, If-None-Match,\n   * If-Unmodified-Since, Last-Modified, Pragma, Request-Id, Retry-After, Server, Transfer-Encoding, and User-Agent.\n   *\n   * Any headers specified in this field will be added to that list.\n   * Any other values will be written to logs as \"REDACTED\".\n   */\n  allowedHeaderNames?: string[];\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   */\n  allowedQueryParameters?: string[];\n\n  /**\n   * The Debugger (logger) instance to use for writing pipeline logs.\n   */\n  logger?: Debugger;\n}\n\n/**\n * Creates a policy that logs information about the outgoing request and the incoming responses.\n * @param loggingOptions - Logging options.\n * @returns An instance of the {@link LogPolicy}\n */\nexport function logPolicy(loggingOptions: LogPolicyOptions = {}): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new LogPolicy(nextPolicy, options, loggingOptions);\n    },\n  };\n}\n\n/**\n * A policy that logs information about the outgoing request and the incoming responses.\n */\nexport class LogPolicy extends BaseRequestPolicy {\n  logger: Debugger;\n  sanitizer: Sanitizer;\n\n  /**\n   * Header names whose values will be logged when logging is enabled. Defaults to\n   * Date, traceparent, x-ms-client-request-id, and x-ms-request id.  Any headers\n   * specified in this field will be added to that list.  Any other values will\n   * be written to logs as \"REDACTED\".\n   * @deprecated Pass these into the constructor instead.\n   */\n  public get allowedHeaderNames(): Set<string> {\n    return this.sanitizer.allowedHeaderNames;\n  }\n\n  /**\n   * Header names whose values will be logged when logging is enabled. Defaults to\n   * Date, traceparent, x-ms-client-request-id, and x-ms-request id.  Any headers\n   * specified in this field will be added to that list.  Any other values will\n   * be written to logs as \"REDACTED\".\n   * @deprecated Pass these into the constructor instead.\n   */\n  public set allowedHeaderNames(allowedHeaderNames: Set<string>) {\n    this.sanitizer.allowedHeaderNames = allowedHeaderNames;\n  }\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   * @deprecated Pass these into the constructor instead.\n   */\n  public get allowedQueryParameters(): Set<string> {\n    return this.sanitizer.allowedQueryParameters;\n  }\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   * @deprecated Pass these into the constructor instead.\n   */\n  public set allowedQueryParameters(allowedQueryParameters: Set<string>) {\n    this.sanitizer.allowedQueryParameters = allowedQueryParameters;\n  }\n\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    {\n      logger = coreLogger.info,\n      allowedHeaderNames = [],\n      allowedQueryParameters = [],\n    }: LogPolicyOptions = {}\n  ) {\n    super(nextPolicy, options);\n    this.logger = logger;\n    this.sanitizer = new Sanitizer({ allowedHeaderNames, allowedQueryParameters });\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    if (!this.logger.enabled) return this._nextPolicy.sendRequest(request);\n\n    this.logRequest(request);\n    return this._nextPolicy.sendRequest(request).then((response) => this.logResponse(response));\n  }\n\n  private logRequest(request: WebResourceLike): void {\n    this.logger(`Request: ${this.sanitizer.sanitize(request)}`);\n  }\n\n  private logResponse(response: HttpOperationResponse): HttpOperationResponse {\n    this.logger(`Response status code: ${response.status}`);\n    this.logger(`Headers: ${this.sanitizer.sanitize(response.headers)}`);\n    return response;\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { Mapper } from \"./serializer\";\nimport { QueryCollectionFormat } from \"./queryCollectionFormat\";\n\n/**\n * A path which describes how to access a particular property in a given object data source. May be a single property name, an array that denotes nested property names, or a set of multiple named properties with paths in the case of complex object values.\n */\nexport type ParameterPath = string | string[] | { [propertyName: string]: ParameterPath };\n\n/**\n * A common interface that all Operation parameter's extend.\n */\nexport interface OperationParameter {\n  /**\n   * The path to this parameter's value in OperationArguments or the object that contains paths for\n   * each property's value in OperationArguments.\n   */\n  parameterPath: ParameterPath;\n\n  /**\n   * The mapper that defines how to validate and serialize this parameter's value.\n   */\n  mapper: Mapper;\n}\n\n/**\n * A parameter for an operation that will be substituted into the operation's request URL.\n */\nexport interface OperationURLParameter extends OperationParameter {\n  /**\n   * Whether or not to skip encoding the URL parameter's value before adding it to the URL.\n   */\n  skipEncoding?: boolean;\n}\n\n/**\n * A parameter for an operation that will be added as a query parameter to the operation's HTTP\n * request.\n */\nexport interface OperationQueryParameter extends OperationParameter {\n  /**\n   * Whether or not to skip encoding the query parameter's value before adding it to the URL.\n   */\n  skipEncoding?: boolean;\n\n  /**\n   * If this query parameter's value is a collection, what type of format should the value be\n   * converted to.\n   */\n  collectionFormat?: QueryCollectionFormat;\n}\n\n/**\n * Get the path to this parameter's value as a dotted string (a.b.c).\n * @param parameter - The parameter to get the path string for.\n * @returns The path to this parameter's value as a dotted string.\n */\nexport function getPathStringFromParameter(parameter: OperationParameter): string {\n  return getPathStringFromParameterPath(parameter.parameterPath, parameter.mapper);\n}\n\nexport function getPathStringFromParameterPath(\n  parameterPath: ParameterPath,\n  mapper: Mapper\n): string {\n  let result: string;\n  if (typeof parameterPath === \"string\") {\n    result = parameterPath;\n  } else if (Array.isArray(parameterPath)) {\n    result = parameterPath.join(\".\");\n  } else {\n    result = mapper.serializedName!;\n  }\n  return result;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { MapperType, Serializer } from \"./serializer\";\nimport {\n  OperationParameter,\n  OperationQueryParameter,\n  OperationURLParameter,\n} from \"./operationParameter\";\nimport { HttpMethods } from \"./webResource\";\nimport { OperationResponse } from \"./operationResponse\";\n\n/**\n * A specification that defines how to perform a particular service operation over HTTP, including how to properly serialize request information into and deserialize response information into an object payload returnable by the {@link ServiceClient}.\n */\nexport interface OperationSpec {\n  /**\n   * The serializer to use in this operation.\n   */\n  readonly serializer: Serializer;\n\n  /**\n   * The HTTP method that should be used by requests for this operation.\n   */\n  readonly httpMethod: HttpMethods;\n\n  /**\n   * The URL that was provided in the service's specification. This will still have all of the URL\n   * template variables in it. If this is not provided when the OperationSpec is created, then it\n   * will be populated by a \"baseUri\" property on the ServiceClient.\n   */\n  readonly baseUrl?: string;\n\n  /**\n   * The fixed path for this operation's URL. This will still have all of the URL template variables\n   * in it.\n   */\n  readonly path?: string;\n\n  /**\n   * The content type of the request body. This value will be used as the \"Content-Type\" header if\n   * it is provided.\n   */\n  readonly contentType?: string;\n\n  /**\n   * The media type of the request body.\n   * This value can be used to aide in serialization if it is provided.\n   */\n  readonly mediaType?:\n    | \"json\"\n    | \"xml\"\n    | \"form\"\n    | \"binary\"\n    | \"multipart\"\n    | \"text\"\n    | \"unknown\"\n    | string;\n  /**\n   * The parameter that will be used to construct the HTTP request's body.\n   */\n  readonly requestBody?: OperationParameter;\n\n  /**\n   * Whether or not this operation uses XML request and response bodies.\n   */\n  readonly isXML?: boolean;\n\n  /**\n   * The parameters to the operation method that will be substituted into the constructed URL.\n   */\n  readonly urlParameters?: ReadonlyArray<OperationURLParameter>;\n\n  /**\n   * The parameters to the operation method that will be added to the constructed URL's query.\n   */\n  readonly queryParameters?: ReadonlyArray<OperationQueryParameter>;\n\n  /**\n   * The parameters to the operation method that will be converted to headers on the operation's\n   * HTTP request.\n   */\n  readonly headerParameters?: ReadonlyArray<OperationParameter>;\n\n  /**\n   * The parameters to the operation method that will be used to create a formdata body for the\n   * operation's HTTP request.\n   */\n  readonly formDataParameters?: ReadonlyArray<OperationParameter>;\n\n  /**\n   * The different types of responses that this operation can return based on what status code is\n   * returned.\n   */\n  readonly responses: { [responseCode: string]: OperationResponse };\n}\n\n/**\n * Gets the list of status codes for streaming responses.\n * @internal\n */\nexport function getStreamResponseStatusCodes(operationSpec: OperationSpec): Set<number> {\n  const result = new Set<number>();\n  for (const statusCode in operationSpec.responses) {\n    const operationResponse = operationSpec.responses[statusCode];\n    if (\n      operationResponse.bodyMapper &&\n      operationResponse.bodyMapper.type.name === MapperType.Stream\n    ) {\n      result.add(Number(statusCode));\n    }\n  }\n  return result;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as os from \"os\";\nimport { Constants } from \"../util/constants\";\nimport { TelemetryInfo } from \"./userAgentPolicy\";\n\nexport function getDefaultUserAgentKey(): string {\n  return Constants.HeaderConstants.USER_AGENT;\n}\n\nexport function getPlatformSpecificData(): TelemetryInfo[] {\n  const runtimeInfo = {\n    key: \"Node\",\n    value: process.version,\n  };\n\n  const osInfo = {\n    key: \"OS\",\n    value: `(${os.arch()}-${os.type()}-${os.release()})`,\n  };\n\n  return [runtimeInfo, osInfo];\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"./requestPolicy\";\nimport { getDefaultUserAgentKey, getPlatformSpecificData } from \"./msRestUserAgentPolicy\";\nimport { Constants } from \"../util/constants\";\nimport { HttpHeaders } from \"../httpHeaders\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\n\n/**\n * Telemetry information. Key/value pairs to include inside the User-Agent string.\n */\nexport type TelemetryInfo = { key?: string; value?: string };\n\n/**\n * Options for adding user agent details to outgoing requests.\n */\nexport interface UserAgentOptions {\n  /**\n   * String prefix to add to the user agent for outgoing requests.\n   * Defaults to an empty string.\n   */\n  userAgentPrefix?: string;\n}\n\nfunction getRuntimeInfo(): TelemetryInfo[] {\n  const msRestRuntime = {\n    key: \"core-http\",\n    value: Constants.coreHttpVersion,\n  };\n\n  return [msRestRuntime];\n}\n\nfunction getUserAgentString(\n  telemetryInfo: TelemetryInfo[],\n  keySeparator = \" \",\n  valueSeparator = \"/\"\n): string {\n  return telemetryInfo\n    .map((info) => {\n      const value = info.value ? `${valueSeparator}${info.value}` : \"\";\n      return `${info.key}${value}`;\n    })\n    .join(keySeparator);\n}\n\nexport const getDefaultUserAgentHeaderName = getDefaultUserAgentKey;\n\n/**\n * The default approach to generate user agents.\n * Uses static information from this package, plus system information available from the runtime.\n */\nexport function getDefaultUserAgentValue(): string {\n  const runtimeInfo = getRuntimeInfo();\n  const platformSpecificData = getPlatformSpecificData();\n  const userAgent = getUserAgentString(runtimeInfo.concat(platformSpecificData));\n  return userAgent;\n}\n\n/**\n * Returns a policy that adds the user agent header to outgoing requests based on the given {@link TelemetryInfo}.\n * @param userAgentData - Telemetry information.\n * @returns A new {@link UserAgentPolicy}.\n */\nexport function userAgentPolicy(userAgentData?: TelemetryInfo): RequestPolicyFactory {\n  const key: string =\n    !userAgentData || userAgentData.key === undefined || userAgentData.key === null\n      ? getDefaultUserAgentKey()\n      : userAgentData.key;\n  const value: string =\n    !userAgentData || userAgentData.value === undefined || userAgentData.value === null\n      ? getDefaultUserAgentValue()\n      : userAgentData.value;\n\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new UserAgentPolicy(nextPolicy, options, key, value);\n    },\n  };\n}\n\n/**\n * A policy that adds the user agent header to outgoing requests based on the given {@link TelemetryInfo}.\n */\nexport class UserAgentPolicy extends BaseRequestPolicy {\n  constructor(\n    readonly _nextPolicy: RequestPolicy,\n    readonly _options: RequestPolicyOptions,\n    protected headerKey: string,\n    protected headerValue: string\n  ) {\n    super(_nextPolicy, _options);\n  }\n\n  sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    this.addUserAgentHeader(request);\n    return this._nextPolicy.sendRequest(request);\n  }\n\n  /**\n   * Adds the user agent header to the outgoing request.\n   */\n  addUserAgentHeader(request: WebResourceLike): void {\n    if (!request.headers) {\n      request.headers = new HttpHeaders();\n    }\n\n    if (!request.headers.get(this.headerKey) && this.headerValue) {\n      request.headers.set(this.headerKey, this.headerValue);\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * The format that will be used to join an array of values together for a query parameter value.\n */\nexport enum QueryCollectionFormat {\n  /**\n   * CSV: Each pair of segments joined by a single comma.\n   */\n  Csv = \",\",\n  /**\n   * SSV: Each pair of segments joined by a single space character.\n   */\n  Ssv = \" \",\n  /**\n   * TSV: Each pair of segments joined by a single tab character.\n   */\n  Tsv = \"\\t\",\n  /**\n   * Pipes: Each pair of segments joined by a single pipe character.\n   */\n  Pipes = \"|\",\n  /**\n   * Denotes this is an array of values that should be passed to the server in multiple key/value pairs, e.g. `?queryParam=value1&queryParam=value2`\n   */\n  Multi = \"Multi\",\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"../policies/requestPolicy\";\nimport { Constants } from \"../util/constants\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\nimport { delay } from \"@azure/core-util\";\n\n// #region Access Token Cycler\n\n/**\n * A function that gets a promise of an access token and allows providing\n * options.\n *\n * @param options - the options to pass to the underlying token provider\n */\ntype AccessTokenGetter = (options: GetTokenOptions) => Promise<AccessToken>;\n\ninterface TokenCyclerOptions {\n  /**\n   * The window of time before token expiration during which the token will be\n   * considered unusable due to risk of the token expiring before sending the\n   * request.\n   *\n   * This will only become meaningful if the refresh fails for over\n   * (refreshWindow - forcedRefreshWindow) milliseconds.\n   */\n  forcedRefreshWindowInMs: number;\n  /**\n   * Interval in milliseconds to retry failed token refreshes.\n   */\n  retryIntervalInMs: number;\n  /**\n   * The window of time before token expiration during which\n   * we will attempt to refresh the token.\n   */\n  refreshWindowInMs: number;\n}\n\n// Default options for the cycler if none are provided\nexport const DEFAULT_CYCLER_OPTIONS: TokenCyclerOptions = {\n  forcedRefreshWindowInMs: 1000, // Force waiting for a refresh 1s before the token expires\n  retryIntervalInMs: 3000, // Allow refresh attempts every 3s\n  refreshWindowInMs: 1000 * 60 * 2, // Start refreshing 2m before expiry\n};\n\n/**\n * Converts an an unreliable access token getter (which may resolve with null)\n * into an AccessTokenGetter by retrying the unreliable getter in a regular\n * interval.\n *\n * @param getAccessToken - a function that produces a promise of an access\n * token that may fail by returning null\n * @param retryIntervalInMs - the time (in milliseconds) to wait between retry\n * attempts\n * @param timeoutInMs - the timestamp after which the refresh attempt will fail,\n * throwing an exception\n * @returns - a promise that, if it resolves, will resolve with an access token\n */\nasync function beginRefresh(\n  getAccessToken: () => Promise<AccessToken | null>,\n  retryIntervalInMs: number,\n  timeoutInMs: number\n): Promise<AccessToken> {\n  // This wrapper handles exceptions gracefully as long as we haven't exceeded\n  // the timeout.\n  async function tryGetAccessToken(): Promise<AccessToken | null> {\n    if (Date.now() < timeoutInMs) {\n      try {\n        return await getAccessToken();\n      } catch {\n        return null;\n      }\n    } else {\n      const finalToken = await getAccessToken();\n\n      // Timeout is up, so throw if it's still null\n      if (finalToken === null) {\n        throw new Error(\"Failed to refresh access token.\");\n      }\n\n      return finalToken;\n    }\n  }\n\n  let token: AccessToken | null = await tryGetAccessToken();\n\n  while (token === null) {\n    await delay(retryIntervalInMs);\n\n    token = await tryGetAccessToken();\n  }\n\n  return token;\n}\n\n/**\n * Creates a token cycler from a credential, scopes, and optional settings.\n *\n * A token cycler represents a way to reliably retrieve a valid access token\n * from a TokenCredential. It will handle initializing the token, refreshing it\n * when it nears expiration, and synchronizes refresh attempts to avoid\n * concurrency hazards.\n *\n * @param credential - the underlying TokenCredential that provides the access\n * token\n * @param scopes - the scopes to request authorization for\n * @param tokenCyclerOptions - optionally override default settings for the cycler\n *\n * @returns - a function that reliably produces a valid access token\n */\nfunction createTokenCycler(\n  credential: TokenCredential,\n  scopes: string | string[],\n  tokenCyclerOptions?: Partial<TokenCyclerOptions>\n): AccessTokenGetter {\n  let refreshWorker: Promise<AccessToken> | null = null;\n  let token: AccessToken | null = null;\n\n  const options = {\n    ...DEFAULT_CYCLER_OPTIONS,\n    ...tokenCyclerOptions,\n  };\n\n  /**\n   * This little holder defines several predicates that we use to construct\n   * the rules of refreshing the token.\n   */\n  const cycler = {\n    /**\n     * Produces true if a refresh job is currently in progress.\n     */\n    get isRefreshing(): boolean {\n      return refreshWorker !== null;\n    },\n    /**\n     * Produces true if the cycler SHOULD refresh (we are within the refresh\n     * window and not already refreshing)\n     */\n    get shouldRefresh(): boolean {\n      return (\n        !cycler.isRefreshing &&\n        (token?.expiresOnTimestamp ?? 0) - options.refreshWindowInMs < Date.now()\n      );\n    },\n    /**\n     * Produces true if the cycler MUST refresh (null or nearly-expired\n     * token).\n     */\n    get mustRefresh(): boolean {\n      return (\n        token === null || token.expiresOnTimestamp - options.forcedRefreshWindowInMs < Date.now()\n      );\n    },\n  };\n\n  /**\n   * Starts a refresh job or returns the existing job if one is already\n   * running.\n   */\n  function refresh(getTokenOptions: GetTokenOptions): Promise<AccessToken> {\n    if (!cycler.isRefreshing) {\n      // We bind `scopes` here to avoid passing it around a lot\n      const tryGetAccessToken = (): Promise<AccessToken | null> =>\n        credential.getToken(scopes, getTokenOptions);\n\n      // Take advantage of promise chaining to insert an assignment to `token`\n      // before the refresh can be considered done.\n      refreshWorker = beginRefresh(\n        tryGetAccessToken,\n        options.retryIntervalInMs,\n        // If we don't have a token, then we should timeout immediately\n        token?.expiresOnTimestamp ?? Date.now()\n      )\n        .then((_token) => {\n          refreshWorker = null;\n          token = _token;\n          return token;\n        })\n        .catch((reason) => {\n          // We also should reset the refresher if we enter a failed state.  All\n          // existing awaiters will throw, but subsequent requests will start a\n          // new retry chain.\n          refreshWorker = null;\n          token = null;\n          throw reason;\n        });\n    }\n\n    return refreshWorker as Promise<AccessToken>;\n  }\n\n  return async (tokenOptions: GetTokenOptions): Promise<AccessToken> => {\n    //\n    // Simple rules:\n    // - If we MUST refresh, then return the refresh task, blocking\n    //   the pipeline until a token is available.\n    // - If we SHOULD refresh, then run refresh but don't return it\n    //   (we can still use the cached token).\n    // - Return the token, since it's fine if we didn't return in\n    //   step 1.\n    //\n\n    if (cycler.mustRefresh) return refresh(tokenOptions);\n\n    if (cycler.shouldRefresh) {\n      refresh(tokenOptions);\n    }\n\n    return token as AccessToken;\n  };\n}\n\n// #endregion\n\n/**\n * Creates a new factory for a RequestPolicy that applies a bearer token to\n * the requests' `Authorization` headers.\n *\n * @param credential - The TokenCredential implementation that can supply the bearer token.\n * @param scopes - The scopes for which the bearer token applies.\n */\nexport function bearerTokenAuthenticationPolicy(\n  credential: TokenCredential,\n  scopes: string | string[]\n): RequestPolicyFactory {\n  // This simple function encapsulates the entire process of reliably retrieving the token\n  const getToken = createTokenCycler(credential, scopes /* , options */);\n\n  class BearerTokenAuthenticationPolicy extends BaseRequestPolicy {\n    public constructor(nextPolicy: RequestPolicy, options: RequestPolicyOptions) {\n      super(nextPolicy, options);\n    }\n\n    public async sendRequest(webResource: WebResourceLike): Promise<HttpOperationResponse> {\n      if (!webResource.url.toLowerCase().startsWith(\"https://\")) {\n        throw new Error(\n          \"Bearer token authentication is not permitted for non-TLS protected (non-https) URLs.\"\n        );\n      }\n\n      const { token } = await getToken({\n        abortSignal: webResource.abortSignal,\n        tracingOptions: {\n          tracingContext: webResource.tracingContext,\n        },\n      });\n      webResource.headers.set(Constants.HeaderConstants.AUTHORIZATION, `Bearer ${token}`);\n      return this._nextPolicy.sendRequest(webResource);\n    }\n  }\n\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new BearerTokenAuthenticationPolicy(nextPolicy, options);\n    },\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"./requestPolicy\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResource } from \"../webResource\";\n\n/**\n * Returns a request policy factory that can be used to create an instance of\n * {@link DisableResponseDecompressionPolicy}.\n */\nexport function disableResponseDecompressionPolicy(): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new DisableResponseDecompressionPolicy(nextPolicy, options);\n    },\n  };\n}\n\n/**\n * A policy to disable response decompression according to Accept-Encoding header\n * https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Encoding\n */\nexport class DisableResponseDecompressionPolicy extends BaseRequestPolicy {\n  /**\n   * Creates an instance of DisableResponseDecompressionPolicy.\n   *\n   * @param nextPolicy -\n   * @param options -\n   */\n  // The parent constructor is protected.\n  /* eslint-disable-next-line @typescript-eslint/no-useless-constructor */\n  constructor(nextPolicy: RequestPolicy, options: RequestPolicyOptions) {\n    super(nextPolicy, options);\n  }\n\n  /**\n   * Sends out request.\n   *\n   * @param request -\n   * @returns\n   */\n  public async sendRequest(request: WebResource): Promise<HttpOperationResponse> {\n    request.decompressResponse = false;\n    return this._nextPolicy.sendRequest(request);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"./requestPolicy\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\n\n/**\n * Creates a policy that assigns a unique request id to outgoing requests.\n * @param requestIdHeaderName - The name of the header to use when assigning the unique id to the request.\n */\nexport function generateClientRequestIdPolicy(\n  requestIdHeaderName = \"x-ms-client-request-id\"\n): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new GenerateClientRequestIdPolicy(nextPolicy, options, requestIdHeaderName);\n    },\n  };\n}\n\nexport class GenerateClientRequestIdPolicy extends BaseRequestPolicy {\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    private _requestIdHeaderName: string\n  ) {\n    super(nextPolicy, options);\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    if (!request.headers.contains(this._requestIdHeaderName)) {\n      request.headers.set(this._requestIdHeaderName, request.requestId);\n    }\n    return this._nextPolicy.sendRequest(request);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { DefaultHttpClient } from \"./defaultHttpClient\";\nimport { HttpClient } from \"./httpClient\";\n\nlet cachedHttpClient: HttpClient | undefined;\n\nexport function getCachedDefaultHttpClient(): HttpClient {\n  if (!cachedHttpClient) {\n    cachedHttpClient = new DefaultHttpClient();\n  }\n\n  return cachedHttpClient;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n// BaseRequestPolicy has a protected constructor.\n/* eslint-disable @typescript-eslint/no-useless-constructor */\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"./requestPolicy\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\n\nexport function ndJsonPolicy(): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new NdJsonPolicy(nextPolicy, options);\n    },\n  };\n}\n\n/**\n * NdJsonPolicy that formats a JSON array as newline-delimited JSON\n */\nclass NdJsonPolicy extends BaseRequestPolicy {\n  /**\n   * Creates an instance of KeepAlivePolicy.\n   */\n  constructor(nextPolicy: RequestPolicy, options: RequestPolicyOptions) {\n    super(nextPolicy, options);\n  }\n\n  /**\n   * Sends a request.\n   */\n  public async sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    // There currently isn't a good way to bypass the serializer\n    if (typeof request.body === \"string\" && request.body.startsWith(\"[\")) {\n      const body = JSON.parse(request.body);\n      if (Array.isArray(body)) {\n        request.body = body.map((item) => JSON.stringify(item) + \"\\n\").join(\"\");\n      }\n    }\n    return this._nextPolicy.sendRequest(request);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"./requestPolicy\";\nimport { Constants } from \"../util/constants\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { ProxySettings } from \"../serviceClient\";\nimport { URLBuilder } from \"../url\";\nimport { WebResourceLike } from \"../webResource\";\nimport { getEnvironmentValue } from \"../util/utils\";\n\n/**\n * Stores the patterns specified in NO_PROXY environment variable.\n * @internal\n */\nexport const globalNoProxyList: string[] = [];\nlet noProxyListLoaded: boolean = false;\n\n/** A cache of whether a host should bypass the proxy. */\nconst globalBypassedMap: Map<string, boolean> = new Map();\n\nfunction loadEnvironmentProxyValue(): string | undefined {\n  if (!process) {\n    return undefined;\n  }\n\n  const httpsProxy = getEnvironmentValue(Constants.HTTPS_PROXY);\n  const allProxy = getEnvironmentValue(Constants.ALL_PROXY);\n  const httpProxy = getEnvironmentValue(Constants.HTTP_PROXY);\n\n  return httpsProxy || allProxy || httpProxy;\n}\n\n/**\n * Check whether the host of a given `uri` matches any pattern in the no proxy list.\n * If there's a match, any request sent to the same host shouldn't have the proxy settings set.\n * This implementation is a port of https://github.com/Azure/azure-sdk-for-net/blob/8cca811371159e527159c7eb65602477898683e2/sdk/core/Azure.Core/src/Pipeline/Internal/HttpEnvironmentProxy.cs#L210\n */\nfunction isBypassed(\n  uri: string,\n  noProxyList: string[],\n  bypassedMap?: Map<string, boolean>\n): boolean | undefined {\n  if (noProxyList.length === 0) {\n    return false;\n  }\n  const host = URLBuilder.parse(uri).getHost()!;\n  if (bypassedMap?.has(host)) {\n    return bypassedMap.get(host);\n  }\n  let isBypassedFlag = false;\n  for (const pattern of noProxyList) {\n    if (pattern[0] === \".\") {\n      // This should match either domain it self or any subdomain or host\n      // .foo.com will match foo.com it self or *.foo.com\n      if (host.endsWith(pattern)) {\n        isBypassedFlag = true;\n      } else {\n        if (host.length === pattern.length - 1 && host === pattern.slice(1)) {\n          isBypassedFlag = true;\n        }\n      }\n    } else {\n      if (host === pattern) {\n        isBypassedFlag = true;\n      }\n    }\n  }\n  bypassedMap?.set(host, isBypassedFlag);\n  return isBypassedFlag;\n}\n\n/**\n * @internal\n */\nexport function loadNoProxy(): string[] {\n  const noProxy = getEnvironmentValue(Constants.NO_PROXY);\n  noProxyListLoaded = true;\n  if (noProxy) {\n    return noProxy\n      .split(\",\")\n      .map((item) => item.trim())\n      .filter((item) => item.length);\n  }\n\n  return [];\n}\n\n/**\n * Converts a given URL of a proxy server into `ProxySettings` or attempts to retrieve `ProxySettings` from the current environment if one is not passed.\n * @param proxyUrl - URL of the proxy\n * @returns The default proxy settings, or undefined.\n */\nexport function getDefaultProxySettings(proxyUrl?: string): ProxySettings | undefined {\n  if (!proxyUrl) {\n    proxyUrl = loadEnvironmentProxyValue();\n    if (!proxyUrl) {\n      return undefined;\n    }\n  }\n\n  const { username, password, urlWithoutAuth } = extractAuthFromUrl(proxyUrl);\n  const parsedUrl = URLBuilder.parse(urlWithoutAuth);\n  const schema = parsedUrl.getScheme() ? parsedUrl.getScheme() + \"://\" : \"\";\n  return {\n    host: schema + parsedUrl.getHost(),\n    port: Number.parseInt(parsedUrl.getPort() || \"80\"),\n    username,\n    password,\n  };\n}\n\n/**\n * A policy that allows one to apply proxy settings to all requests.\n * If not passed static settings, they will be retrieved from the HTTPS_PROXY\n * or HTTP_PROXY environment variables.\n * @param proxySettings - ProxySettings to use on each request.\n * @param options - additional settings, for example, custom NO_PROXY patterns\n */\nexport function proxyPolicy(\n  proxySettings?: ProxySettings,\n  options?: {\n    /** a list of patterns to override those loaded from NO_PROXY environment variable. */\n    customNoProxyList?: string[];\n  }\n): RequestPolicyFactory {\n  if (!proxySettings) {\n    proxySettings = getDefaultProxySettings();\n  }\n  if (!noProxyListLoaded) {\n    globalNoProxyList.push(...loadNoProxy());\n  }\n  return {\n    create: (nextPolicy: RequestPolicy, requestPolicyOptions: RequestPolicyOptions) => {\n      return new ProxyPolicy(\n        nextPolicy,\n        requestPolicyOptions,\n        proxySettings!,\n        options?.customNoProxyList\n      );\n    },\n  };\n}\n\nfunction extractAuthFromUrl(url: string): {\n  username?: string;\n  password?: string;\n  urlWithoutAuth: string;\n} {\n  const atIndex = url.indexOf(\"@\");\n  if (atIndex === -1) {\n    return { urlWithoutAuth: url };\n  }\n\n  const schemeIndex = url.indexOf(\"://\");\n  const authStart = schemeIndex !== -1 ? schemeIndex + 3 : 0;\n  const auth = url.substring(authStart, atIndex);\n  const colonIndex = auth.indexOf(\":\");\n  const hasPassword = colonIndex !== -1;\n  const username = hasPassword ? auth.substring(0, colonIndex) : auth;\n  const password = hasPassword ? auth.substring(colonIndex + 1) : undefined;\n  const urlWithoutAuth = url.substring(0, authStart) + url.substring(atIndex + 1);\n  return {\n    username,\n    password,\n    urlWithoutAuth,\n  };\n}\n\nexport class ProxyPolicy extends BaseRequestPolicy {\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    public proxySettings: ProxySettings,\n    private customNoProxyList?: string[]\n  ) {\n    super(nextPolicy, options);\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    if (\n      !request.proxySettings &&\n      !isBypassed(\n        request.url,\n        this.customNoProxyList ?? globalNoProxyList,\n        this.customNoProxyList ? undefined : globalBypassedMap\n      )\n    ) {\n      request.proxySettings = this.proxySettings;\n    }\n    return this._nextPolicy.sendRequest(request);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as utils from \"../util/utils\";\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"./requestPolicy\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\nimport { delay } from \"@azure/core-util\";\n\nexport function rpRegistrationPolicy(retryTimeout = 30): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new RPRegistrationPolicy(nextPolicy, options, retryTimeout);\n    },\n  };\n}\n\nexport class RPRegistrationPolicy extends BaseRequestPolicy {\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    readonly _retryTimeout = 30\n  ) {\n    super(nextPolicy, options);\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    return this._nextPolicy\n      .sendRequest(request.clone())\n      .then((response) => registerIfNeeded(this, request, response));\n  }\n}\n\nfunction registerIfNeeded(\n  policy: RPRegistrationPolicy,\n  request: WebResourceLike,\n  response: HttpOperationResponse\n): Promise<HttpOperationResponse> {\n  if (response.status === 409) {\n    const rpName = checkRPNotRegisteredError(response.bodyAsText as string);\n    if (rpName) {\n      const urlPrefix = extractSubscriptionUrl(request.url);\n      return (\n        registerRP(policy, urlPrefix, rpName, request)\n          // Autoregistration of ${provider} failed for some reason. We will not return this error\n          // instead will return the initial response with 409 status code back to the user.\n          // do nothing here as we are returning the original response at the end of this method.\n          .catch(() => false)\n          .then((registrationStatus) => {\n            if (registrationStatus) {\n              // Retry the original request. We have to change the x-ms-client-request-id\n              // otherwise Azure endpoint will return the initial 409 (cached) response.\n              request.headers.set(\"x-ms-client-request-id\", utils.generateUuid());\n              return policy._nextPolicy.sendRequest(request.clone());\n            }\n            return response;\n          })\n      );\n    }\n  }\n\n  return Promise.resolve(response);\n}\n\n/**\n * Reuses the headers of the original request and url (if specified).\n * @param originalRequest - The original request\n * @param reuseUrlToo - Should the url from the original request be reused as well. Default false.\n * @returns A new request object with desired headers.\n */\nfunction getRequestEssentials(\n  originalRequest: WebResourceLike,\n  reuseUrlToo = false\n): WebResourceLike {\n  const reqOptions: WebResourceLike = originalRequest.clone();\n  if (reuseUrlToo) {\n    reqOptions.url = originalRequest.url;\n  }\n\n  // We have to change the x-ms-client-request-id otherwise Azure endpoint\n  // will return the initial 409 (cached) response.\n  reqOptions.headers.set(\"x-ms-client-request-id\", utils.generateUuid());\n\n  // Set content-type to application/json\n  reqOptions.headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n\n  return reqOptions;\n}\n\n/**\n * Validates the error code and message associated with 409 response status code. If it matches to that of\n * RP not registered then it returns the name of the RP else returns undefined.\n * @param body - The response body received after making the original request.\n * @returns The name of the RP if condition is satisfied else undefined.\n */\nfunction checkRPNotRegisteredError(body: string): string {\n  let result, responseBody;\n  if (body) {\n    try {\n      responseBody = JSON.parse(body);\n    } catch (err: any) {\n      // do nothing;\n    }\n    if (\n      responseBody &&\n      responseBody.error &&\n      responseBody.error.message &&\n      responseBody.error.code &&\n      responseBody.error.code === \"MissingSubscriptionRegistration\"\n    ) {\n      const matchRes = responseBody.error.message.match(/.*'(.*)'/i);\n      if (matchRes) {\n        result = matchRes.pop();\n      }\n    }\n  }\n  return result;\n}\n\n/**\n * Extracts the first part of the URL, just after subscription:\n * https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/\n * @param url - The original request url\n * @returns The url prefix as explained above.\n */\nfunction extractSubscriptionUrl(url: string): string {\n  let result;\n  const matchRes = url.match(/.*\\/subscriptions\\/[a-f0-9-]+\\//gi);\n  if (matchRes && matchRes[0]) {\n    result = matchRes[0];\n  } else {\n    throw new Error(`Unable to extract subscriptionId from the given url - ${url}.`);\n  }\n  return result;\n}\n\n/**\n * Registers the given provider.\n * @param policy - The RPRegistrationPolicy this function is being called against.\n * @param urlPrefix - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/\n * @param provider - The provider name to be registered.\n * @param originalRequest - The original request sent by the user that returned a 409 response\n * with a message that the provider is not registered.\n */\nasync function registerRP(\n  policy: RPRegistrationPolicy,\n  urlPrefix: string,\n  provider: string,\n  originalRequest: WebResourceLike\n): Promise<boolean> {\n  const postUrl = `${urlPrefix}providers/${provider}/register?api-version=2016-02-01`;\n  const getUrl = `${urlPrefix}providers/${provider}?api-version=2016-02-01`;\n  const reqOptions = getRequestEssentials(originalRequest);\n  reqOptions.method = \"POST\";\n  reqOptions.url = postUrl;\n\n  const response = await policy._nextPolicy.sendRequest(reqOptions);\n  if (response.status !== 200) {\n    throw new Error(`Autoregistration of ${provider} failed. Please try registering manually.`);\n  }\n  return getRegistrationStatus(policy, getUrl, originalRequest);\n}\n\n/**\n * Polls the registration status of the provider that was registered. Polling happens at an interval of 30 seconds.\n * Polling will happen till the registrationState property of the response body is \"Registered\".\n * @param policy - The RPRegistrationPolicy this function is being called against.\n * @param url - The request url for polling\n * @param originalRequest - The original request sent by the user that returned a 409 response\n * with a message that the provider is not registered.\n * @returns True if RP Registration is successful.\n */\nasync function getRegistrationStatus(\n  policy: RPRegistrationPolicy,\n  url: string,\n  originalRequest: WebResourceLike\n): Promise<boolean> {\n  const reqOptions: any = getRequestEssentials(originalRequest);\n  reqOptions.url = url;\n  reqOptions.method = \"GET\";\n\n  const res = await policy._nextPolicy.sendRequest(reqOptions);\n  const obj = res.parsedBody;\n  if (res.parsedBody && obj.registrationState && obj.registrationState === \"Registered\") {\n    return true;\n  } else {\n    await delay(policy._retryTimeout * 1000);\n    return getRegistrationStatus(policy, url, originalRequest);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"./requestPolicy\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { ServiceClientCredentials } from \"../credentials/serviceClientCredentials\";\nimport { WebResourceLike } from \"../webResource\";\n\n/**\n * Creates a policy that signs outgoing requests by calling to the provided `authenticationProvider`'s `signRequest` method.\n * @param authenticationProvider - The authentication provider.\n * @returns An instance of the {@link SigningPolicy}.\n */\nexport function signingPolicy(\n  authenticationProvider: ServiceClientCredentials\n): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new SigningPolicy(nextPolicy, options, authenticationProvider);\n    },\n  };\n}\n\n/**\n * A policy that signs outgoing requests by calling to the provided `authenticationProvider`'s `signRequest` method.\n */\nexport class SigningPolicy extends BaseRequestPolicy {\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    public authenticationProvider: ServiceClientCredentials\n  ) {\n    super(nextPolicy, options);\n  }\n\n  signRequest(request: WebResourceLike): Promise<WebResourceLike> {\n    return this.authenticationProvider.signRequest(request);\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    return this.signRequest(request).then((nextRequest) =>\n      this._nextPolicy.sendRequest(nextRequest)\n    );\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"./requestPolicy\";\nimport {\n  DEFAULT_CLIENT_MAX_RETRY_INTERVAL,\n  DEFAULT_CLIENT_MIN_RETRY_INTERVAL,\n  DEFAULT_CLIENT_RETRY_COUNT,\n  DEFAULT_CLIENT_RETRY_INTERVAL,\n  RetryData,\n  RetryError,\n  isNumber,\n  shouldRetry,\n  updateRetryData,\n} from \"../util/exponentialBackoffStrategy\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\nimport { delay } from \"@azure/core-util\";\n\n/**\n * A policy that retries when there's a system error, identified by the codes \"ETIMEDOUT\", \"ESOCKETTIMEDOUT\", \"ECONNREFUSED\", \"ECONNRESET\" or \"ENOENT\".\n * @param retryCount - Maximum number of retries.\n * @param retryInterval - The client retry interval, in milliseconds.\n * @param minRetryInterval - The minimum retry interval, in milliseconds.\n * @param maxRetryInterval - The maximum retry interval, in milliseconds.\n * @returns An instance of the {@link SystemErrorRetryPolicy}\n */\nexport function systemErrorRetryPolicy(\n  retryCount?: number,\n  retryInterval?: number,\n  minRetryInterval?: number,\n  maxRetryInterval?: number\n): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new SystemErrorRetryPolicy(\n        nextPolicy,\n        options,\n        retryCount,\n        retryInterval,\n        minRetryInterval,\n        maxRetryInterval\n      );\n    },\n  };\n}\n\n/**\n * A policy that retries when there's a system error, identified by the codes \"ETIMEDOUT\", \"ESOCKETTIMEDOUT\", \"ECONNREFUSED\", \"ECONNRESET\" or \"ENOENT\".\n * @param retryCount - The client retry count.\n * @param retryInterval - The client retry interval, in milliseconds.\n * @param minRetryInterval - The minimum retry interval, in milliseconds.\n * @param maxRetryInterval - The maximum retry interval, in milliseconds.\n */\nexport class SystemErrorRetryPolicy extends BaseRequestPolicy {\n  retryCount: number;\n  retryInterval: number;\n  minRetryInterval: number;\n  maxRetryInterval: number;\n\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    retryCount?: number,\n    retryInterval?: number,\n    minRetryInterval?: number,\n    maxRetryInterval?: number\n  ) {\n    super(nextPolicy, options);\n    this.retryCount = isNumber(retryCount) ? retryCount : DEFAULT_CLIENT_RETRY_COUNT;\n    this.retryInterval = isNumber(retryInterval) ? retryInterval : DEFAULT_CLIENT_RETRY_INTERVAL;\n    this.minRetryInterval = isNumber(minRetryInterval)\n      ? minRetryInterval\n      : DEFAULT_CLIENT_MIN_RETRY_INTERVAL;\n    this.maxRetryInterval = isNumber(maxRetryInterval)\n      ? maxRetryInterval\n      : DEFAULT_CLIENT_MAX_RETRY_INTERVAL;\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    return this._nextPolicy\n      .sendRequest(request.clone())\n      .catch((error) => retry(this, request, error.response, error));\n  }\n}\n\nasync function retry(\n  policy: SystemErrorRetryPolicy,\n  request: WebResourceLike,\n  operationResponse: HttpOperationResponse,\n  err?: RetryError,\n  retryData?: RetryData\n): Promise<HttpOperationResponse> {\n  retryData = updateRetryData(policy, retryData, err);\n\n  function shouldPolicyRetry(_response?: HttpOperationResponse, error?: RetryError): boolean {\n    if (\n      error &&\n      error.code &&\n      (error.code === \"ETIMEDOUT\" ||\n        error.code === \"ESOCKETTIMEDOUT\" ||\n        error.code === \"ECONNREFUSED\" ||\n        error.code === \"ECONNRESET\" ||\n        error.code === \"ENOENT\")\n    ) {\n      return true;\n    }\n    return false;\n  }\n\n  if (shouldRetry(policy.retryCount, shouldPolicyRetry, retryData, operationResponse, err)) {\n    // If previous operation ended with an error and the policy allows a retry, do that\n    try {\n      await delay(retryData.retryInterval);\n      return policy._nextPolicy.sendRequest(request.clone());\n    } catch (nestedErr: any) {\n      return retry(policy, request, operationResponse, nestedErr, retryData);\n    }\n  } else {\n    if (err) {\n      // If the operation failed in the end, return all errors instead of just the last one\n      return Promise.reject(retryData.error);\n    }\n    return operationResponse;\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Maximum number of retries for the throttling retry policy\n */\nexport const DEFAULT_CLIENT_MAX_RETRY_COUNT = 3;\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"./requestPolicy\";\nimport { AbortError } from \"@azure/abort-controller\";\nimport { Constants } from \"../util/constants\";\nimport { DEFAULT_CLIENT_MAX_RETRY_COUNT } from \"../util/throttlingRetryStrategy\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\nimport { delay } from \"@azure/core-util\";\n\ntype ResponseHandler = (\n  httpRequest: WebResourceLike,\n  response: HttpOperationResponse\n) => Promise<HttpOperationResponse>;\nconst StatusCodes = Constants.HttpConstants.StatusCodes;\n\n/**\n * Creates a policy that re-sends the request if the response indicates the request failed because of throttling reasons.\n * For example, if the response contains a `Retry-After` header, it will retry sending the request based on the value of that header.\n *\n * To learn more, please refer to\n * https://docs.microsoft.com/en-us/azure/azure-resource-manager/resource-manager-request-limits,\n * https://docs.microsoft.com/en-us/azure/azure-subscription-service-limits and\n * https://docs.microsoft.com/en-us/azure/virtual-machines/troubleshooting/troubleshooting-throttling-errors\n * @returns\n */\nexport function throttlingRetryPolicy(): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new ThrottlingRetryPolicy(nextPolicy, options);\n    },\n  };\n}\n\nconst StandardAbortMessage = \"The operation was aborted.\";\n\n/**\n * Creates a policy that re-sends the request if the response indicates the request failed because of throttling reasons.\n * For example, if the response contains a `Retry-After` header, it will retry sending the request based on the value of that header.\n *\n * To learn more, please refer to\n * https://docs.microsoft.com/en-us/azure/azure-resource-manager/resource-manager-request-limits,\n * https://docs.microsoft.com/en-us/azure/azure-subscription-service-limits and\n * https://docs.microsoft.com/en-us/azure/virtual-machines/troubleshooting/troubleshooting-throttling-errors\n */\nexport class ThrottlingRetryPolicy extends BaseRequestPolicy {\n  private _handleResponse: ResponseHandler;\n  private numberOfRetries = 0;\n\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    _handleResponse?: ResponseHandler\n  ) {\n    super(nextPolicy, options);\n    this._handleResponse = _handleResponse || this._defaultResponseHandler;\n  }\n\n  public async sendRequest(httpRequest: WebResourceLike): Promise<HttpOperationResponse> {\n    const response = await this._nextPolicy.sendRequest(httpRequest.clone());\n    if (\n      response.status !== StatusCodes.TooManyRequests &&\n      response.status !== StatusCodes.ServiceUnavailable\n    ) {\n      return response;\n    } else {\n      return this._handleResponse(httpRequest, response);\n    }\n  }\n\n  private async _defaultResponseHandler(\n    httpRequest: WebResourceLike,\n    httpResponse: HttpOperationResponse\n  ): Promise<HttpOperationResponse> {\n    const retryAfterHeader: string | undefined = httpResponse.headers.get(\n      Constants.HeaderConstants.RETRY_AFTER\n    );\n\n    if (retryAfterHeader) {\n      const delayInMs: number | undefined =\n        ThrottlingRetryPolicy.parseRetryAfterHeader(retryAfterHeader);\n      if (delayInMs) {\n        this.numberOfRetries += 1;\n\n        await delay(delayInMs, {\n          abortSignal: httpRequest.abortSignal,\n          abortErrorMsg: StandardAbortMessage,\n        });\n\n        if (httpRequest.abortSignal?.aborted) {\n          throw new AbortError(StandardAbortMessage);\n        }\n\n        if (this.numberOfRetries < DEFAULT_CLIENT_MAX_RETRY_COUNT) {\n          return this.sendRequest(httpRequest);\n        } else {\n          return this._nextPolicy.sendRequest(httpRequest);\n        }\n      }\n    }\n\n    return httpResponse;\n  }\n\n  public static parseRetryAfterHeader(headerValue: string): number | undefined {\n    const retryAfterInSeconds = Number(headerValue);\n    if (Number.isNaN(retryAfterInSeconds)) {\n      return ThrottlingRetryPolicy.parseDateRetryAfterHeader(headerValue);\n    } else {\n      return retryAfterInSeconds * 1000;\n    }\n  }\n\n  public static parseDateRetryAfterHeader(headerValue: string): number | undefined {\n    try {\n      const now: number = Date.now();\n      const date: number = Date.parse(headerValue);\n      const diff = date - now;\n\n      return Number.isNaN(diff) ? undefined : diff;\n    } catch (error: any) {\n      return undefined;\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"./requestPolicy\";\nimport {\n  Span,\n  SpanKind,\n  SpanStatusCode,\n  createSpanFunction,\n  getTraceParentHeader,\n  isSpanContextValid,\n} from \"@azure/core-tracing\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\nimport { logger } from \"../log\";\n\nconst createSpan = createSpanFunction({\n  packagePrefix: \"\",\n  namespace: \"\",\n});\n\n/**\n * Options to customize the tracing policy.\n */\nexport interface TracingPolicyOptions {\n  /**\n   * User agent used to better identify the outgoing requests traced by the tracing policy.\n   */\n  userAgent?: string;\n}\n\n/**\n * Creates a policy that wraps outgoing requests with a tracing span.\n * @param tracingOptions - Tracing options.\n * @returns An instance of the {@link TracingPolicy} class.\n */\nexport function tracingPolicy(tracingOptions: TracingPolicyOptions = {}): RequestPolicyFactory {\n  return {\n    create(nextPolicy: RequestPolicy, options: RequestPolicyOptions) {\n      return new TracingPolicy(nextPolicy, options, tracingOptions);\n    },\n  };\n}\n\n/**\n * A policy that wraps outgoing requests with a tracing span.\n */\nexport class TracingPolicy extends BaseRequestPolicy {\n  private userAgent?: string;\n\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    tracingOptions: TracingPolicyOptions\n  ) {\n    super(nextPolicy, options);\n    this.userAgent = tracingOptions.userAgent;\n  }\n\n  public async sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    if (!request.tracingContext) {\n      return this._nextPolicy.sendRequest(request);\n    }\n\n    const span = this.tryCreateSpan(request);\n\n    if (!span) {\n      return this._nextPolicy.sendRequest(request);\n    }\n\n    try {\n      const response = await this._nextPolicy.sendRequest(request);\n      this.tryProcessResponse(span, response);\n      return response;\n    } catch (err: any) {\n      this.tryProcessError(span, err);\n      throw err;\n    }\n  }\n\n  tryCreateSpan(request: WebResourceLike): Span | undefined {\n    try {\n      // Passing spanOptions as part of tracingOptions to maintain compatibility @azure/core-tracing@preview.13 and earlier.\n      // We can pass this as a separate parameter once we upgrade to the latest core-tracing.\n      const { span } = createSpan(`HTTP ${request.method}`, {\n        tracingOptions: {\n          spanOptions: {\n            ...(request as any).spanOptions,\n            kind: SpanKind.CLIENT,\n          },\n          tracingContext: request.tracingContext,\n        },\n      });\n\n      // If the span is not recording, don't do any more work.\n      if (!span.isRecording()) {\n        span.end();\n        return undefined;\n      }\n\n      const namespaceFromContext = request.tracingContext?.getValue(Symbol.for(\"az.namespace\"));\n\n      if (typeof namespaceFromContext === \"string\") {\n        span.setAttribute(\"az.namespace\", namespaceFromContext);\n      }\n\n      span.setAttributes({\n        \"http.method\": request.method,\n        \"http.url\": request.url,\n        requestId: request.requestId,\n      });\n\n      if (this.userAgent) {\n        span.setAttribute(\"http.user_agent\", this.userAgent);\n      }\n\n      // set headers\n      const spanContext = span.spanContext();\n      const traceParentHeader = getTraceParentHeader(spanContext);\n      if (traceParentHeader && isSpanContextValid(spanContext)) {\n        request.headers.set(\"traceparent\", traceParentHeader);\n        const traceState = spanContext.traceState && spanContext.traceState.serialize();\n        // if tracestate is set, traceparent MUST be set, so only set tracestate after traceparent\n        if (traceState) {\n          request.headers.set(\"tracestate\", traceState);\n        }\n      }\n      return span;\n    } catch (error: any) {\n      logger.warning(`Skipping creating a tracing span due to an error: ${error.message}`);\n      return undefined;\n    }\n  }\n\n  private tryProcessError(span: Span, err: any): void {\n    try {\n      span.setStatus({\n        code: SpanStatusCode.ERROR,\n        message: err.message,\n      });\n\n      if (err.statusCode) {\n        span.setAttribute(\"http.status_code\", err.statusCode);\n      }\n      span.end();\n    } catch (error: any) {\n      logger.warning(`Skipping tracing span processing due to an error: ${error.message}`);\n    }\n  }\n\n  private tryProcessResponse(span: Span, response: HttpOperationResponse): void {\n    try {\n      span.setAttribute(\"http.status_code\", response.status);\n      const serviceRequestId = response.headers.get(\"x-ms-request-id\");\n      if (serviceRequestId) {\n        span.setAttribute(\"serviceRequestId\", serviceRequestId);\n      }\n      span.setStatus({\n        code: SpanStatusCode.OK,\n      });\n      span.end();\n    } catch (error: any) {\n      logger.warning(`Skipping tracing span processing due to an error: ${error.message}`);\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as utils from \"./util/utils\";\nimport { CompositeMapper, DictionaryMapper, Mapper, MapperType, Serializer } from \"./serializer\";\nimport {\n  DefaultDeserializationOptions,\n  DeserializationContentTypes,\n  deserializationPolicy,\n} from \"./policies/deserializationPolicy\";\nimport { DefaultKeepAliveOptions, keepAlivePolicy } from \"./policies/keepAlivePolicy\";\nimport { DefaultRedirectOptions, redirectPolicy } from \"./policies/redirectPolicy\";\nimport { DefaultRetryOptions, exponentialRetryPolicy } from \"./policies/exponentialRetryPolicy\";\nimport { HttpOperationResponse, RestResponse } from \"./httpOperationResponse\";\nimport { LogPolicyOptions, logPolicy } from \"./policies/logPolicy\";\nimport {\n  OperationParameter,\n  ParameterPath,\n  getPathStringFromParameter,\n  getPathStringFromParameterPath,\n} from \"./operationParameter\";\nimport { OperationSpec, getStreamResponseStatusCodes } from \"./operationSpec\";\nimport {\n  RequestOptionsBase,\n  RequestPrepareOptions,\n  WebResource,\n  WebResourceLike,\n  isWebResourceLike,\n} from \"./webResource\";\nimport {\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"./policies/requestPolicy\";\nimport { SerializerOptions, XML_ATTRKEY, XML_CHARKEY } from \"./util/serializer.common\";\nimport { ServiceCallback, isNode } from \"./util/utils\";\nimport { TokenCredential, isTokenCredential } from \"@azure/core-auth\";\nimport {\n  getDefaultUserAgentHeaderName,\n  getDefaultUserAgentValue,\n  userAgentPolicy,\n} from \"./policies/userAgentPolicy\";\nimport { HttpClient } from \"./httpClient\";\nimport { HttpPipelineLogger } from \"./httpPipelineLogger\";\nimport { InternalPipelineOptions } from \"./pipelineOptions\";\nimport { OperationArguments } from \"./operationArguments\";\nimport { OperationResponse } from \"./operationResponse\";\nimport { QueryCollectionFormat } from \"./queryCollectionFormat\";\nimport { ServiceClientCredentials } from \"./credentials/serviceClientCredentials\";\nimport { URLBuilder } from \"./url\";\nimport { bearerTokenAuthenticationPolicy } from \"./policies/bearerTokenAuthenticationPolicy\";\nimport { disableResponseDecompressionPolicy } from \"./policies/disableResponseDecompressionPolicy\";\nimport { generateClientRequestIdPolicy } from \"./policies/generateClientRequestIdPolicy\";\nimport { getCachedDefaultHttpClient } from \"./httpClientCache\";\nimport { logger } from \"./log\";\nimport { ndJsonPolicy } from \"./policies/ndJsonPolicy\";\nimport { proxyPolicy } from \"./policies/proxyPolicy\";\nimport { rpRegistrationPolicy } from \"./policies/rpRegistrationPolicy\";\nimport { signingPolicy } from \"./policies/signingPolicy\";\nimport { stringifyXML } from \"./util/xml\";\nimport { systemErrorRetryPolicy } from \"./policies/systemErrorRetryPolicy\";\nimport { throttlingRetryPolicy } from \"./policies/throttlingRetryPolicy\";\nimport { tracingPolicy } from \"./policies/tracingPolicy\";\n\n/**\n * Options to configure a proxy for outgoing requests (Node.js only).\n */\nexport interface ProxySettings {\n  /**\n   * The proxy's host address.\n   */\n  host: string;\n\n  /**\n   * The proxy host's port.\n   */\n  port: number;\n\n  /**\n   * The user name to authenticate with the proxy, if required.\n   */\n  username?: string;\n\n  /**\n   * The password to authenticate with the proxy, if required.\n   */\n  password?: string;\n}\n\n/**\n * An alias of {@link ProxySettings} for future use.\n */\nexport type ProxyOptions = ProxySettings;\n\n/**\n * Options to be provided while creating the client.\n */\nexport interface ServiceClientOptions {\n  /**\n   * An array of factories which get called to create the RequestPolicy pipeline used to send a HTTP\n   * request on the wire, or a function that takes in the defaultRequestPolicyFactories and returns\n   * the requestPolicyFactories that will be used.\n   */\n  requestPolicyFactories?:\n    | RequestPolicyFactory[]\n    | ((defaultRequestPolicyFactories: RequestPolicyFactory[]) => void | RequestPolicyFactory[]);\n  /**\n   * The HttpClient that will be used to send HTTP requests.\n   */\n  httpClient?: HttpClient;\n  /**\n   * The HttpPipelineLogger that can be used to debug RequestPolicies within the HTTP pipeline.\n   */\n  httpPipelineLogger?: HttpPipelineLogger;\n  /**\n   * If set to true, turn off the default retry policy.\n   */\n  noRetryPolicy?: boolean;\n  /**\n   * Gets or sets the retry timeout in seconds for AutomaticRPRegistration. Default value is 30.\n   */\n  rpRegistrationRetryTimeout?: number;\n  /**\n   * Whether or not to generate a client request ID header for each HTTP request.\n   */\n  generateClientRequestIdHeader?: boolean;\n  /**\n   * Whether to include credentials in CORS requests in the browser.\n   * See https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/withCredentials for more information.\n   */\n  withCredentials?: boolean;\n  /**\n   * If specified, a GenerateRequestIdPolicy will be added to the HTTP pipeline that will add a\n   * header to all outgoing requests with this header name and a random UUID as the request ID.\n   */\n  clientRequestIdHeaderName?: string;\n  /**\n   * The content-types that will be associated with JSON or XML serialization.\n   */\n  deserializationContentTypes?: DeserializationContentTypes;\n  /**\n   * The header name to use for the telemetry header while sending the request. If this is not\n   * specified, then \"User-Agent\" will be used when running on Node.js and \"x-ms-useragent\" will\n   * be used when running in a browser.\n   */\n  userAgentHeaderName?: string | ((defaultUserAgentHeaderName: string) => string);\n  /**\n   * The string to be set to the telemetry header while sending the request, or a function that\n   * takes in the default user-agent string and returns the user-agent string that will be used.\n   */\n  userAgent?: string | ((defaultUserAgent: string) => string);\n  /**\n   * Proxy settings which will be used for every HTTP request (Node.js only).\n   */\n  proxySettings?: ProxySettings;\n  /**\n   * If specified, will be used to build the BearerTokenAuthenticationPolicy.\n   */\n  credentialScopes?: string | string[];\n}\n\n/**\n * ServiceClient sends service requests and receives responses.\n */\nexport class ServiceClient {\n  /**\n   * If specified, this is the base URI that requests will be made against for this ServiceClient.\n   * If it is not specified, then all OperationSpecs must contain a baseUrl property.\n   */\n  protected baseUri?: string;\n\n  /**\n   * The default request content type for the service.\n   * Used if no requestContentType is present on an OperationSpec.\n   */\n  protected requestContentType?: string;\n\n  /**\n   * The HTTP client that will be used to send requests.\n   */\n  private readonly _httpClient: HttpClient;\n  private readonly _requestPolicyOptions: RequestPolicyOptions;\n\n  private readonly _requestPolicyFactories: RequestPolicyFactory[];\n  private readonly _withCredentials: boolean;\n\n  /**\n   * The ServiceClient constructor\n   * @param credentials - The credentials used for authentication with the service.\n   * @param options - The service client options that govern the behavior of the client.\n   */\n  constructor(\n    credentials?: TokenCredential | ServiceClientCredentials,\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options */\n    options?: ServiceClientOptions\n  ) {\n    if (!options) {\n      options = {};\n    }\n\n    this._withCredentials = options.withCredentials || false;\n    this._httpClient = options.httpClient || getCachedDefaultHttpClient();\n    this._requestPolicyOptions = new RequestPolicyOptions(options.httpPipelineLogger);\n\n    let requestPolicyFactories: RequestPolicyFactory[];\n    if (Array.isArray(options.requestPolicyFactories)) {\n      logger.info(\"ServiceClient: using custom request policies\");\n      requestPolicyFactories = options.requestPolicyFactories;\n    } else {\n      let authPolicyFactory: RequestPolicyFactory | undefined = undefined;\n      if (isTokenCredential(credentials)) {\n        logger.info(\n          \"ServiceClient: creating bearer token authentication policy from provided credentials\"\n        );\n        // Create a wrapped RequestPolicyFactory here so that we can provide the\n        // correct scope to the BearerTokenAuthenticationPolicy at the first time\n        // one is requested.  This is needed because generated ServiceClient\n        // implementations do not set baseUri until after ServiceClient's constructor\n        // is finished, leaving baseUri empty at the time when it is needed to\n        // build the correct scope name.\n        const wrappedPolicyFactory: () => RequestPolicyFactory = () => {\n          let bearerTokenPolicyFactory: RequestPolicyFactory | undefined = undefined;\n          // eslint-disable-next-line @typescript-eslint/no-this-alias\n          const serviceClient = this;\n          const serviceClientOptions = options;\n          return {\n            create(nextPolicy: RequestPolicy, createOptions: RequestPolicyOptions): RequestPolicy {\n              const credentialScopes = getCredentialScopes(\n                serviceClientOptions,\n                serviceClient.baseUri\n              );\n\n              if (!credentialScopes) {\n                throw new Error(\n                  `When using credential, the ServiceClient must contain a baseUri or a credentialScopes in ServiceClientOptions. Unable to create a bearerTokenAuthenticationPolicy`\n                );\n              }\n\n              if (bearerTokenPolicyFactory === undefined || bearerTokenPolicyFactory === null) {\n                bearerTokenPolicyFactory = bearerTokenAuthenticationPolicy(\n                  credentials,\n                  credentialScopes\n                );\n              }\n\n              return bearerTokenPolicyFactory.create(nextPolicy, createOptions);\n            },\n          };\n        };\n\n        authPolicyFactory = wrappedPolicyFactory();\n      } else if (credentials && typeof credentials.signRequest === \"function\") {\n        logger.info(\"ServiceClient: creating signing policy from provided credentials\");\n        authPolicyFactory = signingPolicy(credentials);\n      } else if (credentials !== undefined && credentials !== null) {\n        throw new Error(\"The credentials argument must implement the TokenCredential interface\");\n      }\n\n      logger.info(\"ServiceClient: using default request policies\");\n      requestPolicyFactories = createDefaultRequestPolicyFactories(authPolicyFactory, options);\n      if (options.requestPolicyFactories) {\n        // options.requestPolicyFactories can also be a function that manipulates\n        // the default requestPolicyFactories array\n        const newRequestPolicyFactories: void | RequestPolicyFactory[] =\n          options.requestPolicyFactories(requestPolicyFactories);\n        if (newRequestPolicyFactories) {\n          requestPolicyFactories = newRequestPolicyFactories;\n        }\n      }\n    }\n    this._requestPolicyFactories = requestPolicyFactories;\n  }\n\n  /**\n   * Send the provided httpRequest.\n   */\n  sendRequest(options: RequestPrepareOptions | WebResourceLike): Promise<HttpOperationResponse> {\n    if (options === null || options === undefined || typeof options !== \"object\") {\n      throw new Error(\"options cannot be null or undefined and it must be of type object.\");\n    }\n\n    let httpRequest: WebResourceLike;\n    try {\n      if (isWebResourceLike(options)) {\n        options.validateRequestProperties();\n        httpRequest = options;\n      } else {\n        httpRequest = new WebResource();\n        httpRequest = httpRequest.prepare(options);\n      }\n    } catch (error: any) {\n      return Promise.reject(error);\n    }\n\n    let httpPipeline: RequestPolicy = this._httpClient;\n    if (this._requestPolicyFactories && this._requestPolicyFactories.length > 0) {\n      for (let i = this._requestPolicyFactories.length - 1; i >= 0; --i) {\n        httpPipeline = this._requestPolicyFactories[i].create(\n          httpPipeline,\n          this._requestPolicyOptions\n        );\n      }\n    }\n    return httpPipeline.sendRequest(httpRequest);\n  }\n\n  /**\n   * Send an HTTP request that is populated using the provided OperationSpec.\n   * @param operationArguments - The arguments that the HTTP request's templated values will be populated from.\n   * @param operationSpec - The OperationSpec to use to populate the httpRequest.\n   * @param callback - The callback to call when the response is received.\n   */\n  async sendOperationRequest(\n    operationArguments: OperationArguments,\n    operationSpec: OperationSpec,\n    callback?: ServiceCallback<any>\n  ): Promise<RestResponse> {\n    if (typeof operationArguments.options === \"function\") {\n      callback = operationArguments.options;\n      operationArguments.options = undefined;\n    }\n\n    const serializerOptions = operationArguments.options?.serializerOptions;\n    const httpRequest: WebResourceLike = new WebResource();\n\n    let result: Promise<RestResponse>;\n    try {\n      const baseUri: string | undefined = operationSpec.baseUrl || this.baseUri;\n      if (!baseUri) {\n        throw new Error(\n          \"If operationSpec.baseUrl is not specified, then the ServiceClient must have a baseUri string property that contains the base URL to use.\"\n        );\n      }\n\n      httpRequest.method = operationSpec.httpMethod;\n      httpRequest.operationSpec = operationSpec;\n\n      const requestUrl: URLBuilder = URLBuilder.parse(baseUri);\n      if (operationSpec.path) {\n        requestUrl.appendPath(operationSpec.path);\n      }\n      if (operationSpec.urlParameters && operationSpec.urlParameters.length > 0) {\n        for (const urlParameter of operationSpec.urlParameters) {\n          let urlParameterValue: string = getOperationArgumentValueFromParameter(\n            this,\n            operationArguments,\n            urlParameter,\n            operationSpec.serializer\n          );\n          urlParameterValue = operationSpec.serializer.serialize(\n            urlParameter.mapper,\n            urlParameterValue,\n            getPathStringFromParameter(urlParameter),\n            serializerOptions\n          );\n          if (!urlParameter.skipEncoding) {\n            urlParameterValue = encodeURIComponent(urlParameterValue);\n          }\n          requestUrl.replaceAll(\n            `{${urlParameter.mapper.serializedName || getPathStringFromParameter(urlParameter)}}`,\n            urlParameterValue\n          );\n        }\n      }\n      if (operationSpec.queryParameters && operationSpec.queryParameters.length > 0) {\n        for (const queryParameter of operationSpec.queryParameters) {\n          let queryParameterValue: any = getOperationArgumentValueFromParameter(\n            this,\n            operationArguments,\n            queryParameter,\n            operationSpec.serializer\n          );\n          if (queryParameterValue !== undefined && queryParameterValue !== null) {\n            queryParameterValue = operationSpec.serializer.serialize(\n              queryParameter.mapper,\n              queryParameterValue,\n              getPathStringFromParameter(queryParameter),\n              serializerOptions\n            );\n            if (\n              queryParameter.collectionFormat !== undefined &&\n              queryParameter.collectionFormat !== null\n            ) {\n              if (queryParameter.collectionFormat === QueryCollectionFormat.Multi) {\n                if (queryParameterValue.length === 0) {\n                  // The collection is empty, no need to try serializing the current queryParam\n                  continue;\n                } else {\n                  for (const index in queryParameterValue) {\n                    const item = queryParameterValue[index];\n                    queryParameterValue[index] =\n                      item === undefined || item === null ? \"\" : item.toString();\n                  }\n                }\n              } else if (\n                queryParameter.collectionFormat === QueryCollectionFormat.Ssv ||\n                queryParameter.collectionFormat === QueryCollectionFormat.Tsv\n              ) {\n                queryParameterValue = queryParameterValue.join(queryParameter.collectionFormat);\n              }\n            }\n            if (!queryParameter.skipEncoding) {\n              if (Array.isArray(queryParameterValue)) {\n                for (const index in queryParameterValue) {\n                  if (\n                    queryParameterValue[index] !== undefined &&\n                    queryParameterValue[index] !== null\n                  ) {\n                    queryParameterValue[index] = encodeURIComponent(queryParameterValue[index]);\n                  }\n                }\n              } else {\n                queryParameterValue = encodeURIComponent(queryParameterValue);\n              }\n            }\n            if (\n              queryParameter.collectionFormat !== undefined &&\n              queryParameter.collectionFormat !== null &&\n              queryParameter.collectionFormat !== QueryCollectionFormat.Multi &&\n              queryParameter.collectionFormat !== QueryCollectionFormat.Ssv &&\n              queryParameter.collectionFormat !== QueryCollectionFormat.Tsv\n            ) {\n              queryParameterValue = queryParameterValue.join(queryParameter.collectionFormat);\n            }\n            requestUrl.setQueryParameter(\n              queryParameter.mapper.serializedName || getPathStringFromParameter(queryParameter),\n              queryParameterValue\n            );\n          }\n        }\n      }\n      httpRequest.url = requestUrl.toString();\n\n      const contentType = operationSpec.contentType || this.requestContentType;\n      if (contentType && operationSpec.requestBody) {\n        httpRequest.headers.set(\"Content-Type\", contentType);\n      }\n\n      if (operationSpec.headerParameters) {\n        for (const headerParameter of operationSpec.headerParameters) {\n          let headerValue: any = getOperationArgumentValueFromParameter(\n            this,\n            operationArguments,\n            headerParameter,\n            operationSpec.serializer\n          );\n          if (headerValue !== undefined && headerValue !== null) {\n            headerValue = operationSpec.serializer.serialize(\n              headerParameter.mapper,\n              headerValue,\n              getPathStringFromParameter(headerParameter),\n              serializerOptions\n            );\n            const headerCollectionPrefix = (headerParameter.mapper as DictionaryMapper)\n              .headerCollectionPrefix;\n            if (headerCollectionPrefix) {\n              for (const key of Object.keys(headerValue)) {\n                httpRequest.headers.set(headerCollectionPrefix + key, headerValue[key]);\n              }\n            } else {\n              httpRequest.headers.set(\n                headerParameter.mapper.serializedName ||\n                  getPathStringFromParameter(headerParameter),\n                headerValue\n              );\n            }\n          }\n        }\n      }\n\n      const options: RequestOptionsBase | undefined = operationArguments.options;\n      if (options) {\n        if (options.customHeaders) {\n          for (const customHeaderName in options.customHeaders) {\n            httpRequest.headers.set(customHeaderName, options.customHeaders[customHeaderName]);\n          }\n        }\n\n        if (options.abortSignal) {\n          httpRequest.abortSignal = options.abortSignal;\n        }\n\n        if (options.timeout) {\n          httpRequest.timeout = options.timeout;\n        }\n\n        if (options.onUploadProgress) {\n          httpRequest.onUploadProgress = options.onUploadProgress;\n        }\n\n        if (options.onDownloadProgress) {\n          httpRequest.onDownloadProgress = options.onDownloadProgress;\n        }\n\n        if (options.spanOptions) {\n          // By passing spanOptions if they exist at runtime, we're backwards compatible with @azure/core-tracing@preview.13 and earlier.\n          (httpRequest as any).spanOptions = options.spanOptions;\n        }\n\n        if (options.tracingContext) {\n          httpRequest.tracingContext = options.tracingContext;\n        }\n\n        if (options.shouldDeserialize !== undefined && options.shouldDeserialize !== null) {\n          httpRequest.shouldDeserialize = options.shouldDeserialize;\n        }\n      }\n\n      httpRequest.withCredentials = this._withCredentials;\n\n      serializeRequestBody(this, httpRequest, operationArguments, operationSpec);\n\n      if (httpRequest.streamResponseStatusCodes === undefined) {\n        httpRequest.streamResponseStatusCodes = getStreamResponseStatusCodes(operationSpec);\n      }\n\n      let rawResponse: HttpOperationResponse;\n      let sendRequestError;\n      try {\n        rawResponse = await this.sendRequest(httpRequest);\n      } catch (error: any) {\n        sendRequestError = error;\n      }\n      if (sendRequestError) {\n        if (sendRequestError.response) {\n          sendRequestError.details = flattenResponse(\n            sendRequestError.response,\n            operationSpec.responses[sendRequestError.statusCode] ||\n              operationSpec.responses[\"default\"]\n          );\n        }\n        result = Promise.reject(sendRequestError);\n      } else {\n        result = Promise.resolve(\n          flattenResponse(rawResponse!, operationSpec.responses[rawResponse!.status])\n        );\n      }\n    } catch (error: any) {\n      result = Promise.reject(error);\n    }\n\n    const cb = callback;\n    if (cb) {\n      result\n        .then((res) => cb(null, res._response.parsedBody, res._response.request, res._response))\n        .catch((err) => cb(err));\n    }\n\n    return result;\n  }\n}\n\nexport function serializeRequestBody(\n  serviceClient: ServiceClient,\n  httpRequest: WebResourceLike,\n  operationArguments: OperationArguments,\n  operationSpec: OperationSpec\n): void {\n  const serializerOptions = operationArguments.options?.serializerOptions ?? {};\n  const updatedOptions: Required<SerializerOptions> = {\n    rootName: serializerOptions.rootName ?? \"\",\n    includeRoot: serializerOptions.includeRoot ?? false,\n    xmlCharKey: serializerOptions.xmlCharKey ?? XML_CHARKEY,\n  };\n\n  const xmlCharKey = serializerOptions.xmlCharKey;\n  if (operationSpec.requestBody && operationSpec.requestBody.mapper) {\n    httpRequest.body = getOperationArgumentValueFromParameter(\n      serviceClient,\n      operationArguments,\n      operationSpec.requestBody,\n      operationSpec.serializer\n    );\n\n    const bodyMapper = operationSpec.requestBody.mapper;\n    const { required, xmlName, xmlElementName, serializedName, xmlNamespace, xmlNamespacePrefix } =\n      bodyMapper;\n    const typeName = bodyMapper.type.name;\n\n    try {\n      if ((httpRequest.body !== undefined && httpRequest.body !== null) || required) {\n        const requestBodyParameterPathString: string = getPathStringFromParameter(\n          operationSpec.requestBody\n        );\n        httpRequest.body = operationSpec.serializer.serialize(\n          bodyMapper,\n          httpRequest.body,\n          requestBodyParameterPathString,\n          updatedOptions\n        );\n\n        const isStream = typeName === MapperType.Stream;\n\n        if (operationSpec.isXML) {\n          const xmlnsKey = xmlNamespacePrefix ? `xmlns:${xmlNamespacePrefix}` : \"xmlns\";\n          const value = getXmlValueWithNamespace(\n            xmlNamespace,\n            xmlnsKey,\n            typeName,\n            httpRequest.body,\n            updatedOptions\n          );\n          if (typeName === MapperType.Sequence) {\n            httpRequest.body = stringifyXML(\n              utils.prepareXMLRootList(\n                value,\n                xmlElementName || xmlName || serializedName!,\n                xmlnsKey,\n                xmlNamespace\n              ),\n              {\n                rootName: xmlName || serializedName,\n                xmlCharKey,\n              }\n            );\n          } else if (!isStream) {\n            httpRequest.body = stringifyXML(value, {\n              rootName: xmlName || serializedName,\n              xmlCharKey,\n            });\n          }\n        } else if (\n          typeName === MapperType.String &&\n          (operationSpec.contentType?.match(\"text/plain\") || operationSpec.mediaType === \"text\")\n        ) {\n          // the String serializer has validated that request body is a string\n          // so just send the string.\n          return;\n        } else if (!isStream) {\n          httpRequest.body = JSON.stringify(httpRequest.body);\n        }\n      }\n    } catch (error: any) {\n      throw new Error(\n        `Error \"${error.message}\" occurred in serializing the payload - ${JSON.stringify(\n          serializedName,\n          undefined,\n          \"  \"\n        )}.`\n      );\n    }\n  } else if (operationSpec.formDataParameters && operationSpec.formDataParameters.length > 0) {\n    httpRequest.formData = {};\n    for (const formDataParameter of operationSpec.formDataParameters) {\n      const formDataParameterValue: any = getOperationArgumentValueFromParameter(\n        serviceClient,\n        operationArguments,\n        formDataParameter,\n        operationSpec.serializer\n      );\n      if (formDataParameterValue !== undefined && formDataParameterValue !== null) {\n        const formDataParameterPropertyName: string =\n          formDataParameter.mapper.serializedName || getPathStringFromParameter(formDataParameter);\n        httpRequest.formData[formDataParameterPropertyName] = operationSpec.serializer.serialize(\n          formDataParameter.mapper,\n          formDataParameterValue,\n          getPathStringFromParameter(formDataParameter),\n          updatedOptions\n        );\n      }\n    }\n  }\n}\n\n/**\n * Adds an xml namespace to the xml serialized object if needed, otherwise it just returns the value itself\n */\nfunction getXmlValueWithNamespace(\n  xmlNamespace: string | undefined,\n  xmlnsKey: string,\n  typeName: string,\n  serializedValue: any,\n  options: Required<SerializerOptions>\n): any {\n  // Composite and Sequence schemas already got their root namespace set during serialization\n  // We just need to add xmlns to the other schema types\n  if (xmlNamespace && ![\"Composite\", \"Sequence\", \"Dictionary\"].includes(typeName)) {\n    const result: any = {};\n    result[options.xmlCharKey] = serializedValue;\n    result[XML_ATTRKEY] = { [xmlnsKey]: xmlNamespace };\n    return result;\n  }\n\n  return serializedValue;\n}\n\nfunction getValueOrFunctionResult(\n  value: undefined | string | ((defaultValue: string) => string),\n  defaultValueCreator: () => string\n): string {\n  let result: string;\n  if (typeof value === \"string\") {\n    result = value;\n  } else {\n    result = defaultValueCreator();\n    if (typeof value === \"function\") {\n      result = value(result);\n    }\n  }\n  return result;\n}\n\nfunction createDefaultRequestPolicyFactories(\n  authPolicyFactory: RequestPolicyFactory | undefined,\n  options: ServiceClientOptions\n): RequestPolicyFactory[] {\n  const factories: RequestPolicyFactory[] = [];\n\n  if (options.generateClientRequestIdHeader) {\n    factories.push(generateClientRequestIdPolicy(options.clientRequestIdHeaderName));\n  }\n\n  if (authPolicyFactory) {\n    factories.push(authPolicyFactory);\n  }\n\n  const userAgentHeaderName: string = getValueOrFunctionResult(\n    options.userAgentHeaderName,\n    getDefaultUserAgentHeaderName\n  );\n  const userAgentHeaderValue: string = getValueOrFunctionResult(\n    options.userAgent,\n    getDefaultUserAgentValue\n  );\n  if (userAgentHeaderName && userAgentHeaderValue) {\n    factories.push(userAgentPolicy({ key: userAgentHeaderName, value: userAgentHeaderValue }));\n  }\n  factories.push(redirectPolicy());\n  factories.push(rpRegistrationPolicy(options.rpRegistrationRetryTimeout));\n\n  if (!options.noRetryPolicy) {\n    factories.push(exponentialRetryPolicy());\n    factories.push(systemErrorRetryPolicy());\n    factories.push(throttlingRetryPolicy());\n  }\n\n  factories.push(deserializationPolicy(options.deserializationContentTypes));\n\n  if (isNode) {\n    factories.push(proxyPolicy(options.proxySettings));\n  }\n\n  factories.push(logPolicy({ logger: logger.info }));\n\n  return factories;\n}\n\n/**\n * Creates an HTTP pipeline based on the given options.\n * @param pipelineOptions - Defines options that are used to configure policies in the HTTP pipeline for an SDK client.\n * @param authPolicyFactory - An optional authentication policy factory to use for signing requests.\n * @returns A set of options that can be passed to create a new {@link ServiceClient}.\n */\nexport function createPipelineFromOptions(\n  pipelineOptions: InternalPipelineOptions,\n  authPolicyFactory?: RequestPolicyFactory\n): ServiceClientOptions {\n  const requestPolicyFactories: RequestPolicyFactory[] = [];\n\n  if (pipelineOptions.sendStreamingJson) {\n    requestPolicyFactories.push(ndJsonPolicy());\n  }\n\n  let userAgentValue = undefined;\n  if (pipelineOptions.userAgentOptions && pipelineOptions.userAgentOptions.userAgentPrefix) {\n    const userAgentInfo: string[] = [];\n    userAgentInfo.push(pipelineOptions.userAgentOptions.userAgentPrefix);\n\n    // Add the default user agent value if it isn't already specified\n    // by the userAgentPrefix option.\n    const defaultUserAgentInfo = getDefaultUserAgentValue();\n    if (userAgentInfo.indexOf(defaultUserAgentInfo) === -1) {\n      userAgentInfo.push(defaultUserAgentInfo);\n    }\n\n    userAgentValue = userAgentInfo.join(\" \");\n  }\n\n  const keepAliveOptions = {\n    ...DefaultKeepAliveOptions,\n    ...pipelineOptions.keepAliveOptions,\n  };\n\n  const retryOptions = {\n    ...DefaultRetryOptions,\n    ...pipelineOptions.retryOptions,\n  };\n\n  const redirectOptions = {\n    ...DefaultRedirectOptions,\n    ...pipelineOptions.redirectOptions,\n  };\n\n  if (isNode) {\n    requestPolicyFactories.push(proxyPolicy(pipelineOptions.proxyOptions));\n  }\n\n  const deserializationOptions = {\n    ...DefaultDeserializationOptions,\n    ...pipelineOptions.deserializationOptions,\n  };\n\n  const loggingOptions: LogPolicyOptions = {\n    ...pipelineOptions.loggingOptions,\n  };\n\n  requestPolicyFactories.push(\n    tracingPolicy({ userAgent: userAgentValue }),\n    keepAlivePolicy(keepAliveOptions),\n    userAgentPolicy({ value: userAgentValue }),\n    generateClientRequestIdPolicy(),\n    deserializationPolicy(deserializationOptions.expectedContentTypes),\n    throttlingRetryPolicy(),\n    systemErrorRetryPolicy(),\n    exponentialRetryPolicy(\n      retryOptions.maxRetries,\n      retryOptions.retryDelayInMs,\n      retryOptions.maxRetryDelayInMs\n    )\n  );\n\n  if (redirectOptions.handleRedirects) {\n    requestPolicyFactories.push(redirectPolicy(redirectOptions.maxRetries));\n  }\n\n  if (authPolicyFactory) {\n    requestPolicyFactories.push(authPolicyFactory);\n  }\n\n  requestPolicyFactories.push(logPolicy(loggingOptions));\n\n  if (isNode && pipelineOptions.decompressResponse === false) {\n    requestPolicyFactories.push(disableResponseDecompressionPolicy());\n  }\n\n  return {\n    httpClient: pipelineOptions.httpClient,\n    requestPolicyFactories,\n  };\n}\n\nexport type PropertyParent = { [propertyName: string]: any };\n\n/**\n * Get the property parent for the property at the provided path when starting with the provided\n * parent object.\n */\nexport function getPropertyParent(parent: PropertyParent, propertyPath: string[]): PropertyParent {\n  if (parent && propertyPath) {\n    const propertyPathLength: number = propertyPath.length;\n    for (let i = 0; i < propertyPathLength - 1; ++i) {\n      const propertyName: string = propertyPath[i];\n      if (!parent[propertyName]) {\n        parent[propertyName] = {};\n      }\n      parent = parent[propertyName];\n    }\n  }\n  return parent;\n}\n\nfunction getOperationArgumentValueFromParameter(\n  serviceClient: ServiceClient,\n  operationArguments: OperationArguments,\n  parameter: OperationParameter,\n  serializer: Serializer\n): any {\n  return getOperationArgumentValueFromParameterPath(\n    serviceClient,\n    operationArguments,\n    parameter.parameterPath,\n    parameter.mapper,\n    serializer\n  );\n}\n\nexport function getOperationArgumentValueFromParameterPath(\n  serviceClient: ServiceClient,\n  operationArguments: OperationArguments,\n  parameterPath: ParameterPath,\n  parameterMapper: Mapper,\n  serializer: Serializer\n): any {\n  let value: any;\n  if (typeof parameterPath === \"string\") {\n    parameterPath = [parameterPath];\n  }\n  const serializerOptions = operationArguments.options?.serializerOptions;\n  if (Array.isArray(parameterPath)) {\n    if (parameterPath.length > 0) {\n      if (parameterMapper.isConstant) {\n        value = parameterMapper.defaultValue;\n      } else {\n        let propertySearchResult: PropertySearchResult = getPropertyFromParameterPath(\n          operationArguments,\n          parameterPath\n        );\n        if (!propertySearchResult.propertyFound) {\n          propertySearchResult = getPropertyFromParameterPath(serviceClient, parameterPath);\n        }\n\n        let useDefaultValue = false;\n        if (!propertySearchResult.propertyFound) {\n          useDefaultValue =\n            parameterMapper.required ||\n            (parameterPath[0] === \"options\" && parameterPath.length === 2);\n        }\n        value = useDefaultValue ? parameterMapper.defaultValue : propertySearchResult.propertyValue;\n      }\n\n      // Serialize just for validation purposes.\n      const parameterPathString: string = getPathStringFromParameterPath(\n        parameterPath,\n        parameterMapper\n      );\n      serializer.serialize(parameterMapper, value, parameterPathString, serializerOptions);\n    }\n  } else {\n    if (parameterMapper.required) {\n      value = {};\n    }\n\n    for (const propertyName in parameterPath) {\n      const propertyMapper: Mapper = (parameterMapper as CompositeMapper).type.modelProperties![\n        propertyName\n      ];\n      const propertyPath: ParameterPath = parameterPath[propertyName];\n      const propertyValue: any = getOperationArgumentValueFromParameterPath(\n        serviceClient,\n        operationArguments,\n        propertyPath,\n        propertyMapper,\n        serializer\n      );\n      // Serialize just for validation purposes.\n      const propertyPathString: string = getPathStringFromParameterPath(\n        propertyPath,\n        propertyMapper\n      );\n      serializer.serialize(propertyMapper, propertyValue, propertyPathString, serializerOptions);\n      if (propertyValue !== undefined && propertyValue !== null) {\n        if (!value) {\n          value = {};\n        }\n        value[propertyName] = propertyValue;\n      }\n    }\n  }\n  return value;\n}\n\ninterface PropertySearchResult {\n  propertyValue?: any;\n  propertyFound: boolean;\n}\n\nfunction getPropertyFromParameterPath(\n  parent: { [parameterName: string]: any },\n  parameterPath: string[]\n): PropertySearchResult {\n  const result: PropertySearchResult = { propertyFound: false };\n  let i = 0;\n  for (; i < parameterPath.length; ++i) {\n    const parameterPathPart: string = parameterPath[i];\n    // Make sure to check inherited properties too, so don't use hasOwnProperty().\n    if (parent !== undefined && parent !== null && parameterPathPart in parent) {\n      parent = parent[parameterPathPart];\n    } else {\n      break;\n    }\n  }\n  if (i === parameterPath.length) {\n    result.propertyValue = parent;\n    result.propertyFound = true;\n  }\n  return result;\n}\n\n/**\n * Parses an {@link HttpOperationResponse} into a normalized HTTP response object ({@link RestResponse}).\n * @param _response - Wrapper object for http response.\n * @param responseSpec - Mappers for how to parse the response properties.\n * @returns - A normalized response object.\n */\nexport function flattenResponse(\n  _response: HttpOperationResponse,\n  responseSpec: OperationResponse | undefined\n): RestResponse {\n  const parsedHeaders = _response.parsedHeaders;\n  const bodyMapper = responseSpec && responseSpec.bodyMapper;\n\n  const addOperationResponse = <T extends Record<string, unknown>>(\n    obj: T\n  ): T & {\n    _response: HttpOperationResponse;\n  } => {\n    return Object.defineProperty(obj, \"_response\", {\n      value: _response,\n    }) as T & {\n      _response: HttpOperationResponse;\n    };\n  };\n\n  if (bodyMapper) {\n    const typeName = bodyMapper.type.name;\n    if (typeName === \"Stream\") {\n      return addOperationResponse({\n        ...parsedHeaders,\n        blobBody: _response.blobBody,\n        readableStreamBody: _response.readableStreamBody,\n      });\n    }\n\n    const modelProperties =\n      (typeName === \"Composite\" && (bodyMapper as CompositeMapper).type.modelProperties) || {};\n    const isPageableResponse = Object.keys(modelProperties).some(\n      (k) => modelProperties[k].serializedName === \"\"\n    );\n    if (typeName === \"Sequence\" || isPageableResponse) {\n      const arrayResponse = [...(_response.parsedBody || [])] as RestResponse & any[];\n\n      for (const key of Object.keys(modelProperties)) {\n        if (modelProperties[key].serializedName) {\n          arrayResponse[key] = _response.parsedBody[key];\n        }\n      }\n\n      if (parsedHeaders) {\n        for (const key of Object.keys(parsedHeaders)) {\n          arrayResponse[key] = parsedHeaders[key];\n        }\n      }\n      addOperationResponse(arrayResponse);\n      return arrayResponse;\n    }\n\n    if (typeName === \"Composite\" || typeName === \"Dictionary\") {\n      return addOperationResponse({\n        ...parsedHeaders,\n        ..._response.parsedBody,\n      });\n    }\n  }\n\n  if (\n    bodyMapper ||\n    _response.request.method === \"HEAD\" ||\n    utils.isPrimitiveType(_response.parsedBody)\n  ) {\n    // primitive body types and HEAD booleans\n    return addOperationResponse({\n      ...parsedHeaders,\n      body: _response.parsedBody,\n    });\n  }\n\n  return addOperationResponse({\n    ...parsedHeaders,\n    ..._response.parsedBody,\n  });\n}\n\nfunction getCredentialScopes(\n  options?: ServiceClientOptions,\n  baseUri?: string\n): string | string[] | undefined {\n  if (options?.credentialScopes) {\n    const scopes = options.credentialScopes;\n    return Array.isArray(scopes)\n      ? scopes.map((scope) => new URL(scope).toString())\n      : new URL(scopes).toString();\n  }\n\n  if (baseUri) {\n    return `${baseUri}/.default`;\n  }\n  return undefined;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n// NOTE: we've moved this code into core-tracing but these functions\n// were a part of the GA'd library and can't be removed until the next major\n// release. They currently get called always, even if tracing is not enabled.\n\nimport { Span, createSpanFunction as coreTracingCreateSpanFunction } from \"@azure/core-tracing\";\nimport { OperationOptions } from \"./operationOptions\";\n\n/**\n * This function is only here for compatibility. Use createSpanFunction in core-tracing.\n *\n * @deprecated This function is only here for compatibility. Use core-tracing instead.\n * @hidden\n */\nexport interface SpanConfig {\n  /**\n   * Package name prefix\n   */\n  packagePrefix: string;\n  /**\n   * Service namespace\n   */\n  namespace: string;\n}\n\n/**\n * This function is only here for compatibility. Use createSpanFunction in core-tracing.\n *\n * @deprecated This function is only here for compatibility. Use createSpanFunction in core-tracing.\n * @hidden\n\n * @param spanConfig - The name of the operation being performed.\n * @param tracingOptions - The options for the underlying http request.\n */\nexport function createSpanFunction(\n  args: SpanConfig\n): <T extends OperationOptions>(\n  operationName: string,\n  operationOptions: T\n) => { span: Span; updatedOptions: T } {\n  return coreTracingCreateSpanFunction(args);\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken } from \"@azure/core-auth\";\n\n/**\n * Defines the default token refresh buffer duration.\n */\nexport const TokenRefreshBufferMs = 2 * 60 * 1000; // 2 Minutes\n\n/**\n * Provides a cache for an AccessToken that was that\n * was returned from a TokenCredential.\n */\nexport interface AccessTokenCache {\n  /**\n   * Sets the cached token.\n   *\n   * @param accessToken - The {@link AccessToken} to be cached or null to\n   *        clear the cached token.\n   */\n  setCachedToken(accessToken: AccessToken | undefined): void;\n\n  /**\n   * Returns the cached {@link AccessToken} or undefined if nothing is cached.\n   */\n  getCachedToken(): AccessToken | undefined;\n}\n\n/**\n * Provides an {@link AccessTokenCache} implementation which clears\n * the cached {@link AccessToken}'s after the expiresOnTimestamp has\n * passed.\n *\n * @deprecated No longer used in the bearer authorization policy.\n */\nexport class ExpiringAccessTokenCache implements AccessTokenCache {\n  private tokenRefreshBufferMs: number;\n  private cachedToken?: AccessToken = undefined;\n\n  /**\n   * Constructs an instance of {@link ExpiringAccessTokenCache} with\n   * an optional expiration buffer time.\n   */\n  constructor(tokenRefreshBufferMs: number = TokenRefreshBufferMs) {\n    this.tokenRefreshBufferMs = tokenRefreshBufferMs;\n  }\n\n  /**\n   * Saves an access token into the internal in-memory cache.\n   * @param accessToken - Access token or undefined to clear the cache.\n   */\n  setCachedToken(accessToken: AccessToken | undefined): void {\n    this.cachedToken = accessToken;\n  }\n\n  /**\n   * Returns the cached access token, or `undefined` if one is not cached or the cached one is expiring soon.\n   */\n  getCachedToken(): AccessToken | undefined {\n    if (\n      this.cachedToken &&\n      Date.now() + this.tokenRefreshBufferMs >= this.cachedToken.expiresOnTimestamp\n    ) {\n      this.cachedToken = undefined;\n    }\n\n    return this.cachedToken;\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\n\n/**\n * Helps the core-http token authentication policies with requesting a new token if we're not currently waiting for a new token.\n *\n * @deprecated No longer used in the bearer authorization policy.\n */\nexport class AccessTokenRefresher {\n  private promise: Promise<AccessToken | undefined> | undefined;\n  private lastCalled = 0;\n\n  constructor(\n    private credential: TokenCredential,\n    private scopes: string | string[],\n    private requiredMillisecondsBeforeNewRefresh: number = 30000\n  ) {}\n\n  /**\n   * Returns true if the required milliseconds(defaulted to 30000) have been passed signifying\n   * that we are ready for a new refresh.\n   */\n  public isReady(): boolean {\n    // We're only ready for a new refresh if the required milliseconds have passed.\n    return (\n      !this.lastCalled || Date.now() - this.lastCalled > this.requiredMillisecondsBeforeNewRefresh\n    );\n  }\n\n  /**\n   * Stores the time in which it is called,\n   * then requests a new token,\n   * then sets this.promise to undefined,\n   * then returns the token.\n   */\n  private async getToken(options: GetTokenOptions): Promise<AccessToken | undefined> {\n    this.lastCalled = Date.now();\n    const token = await this.credential.getToken(this.scopes, options);\n    this.promise = undefined;\n    return token || undefined;\n  }\n\n  /**\n   * Requests a new token if we're not currently waiting for a new token.\n   * Returns null if the required time between each call hasn't been reached.\n   */\n  public refresh(options: GetTokenOptions): Promise<AccessToken | undefined> {\n    if (!this.promise) {\n      this.promise = this.getToken(options);\n    }\n\n    return this.promise;\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as base64 from \"../util/base64\";\nimport { Constants } from \"../util/constants\";\nimport { HttpHeaders } from \"../httpHeaders\";\nimport { ServiceClientCredentials } from \"./serviceClientCredentials\";\nimport { WebResourceLike } from \"../webResource\";\n\nconst HeaderConstants = Constants.HeaderConstants;\nconst DEFAULT_AUTHORIZATION_SCHEME = \"Basic\";\n\n/**\n * A simple {@link ServiceClientCredential} that authenticates with a username and a password.\n */\nexport class BasicAuthenticationCredentials implements ServiceClientCredentials {\n  /**\n   * Username\n   */\n  userName: string;\n\n  /**\n   * Password\n   */\n  password: string;\n\n  /**\n   * Authorization scheme. Defaults to \"Basic\".\n   * More information about authorization schemes is available here: https://developer.mozilla.org/docs/Web/HTTP/Authentication#authentication_schemes\n   */\n  authorizationScheme: string = DEFAULT_AUTHORIZATION_SCHEME;\n\n  /**\n   * Creates a new BasicAuthenticationCredentials object.\n   *\n   * @param userName - User name.\n   * @param password - Password.\n   * @param authorizationScheme - The authorization scheme.\n   */\n  constructor(\n    userName: string,\n    password: string,\n    authorizationScheme: string = DEFAULT_AUTHORIZATION_SCHEME\n  ) {\n    if (userName === null || userName === undefined || typeof userName.valueOf() !== \"string\") {\n      throw new Error(\"userName cannot be null or undefined and must be of type string.\");\n    }\n    if (password === null || password === undefined || typeof password.valueOf() !== \"string\") {\n      throw new Error(\"password cannot be null or undefined and must be of type string.\");\n    }\n    this.userName = userName;\n    this.password = password;\n    this.authorizationScheme = authorizationScheme;\n  }\n\n  /**\n   * Signs a request with the Authentication header.\n   *\n   * @param webResource - The WebResourceLike to be signed.\n   * @returns The signed request object.\n   */\n  signRequest(webResource: WebResourceLike): Promise<WebResourceLike> {\n    const credentials = `${this.userName}:${this.password}`;\n    const encodedCredentials = `${this.authorizationScheme} ${base64.encodeString(credentials)}`;\n    if (!webResource.headers) webResource.headers = new HttpHeaders();\n    webResource.headers.set(HeaderConstants.AUTHORIZATION, encodedCredentials);\n    return Promise.resolve(webResource);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpHeaders } from \"../httpHeaders\";\nimport { ServiceClientCredentials } from \"./serviceClientCredentials\";\nimport { WebResourceLike } from \"../webResource\";\n\n/**\n * Describes the options to be provided while creating an instance of ApiKeyCredentials\n */\nexport interface ApiKeyCredentialOptions {\n  /**\n   * A key value pair of the header parameters that need to be applied to the request.\n   */\n  inHeader?: { [x: string]: any };\n  /**\n   * A key value pair of the query parameters that need to be applied to the request.\n   */\n  inQuery?: { [x: string]: any };\n}\n\n/**\n * Authenticates to a service using an API key.\n */\nexport class ApiKeyCredentials implements ServiceClientCredentials {\n  /**\n   * A key value pair of the header parameters that need to be applied to the request.\n   */\n  private readonly inHeader?: { [x: string]: any };\n  /**\n   * A key value pair of the query parameters that need to be applied to the request.\n   */\n  private readonly inQuery?: { [x: string]: any };\n\n  /**\n   * @param options - Specifies the options to be provided for auth. Either header or query needs to be provided.\n   */\n  constructor(options: ApiKeyCredentialOptions) {\n    if (!options || (options && !options.inHeader && !options.inQuery)) {\n      throw new Error(\n        `options cannot be null or undefined. Either \"inHeader\" or \"inQuery\" property of the options object needs to be provided.`\n      );\n    }\n    this.inHeader = options.inHeader;\n    this.inQuery = options.inQuery;\n  }\n\n  /**\n   * Signs a request with the values provided in the inHeader and inQuery parameter.\n   *\n   * @param webResource - The WebResourceLike to be signed.\n   * @returns The signed request object.\n   */\n  signRequest(webResource: WebResourceLike): Promise<WebResourceLike> {\n    if (!webResource) {\n      return Promise.reject(\n        new Error(`webResource cannot be null or undefined and must be of type \"object\".`)\n      );\n    }\n\n    if (this.inHeader) {\n      if (!webResource.headers) {\n        webResource.headers = new HttpHeaders();\n      }\n      for (const headerName in this.inHeader) {\n        webResource.headers.set(headerName, this.inHeader[headerName]);\n      }\n    }\n\n    if (this.inQuery) {\n      if (!webResource.url) {\n        return Promise.reject(new Error(`url cannot be null in the request object.`));\n      }\n      if (webResource.url.indexOf(\"?\") < 0) {\n        webResource.url += \"?\";\n      }\n      for (const key in this.inQuery) {\n        if (!webResource.url.endsWith(\"?\")) {\n          webResource.url += \"&\";\n        }\n        webResource.url += `${key}=${this.inQuery[key]}`;\n      }\n    }\n\n    return Promise.resolve(webResource);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { ApiKeyCredentialOptions, ApiKeyCredentials } from \"./apiKeyCredentials\";\n\n/**\n * A {@link TopicCredentials} object used for Azure Event Grid.\n */\nexport class TopicCredentials extends ApiKeyCredentials {\n  /**\n   * Creates a new EventGrid TopicCredentials object.\n   *\n   * @param topicKey - The EventGrid topic key\n   */\n  constructor(topicKey: string) {\n    if (!topicKey || (topicKey && typeof topicKey !== \"string\")) {\n      throw new Error(\"topicKey cannot be null or undefined and must be of type string.\");\n    }\n    const options: ApiKeyCredentialOptions = {\n      inHeader: {\n        \"aeg-sas-key\": topicKey,\n      },\n    };\n    super(options);\n  }\n}\n"], "names": ["uuidv4", "base64.decodeString", "base64.encodeByteArray", "utils.isValidUuid", "utils.isDuration", "tunnel", "inspect", "createClientLogger", "Transform", "tough", "abortController", "AbortController", "AbortError", "FormData", "https", "http", "node_fetch", "HttpPipelineLogLevel", "__rest", "xml2js", "RetryMode", "retry", "delay", "logger", "core<PERSON>ogger", "os", "QueryCollectionFormat", "DefaultHttpClient", "utils.generateUuid", "createSpanFunction", "SpanKind", "getTraceParentHeader", "isSpanContextValid", "SpanStatusCode", "isTokenCredential", "utils.prepareXMLRootList", "utils.isPrimitiveType", "coreTracingCreateSpanFunction", "base64.encodeString"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AAEA;;AAEG;AACH,SAAS,YAAY,CAAC,UAAkB,EAAA;AACtC,IAAA,OAAO,UAAU,CAAC,WAAW,EAAE,CAAC;AAClC,CAAC;AA4EK,SAAU,iBAAiB,CAAC,MAAgB,EAAA;AAChD,IAAA,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QACxC,MAAM,UAAU,GAAG,MAWlB,CAAC;AACF,QAAA,IACE,OAAO,UAAU,CAAC,UAAU,KAAK,UAAU;AAC3C,YAAA,OAAO,UAAU,CAAC,KAAK,KAAK,UAAU;AACtC,YAAA,OAAO,UAAU,CAAC,GAAG,KAAK,UAAU;AACpC,YAAA,OAAO,UAAU,CAAC,GAAG,KAAK,UAAU;AACpC,YAAA,OAAO,UAAU,CAAC,QAAQ,KAAK,UAAU;AACzC,YAAA,OAAO,UAAU,CAAC,MAAM,KAAK,UAAU;AACvC,YAAA,OAAO,UAAU,CAAC,YAAY,KAAK,UAAU;AAC7C,YAAA,OAAO,UAAU,CAAC,YAAY,KAAK,UAAU;AAC7C,YAAA,OAAO,UAAU,CAAC,WAAW,KAAK,UAAU;AAC5C,YAAA,OAAO,UAAU,CAAC,MAAM,KAAK,UAAU,EACvC;AACA,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AACF,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;AAEG;MACU,WAAW,CAAA;AAGtB,IAAA,WAAA,CAAY,UAA2B,EAAA;AACrC,QAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,QAAA,IAAI,UAAU,EAAE;AACd,YAAA,KAAK,MAAM,UAAU,IAAI,UAAU,EAAE;gBACnC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;AAC9C,aAAA;AACF,SAAA;KACF;AAED;;;;;AAKG;IACI,GAAG,CAAC,UAAkB,EAAE,WAA4B,EAAA;QACzD,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,GAAG;AAC3C,YAAA,IAAI,EAAE,UAAU;AAChB,YAAA,KAAK,EAAE,WAAW,CAAC,QAAQ,EAAE;SAC9B,CAAC;KACH;AAED;;;;AAIG;AACI,IAAA,GAAG,CAAC,UAAkB,EAAA;QAC3B,MAAM,MAAM,GAAe,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;AACtE,QAAA,OAAO,CAAC,MAAM,GAAG,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC;KAC3C;AAED;;AAEG;AACI,IAAA,QAAQ,CAAC,UAAkB,EAAA;QAChC,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;KACrD;AAED;;;;AAIG;AACI,IAAA,MAAM,CAAC,UAAkB,EAAA;QAC9B,MAAM,MAAM,GAAY,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;AAClD,QAAA,OAAO,MAAM,CAAC;KACf;AAED;;AAEG;IACI,UAAU,GAAA;QACf,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;KAC5C;AAED;;AAEG;IACI,YAAY,GAAA;QACjB,MAAM,OAAO,GAAiB,EAAE,CAAC;AACjC,QAAA,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACxC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC;AAC3C,SAAA;AACD,QAAA,OAAO,OAAO,CAAC;KAChB;AAED;;AAEG;IACI,WAAW,GAAA;QAChB,MAAM,WAAW,GAAa,EAAE,CAAC;AACjC,QAAA,MAAM,OAAO,GAAiB,IAAI,CAAC,YAAY,EAAE,CAAC;AAClD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACvC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACnC,SAAA;AACD,QAAA,OAAO,WAAW,CAAC;KACpB;AAED;;AAEG;IACI,YAAY,GAAA;QACjB,MAAM,YAAY,GAAa,EAAE,CAAC;AAClC,QAAA,MAAM,OAAO,GAAiB,IAAI,CAAC,YAAY,EAAE,CAAC;AAClD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACvC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AACrC,SAAA;AACD,QAAA,OAAO,YAAY,CAAC;KACrB;AAED;;AAEG;IACI,MAAM,CAAC,UAAsC,EAAE,EAAA;QACpD,MAAM,MAAM,GAAmB,EAAE,CAAC;QAClC,IAAI,OAAO,CAAC,YAAY,EAAE;AACxB,YAAA,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;gBACxC,MAAM,MAAM,GAAe,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;gBACvD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;AACpC,aAAA;AACF,SAAA;AAAM,aAAA;AACL,YAAA,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;gBACxC,MAAM,MAAM,GAAe,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AACvD,gBAAA,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;AAClD,aAAA;AACF,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;AAED;;AAEG;IACI,QAAQ,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;KAC5D;AAED;;AAEG;IACI,KAAK,GAAA;QACV,MAAM,sBAAsB,GAAmB,EAAE,CAAC;AAClD,QAAA,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACxC,MAAM,MAAM,GAAe,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YACvD,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;AACpD,SAAA;AACD,QAAA,OAAO,IAAI,WAAW,CAAC,sBAAsB,CAAC,CAAC;KAChD;AACF;;AC5PD;AACA;AAEA;;;AAGG;AACG,SAAU,YAAY,CAAC,KAAa,EAAA;IACxC,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC/C,CAAC;AAED;;;AAGG;AACG,SAAU,eAAe,CAAC,KAAiB,EAAA;;;IAG/C,MAAM,WAAW,GAAG,KAAK,YAAY,MAAM,GAAG,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC;AAC/F,IAAA,OAAO,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACxC,CAAC;AAED;;;AAGG;AACG,SAAU,YAAY,CAAC,KAAa,EAAA;IACxC,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AACtC;;AC5BA;AACA;AACA;;AAEG;AACU,MAAA,SAAS,GAAG;AACvB;;AAEG;AACH,IAAA,eAAe,EAAE,OAAO;AAExB;;AAEG;AACH,IAAA,IAAI,EAAE,OAAO;AAEb;;AAEG;AACH,IAAA,KAAK,EAAE,QAAQ;AAEf;;AAEG;AACH,IAAA,UAAU,EAAE,YAAY;AAExB;;AAEG;AACH,IAAA,WAAW,EAAE,aAAa;AAE1B;;AAEG;AACH,IAAA,QAAQ,EAAE,UAAU;AAEpB;;AAEG;AACH,IAAA,SAAS,EAAE,WAAW;AAEtB,IAAA,aAAa,EAAE;AACb;;AAEG;AACH,QAAA,SAAS,EAAE;AACT,YAAA,GAAG,EAAE,KAAK;AACV,YAAA,GAAG,EAAE,KAAK;AACV,YAAA,MAAM,EAAE,QAAQ;AAChB,YAAA,IAAI,EAAE,MAAM;AACZ,YAAA,KAAK,EAAE,OAAO;AACd,YAAA,IAAI,EAAE,MAAM;AACZ,YAAA,KAAK,EAAE,OAAO;AACf,SAAA;AAED,QAAA,WAAW,EAAE;AACX,YAAA,eAAe,EAAE,GAAG;AACpB,YAAA,kBAAkB,EAAE,GAAG;AACxB,SAAA;AACF,KAAA;AAED;;AAEG;AACH,IAAA,eAAe,EAAE;AACf;;AAEG;AACH,QAAA,aAAa,EAAE,eAAe;AAE9B,QAAA,oBAAoB,EAAE,QAAQ;AAE9B;;;;AAIG;AACH,QAAA,WAAW,EAAE,aAAa;AAE1B;;AAEG;AACH,QAAA,UAAU,EAAE,YAAY;AACzB,KAAA;;;ACnFH;AACA;AAEA;;AAEG;AACI,MAAM,WAAW,GAAG,IAAI;AAC/B;;AAEG;AACI,MAAM,WAAW,GAAG;;ACV3B;AAUA,MAAM,cAAc,GAClB,gFAAgF,CAAC;AAEnF;;AAEG;AACU,MAAA,MAAM,GACjB,OAAO,OAAO,KAAK,WAAW;IAC9B,CAAC,CAAC,OAAO,CAAC,OAAO;IACjB,CAAC,CAAC,OAAO,CAAC,QAAQ;AAClB,IAAA,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK;AAY1B;;;;;AAKG;AACG,SAAU,SAAS,CAAC,GAAW,EAAA;IACnC,OAAO,kBAAkB,CAAC,GAAG,CAAC;AAC3B,SAAA,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;AACpB,SAAA,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;AACpB,SAAA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;AACrB,SAAA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;AACrB,SAAA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC3B,CAAC;AAED;;;;;;AAMG;AACG,SAAU,aAAa,CAAC,QAA+B,EAAA;IAC3D,MAAM,gBAAgB,GAAQ,EAAE,CAAC;AACjC,IAAA,gBAAgB,CAAC,IAAI,GAAG,QAAQ,CAAC,UAAU,CAAC;AAC5C,IAAA,gBAAgB,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;AAC5C,IAAA,gBAAgB,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AAC1C,IAAA,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED;;;;;;AAMG;AACG,SAAU,YAAY,CAAC,OAAwB,EAAA;AACnD,IAAA,MAAM,eAAe,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;IACxC,IAAI,eAAe,CAAC,OAAO,EAAE;AAC3B,QAAA,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;AACjD,KAAA;AACD,IAAA,OAAO,eAAe,CAAC;AACzB,CAAC;AAED;;;;;AAKG;AACG,SAAU,WAAW,CAAC,IAAY,EAAA;AACtC,IAAA,OAAO,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnC,CAAC;AAED;;;;AAIG;SACa,YAAY,GAAA;IAC1B,OAAOA,OAAM,EAAE,CAAC;AAClB,CAAC;AAED;;;;;;;;AAQG;AACa,SAAA,2BAA2B,CACzC,gBAA4B,EAC5B,SAAkB,EAAA;IAElB,IAAI,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACxC,IAAA,gBAAgB,CAAC,OAAO,CAAC,CAAC,cAAc,KAAI;AAC1C,QAAA,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AACvC,KAAC,CAAC,CAAC;AACH,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAqBD;;;;;AAKG;AACH;AACM,SAAU,iBAAiB,CAAC,OAAqB,EAAA;AACrD,IAAA,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE;AACtC,QAAA,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;AACzD,KAAA;;IAED,OAAO,CAAC,EAAY,KAAU;QAC5B,OAAO;AACJ,aAAA,IAAI,CAAC,CAAC,IAAS,KAAI;;AAElB,YAAA,OAAO,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AAC7B,SAAC,CAAC;AACD,aAAA,KAAK,CAAC,CAAC,GAAU,KAAI;;YAEpB,EAAE,CAAC,GAAG,CAAC,CAAC;AACV,SAAC,CAAC,CAAC;AACP,KAAC,CAAC;AACJ,CAAC;AAED;;;;AAIG;AACG,SAAU,wBAAwB,CACtC,OAAuC,EAAA;AAEvC,IAAA,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE;AACtC,QAAA,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;AACzD,KAAA;IACD,OAAO,CAAC,EAAsB,KAAU;QACtC,OAAO;AACJ,aAAA,IAAI,CAAC,CAAC,IAA2B,KAAI;AACpC,YAAA,OAAO,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,UAAe,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACnF,SAAC,CAAC;AACD,aAAA,KAAK,CAAC,CAAC,GAAU,KAAI;AACpB,YAAA,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;AAC5B,SAAC,CAAC,CAAC;AACP,KAAC,CAAC;AACJ,CAAC;AAEK,SAAU,kBAAkB,CAChC,GAAY,EACZ,WAAmB,EACnB,eAAwB,EACxB,YAAqB,EAAA;AAErB,IAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AACvB,QAAA,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AACb,KAAA;AAED,IAAA,IAAI,CAAC,eAAe,IAAI,CAAC,YAAY,EAAE;AACrC,QAAA,OAAO,EAAE,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;AAC/B,KAAA;IAED,MAAM,MAAM,GAAG,EAAE,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;IACtC,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,eAAe,GAAG,YAAY,EAAE,CAAC;AAC1D,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;AAIG;AACa,SAAA,WAAW,CAAC,eAAwB,EAAE,WAAkB,EAAA;IACtE,MAAM,mBAAmB,GAAG,eAE3B,CAAC;AACF,IAAA,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,KAAI;AACjC,QAAA,MAAM,CAAC,mBAAmB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAChE,YAAA,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACnE,SAAC,CAAC,CAAC;AACL,KAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAM,mBAAmB,GACvB,qKAAqK,CAAC;AAExK;;;;AAIG;AACG,SAAU,UAAU,CAAC,KAAa,EAAA;AACtC,IAAA,OAAO,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzC,CAAC;AAED;;;;;;AAMG;SACa,UAAU,CACxB,KAAyB,EACzB,WAAmB,EACnB,YAAoB,EAAA;IAEpB,OAAO,CAAC,KAAK,IAAI,CAAC,WAAW,GAAG,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC;AAC5F,CAAC;AAED;;;;;AAKG;AACG,SAAU,eAAe,CAAC,KAAc,EAAA;AAC5C,IAAA,OAAO,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,UAAU,KAAK,KAAK,KAAK,IAAI,CAAC;AACtF,CAAC;AAEK,SAAU,mBAAmB,CAAC,IAAY,EAAA;AAC9C,IAAA,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACrB,QAAA,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC1B,KAAA;SAAM,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE;QAC1C,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AACxC,KAAA;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAOD;;;AAGG;AACG,SAAU,QAAQ,CAAC,KAAc,EAAA;AACrC,IAAA,QACE,OAAO,KAAK,KAAK,QAAQ;AACzB,QAAA,KAAK,KAAK,IAAI;AACd,QAAA,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;AACrB,QAAA,EAAE,KAAK,YAAY,MAAM,CAAC;AAC1B,QAAA,EAAE,KAAK,YAAY,IAAI,CAAC,EACxB;AACJ;;ACxRA;AAQA;AAEA;;;;AAIG;MACU,UAAU,CAAA;AACrB,IAAA,WAAA;AACE;;AAEG;AACa,IAAA,YAAA,GAAuC,EAAE;AACzD;;AAEG;IACa,KAAe,EAAA;QAJf,IAAY,CAAA,YAAA,GAAZ,YAAY,CAA6B;QAIzC,IAAK,CAAA,KAAA,GAAL,KAAK,CAAU;KAC7B;AAEJ;;;;;;AAMG;AACH,IAAA,mBAAmB,CAAC,MAAc,EAAE,KAAc,EAAE,UAAkB,EAAA;AACpE,QAAA,MAAM,cAAc,GAAG,CACrB,cAAuC,EACvC,eAAoB,KACX;AACT,YAAA,MAAM,IAAI,KAAK,CACb,CAAA,CAAA,EAAI,UAAU,CAAA,cAAA,EAAiB,KAAK,CAAA,iCAAA,EAAoC,cAAc,CAAA,GAAA,EAAM,eAAe,CAAA,CAAA,CAAG,CAC/G,CAAC;AACJ,SAAC,CAAC;AACF,QAAA,IAAI,MAAM,CAAC,WAAW,IAAI,KAAK,IAAI,SAAS,EAAE;YAC5C,MAAM,aAAa,GAAG,KAAe,CAAC;YACtC,MAAM,EACJ,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,UAAU,EACV,OAAO,EACP,WAAW,GACZ,GAAG,MAAM,CAAC,WAAW,CAAC;AACvB,YAAA,IAAI,gBAAgB,IAAI,SAAS,IAAI,aAAa,IAAI,gBAAgB,EAAE;AACtE,gBAAA,cAAc,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;AACtD,aAAA;AACD,YAAA,IAAI,gBAAgB,IAAI,SAAS,IAAI,aAAa,IAAI,gBAAgB,EAAE;AACtE,gBAAA,cAAc,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;AACtD,aAAA;AACD,YAAA,IAAI,gBAAgB,IAAI,SAAS,IAAI,aAAa,GAAG,gBAAgB,EAAE;AACrE,gBAAA,cAAc,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;AACtD,aAAA;AACD,YAAA,IAAI,gBAAgB,IAAI,SAAS,IAAI,aAAa,GAAG,gBAAgB,EAAE;AACrE,gBAAA,cAAc,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;AACtD,aAAA;YACD,MAAM,YAAY,GAAG,KAAc,CAAC;YACpC,IAAI,QAAQ,IAAI,SAAS,IAAI,YAAY,CAAC,MAAM,GAAG,QAAQ,EAAE;AAC3D,gBAAA,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;AACtC,aAAA;YACD,IAAI,SAAS,IAAI,SAAS,IAAI,YAAY,CAAC,MAAM,GAAG,SAAS,EAAE;AAC7D,gBAAA,cAAc,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;AACxC,aAAA;YACD,IAAI,QAAQ,IAAI,SAAS,IAAI,YAAY,CAAC,MAAM,GAAG,QAAQ,EAAE;AAC3D,gBAAA,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;AACtC,aAAA;YACD,IAAI,SAAS,IAAI,SAAS,IAAI,YAAY,CAAC,MAAM,GAAG,SAAS,EAAE;AAC7D,gBAAA,cAAc,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;AACxC,aAAA;YACD,IAAI,UAAU,IAAI,SAAS,IAAI,aAAa,GAAG,UAAU,KAAK,CAAC,EAAE;AAC/D,gBAAA,cAAc,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;AAC1C,aAAA;AACD,YAAA,IAAI,OAAO,EAAE;AACX,gBAAA,MAAM,OAAO,GAAW,OAAO,OAAO,KAAK,QAAQ,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;AACpF,gBAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;AAC9D,oBAAA,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AACpC,iBAAA;AACF,aAAA;AACD,YAAA,IACE,WAAW;gBACX,YAAY,CAAC,IAAI,CAAC,CAAC,IAAS,EAAE,CAAS,EAAE,EAAc,KAAK,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EACnF;AACA,gBAAA,cAAc,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;AAC5C,aAAA;AACF,SAAA;KACF;AAED;;;;;;;;AAQG;IACH,SAAS,CACP,MAAc,EACd,MAAe,EACf,UAAmB,EACnB,UAA6B,EAAE,EAAA;;AAE/B,QAAA,MAAM,cAAc,GAAgC;AAClD,YAAA,QAAQ,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,QAAQ,mCAAI,EAAE;AAChC,YAAA,WAAW,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,WAAW,mCAAI,KAAK;AACzC,YAAA,UAAU,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,UAAU,mCAAI,WAAW;SAC9C,CAAC;QACF,IAAI,OAAO,GAAQ,EAAE,CAAC;AACtB,QAAA,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,IAAc,CAAC;QAC9C,IAAI,CAAC,UAAU,EAAE;AACf,YAAA,UAAU,GAAG,MAAM,CAAC,cAAe,CAAC;AACrC,SAAA;QACD,IAAI,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;YAC5C,OAAO,GAAG,EAAE,CAAC;AACd,SAAA;QAED,IAAI,MAAM,CAAC,UAAU,EAAE;AACrB,YAAA,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC;AAC9B,SAAA;;;;;;;;;;AAYD,QAAA,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;AAEtC,QAAA,IAAI,QAAQ,IAAI,QAAQ,IAAI,MAAM,KAAK,SAAS,EAAE;AAChD,YAAA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,CAAA,qBAAA,CAAuB,CAAC,CAAC;AACvD,SAAA;QACD,IAAI,QAAQ,IAAI,CAAC,QAAQ,IAAI,MAAM,IAAI,SAAS,EAAE;AAChD,YAAA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,CAAA,6BAAA,CAA+B,CAAC,CAAC;AAC/D,SAAA;QACD,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,IAAI,MAAM,KAAK,IAAI,EAAE;AACtD,YAAA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,CAAA,gBAAA,CAAkB,CAAC,CAAC;AAClD,SAAA;QAED,IAAI,MAAM,IAAI,SAAS,EAAE;YACvB,OAAO,GAAG,MAAM,CAAC;AAClB,SAAA;AAAM,aAAA;YACL,IAAI,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE;gBACvC,OAAO,GAAG,MAAM,CAAC;AAClB,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,+CAA+C,CAAC,KAAK,IAAI,EAAE;gBACrF,OAAO,GAAG,mBAAmB,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;AAC/D,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;gBAC/C,MAAM,UAAU,GAAe,MAAoB,CAAC;AACpD,gBAAA,OAAO,GAAG,iBAAiB,CAAC,UAAU,EAAE,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AAChF,aAAA;iBAAM,IACL,UAAU,CAAC,KAAK,CAAC,sDAAsD,CAAC,KAAK,IAAI,EACjF;gBACA,OAAO,GAAG,kBAAkB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;AAC9D,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;AACpD,gBAAA,OAAO,GAAG,sBAAsB,CAAC,UAAU,EAAE,MAAoB,CAAC,CAAC;AACpE,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;AACpD,gBAAA,OAAO,GAAG,sBAAsB,CAAC,UAAU,EAAE,MAAoB,CAAC,CAAC;AACpE,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;gBACnD,OAAO,GAAG,qBAAqB,CAC7B,IAAI,EACJ,MAAwB,EACxB,MAAM,EACN,UAAU,EACV,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EACnB,cAAc,CACf,CAAC;AACH,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,IAAI,EAAE;gBACrD,OAAO,GAAG,uBAAuB,CAC/B,IAAI,EACJ,MAA0B,EAC1B,MAAM,EACN,UAAU,EACV,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EACnB,cAAc,CACf,CAAC;AACH,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;gBACpD,OAAO,GAAG,sBAAsB,CAC9B,IAAI,EACJ,MAAyB,EACzB,MAAM,EACN,UAAU,EACV,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EACnB,cAAc,CACf,CAAC;AACH,aAAA;AACF,SAAA;AACD,QAAA,OAAO,OAAO,CAAC;KAChB;AAED;;;;;;;;AAQG;IACH,WAAW,CACT,MAAc,EACd,YAAqB,EACrB,UAAkB,EAClB,UAA6B,EAAE,EAAA;;AAE/B,QAAA,MAAM,cAAc,GAAgC;AAClD,YAAA,QAAQ,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,QAAQ,mCAAI,EAAE;AAChC,YAAA,WAAW,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,WAAW,mCAAI,KAAK;AACzC,YAAA,UAAU,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,UAAU,mCAAI,WAAW;SAC9C,CAAC;QACF,IAAI,YAAY,IAAI,SAAS,EAAE;AAC7B,YAAA,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;;;;gBAIzE,YAAY,GAAG,EAAE,CAAC;AACnB,aAAA;;AAED,YAAA,IAAI,MAAM,CAAC,YAAY,KAAK,SAAS,EAAE;AACrC,gBAAA,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;AACpC,aAAA;AACD,YAAA,OAAO,YAAY,CAAC;AACrB,SAAA;AAED,QAAA,IAAI,OAAY,CAAC;AACjB,QAAA,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QACpC,IAAI,CAAC,UAAU,EAAE;AACf,YAAA,UAAU,GAAG,MAAM,CAAC,cAAe,CAAC;AACrC,SAAA;QAED,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;AAC7C,YAAA,OAAO,GAAG,wBAAwB,CAChC,IAAI,EACJ,MAAyB,EACzB,YAAY,EACZ,UAAU,EACV,cAAc,CACf,CAAC;AACH,SAAA;AAAM,aAAA;YACL,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,gBAAA,MAAM,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;gBAC7C,MAAM,gBAAgB,GAAG,YAAuC,CAAC;AACjE;;;;AAIG;AACH,gBAAA,IACE,gBAAgB,CAAC,WAAW,CAAC,IAAI,SAAS;AAC1C,oBAAA,gBAAgB,CAAC,UAAU,CAAC,IAAI,SAAS,EACzC;AACA,oBAAA,YAAY,GAAG,gBAAgB,CAAC,UAAU,CAAC,CAAC;AAC7C,iBAAA;AACF,aAAA;YAED,IAAI,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;AAC1C,gBAAA,OAAO,GAAG,UAAU,CAAC,YAAsB,CAAC,CAAC;AAC7C,gBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE;oBAClB,OAAO,GAAG,YAAY,CAAC;AACxB,iBAAA;AACF,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI,EAAE;gBAClD,IAAI,YAAY,KAAK,MAAM,EAAE;oBAC3B,OAAO,GAAG,IAAI,CAAC;AAChB,iBAAA;qBAAM,IAAI,YAAY,KAAK,OAAO,EAAE;oBACnC,OAAO,GAAG,KAAK,CAAC;AACjB,iBAAA;AAAM,qBAAA;oBACL,OAAO,GAAG,YAAY,CAAC;AACxB,iBAAA;AACF,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,kDAAkD,CAAC,KAAK,IAAI,EAAE;gBACxF,OAAO,GAAG,YAAY,CAAC;AACxB,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,oCAAoC,CAAC,KAAK,IAAI,EAAE;AAC1E,gBAAA,OAAO,GAAG,IAAI,IAAI,CAAC,YAAsB,CAAC,CAAC;AAC5C,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;AACnD,gBAAA,OAAO,GAAG,cAAc,CAAC,YAAsB,CAAC,CAAC;AAClD,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;AACpD,gBAAA,OAAO,GAAGC,YAAmB,CAAC,YAAsB,CAAC,CAAC;AACvD,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;AACpD,gBAAA,OAAO,GAAG,oBAAoB,CAAC,YAAsB,CAAC,CAAC;AACxD,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;AACnD,gBAAA,OAAO,GAAG,uBAAuB,CAC/B,IAAI,EACJ,MAAwB,EACxB,YAAY,EACZ,UAAU,EACV,cAAc,CACf,CAAC;AACH,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,IAAI,EAAE;AACrD,gBAAA,OAAO,GAAG,yBAAyB,CACjC,IAAI,EACJ,MAA0B,EAC1B,YAAY,EACZ,UAAU,EACV,cAAc,CACf,CAAC;AACH,aAAA;AACF,SAAA;QAED,IAAI,MAAM,CAAC,UAAU,EAAE;AACrB,YAAA,OAAO,GAAG,MAAM,CAAC,YAAY,CAAC;AAC/B,SAAA;AAED,QAAA,OAAO,OAAO,CAAC;KAChB;AACF,CAAA;AAED,SAAS,OAAO,CAAC,GAAW,EAAE,EAAU,EAAA;AACtC,IAAA,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;AACrB,IAAA,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;AAC1C,QAAA,EAAE,GAAG,CAAC;AACP,KAAA;IACD,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,iBAAiB,CAAC,MAAW,EAAA;IACpC,IAAI,CAAC,MAAM,EAAE;AACX,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;AACD,IAAA,IAAI,EAAE,MAAM,YAAY,UAAU,CAAC,EAAE;AACnC,QAAA,MAAM,IAAI,KAAK,CAAC,CAAA,uEAAA,CAAyE,CAAC,CAAC;AAC5F,KAAA;;IAED,MAAM,GAAG,GAAGC,eAAsB,CAAC,MAAM,CAAC,CAAC;;IAE3C,OAAO,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACnE,CAAC;AAED,SAAS,oBAAoB,CAAC,GAAW,EAAA;IACvC,IAAI,CAAC,GAAG,EAAE;AACR,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;IACD,IAAI,GAAG,IAAI,OAAO,GAAG,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;AAC5C,QAAA,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;AACxF,KAAA;;AAED,IAAA,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;;AAEhD,IAAA,OAAOD,YAAmB,CAAC,GAAG,CAAC,CAAC;AAClC,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAwB,EAAA;IAClD,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,IAAI,YAAY,GAAG,EAAE,CAAC;AACtB,IAAA,IAAI,IAAI,EAAE;QACR,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAEjC,QAAA,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE;AAC3B,YAAA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;AACzC,gBAAA,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;AACvD,aAAA;AAAM,iBAAA;gBACL,YAAY,IAAI,IAAI,CAAC;AACrB,gBAAA,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC3B,YAAY,GAAG,EAAE,CAAC;AACnB,aAAA;AACF,SAAA;AACF,KAAA;AAED,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,cAAc,CAAC,CAAgB,EAAA;IACtC,IAAI,CAAC,CAAC,EAAE;AACN,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;AAED,IAAA,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;AACnC,QAAA,CAAC,GAAG,IAAI,IAAI,CAAC,CAAW,CAAC,CAAC;AAC3B,KAAA;IACD,OAAO,IAAI,CAAC,KAAK,CAAE,CAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;AAClD,CAAC;AAED,SAAS,cAAc,CAAC,CAAS,EAAA;IAC/B,IAAI,CAAC,CAAC,EAAE;AACN,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;AACD,IAAA,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,mBAAmB,CAAC,QAAgB,EAAE,UAAkB,EAAE,KAAU,EAAA;AAC3E,IAAA,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;QACzC,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;AACxC,YAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,MAAM,IAAI,KAAK,CAAC,CAAA,EAAG,UAAU,CAAe,YAAA,EAAA,KAAK,CAA0B,wBAAA,CAAA,CAAC,CAAC;AAC9E,aAAA;AACF,SAAA;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;AAC/C,YAAA,IAAI,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;gBACvC,MAAM,IAAI,KAAK,CAAC,CAAA,EAAG,UAAU,CAAgB,aAAA,EAAA,KAAK,CAA2B,yBAAA,CAAA,CAAC,CAAC;AAChF,aAAA;AACF,SAAA;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;AAC7C,YAAA,IAAI,EAAE,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAIE,WAAiB,CAAC,KAAK,CAAC,CAAC,EAAE;gBACtE,MAAM,IAAI,KAAK,CACb,CAAA,EAAG,UAAU,CAAgB,aAAA,EAAA,KAAK,CAA4C,0CAAA,CAAA,CAC/E,CAAC;AACH,aAAA;AACF,SAAA;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI,EAAE;AAChD,YAAA,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE;gBAC9B,MAAM,IAAI,KAAK,CAAC,CAAA,EAAG,UAAU,CAAe,YAAA,EAAA,KAAK,CAA2B,yBAAA,CAAA,CAAC,CAAC;AAC/E,aAAA;AACF,SAAA;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;AAC/C,YAAA,MAAM,UAAU,GAAG,OAAO,KAAK,CAAC;YAChC,IACE,UAAU,KAAK,QAAQ;AACvB,gBAAA,UAAU,KAAK,UAAU;AACzB,gBAAA,EAAE,KAAK,YAAY,WAAW,CAAC;AAC/B,gBAAA,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1B,gBAAA,EAAE,CAAC,OAAO,IAAI,KAAK,UAAU,IAAI,OAAO,IAAI,KAAK,QAAQ,KAAK,KAAK,YAAY,IAAI,CAAC,EACpF;AACA,gBAAA,MAAM,IAAI,KAAK,CACb,GAAG,UAAU,CAAA,qGAAA,CAAuG,CACrH,CAAC;AACH,aAAA;AACF,SAAA;AACF,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,iBAAiB,CAAC,UAAkB,EAAE,aAAyB,EAAE,KAAU,EAAA;IAClF,IAAI,CAAC,aAAa,EAAE;AAClB,QAAA,MAAM,IAAI,KAAK,CACb,qDAAqD,UAAU,CAAA,iBAAA,CAAmB,CACnF,CAAC;AACH,KAAA;IACD,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,KAAI;AAC5C,QAAA,IAAI,OAAO,IAAI,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;YACtC,OAAO,IAAI,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,WAAW,EAAE,CAAC;AACnD,SAAA;QACD,OAAO,IAAI,KAAK,KAAK,CAAC;AACxB,KAAC,CAAC,CAAC;IACH,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,MAAM,IAAI,KAAK,CACb,CAAG,EAAA,KAAK,6BAA6B,UAAU,CAAA,wBAAA,EAA2B,IAAI,CAAC,SAAS,CACtF,aAAa,CACd,CAAA,CAAA,CAAG,CACL,CAAC;AACH,KAAA;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,sBAAsB,CAAC,UAAkB,EAAE,KAAiB,EAAA;IACnE,IAAI,WAAW,GAAW,EAAE,CAAC;IAC7B,IAAI,KAAK,IAAI,SAAS,EAAE;AACtB,QAAA,IAAI,EAAE,KAAK,YAAY,UAAU,CAAC,EAAE;AAClC,YAAA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,CAAA,4BAAA,CAA8B,CAAC,CAAC;AAC9D,SAAA;AACD,QAAA,WAAW,GAAGD,eAAsB,CAAC,KAAK,CAAC,CAAC;AAC7C,KAAA;AACD,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,sBAAsB,CAAC,UAAkB,EAAE,KAAiB,EAAA;IACnE,IAAI,WAAW,GAAW,EAAE,CAAC;IAC7B,IAAI,KAAK,IAAI,SAAS,EAAE;AACtB,QAAA,IAAI,EAAE,KAAK,YAAY,UAAU,CAAC,EAAE;AAClC,YAAA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,CAAA,4BAAA,CAA8B,CAAC,CAAC;AAC9D,SAAA;AACD,QAAA,WAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;AAC9C,KAAA;AACD,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,kBAAkB,CAAC,QAAgB,EAAE,KAAU,EAAE,UAAkB,EAAA;IAC1E,IAAI,KAAK,IAAI,SAAS,EAAE;QACtB,IAAI,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;AACtC,YAAA,IACE,EACE,KAAK,YAAY,IAAI;iBACpB,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACnE,EACD;AACA,gBAAA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,CAAA,0DAAA,CAA4D,CAAC,CAAC;AAC5F,aAAA;YACD,KAAK;AACH,gBAAA,KAAK,YAAY,IAAI;sBACjB,KAAK,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;AACtC,sBAAE,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACtD,SAAA;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;AACjD,YAAA,IACE,EACE,KAAK,YAAY,IAAI;iBACpB,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACnE,EACD;AACA,gBAAA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,CAAA,0DAAA,CAA4D,CAAC,CAAC;AAC5F,aAAA;YACD,KAAK,GAAG,KAAK,YAAY,IAAI,GAAG,KAAK,CAAC,WAAW,EAAE,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;AACrF,SAAA;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,oBAAoB,CAAC,KAAK,IAAI,EAAE;AACxD,YAAA,IACE,EACE,KAAK,YAAY,IAAI;iBACpB,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACnE,EACD;AACA,gBAAA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,CAAA,2DAAA,CAA6D,CAAC,CAAC;AAC7F,aAAA;YACD,KAAK,GAAG,KAAK,YAAY,IAAI,GAAG,KAAK,CAAC,WAAW,EAAE,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;AACrF,SAAA;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;AACjD,YAAA,IACE,EACE,KAAK,YAAY,IAAI;iBACpB,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACnE,EACD;AACA,gBAAA,MAAM,IAAI,KAAK,CACb,CAAA,EAAG,UAAU,CAAqE,mEAAA,CAAA;AAChF,oBAAA,CAAA,iDAAA,CAAmD,CACtD,CAAC;AACH,aAAA;AACD,YAAA,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;AAC/B,SAAA;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;AACjD,YAAA,IAAI,CAACE,UAAgB,CAAC,KAAK,CAAC,EAAE;gBAC5B,MAAM,IAAI,KAAK,CACb,CAAA,EAAG,UAAU,CAAsD,mDAAA,EAAA,KAAK,CAAI,EAAA,CAAA,CAC7E,CAAC;AACH,aAAA;AACF,SAAA;AACF,KAAA;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,qBAAqB,CAC5B,UAAsB,EACtB,MAAsB,EACtB,MAAW,EACX,UAAkB,EAClB,KAAc,EACd,OAAoC,EAAA;AAEpC,IAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AAC1B,QAAA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,CAAA,uBAAA,CAAyB,CAAC,CAAC;AACzD,KAAA;AACD,IAAA,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;AACxC,IAAA,IAAI,CAAC,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;QACnD,MAAM,IAAI,KAAK,CACb,CAAwD,sDAAA,CAAA;YACtD,CAA0C,uCAAA,EAAA,UAAU,CAAG,CAAA,CAAA,CAC1D,CAAC;AACH,KAAA;IACD,MAAM,SAAS,GAAG,EAAE,CAAC;AACrB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,QAAA,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;AAE1F,QAAA,IAAI,KAAK,IAAI,WAAW,CAAC,YAAY,EAAE;AACrC,YAAA,MAAM,QAAQ,GAAG,WAAW,CAAC,kBAAkB;AAC7C,kBAAE,CAAA,MAAA,EAAS,WAAW,CAAC,kBAAkB,CAAE,CAAA;kBACzC,OAAO,CAAC;AACZ,YAAA,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE;AACzC,gBAAA,SAAS,CAAC,CAAC,CAAC,GAAQ,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,eAAe,CAAE,CAAC;AACtC,gBAAA,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC;AACtE,aAAA;AAAM,iBAAA;AACL,gBAAA,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;gBAClB,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,eAAe,CAAC;AACnD,gBAAA,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC;AACtE,aAAA;AACF,SAAA;AAAM,aAAA;AACL,YAAA,SAAS,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC;AAChC,SAAA;AACF,KAAA;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,uBAAuB,CAC9B,UAAsB,EACtB,MAAwB,EACxB,MAAW,EACX,UAAkB,EAClB,KAAc,EACd,OAAoC,EAAA;AAEpC,IAAA,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAC9B,QAAA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,CAAA,wBAAA,CAA0B,CAAC,CAAC;AAC1D,KAAA;AACD,IAAA,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;AACpC,IAAA,IAAI,CAAC,SAAS,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;QAC/C,MAAM,IAAI,KAAK,CACb,CAA2D,yDAAA,CAAA;YACzD,CAA0C,uCAAA,EAAA,UAAU,CAAG,CAAA,CAAA,CAC1D,CAAC;AACH,KAAA;IACD,MAAM,cAAc,GAA2B,EAAE,CAAC;IAClD,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AACrC,QAAA,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;;AAE1F,QAAA,cAAc,CAAC,GAAG,CAAC,GAAG,iBAAiB,CAAC,SAAS,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AACrF,KAAA;;AAGD,IAAA,IAAI,KAAK,IAAI,MAAM,CAAC,YAAY,EAAE;AAChC,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,kBAAkB,GAAG,CAAS,MAAA,EAAA,MAAM,CAAC,kBAAkB,CAAA,CAAE,GAAG,OAAO,CAAC;QAE5F,MAAM,MAAM,GAAG,cAAc,CAAC;AAC9B,QAAA,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;AAC1D,QAAA,OAAO,MAAM,CAAC;AACf,KAAA;AAED,IAAA,OAAO,cAAc,CAAC;AACxB,CAAC;AAED;;;;;AAKG;AACH,SAAS,2BAA2B,CAClC,UAAsB,EACtB,MAAuB,EACvB,UAAkB,EAAA;AAElB,IAAA,MAAM,oBAAoB,GAAG,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;IAE9D,IAAI,CAAC,oBAAoB,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE;QAClD,MAAM,WAAW,GAAG,uBAAuB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QAC5E,OAAO,WAAW,aAAX,WAAW,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAX,WAAW,CAAE,IAAI,CAAC,oBAAoB,CAAC;AAC/C,KAAA;AAED,IAAA,OAAO,oBAAoB,CAAC;AAC9B,CAAC;AAED;;;;;AAKG;AACH,SAAS,uBAAuB,CAC9B,UAAsB,EACtB,MAAuB,EACvB,UAAkB,EAAA;AAElB,IAAA,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;IACxC,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,MAAM,IAAI,KAAK,CACb,yBAAyB,UAAU,CAAA,iCAAA,EAAoC,IAAI,CAAC,SAAS,CACnF,MAAM,EACN,SAAS,EACT,CAAC,CACF,CAAA,EAAA,CAAI,CACN,CAAC;AACH,KAAA;AAED,IAAA,OAAO,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AAC5C,CAAC;AAED;;;;AAIG;AACH,SAAS,sBAAsB,CAC7B,UAAsB,EACtB,MAAuB,EACvB,UAAkB,EAAA;AAElB,IAAA,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC;IAC7C,IAAI,CAAC,UAAU,EAAE;QACf,MAAM,WAAW,GAAG,uBAAuB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QAC5E,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,CAAmD,gDAAA,EAAA,MAAM,CAAC,IAAI,CAAC,SAAS,CAAI,EAAA,CAAA,CAAC,CAAC;AAC/F,SAAA;QACD,UAAU,GAAG,WAAW,KAAA,IAAA,IAAX,WAAW,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAX,WAAW,CAAE,IAAI,CAAC,eAAe,CAAC;QAC/C,IAAI,CAAC,UAAU,EAAE;YACf,MAAM,IAAI,KAAK,CACb,CAAqD,mDAAA,CAAA;AACnD,gBAAA,CAAA,QAAA,EAAW,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CACpC,WAAA,EAAA,MAAM,CAAC,IAAI,CAAC,SACd,CAAA,cAAA,EAAiB,UAAU,CAAA,EAAA,CAAI,CAClC,CAAC;AACH,SAAA;AACF,KAAA;AAED,IAAA,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,SAAS,sBAAsB,CAC7B,UAAsB,EACtB,MAAuB,EACvB,MAAW,EACX,UAAkB,EAClB,KAAc,EACd,OAAoC,EAAA;AAEpC,IAAA,IAAI,sCAAsC,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE;QAC9D,MAAM,GAAG,oBAAoB,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;AACzE,KAAA;IAED,IAAI,MAAM,IAAI,SAAS,EAAE;QACvB,MAAM,OAAO,GAAQ,EAAE,CAAC;QACxB,MAAM,UAAU,GAAG,sBAAsB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QAC1E,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACzC,YAAA,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;YACvC,IAAI,cAAc,CAAC,QAAQ,EAAE;gBAC3B,SAAS;AACV,aAAA;AAED,YAAA,IAAI,QAA4B,CAAC;YACjC,IAAI,YAAY,GAAQ,OAAO,CAAC;YAChC,IAAI,UAAU,CAAC,KAAK,EAAE;gBACpB,IAAI,cAAc,CAAC,YAAY,EAAE;AAC/B,oBAAA,QAAQ,GAAG,cAAc,CAAC,OAAO,CAAC;AACnC,iBAAA;AAAM,qBAAA;oBACL,QAAQ,GAAG,cAAc,CAAC,cAAc,IAAI,cAAc,CAAC,OAAO,CAAC;AACpE,iBAAA;AACF,aAAA;AAAM,iBAAA;gBACL,MAAM,KAAK,GAAG,kBAAkB,CAAC,cAAc,CAAC,cAAe,CAAC,CAAC;AACjE,gBAAA,QAAQ,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;AAEvB,gBAAA,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE;AAC5B,oBAAA,MAAM,WAAW,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;oBAC3C,IACE,WAAW,IAAI,SAAS;AACxB,yBAAC,MAAM,CAAC,GAAG,CAAC,IAAI,SAAS,IAAI,cAAc,CAAC,YAAY,KAAK,SAAS,CAAC,EACvE;AACA,wBAAA,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;AAC7B,qBAAA;AACD,oBAAA,YAAY,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;AACvC,iBAAA;AACF,aAAA;YAED,IAAI,YAAY,IAAI,SAAS,EAAE;AAC7B,gBAAA,IAAI,KAAK,IAAI,MAAM,CAAC,YAAY,EAAE;AAChC,oBAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,kBAAkB;AACxC,0BAAE,CAAA,MAAA,EAAS,MAAM,CAAC,kBAAkB,CAAE,CAAA;0BACpC,OAAO,CAAC;AACZ,oBAAA,YAAY,CAAC,WAAW,CAAC,GACpB,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,YAAY,CAAC,WAAW,CAAC,CAC5B,EAAA,EAAA,CAAC,QAAQ,GAAG,MAAM,CAAC,YAAY,GAChC,CAAC;AACH,iBAAA;AACD,gBAAA,MAAM,kBAAkB,GACtB,cAAc,CAAC,cAAc,KAAK,EAAE;AAClC,sBAAE,UAAU,GAAG,GAAG,GAAG,cAAc,CAAC,cAAc;sBAChD,UAAU,CAAC;AAEjB,gBAAA,IAAI,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC9B,MAAM,wBAAwB,GAAG,sCAAsC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AAC5F,gBAAA,IACE,wBAAwB;oBACxB,wBAAwB,CAAC,UAAU,KAAK,GAAG;oBAC3C,WAAW,IAAI,SAAS,EACxB;AACA,oBAAA,WAAW,GAAG,MAAM,CAAC,cAAc,CAAC;AACrC,iBAAA;AAED,gBAAA,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAC1C,cAAc,EACd,WAAW,EACX,kBAAkB,EAClB,OAAO,CACR,CAAC;AAEF,gBAAA,IAAI,eAAe,KAAK,SAAS,IAAI,QAAQ,IAAI,SAAS,EAAE;AAC1D,oBAAA,MAAM,KAAK,GAAG,iBAAiB,CAAC,cAAc,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AACjF,oBAAA,IAAI,KAAK,IAAI,cAAc,CAAC,cAAc,EAAE;;;;wBAI1C,YAAY,CAAC,WAAW,CAAC,GAAG,YAAY,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;wBAC5D,YAAY,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,GAAG,eAAe,CAAC;AACvD,qBAAA;AAAM,yBAAA,IAAI,KAAK,IAAI,cAAc,CAAC,YAAY,EAAE;AAC/C,wBAAA,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,cAAc,CAAC,cAAe,GAAG,KAAK,EAAE,CAAC;AACtE,qBAAA;AAAM,yBAAA;AACL,wBAAA,YAAY,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;AAChC,qBAAA;AACF,iBAAA;AACF,aAAA;AACF,SAAA;QAED,MAAM,0BAA0B,GAAG,2BAA2B,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;AAC/F,QAAA,IAAI,0BAA0B,EAAE;YAC9B,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC1C,YAAA,KAAK,MAAM,cAAc,IAAI,MAAM,EAAE;AACnC,gBAAA,MAAM,oBAAoB,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,cAAc,CAAC,CAAC;AAC5E,gBAAA,IAAI,oBAAoB,EAAE;oBACxB,OAAO,CAAC,cAAc,CAAC,GAAG,UAAU,CAAC,SAAS,CAC5C,0BAA0B,EAC1B,MAAM,CAAC,cAAc,CAAC,EACtB,UAAU,GAAG,IAAI,GAAG,cAAc,GAAG,IAAI,EACzC,OAAO,CACR,CAAC;AACH,iBAAA;AACF,aAAA;AACF,SAAA;AAED,QAAA,OAAO,OAAO,CAAC;AAChB,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,iBAAiB,CACxB,cAAsB,EACtB,eAAoB,EACpB,KAAc,EACd,OAAoC,EAAA;AAEpC,IAAA,IAAI,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE;AAC1C,QAAA,OAAO,eAAe,CAAC;AACxB,KAAA;AAED,IAAA,MAAM,QAAQ,GAAG,cAAc,CAAC,kBAAkB;AAChD,UAAE,CAAA,MAAA,EAAS,cAAc,CAAC,kBAAkB,CAAE,CAAA;UAC5C,OAAO,CAAC;IACZ,MAAM,YAAY,GAAG,EAAE,CAAC,QAAQ,GAAG,cAAc,CAAC,YAAY,EAAE,CAAC;AAEjE,IAAA,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AACpD,QAAA,IAAI,eAAe,CAAC,WAAW,CAAC,EAAE;AAChC,YAAA,OAAO,eAAe,CAAC;AACxB,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,MAAM,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAa,eAAe,CAAE,CAAC;AAC3C,YAAA,MAAM,CAAC,WAAW,CAAC,GAAG,YAAY,CAAC;AACnC,YAAA,OAAO,MAAM,CAAC;AACf,SAAA;AACF,KAAA;IACD,MAAM,MAAM,GAAQ,EAAE,CAAC;AACvB,IAAA,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,eAAe,CAAC;AAC7C,IAAA,MAAM,CAAC,WAAW,CAAC,GAAG,YAAY,CAAC;AACnC,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,oBAAoB,CAAC,YAAoB,EAAE,OAAoC,EAAA;AACtF,IAAA,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;AAClE,CAAC;AAED,SAAS,wBAAwB,CAC/B,UAAsB,EACtB,MAAuB,EACvB,YAAiB,EACjB,UAAkB,EAClB,OAAoC,EAAA;;IAEpC,MAAM,UAAU,GAAG,CAAA,EAAA,GAAA,OAAO,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,WAAW,CAAC;AACrD,IAAA,IAAI,sCAAsC,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE;QAC9D,MAAM,GAAG,oBAAoB,CAAC,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,gBAAgB,CAAC,CAAC;AACnF,KAAA;IAED,MAAM,UAAU,GAAG,sBAAsB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;IAC1E,IAAI,QAAQ,GAA2B,EAAE,CAAC;IAC1C,MAAM,oBAAoB,GAAa,EAAE,CAAC;IAE1C,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACzC,QAAA,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;QACvC,MAAM,KAAK,GAAG,kBAAkB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,cAAe,CAAC,CAAC;QAClE,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,cAAc,CAAC;QACnE,IAAI,kBAAkB,GAAG,UAAU,CAAC;AACpC,QAAA,IAAI,cAAc,KAAK,EAAE,IAAI,cAAc,KAAK,SAAS,EAAE;AACzD,YAAA,kBAAkB,GAAG,UAAU,GAAG,GAAG,GAAG,cAAc,CAAC;AACxD,SAAA;AAED,QAAA,MAAM,sBAAsB,GAAI,cAAmC,CAAC,sBAAsB,CAAC;AAC3F,QAAA,IAAI,sBAAsB,EAAE;YAC1B,MAAM,UAAU,GAAQ,EAAE,CAAC;YAC3B,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;AACjD,gBAAA,IAAI,SAAS,CAAC,UAAU,CAAC,sBAAsB,CAAC,EAAE;AAChD,oBAAA,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,WAAW,CACpF,cAAmC,CAAC,IAAI,CAAC,KAAK,EAC/C,YAAY,CAAC,SAAS,CAAC,EACvB,kBAAkB,EAClB,OAAO,CACR,CAAC;AACH,iBAAA;AAED,gBAAA,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACtC,aAAA;AACD,YAAA,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;AAC5B,SAAA;aAAM,IAAI,UAAU,CAAC,KAAK,EAAE;YAC3B,IAAI,cAAc,CAAC,cAAc,IAAI,YAAY,CAAC,WAAW,CAAC,EAAE;gBAC9D,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CACpC,cAAc,EACd,YAAY,CAAC,WAAW,CAAC,CAAC,OAAQ,CAAC,EACnC,kBAAkB,EAClB,OAAO,CACR,CAAC;AACH,aAAA;iBAAM,IAAI,cAAc,CAAC,WAAW,EAAE;AACrC,gBAAA,IAAI,YAAY,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE;oBAC1C,QAAQ,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;AAC1C,iBAAA;AAAM,qBAAA,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;;;AAG3C,oBAAA,QAAQ,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;AAC9B,iBAAA;AACF,aAAA;AAAM,iBAAA;AACL,gBAAA,MAAM,YAAY,GAAG,cAAc,IAAI,OAAO,IAAI,cAAc,CAAC;gBACjE,IAAI,cAAc,CAAC,YAAY,EAAE;AAC/B;;;;;;;;;;;;;AAaE;AACF,oBAAA,MAAM,OAAO,GAAG,YAAY,CAAC,OAAQ,CAAC,CAAC;AACvC,oBAAA,MAAM,WAAW,GAAG,CAAA,EAAA,GAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAG,cAAe,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,EAAE,CAAC;AACrD,oBAAA,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CACpC,cAAc,EACd,WAAW,EACX,kBAAkB,EAClB,OAAO,CACR,CAAC;AACH,iBAAA;AAAM,qBAAA;AACL,oBAAA,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAa,CAAC,CAAC;AAC7C,oBAAA,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CACpC,cAAc,EACd,QAAQ,EACR,kBAAkB,EAClB,OAAO,CACR,CAAC;AACH,iBAAA;AACF,aAAA;AACF,SAAA;AAAM,aAAA;;AAEL,YAAA,IAAI,gBAAgB,CAAC;YACrB,IAAI,GAAG,GAAG,YAAY,CAAC;;AAEvB,YAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AACxB,gBAAA,IAAI,CAAC,GAAG;oBAAE,MAAM;AAChB,gBAAA,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AACjB,aAAA;YACD,gBAAgB,GAAG,GAAG,CAAC;AACvB,YAAA,MAAM,wBAAwB,GAAG,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC;;;;;;;;;;AAUtE,YAAA,IACE,wBAAwB;gBACxB,GAAG,KAAK,wBAAwB,CAAC,UAAU;gBAC3C,gBAAgB,IAAI,SAAS,EAC7B;AACA,gBAAA,gBAAgB,GAAG,MAAM,CAAC,cAAc,CAAC;AAC1C,aAAA;AAED,YAAA,IAAI,eAAe,CAAC;;AAEpB,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,cAAc,KAAK,EAAE,EAAE;AAC7E,gBAAA,gBAAgB,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;AACrC,gBAAA,MAAM,aAAa,GAAG,UAAU,CAAC,WAAW,CAC1C,cAAc,EACd,gBAAgB,EAChB,kBAAkB,EAClB,OAAO,CACR,CAAC;;;AAGF,gBAAA,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAC7C,oBAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE;AAC3D,wBAAA,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACtB,qBAAA;AACF,iBAAA;gBACD,QAAQ,GAAG,aAAa,CAAC;AAC1B,aAAA;iBAAM,IAAI,gBAAgB,KAAK,SAAS,IAAI,cAAc,CAAC,YAAY,KAAK,SAAS,EAAE;AACtF,gBAAA,eAAe,GAAG,UAAU,CAAC,WAAW,CACtC,cAAc,EACd,gBAAgB,EAChB,kBAAkB,EAClB,OAAO,CACR,CAAC;AACF,gBAAA,QAAQ,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC;AACjC,aAAA;AACF,SAAA;AACF,KAAA;AAED,IAAA,MAAM,0BAA0B,GAAG,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;AACpE,IAAA,IAAI,0BAA0B,EAAE;AAC9B,QAAA,MAAM,oBAAoB,GAAG,CAAC,gBAAwB,KAAa;AACjE,YAAA,KAAK,MAAM,cAAc,IAAI,UAAU,EAAE;gBACvC,MAAM,KAAK,GAAG,kBAAkB,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,cAAc,CAAC,CAAC;AAC5E,gBAAA,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,gBAAgB,EAAE;AACjC,oBAAA,OAAO,KAAK,CAAC;AACd,iBAAA;AACF,aAAA;AACD,YAAA,OAAO,IAAI,CAAC;AACd,SAAC,CAAC;AAEF,QAAA,KAAK,MAAM,gBAAgB,IAAI,YAAY,EAAE;AAC3C,YAAA,IAAI,oBAAoB,CAAC,gBAAgB,CAAC,EAAE;gBAC1C,QAAQ,CAAC,gBAAgB,CAAC,GAAG,UAAU,CAAC,WAAW,CACjD,0BAA0B,EAC1B,YAAY,CAAC,gBAAgB,CAAC,EAC9B,UAAU,GAAG,IAAI,GAAG,gBAAgB,GAAG,IAAI,EAC3C,OAAO,CACR,CAAC;AACH,aAAA;AACF,SAAA;AACF,KAAA;AAAM,SAAA,IAAI,YAAY,EAAE;QACvB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;AAC3C,YAAA,IACE,QAAQ,CAAC,GAAG,CAAC,KAAK,SAAS;AAC3B,gBAAA,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC;AACnC,gBAAA,CAAC,oBAAoB,CAAC,GAAG,EAAE,OAAO,CAAC,EACnC;gBACA,QAAQ,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;AACnC,aAAA;AACF,SAAA;AACF,KAAA;AAED,IAAA,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,yBAAyB,CAChC,UAAsB,EACtB,MAAwB,EACxB,YAAiB,EACjB,UAAkB,EAClB,OAAoC,EAAA;AAEpC,IAAA,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;AAChC,IAAA,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QACvC,MAAM,IAAI,KAAK,CACb,CAA2D,yDAAA,CAAA;YACzD,CAA0C,uCAAA,EAAA,UAAU,CAAE,CAAA,CACzD,CAAC;AACH,KAAA;AACD,IAAA,IAAI,YAAY,EAAE;QAChB,MAAM,cAAc,GAA2B,EAAE,CAAC;QAClD,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;AAC3C,YAAA,cAAc,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,YAAY,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;AAC7F,SAAA;AACD,QAAA,OAAO,cAAc,CAAC;AACvB,KAAA;AACD,IAAA,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,SAAS,uBAAuB,CAC9B,UAAsB,EACtB,MAAsB,EACtB,YAAiB,EACjB,UAAkB,EAClB,OAAoC,EAAA;AAEpC,IAAA,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;AACpC,IAAA,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC3C,MAAM,IAAI,KAAK,CACb,CAAwD,sDAAA,CAAA;YACtD,CAA0C,uCAAA,EAAA,UAAU,CAAE,CAAA,CACzD,CAAC;AACH,KAAA;AACD,IAAA,IAAI,YAAY,EAAE;AAChB,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;;AAEhC,YAAA,YAAY,GAAG,CAAC,YAAY,CAAC,CAAC;AAC/B,SAAA;QAED,MAAM,SAAS,GAAG,EAAE,CAAC;AACrB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,SAAS,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,WAAW,CACnC,OAAO,EACP,YAAY,CAAC,CAAC,CAAC,EACf,CAAA,EAAG,UAAU,CAAA,CAAA,EAAI,CAAC,CAAG,CAAA,CAAA,EACrB,OAAO,CACR,CAAC;AACH,SAAA;AACD,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;AACD,IAAA,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,SAAS,oBAAoB,CAC3B,UAAsB,EACtB,MAAuB,EACvB,MAAW,EACX,uBAAwD,EAAA;IAExD,MAAM,wBAAwB,GAAG,sCAAsC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AAC5F,IAAA,IAAI,wBAAwB,EAAE;AAC5B,QAAA,MAAM,iBAAiB,GAAG,wBAAwB,CAAC,uBAAuB,CAAC,CAAC;QAC5E,IAAI,iBAAiB,IAAI,SAAS,EAAE;AAClC,YAAA,MAAM,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACrD,IAAI,kBAAkB,IAAI,SAAS,EAAE;AACnC,gBAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;AACjE,gBAAA,MAAM,kBAAkB,GACtB,kBAAkB,KAAK,QAAQ;AAC7B,sBAAE,kBAAkB;AACpB,sBAAE,QAAQ,GAAG,GAAG,GAAG,kBAAkB,CAAC;gBAC1C,MAAM,iBAAiB,GAAG,UAAU,CAAC,YAAY,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;AACrF,gBAAA,IAAI,iBAAiB,EAAE;oBACrB,MAAM,GAAG,iBAAiB,CAAC;AAC5B,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,sCAAsC,CAC7C,UAAsB,EACtB,MAAuB,EAAA;AAEvB,IAAA,QACE,MAAM,CAAC,IAAI,CAAC,wBAAwB;QACpC,iCAAiC,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QACrE,iCAAiC,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EACpE;AACJ,CAAC;AAED,SAAS,iCAAiC,CAAC,UAAsB,EAAE,QAAiB,EAAA;AAClF,IAAA,QACE,QAAQ;AACR,QAAA,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC;QACjC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,wBAAwB,EAC/D;AACJ,CAAC;AA6TD;;AAEG;AACG,SAAU,eAAe,CAAC,WAAoB,EAAA;IAClD,MAAM,eAAe,GAAG,WAAsC,CAAC;IAC/D,IAAI,WAAW,IAAI,SAAS;AAAE,QAAA,OAAO,SAAS,CAAC;IAC/C,IAAI,WAAW,YAAY,UAAU,EAAE;AACrC,QAAA,WAAW,GAAGF,eAAsB,CAAC,WAAW,CAAC,CAAC;AAClD,QAAA,OAAO,WAAW,CAAC;AACpB,KAAA;SAAM,IAAI,WAAW,YAAY,IAAI,EAAE;AACtC,QAAA,OAAO,WAAW,CAAC,WAAW,EAAE,CAAC;AAClC,KAAA;AAAM,SAAA,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;QACrC,MAAM,KAAK,GAAG,EAAE,CAAC;AACjB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,SAAA;AACD,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AAAM,SAAA,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;QAC1C,MAAM,UAAU,GAA2B,EAAE,CAAC;AAC9C,QAAA,KAAK,MAAM,QAAQ,IAAI,WAAW,EAAE;YAClC,UAAU,CAAC,QAAQ,CAAC,GAAG,eAAe,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;AACnE,SAAA;AACD,QAAA,OAAO,UAAU,CAAC;AACnB,KAAA;AACD,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;AAEG;AACH,SAAS,OAAO,CAAmB,CAAW,EAAA;IAC5C,MAAM,MAAM,GAAQ,EAAE,CAAC;AACvB,IAAA,KAAK,MAAM,GAAG,IAAI,CAAC,EAAE;AACnB,QAAA,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AACnB,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;AAEG;AACH;AACO,MAAM,UAAU,GAAG,OAAO,CAAC;IAChC,WAAW;IACX,SAAS;IACT,WAAW;IACX,WAAW;IACX,MAAM;IACN,UAAU;IACV,iBAAiB;IACjB,YAAY;IACZ,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,UAAU;AACX,CAAA;;ACh+CD;AAmKM,SAAU,iBAAiB,CAAC,MAAe,EAAA;AAC/C,IAAA,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QACxC,MAAM,UAAU,GAAG,MAOlB,CAAC;AACF,QAAA,IACE,OAAO,UAAU,CAAC,GAAG,KAAK,QAAQ;AAClC,YAAA,OAAO,UAAU,CAAC,MAAM,KAAK,QAAQ;AACrC,YAAA,OAAO,UAAU,CAAC,OAAO,KAAK,QAAQ;AACtC,YAAA,iBAAiB,CAAC,UAAU,CAAC,OAAO,CAAC;AACrC,YAAA,OAAO,UAAU,CAAC,yBAAyB,KAAK,UAAU;AAC1D,YAAA,OAAO,UAAU,CAAC,OAAO,KAAK,UAAU;AACxC,YAAA,OAAO,UAAU,CAAC,KAAK,KAAK,UAAU,EACtC;AACA,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AACF,KAAA;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;AAKG;MACU,WAAW,CAAA;AAsGtB,IAAA,WAAA,CACE,GAAY,EACZ,MAAoB,EACpB,IAAc,EACd,KAA8B,EAC9B,OAAkD,EAClD,kBAA4B,EAC5B,eAAyB,EACzB,WAA6B,EAC7B,OAAgB,EAChB,gBAA4D,EAC5D,kBAA8D,EAC9D,aAA6B,EAC7B,SAAmB,EACnB,kBAA4B,EAC5B,yBAAuC,EAAA;AAEvC,QAAA,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;AAC7C,QAAA,IAAI,CAAC,yBAAyB,GAAG,yBAAyB,CAAC;AAC3D,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;AACrB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,KAAK,CAAC;AAC9B,QAAA,IAAI,CAAC,OAAO,GAAG,iBAAiB,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;AAC/E,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;AAC1B,QAAA,IAAI,CAAC,eAAe,GAAG,eAAe,IAAI,KAAK,CAAC;AAChD,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AAC/B,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,CAAC,CAAC;AAC5B,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACzC,QAAA,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;AAC7C,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACnC,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,QAAA,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;AAC7C,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,YAAY,EAAE,CAAC;KAC/E;AAED;;;;AAIG;IACH,yBAAyB,GAAA;AACvB,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAChB,YAAA,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;AACpD,SAAA;AACD,QAAA,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;AACb,YAAA,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;AACjD,SAAA;KACF;AAED;;;;AAIG;AACH,IAAA,OAAO,CAAC,OAA8B,EAAA;QACpC,IAAI,CAAC,OAAO,EAAE;AACZ,YAAA,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;AAC/C,SAAA;AAED,QAAA,IACE,OAAO,CAAC,MAAM,KAAK,SAAS;YAC5B,OAAO,CAAC,MAAM,KAAK,IAAI;YACvB,OAAO,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,QAAQ,EAC5C;AACA,YAAA,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;AACrD,SAAA;AAED,QAAA,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,YAAY,EAAE;AACvC,YAAA,MAAM,IAAI,KAAK,CACb,kGAAkG,CACnG,CAAC;AACH,SAAA;AAED,QAAA,IACE,CAAC,OAAO,CAAC,YAAY,KAAK,SAAS;YACjC,OAAO,CAAC,YAAY,KAAK,IAAI;YAC7B,OAAO,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,QAAQ;AACpD,aAAC,OAAO,CAAC,GAAG,KAAK,SAAS;gBACxB,OAAO,CAAC,GAAG,KAAK,IAAI;gBACpB,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,QAAQ,CAAC,EAC5C;AACA,YAAA,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;AACvF,SAAA;;QAGD,IAAI,OAAO,CAAC,GAAG,EAAE;AACf,YAAA,IAAI,OAAO,OAAO,CAAC,GAAG,KAAK,QAAQ,EAAE;AACnC,gBAAA,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;AAC1D,aAAA;AACD,YAAA,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;AACxB,SAAA;;QAGD,IAAI,OAAO,CAAC,MAAM,EAAE;AAClB,YAAA,MAAM,YAAY,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AAC3F,YAAA,IAAI,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC7D,MAAM,IAAI,KAAK,CACb,uBAAuB;AACrB,oBAAA,OAAO,CAAC,MAAM;oBACd,4CAA4C;AAC5C,oBAAA,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAC/B,CAAC;AACH,aAAA;AACF,SAAA;QACD,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,EAAiB,CAAC;;QAG1D,IAAI,OAAO,CAAC,YAAY,EAAE;AACxB,YAAA,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACjD,YAAA,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;AACpC,gBAAA,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;AACnE,aAAA;AACD,YAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AACpB,gBAAA,OAAO,CAAC,OAAO,GAAG,8BAA8B,CAAC;AAClD,aAAA;AACD,YAAA,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YAChC,IAAI,GAAG,GACL,OAAO;AACP,iBAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC;iBACjC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC;YACxE,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;AACpD,YAAA,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,EAAE;gBAC/B,IAAI,CAAC,cAAc,EAAE;AACnB,oBAAA,MAAM,IAAI,KAAK,CACb,iBAAiB,YAAY,CAAA,wEAAA,CAA0E,CACxG,CAAC;AACH,iBAAA;AACD,gBAAA,QAAQ,CAAC,OAAO,CAAC,UAAU,IAAI,EAAA;oBAC7B,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACxC,oBAAA,MAAM,SAAS,GAAI,cAAyC,CAAC,aAAa,CAAC,CAAC;oBAC5E,IACE,SAAS,KAAK,IAAI;AAClB,wBAAA,SAAS,KAAK,SAAS;wBACvB,EAAE,OAAO,SAAS,KAAK,QAAQ,IAAI,OAAO,SAAS,KAAK,QAAQ,CAAC,EACjE;AACA,wBAAA,MAAM,yBAAyB,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;AAC/E,wBAAA,MAAM,IAAI,KAAK,CACb,iBAAiB,YAAY,CAAA,6BAAA,EAAgC,aAAa,CAAE,CAAA;AAC1E,4BAAA,CAAA,2CAAA,EAA8C,yBAAyB,CAAG,CAAA,CAAA;AAC1E,4BAAA,CAAA,uEAAA,EAA0E,aAAa,CAA6B,2BAAA,CAAA;4BACpH,CAAwC,qCAAA,EAAA,aAAa,CAA6D,2DAAA,CAAA,CACrH,CAAC;AACH,qBAAA;AAED,oBAAA,IAAI,OAAO,SAAS,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;AAC3C,wBAAA,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC;AACxD,qBAAA;AAED,oBAAA,IAAI,OAAO,SAAS,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;AAC3C,wBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;AACpB,4BAAA,MAAM,IAAI,KAAK,CACb,0BAA0B,aAAa,CAAA,iEAAA,CAAmE,CAC3G,CAAC;AACH,yBAAA;wBACD,IAAI,SAAS,CAAC,eAAe,EAAE;4BAC7B,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;AAC1C,yBAAA;AAAM,6BAAA;AACL,4BAAA,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,kBAAkB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;AAC9D,yBAAA;AACF,qBAAA;AACH,iBAAC,CAAC,CAAC;AACJ,aAAA;AACD,YAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AAChB,SAAA;;QAGD,IAAI,OAAO,CAAC,eAAe,EAAE;AAC3B,YAAA,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;AAChD,YAAA,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;gBACvC,MAAM,IAAI,KAAK,CACb,CAA6E,2EAAA,CAAA;oBAC3E,CAAqF,mFAAA,CAAA;AACrF,oBAAA,CAAA,yIAAA,CAA2I,CAC9I,CAAC;AACH,aAAA;;AAED,YAAA,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;AAC5C,gBAAA,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC;AACjB,aAAA;;YAED,MAAM,WAAW,GAAG,EAAE,CAAC;;AAEvB,YAAA,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AAChB,YAAA,KAAK,MAAM,cAAc,IAAI,eAAe,EAAE;AAC5C,gBAAA,MAAM,UAAU,GAAQ,eAAe,CAAC,cAAc,CAAC,CAAC;AACxD,gBAAA,IAAI,UAAU,EAAE;AACd,oBAAA,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;AAClC,wBAAA,WAAW,CAAC,IAAI,CAAC,cAAc,GAAG,GAAG,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;wBACxE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;AAC7D,qBAAA;AAAM,yBAAA,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;AACzC,wBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;AACrB,4BAAA,MAAM,IAAI,KAAK,CACb,2BAA2B,cAAc,CAAA,iEAAA,CAAmE,CAC7G,CAAC;AACH,yBAAA;wBACD,IAAI,UAAU,CAAC,eAAe,EAAE;4BAC9B,WAAW,CAAC,IAAI,CAAC,cAAc,GAAG,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;4BAC1D,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC;AAC/C,yBAAA;AAAM,6BAAA;AACL,4BAAA,WAAW,CAAC,IAAI,CAAC,cAAc,GAAG,GAAG,GAAG,kBAAkB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;AAC9E,4BAAA,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AACnE,yBAAA;AACF,qBAAA;AACF,iBAAA;AACF,aAAA;;YAED,IAAI,CAAC,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnC,SAAA;;QAGD,IAAI,OAAO,CAAC,OAAO,EAAE;AACnB,YAAA,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YAChC,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AACrD,gBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;AACnD,aAAA;AACF,SAAA;;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE;YACxC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;AAC9C,SAAA;;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;YAClF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AAC5D,SAAA;;QAGD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;YACrC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,iCAAiC,CAAC,CAAC;AACrE,SAAA;;AAGD,QAAA,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACzB,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,EAAE;;YAEvD,IAAI,OAAO,CAAC,YAAY,EAAE;gBACxB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE;oBAC1C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC;AAClD,iBAAA;gBACD,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,0BAA0B,EAAE;oBACnE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,0BAA0B,CAAC,CAAC;AAC9D,iBAAA;AACF,aAAA;AAAM,iBAAA;gBACL,IAAI,OAAO,CAAC,mBAAmB,EAAE;oBAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,SAAS,CACnD,OAAO,CAAC,mBAAmB,EAC3B,OAAO,CAAC,IAAI,EACZ,aAAa,CACd,CAAC;AACH,iBAAA;AACD,gBAAA,IAAI,CAAC,OAAO,CAAC,0BAA0B,EAAE;oBACvC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC1C,iBAAA;AACF,aAAA;AACF,SAAA;QAED,IAAI,OAAO,CAAC,WAAW,EAAE;AACvB,YAAA,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;AACxC,SAAA;QAED,IAAI,OAAO,CAAC,cAAc,EAAE;AAC1B,YAAA,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;AAC9C,SAAA;AAED,QAAA,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;AACvC,QAAA,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;AACrD,QAAA,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;AAEjD,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;IACH,KAAK,GAAA;AACH,QAAA,MAAM,MAAM,GAAG,IAAI,WAAW,CAC5B,IAAI,CAAC,GAAG,EACR,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EACpC,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,yBAAyB,CAC/B,CAAC;QAEF,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACjC,SAAA;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;AAC3C,SAAA;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,YAAA,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;AACnD,SAAA;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;AAChC,YAAA,MAAM,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC;AAC/D,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACf;AACF;;AChmBD;AAOA;;AAEG;MACU,QAAQ,CAAA;AAArB,IAAA,WAAA,GAAA;QACmB,IAAS,CAAA,SAAA,GAAwD,EAAE,CAAC;KAiItF;AA/HC;;AAEG;IACI,GAAG,GAAA;AACR,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;KAC/C;AAED;;AAEG;IACI,IAAI,GAAA;QACT,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACpC;AAED;;;;AAIG;IACI,GAAG,CAAC,aAAqB,EAAE,cAAuB,EAAA;QACvD,MAAM,kBAAkB,GAAG,cAE1B,CAAC;AACF,QAAA,IAAI,aAAa,EAAE;AACjB,YAAA,IAAI,kBAAkB,KAAK,SAAS,IAAI,kBAAkB,KAAK,IAAI,EAAE;AACnE,gBAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC;AAChD,sBAAE,kBAAkB;AACpB,sBAAE,kBAAkB,CAAC,QAAQ,EAAE,CAAC;AAClC,gBAAA,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC;AAC1C,aAAA;AAAM,iBAAA;AACL,gBAAA,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;AACtC,aAAA;AACF,SAAA;KACF;AAED;;;AAGG;AACI,IAAA,GAAG,CAAC,aAAqB,EAAA;AAC9B,QAAA,OAAO,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,SAAS,CAAC;KAClE;AAED;;AAEG;IACI,QAAQ,GAAA;QACb,IAAI,MAAM,GAAG,EAAE,CAAC;AAChB,QAAA,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,SAAS,EAAE;AAC1C,YAAA,IAAI,MAAM,EAAE;gBACV,MAAM,IAAI,GAAG,CAAC;AACf,aAAA;YACD,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;AACrD,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;gBACjC,MAAM,gBAAgB,GAAG,EAAE,CAAC;AAC5B,gBAAA,KAAK,MAAM,qBAAqB,IAAI,cAAc,EAAE;oBAClD,gBAAgB,CAAC,IAAI,CAAC,CAAA,EAAG,aAAa,CAAI,CAAA,EAAA,qBAAqB,CAAE,CAAA,CAAC,CAAC;AACpE,iBAAA;AACD,gBAAA,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACtC,aAAA;AAAM,iBAAA;AACL,gBAAA,MAAM,IAAI,CAAG,EAAA,aAAa,CAAI,CAAA,EAAA,cAAc,EAAE,CAAC;AAChD,aAAA;AACF,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;AAED;;AAEG;IACI,OAAO,KAAK,CAAC,IAAY,EAAA;AAC9B,QAAA,MAAM,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;AAE9B,QAAA,IAAI,IAAI,EAAE;AACR,YAAA,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AACxB,gBAAA,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAC1B,aAAA;YAED,IAAI,YAAY,GAAuB,eAAe,CAAC;YAEvD,IAAI,aAAa,GAAG,EAAE,CAAC;YACvB,IAAI,cAAc,GAAG,EAAE,CAAC;AACxB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AACpC,gBAAA,MAAM,gBAAgB,GAAW,IAAI,CAAC,CAAC,CAAC,CAAC;AACzC,gBAAA,QAAQ,YAAY;AAClB,oBAAA,KAAK,eAAe;AAClB,wBAAA,QAAQ,gBAAgB;AACtB,4BAAA,KAAK,GAAG;gCACN,YAAY,GAAG,gBAAgB,CAAC;gCAChC,MAAM;AAER,4BAAA,KAAK,GAAG;gCACN,aAAa,GAAG,EAAE,CAAC;gCACnB,cAAc,GAAG,EAAE,CAAC;gCACpB,MAAM;AAER,4BAAA;gCACE,aAAa,IAAI,gBAAgB,CAAC;gCAClC,MAAM;AACT,yBAAA;wBACD,MAAM;AAER,oBAAA,KAAK,gBAAgB;AACnB,wBAAA,QAAQ,gBAAgB;AACtB,4BAAA,KAAK,GAAG;AACN,gCAAA,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;gCAC1C,aAAa,GAAG,EAAE,CAAC;gCACnB,cAAc,GAAG,EAAE,CAAC;gCACpB,YAAY,GAAG,eAAe,CAAC;gCAC/B,MAAM;AAER,4BAAA;gCACE,cAAc,IAAI,gBAAgB,CAAC;gCACnC,MAAM;AACT,yBAAA;wBACD,MAAM;AAER,oBAAA;AACE,wBAAA,MAAM,IAAI,KAAK,CAAC,qCAAqC,GAAG,YAAY,CAAC,CAAC;AACzE,iBAAA;AACF,aAAA;YACD,IAAI,YAAY,KAAK,gBAAgB,EAAE;AACrC,gBAAA,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;AAC3C,aAAA;AACF,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACf;AACF,CAAA;AAED;;AAEG;MACU,UAAU,CAAA;AAOrB;;;AAGG;AACI,IAAA,SAAS,CAAC,MAA0B,EAAA;QACzC,IAAI,CAAC,MAAM,EAAE;AACX,YAAA,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;AAC1B,SAAA;AAAM,aAAA;AACL,YAAA,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC5B,SAAA;KACF;AAED;;AAEG;IACI,SAAS,GAAA;QACd,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;AAED;;;AAGG;AACI,IAAA,OAAO,CAAC,IAAwB,EAAA;QACrC,IAAI,CAAC,IAAI,EAAE;AACT,YAAA,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;AACxB,SAAA;AAAM,aAAA;AACL,YAAA,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;AAClC,SAAA;KACF;AAED;;AAEG;IACI,OAAO,GAAA;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;AAED;;;AAGG;AACI,IAAA,OAAO,CAAC,IAAiC,EAAA;QAC9C,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE,EAAE;AACtD,YAAA,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;AACxB,SAAA;AAAM,aAAA;YACL,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC,CAAC;AACnC,SAAA;KACF;AAED;;AAEG;IACI,OAAO,GAAA;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;AAED;;;AAGG;AACI,IAAA,OAAO,CAAC,IAAwB,EAAA;QACrC,IAAI,CAAC,IAAI,EAAE;AACT,YAAA,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;AACxB,SAAA;AAAM,aAAA;YACL,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACxC,YAAA,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE;gBACtB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;;;gBAGvD,IAAI,CAAC,GAAG,CAAC,WAAW,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC9E,aAAA;AAAM,iBAAA;AACL,gBAAA,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACxB,aAAA;AACF,SAAA;KACF;AAED;;;AAGG;AACI,IAAA,UAAU,CAAC,IAAwB,EAAA;AACxC,QAAA,IAAI,IAAI,EAAE;AACR,YAAA,IAAI,WAAW,GAAuB,IAAI,CAAC,OAAO,EAAE,CAAC;AACrD,YAAA,IAAI,WAAW,EAAE;AACf,gBAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;oBAC9B,WAAW,IAAI,GAAG,CAAC;AACpB,iBAAA;AAED,gBAAA,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AACxB,oBAAA,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAC1B,iBAAA;AAED,gBAAA,IAAI,GAAG,WAAW,GAAG,IAAI,CAAC;AAC3B,aAAA;AACD,YAAA,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACxB,SAAA;KACF;AAED;;AAEG;IACI,OAAO,GAAA;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;AAED;;AAEG;AACI,IAAA,QAAQ,CAAC,KAAyB,EAAA;QACvC,IAAI,CAAC,KAAK,EAAE;AACV,YAAA,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;AACzB,SAAA;AAAM,aAAA;YACL,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACrC,SAAA;KACF;AAED;;;;AAIG;IACI,iBAAiB,CAAC,kBAA0B,EAAE,mBAA4B,EAAA;AAC/E,QAAA,IAAI,kBAAkB,EAAE;AACtB,YAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAChB,gBAAA,IAAI,CAAC,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;AAC9B,aAAA;YACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;AAC1D,SAAA;KACF;AAED;;;AAGG;AACI,IAAA,sBAAsB,CAAC,kBAA0B,EAAA;AACtD,QAAA,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC,GAAG,SAAS,CAAC;KACtE;AAED;;AAEG;IACI,QAAQ,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,SAAS,CAAC;KACzD;AAED;;AAEG;IACK,GAAG,CAAC,IAAY,EAAE,UAA6B,EAAA;QACrD,MAAM,SAAS,GAAG,IAAI,YAAY,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AAErD,QAAA,OAAO,SAAS,CAAC,IAAI,EAAE,EAAE;AACvB,YAAA,MAAM,KAAK,GAAyB,SAAS,CAAC,OAAO,EAAE,CAAC;AACxD,YAAA,IAAI,SAA6B,CAAC;AAClC,YAAA,IAAI,KAAK,EAAE;gBACT,QAAQ,KAAK,CAAC,IAAI;AAChB,oBAAA,KAAK,QAAQ;wBACX,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI,IAAI,SAAS,CAAC;wBACvC,MAAM;AAER,oBAAA,KAAK,MAAM;wBACT,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,IAAI,SAAS,CAAC;wBACrC,MAAM;AAER,oBAAA,KAAK,MAAM;wBACT,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,IAAI,SAAS,CAAC;wBACrC,MAAM;AAER,oBAAA,KAAK,MAAM;AACT,wBAAA,SAAS,GAAG,KAAK,CAAC,IAAI,IAAI,SAAS,CAAC;AACpC,wBAAA,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,SAAS,KAAK,GAAG,EAAE;AAC1D,4BAAA,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;AACxB,yBAAA;wBACD,MAAM;AAER,oBAAA,KAAK,OAAO;wBACV,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBACzC,MAAM;AAER,oBAAA;wBACE,MAAM,IAAI,KAAK,CAAC,CAAA,2BAAA,EAA8B,KAAK,CAAC,IAAI,CAAE,CAAA,CAAC,CAAC;AAC/D,iBAAA;AACF,aAAA;AACF,SAAA;KACF;AAED;;;AAGG;IACI,QAAQ,GAAA;QACb,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,MAAM,IAAI,CAAG,EAAA,IAAI,CAAC,OAAO,KAAK,CAAC;AAChC,SAAA;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC;AACtB,SAAA;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,MAAM,IAAI,CAAI,CAAA,EAAA,IAAI,CAAC,KAAK,EAAE,CAAC;AAC5B,SAAA;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;gBAC/B,MAAM,IAAI,GAAG,CAAC;AACf,aAAA;AACD,YAAA,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC;AACtB,SAAA;QAED,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE;YACpC,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAA,CAAE,CAAC;AACxC,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACf;AAED;;;AAGG;IACI,UAAU,CAAC,WAAmB,EAAE,YAAoB,EAAA;AACzD,QAAA,IAAI,WAAW,EAAE;AACf,YAAA,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;AACxE,YAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;AACpE,YAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;AACpE,YAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;AACpE,YAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;AACvE,SAAA;KACF;AAED;;AAEG;IACI,OAAO,KAAK,CAAC,IAAY,EAAA;AAC9B,QAAA,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;AAChC,QAAA,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;AACnC,QAAA,OAAO,MAAM,CAAC;KACf;AACF,CAAA;MAMY,QAAQ,CAAA;IACnB,WAAmC,CAAA,IAAY,EAAkB,IAAkB,EAAA;QAAhD,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QAAkB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAc;KAAI;IAEhF,OAAO,MAAM,CAAC,IAAY,EAAA;AAC/B,QAAA,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;KACrC;IAEM,OAAO,IAAI,CAAC,IAAY,EAAA;AAC7B,QAAA,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACnC;IAEM,OAAO,IAAI,CAAC,IAAY,EAAA;AAC7B,QAAA,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACnC;IAEM,OAAO,IAAI,CAAC,IAAY,EAAA;AAC7B,QAAA,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACnC;IAEM,OAAO,KAAK,CAAC,IAAY,EAAA;AAC9B,QAAA,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KACpC;AACF,CAAA;AAED;;;AAGG;AACG,SAAU,uBAAuB,CAAC,SAAiB,EAAA;IACvD,MAAM,aAAa,GAAW,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACtD,IAAA,QACE,CAAC,EAAE,cAAc,aAAa,IAAI,aAAa,IAAI,EAAE;SACpD,EAAE,cAAc,aAAa,IAAI,aAAa,IAAI,EAAE,CAAC;AACtD,SAAC,EAAE,cAAc,aAAa,IAAI,aAAa,IAAI,GAAG,CAAC,YACvD;AACJ,CAAC;AAED;;AAEG;MACU,YAAY,CAAA;IAMvB,WAA4B,CAAA,KAAa,EAAE,KAAyB,EAAA;QAAxC,IAAK,CAAA,KAAA,GAAL,KAAK,CAAQ;AACvC,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAC5C,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,GAAG,KAAK,GAAG,gBAAgB,CAAC;AACtF,QAAA,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KACxB;AAED;;;AAGG;IACI,OAAO,GAAA;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC;KAC3B;AAED;;AAEG;IACI,IAAI,GAAA;AACT,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE;AAC9B,YAAA,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;AAChC,SAAA;AAAM,aAAA;YACL,QAAQ,IAAI,CAAC,aAAa;AACxB,gBAAA,KAAK,QAAQ;oBACX,UAAU,CAAC,IAAI,CAAC,CAAC;oBACjB,MAAM;AAER,gBAAA,KAAK,gBAAgB;oBACnB,gBAAgB,CAAC,IAAI,CAAC,CAAC;oBACvB,MAAM;AAER,gBAAA,KAAK,MAAM;oBACT,QAAQ,CAAC,IAAI,CAAC,CAAC;oBACf,MAAM;AAER,gBAAA,KAAK,MAAM;oBACT,QAAQ,CAAC,IAAI,CAAC,CAAC;oBACf,MAAM;AAER,gBAAA,KAAK,MAAM;oBACT,QAAQ,CAAC,IAAI,CAAC,CAAC;oBACf,MAAM;AAER,gBAAA,KAAK,OAAO;oBACV,SAAS,CAAC,IAAI,CAAC,CAAC;oBAChB,MAAM;AAER,gBAAA;oBACE,MAAM,IAAI,KAAK,CAAC,CAAA,gCAAA,EAAmC,IAAI,CAAC,aAAa,CAAE,CAAA,CAAC,CAAC;AAC5E,aAAA;AACF,SAAA;AACD,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC;KAC7B;AACF,CAAA;AAED;;AAEG;AACH,SAAS,aAAa,CAAC,SAAuB,EAAA;IAC5C,IAAI,MAAM,GAAG,EAAE,CAAC;AAChB,IAAA,IAAI,SAAS,CAAC,aAAa,GAAG,SAAS,CAAC,WAAW,EAAE;QACnD,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;AAC5D,QAAA,SAAS,CAAC,aAAa,GAAG,SAAS,CAAC,WAAW,CAAC;AACjD,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;AAEG;AACH,SAAS,mBAAmB,CAAC,SAAuB,EAAA;AAClD,IAAA,OAAO,SAAS,CAAC,aAAa,GAAG,SAAS,CAAC,WAAW,CAAC;AACzD,CAAC;AAED;;AAEG;AACH,SAAS,mBAAmB,CAAC,SAAuB,EAAA;IAClD,OAAO,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;AAClD,CAAC;AAED;;;AAGG;AACH,SAAS,aAAa,CAAC,SAAuB,EAAE,IAAa,EAAA;AAC3D,IAAA,IAAI,mBAAmB,CAAC,SAAS,CAAC,EAAE;QAClC,IAAI,CAAC,IAAI,EAAE;YACT,IAAI,GAAG,CAAC,CAAC;AACV,SAAA;AACD,QAAA,SAAS,CAAC,aAAa,IAAI,IAAI,CAAC;AACjC,KAAA;AACH,CAAC;AAED;;;AAGG;AACH,SAAS,cAAc,CAAC,SAAuB,EAAE,gBAAwB,EAAA;AACvE,IAAA,IAAI,QAAQ,GAAW,SAAS,CAAC,aAAa,GAAG,gBAAgB,CAAC;AAClE,IAAA,IAAI,SAAS,CAAC,WAAW,GAAG,QAAQ,EAAE;AACpC,QAAA,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC;AAClC,KAAA;AACD,IAAA,OAAO,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;AACtE,CAAC;AAED;;;AAGG;AACH,SAAS,SAAS,CAAC,SAAuB,EAAE,SAAyC,EAAA;IACnF,IAAI,MAAM,GAAG,EAAE,CAAC;AAEhB,IAAA,OAAO,mBAAmB,CAAC,SAAS,CAAC,EAAE;AACrC,QAAA,MAAM,gBAAgB,GAAW,mBAAmB,CAAC,SAAS,CAAC,CAAC;AAChE,QAAA,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE;YAChC,MAAM;AACP,SAAA;AAAM,aAAA;YACL,MAAM,IAAI,gBAAgB,CAAC;YAC3B,aAAa,CAAC,SAAS,CAAC,CAAC;AAC1B,SAAA;AACF,KAAA;AAED,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;AAGG;AACH,SAAS,sBAAsB,CAAC,SAAuB,EAAA;AACrD,IAAA,OAAO,SAAS,CAAC,SAAS,EAAE,CAAC,SAAiB,KAAK,uBAAuB,CAAC,SAAS,CAAC,CAAC,CAAC;AACzF,CAAC;AAED;;;AAGG;AACH,SAAS,kBAAkB,CAAC,SAAuB,EAAE,GAAG,qBAA+B,EAAA;AACrF,IAAA,OAAO,SAAS,CACd,SAAS,EACT,CAAC,SAAiB,KAAK,qBAAqB,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CACvE,CAAC;AACJ,CAAC;AAED,SAAS,UAAU,CAAC,SAAuB,EAAA;AACzC,IAAA,MAAM,MAAM,GAAW,sBAAsB,CAAC,SAAS,CAAC,CAAC;IACzD,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAClD,IAAA,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE;AACnC,QAAA,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;AAClC,KAAA;AAAM,SAAA;AACL,QAAA,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;AAClC,KAAA;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,SAAuB,EAAA;AAC/C,IAAA,MAAM,YAAY,GAAW,kBAAkB,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAC1E,IAAA,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE;QACnC,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACtD,QAAA,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;AAClC,KAAA;AAAM,SAAA,IAAI,mBAAmB,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;QACjD,IAAI,cAAc,CAAC,SAAS,EAAE,CAAC,CAAC,KAAK,KAAK,EAAE;YAC1C,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AACxD,YAAA,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;AAClC,SAAA;AAAM,aAAA;YACL,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACtD,YAAA,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;AAClC,SAAA;AACF,KAAA;AAAM,SAAA;QACL,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACtD,QAAA,IAAI,mBAAmB,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;AAC1C,YAAA,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;AAClC,SAAA;AAAM,aAAA;AACL,YAAA,SAAS,CAAC,aAAa,GAAG,OAAO,CAAC;AACnC,SAAA;AACF,KAAA;AACH,CAAC;AAED,SAAS,QAAQ,CAAC,SAAuB,EAAA;IACvC,IAAI,cAAc,CAAC,SAAS,EAAE,CAAC,CAAC,KAAK,KAAK,EAAE;AAC1C,QAAA,aAAa,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;AAC7B,KAAA;AAED,IAAA,MAAM,IAAI,GAAW,kBAAkB,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAClE,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAE9C,IAAA,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE;AACnC,QAAA,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;AAClC,KAAA;AAAM,SAAA,IAAI,mBAAmB,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;AACjD,QAAA,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;AAClC,KAAA;AAAM,SAAA,IAAI,mBAAmB,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;AACjD,QAAA,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;AAClC,KAAA;AAAM,SAAA;AACL,QAAA,SAAS,CAAC,aAAa,GAAG,OAAO,CAAC;AACnC,KAAA;AACH,CAAC;AAED,SAAS,QAAQ,CAAC,SAAuB,EAAA;AACvC,IAAA,IAAI,mBAAmB,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;QAC1C,aAAa,CAAC,SAAS,CAAC,CAAC;AAC1B,KAAA;IAED,MAAM,IAAI,GAAW,kBAAkB,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC7D,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAE9C,IAAA,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE;AACnC,QAAA,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;AAClC,KAAA;AAAM,SAAA,IAAI,mBAAmB,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;AACjD,QAAA,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;AAClC,KAAA;AAAM,SAAA;AACL,QAAA,SAAS,CAAC,aAAa,GAAG,OAAO,CAAC;AACnC,KAAA;AACH,CAAC;AAED,SAAS,QAAQ,CAAC,SAAuB,EAAA;IACvC,MAAM,IAAI,GAAW,kBAAkB,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;IACxD,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAE9C,IAAA,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE;AACnC,QAAA,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;AAClC,KAAA;AAAM,SAAA;AACL,QAAA,SAAS,CAAC,aAAa,GAAG,OAAO,CAAC;AACnC,KAAA;AACH,CAAC;AAED,SAAS,SAAS,CAAC,SAAuB,EAAA;AACxC,IAAA,IAAI,mBAAmB,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;QAC1C,aAAa,CAAC,SAAS,CAAC,CAAC;AAC1B,KAAA;AAED,IAAA,MAAM,KAAK,GAAW,aAAa,CAAC,SAAS,CAAC,CAAC;IAC/C,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAChD,IAAA,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;AACnC;;ACtqBA;SAWgB,gBAAgB,CAC9B,UAAkB,EAClB,aAA4B,EAC5B,OAAyB,EAAA;AAEzB,IAAA,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,OAAO,EAAY,CAAC;IACtE,IAAI,CAAC,IAAI,EAAE;AACT,QAAA,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;AAClE,KAAA;AACD,IAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;AACpC,QAAA,MAAM,IAAI,KAAK,CAAC,6EAA6E,CAAC,CAAC;AAChG,KAAA;AACD,IAAA,MAAM,aAAa,GAAiC;AAClD,QAAA,KAAK,EAAE;AACL,YAAA,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,OAAO,EAAE,CAAC,OAAO,IAAI,OAAO,CAAC,UAAU,EAAE,KAAK,EAAE;AACjD,SAAA;KACF,CAAC;AAEF,IAAA,IAAI,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,QAAQ,EAAE;AACpD,QAAA,aAAa,CAAC,KAAM,CAAC,SAAS,GAAG,CAAG,EAAA,aAAa,CAAC,QAAQ,CAAI,CAAA,EAAA,aAAa,CAAC,QAAQ,EAAE,CAAC;AACxF,KAAA;SAAM,IAAI,aAAa,CAAC,QAAQ,EAAE;QACjC,aAAa,CAAC,KAAM,CAAC,SAAS,GAAG,GAAG,aAAa,CAAC,QAAQ,CAAA,CAAE,CAAC;AAC9D,KAAA;AAED,IAAA,MAAM,cAAc,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC;IAC9C,MAAM,YAAY,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AAEpD,IAAA,MAAM,UAAU,GAAG;AACjB,QAAA,OAAO,EAAE,cAAc;QACvB,KAAK,EAAE,YAAY,CAAC,cAAc,EAAE,YAAY,EAAE,aAAa,CAAC;KACjE,CAAC;AAEF,IAAA,OAAO,UAAU,CAAC;AACpB,CAAC;AAEK,SAAU,UAAU,CAAC,GAAW,EAAA;AACpC,IAAA,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC;AAC1D,IAAA,OAAO,SAAS,CAAC,WAAW,EAAE,KAAK,OAAO,CAAC;AAC7C,CAAC;SAEe,YAAY,CAC1B,cAAuB,EACvB,YAAqB,EACrB,aAA2C,EAAA;IAE3C,IAAI,cAAc,IAAI,YAAY,EAAE;AAClC,QAAA,OAAOG,iBAAM,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;AAC7C,KAAA;AAAM,SAAA,IAAI,cAAc,IAAI,CAAC,YAAY,EAAE;AAC1C,QAAA,OAAOA,iBAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,KAAA;AAAM,SAAA,IAAI,CAAC,cAAc,IAAI,YAAY,EAAE;AAC1C,QAAA,OAAOA,iBAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,KAAA;AAAM,SAAA;AACL,QAAA,OAAOA,iBAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;AAC3C,KAAA;AACH,CAAC;AAED,SAAS,WAAW,CAAC,IAAY,EAAA;;;AAG/B,IAAA,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC;AACpC;;ACzEA;AAsBA,MAAM,cAAc,GAAG,UAAU,CAAC;AAElC,MAAM,yBAAyB,GAAG;IAChC,wBAAwB;IACxB,+BAA+B;IAC/B,gBAAgB;IAChB,6BAA6B;IAC7B,iBAAiB;IACjB,mBAAmB;IACnB,OAAO;IACP,0BAA0B;IAC1B,aAAa;IAEb,kCAAkC;IAClC,8BAA8B;IAC9B,8BAA8B;IAC9B,6BAA6B;IAC7B,+BAA+B;IAC/B,wBAAwB;IACxB,gCAAgC;IAChC,+BAA+B;IAC/B,QAAQ;IAER,QAAQ;IACR,iBAAiB;IACjB,eAAe;IACf,YAAY;IACZ,gBAAgB;IAChB,cAAc;IACd,MAAM;IACN,MAAM;IACN,SAAS;IACT,UAAU;IACV,mBAAmB;IACnB,eAAe;IACf,qBAAqB;IACrB,eAAe;IACf,QAAQ;IACR,YAAY;IACZ,aAAa;IACb,QAAQ;IACR,mBAAmB;IACnB,YAAY;IACZ,kBAAkB;CACnB,CAAC;AAEF,MAAM,6BAA6B,GAAa,CAAC,aAAa,CAAC,CAAC;MAEnD,SAAS,CAAA;IAIpB,WAAY,CAAA,EAAE,kBAAkB,GAAG,EAAE,EAAE,sBAAsB,GAAG,EAAE,EAAA,GAAuB,EAAE,EAAA;AACzF,QAAA,kBAAkB,GAAG,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC;AACpD,cAAE,yBAAyB,CAAC,MAAM,CAAC,kBAAkB,CAAC;cACpD,yBAAyB,CAAC;AAE9B,QAAA,sBAAsB,GAAG,KAAK,CAAC,OAAO,CAAC,sBAAsB,CAAC;AAC5D,cAAE,6BAA6B,CAAC,MAAM,CAAC,sBAAsB,CAAC;cAC5D,6BAA6B,CAAC;QAElC,IAAI,CAAC,kBAAkB,GAAG,IAAI,GAAG,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAClF,IAAI,CAAC,sBAAsB,GAAG,IAAI,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;KAC3F;AAEM,IAAA,QAAQ,CAAC,GAAY,EAAA;AAC1B,QAAA,MAAM,IAAI,GAAG,IAAI,GAAG,EAAW,CAAC;QAChC,OAAO,IAAI,CAAC,SAAS,CACnB,GAAG,EACH,CAAC,GAAW,EAAE,KAAc,KAAI;;YAE9B,IAAI,KAAK,YAAY,KAAK,EAAE;AAC1B,gBAAA,OAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACK,KAAK,CAAA,EAAA,EACR,IAAI,EAAE,KAAK,CAAC,IAAI,EAChB,OAAO,EAAE,KAAK,CAAC,OAAO,EACtB,CAAA,CAAA;AACH,aAAA;YAED,IAAI,GAAG,KAAK,aAAa,EAAE;AACzB,gBAAA,OAAO,IAAI,CAAC,eAAe,CAAC,KAAsB,CAAC,CAAC;AACrD,aAAA;iBAAM,IAAI,GAAG,KAAK,KAAK,EAAE;AACxB,gBAAA,OAAO,IAAI,CAAC,WAAW,CAAC,KAAe,CAAC,CAAC;AAC1C,aAAA;iBAAM,IAAI,GAAG,KAAK,OAAO,EAAE;AAC1B,gBAAA,OAAO,IAAI,CAAC,aAAa,CAAC,KAAsB,CAAC,CAAC;AACnD,aAAA;iBAAM,IAAI,GAAG,KAAK,MAAM,EAAE;;AAEzB,gBAAA,OAAO,SAAS,CAAC;AAClB,aAAA;iBAAM,IAAI,GAAG,KAAK,UAAU,EAAE;;AAE7B,gBAAA,OAAO,SAAS,CAAC;AAClB,aAAA;iBAAM,IAAI,GAAG,KAAK,eAAe,EAAE;;;AAGlC,gBAAA,OAAO,SAAS,CAAC;AAClB,aAAA;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AAClD,gBAAA,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AACnB,oBAAA,OAAO,YAAY,CAAC;AACrB,iBAAA;AACD,gBAAA,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACjB,aAAA;AAED,YAAA,OAAO,KAAK,CAAC;SACd,EACD,CAAC,CACF,CAAC;KACH;AAEO,IAAA,eAAe,CAAC,KAAoB,EAAA;QAC1C,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;KAClF;AAEO,IAAA,aAAa,CAAC,KAAoB,EAAA;QACxC,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAChF;AAEO,IAAA,cAAc,CACpB,KAAoB,EACpB,WAAwB,EACxB,QAA0C,EAAA;QAE1C,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE;AAC/C,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;QAED,MAAM,SAAS,GAAkB,EAAE,CAAC;QAEpC,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAClC,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,EAAE;gBACpC,SAAS,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACnC,aAAA;AAAM,iBAAA;AACL,gBAAA,SAAS,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC;AAC/B,aAAA;AACF,SAAA;AAED,QAAA,OAAO,SAAS,CAAC;KAClB;AAEO,IAAA,WAAW,CAAC,KAAa,EAAA;QAC/B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE;AAC/C,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;QAED,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC3C,QAAA,MAAM,WAAW,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;QAE1C,IAAI,CAAC,WAAW,EAAE;AAChB,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;QAED,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;AAC1C,QAAA,KAAK,MAAM,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE;AAC5B,YAAA,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,EAAE;AACrD,gBAAA,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;AAC9B,aAAA;AACF,SAAA;QAED,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;AACtC,QAAA,OAAO,UAAU,CAAC,QAAQ,EAAE,CAAC;KAC9B;AACF;;ACtLD;AAKO,MAAM,MAAM,GAAGC,YAAO,CAAC,MAAM;;ACLpC;AAQA,MAAM,cAAc,GAAG,IAAI,SAAS,EAAE,CAAC;AAEvC;;AAEG;AACG,MAAO,SAAU,SAAQ,KAAK,CAAA;IA8BlC,WACE,CAAA,OAAe,EACf,IAAa,EACb,UAAmB,EACnB,OAAyB,EACzB,QAAgC,EAAA;QAEhC,KAAK,CAAC,OAAO,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;AACxB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AAC7B,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACvB,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;KAClD;AAED;;AAEG;AACH,IAAA,CAAC,MAAM,CAAC,GAAA;AACN,QAAA,OAAO,CAAc,WAAA,EAAA,IAAI,CAAC,OAAO,CAAO,IAAA,EAAA,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,CAAE,CAAC;KACzE;;AAnDD;;AAEG;AACa,SAAkB,CAAA,kBAAA,GAAW,oBAAoB,CAAC;AAClE;;AAEG;AACa,SAAW,CAAA,WAAA,GAAW,aAAa;;ACrBrD;AAGO,MAAM,MAAM,GAAGC,2BAAkB,CAAC,WAAW,CAAC;;ACHrD;AAuBA,SAAS,cAAc,CACrB,OAAgB,EAChB,UAAsB,EAAA;AAEtB,IAAA,OAAO,OAAO,GAAG,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC;AAChE,CAAC;AA+BK,MAAO,eAAgB,SAAQC,gBAAS,CAAA;AAS5C,IAAA,WAAA,CAAoB,gBAA2D,EAAA;AAC7E,QAAA,KAAK,EAAE,CAAC;QADU,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAA2C;QARvE,IAAW,CAAA,WAAA,GAAW,CAAC,CAAC;KAU/B;AATD,IAAA,UAAU,CAAC,KAAsB,EAAE,SAAiB,EAAE,QAA4B,EAAA;AAChF,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACjB,QAAA,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC,gBAAiB,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QAC1D,QAAQ,CAAC,SAAS,CAAC,CAAC;KACrB;AAKF,CAAA;AAED,SAAS,gBAAgB,CAAC,IAAS,EAAA;IACjC,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC;AACjD,CAAC;AAED,SAAS,gBAAgB,CAAC,MAAgB,EAAE,OAAyB,EAAA;AACnE,IAAA,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,KAAI;AAC7B,QAAA,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAK;AACxB,YAAA,OAAO,aAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,KAAK,EAAE,CAAC;AACjB,YAAA,OAAO,EAAE,CAAC;AACZ,SAAC,CAAC,CAAC;AACH,QAAA,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAC5B,QAAA,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAChC,KAAC,CAAC,CAAC;AACL,CAAC;AAED;;AAEG;AACG,SAAU,YAAY,CAAC,OAAgB,EAAA;AAC3C,IAAA,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;IAEtC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,KAAI;AAC7B,QAAA,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAC9B,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;AAEG;MACU,mBAAmB,CAAA;AAAhC,IAAA,WAAA,GAAA;;AAkMU,QAAA,IAAA,CAAA,aAAa,GAA4B,IAAI,GAAG,EAAE,CAAC;QACnD,IAAe,CAAA,eAAA,GAAe,EAAE,CAAC;AAExB,QAAA,IAAA,CAAA,SAAS,GAAG,IAAIC,gBAAK,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;KAmHlF;AAvTC;;;;AAIG;IACH,MAAM,WAAW,CAAC,WAA4B,EAAA;;AAC5C,QAAA,IAAI,CAAC,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;AACnD,YAAA,MAAM,IAAI,KAAK,CACb,yFAAyF,CAC1F,CAAC;AACH,SAAA;AAED,QAAA,MAAMC,iBAAe,GAAG,IAAIC,+BAAe,EAAE,CAAC;AAC9C,QAAA,IAAI,aAAiD,CAAC;QACtD,IAAI,WAAW,CAAC,WAAW,EAAE;AAC3B,YAAA,IAAI,WAAW,CAAC,WAAW,CAAC,OAAO,EAAE;AACnC,gBAAA,MAAM,IAAIC,0BAAU,CAAC,4BAA4B,CAAC,CAAC;AACpD,aAAA;AAED,YAAA,aAAa,GAAG,CAAC,KAAY,KAAI;AAC/B,gBAAA,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;oBAC1BF,iBAAe,CAAC,KAAK,EAAE,CAAC;AACzB,iBAAA;AACH,aAAC,CAAC;YACF,WAAW,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;AAClE,SAAA;QAED,IAAI,WAAW,CAAC,OAAO,EAAE;YACvB,UAAU,CAAC,MAAK;gBACdA,iBAAe,CAAC,KAAK,EAAE,CAAC;AAC1B,aAAC,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;AACzB,SAAA;QAED,IAAI,WAAW,CAAC,QAAQ,EAAE;AACxB,YAAA,MAAM,QAAQ,GAAQ,WAAW,CAAC,QAAQ,CAAC;AAC3C,YAAA,MAAM,WAAW,GAAG,IAAIG,4BAAQ,EAAE,CAAC;AACnC,YAAA,MAAM,eAAe,GAAG,CAAC,GAAW,EAAE,KAAU,KAAU;;AAExD,gBAAA,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;oBAC/B,KAAK,GAAG,KAAK,EAAE,CAAC;AACjB,iBAAA;AACD,gBAAA,IACE,KAAK;oBACL,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC;oBACpD,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,EACtD;AACA,oBAAA,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;AACrD,iBAAA;AAAM,qBAAA;AACL,oBAAA,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAChC,iBAAA;AACH,aAAC,CAAC;YACF,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AAC3C,gBAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;AACpC,gBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;AAC5B,oBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBACzC,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC,qBAAA;AACF,iBAAA;AAAM,qBAAA;AACL,oBAAA,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AACrC,iBAAA;AACF,aAAA;AAED,YAAA,WAAW,CAAC,IAAI,GAAG,WAAW,CAAC;AAC/B,YAAA,WAAW,CAAC,QAAQ,GAAG,SAAS,CAAC;YACjC,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAC5D,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,EAAE;AACpE,gBAAA,IAAI,OAAO,WAAW,CAAC,WAAW,KAAK,UAAU,EAAE;AACjD,oBAAA,WAAW,CAAC,OAAO,CAAC,GAAG,CACrB,cAAc,EACd,CAAiC,8BAAA,EAAA,WAAW,CAAC,WAAW,EAAE,CAAA,CAAE,CAC7D,CAAC;AACH,iBAAA;AAAM,qBAAA;;AAEL,oBAAA,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;AAC5C,iBAAA;AACF,aAAA;AACF,SAAA;AAED,QAAA,IAAI,IAAI,GAAG,WAAW,CAAC,IAAI;AACzB,cAAE,OAAO,WAAW,CAAC,IAAI,KAAK,UAAU;AACtC,kBAAE,WAAW,CAAC,IAAI,EAAE;kBAClB,WAAW,CAAC,IAAI;cAClB,SAAS,CAAC;AACd,QAAA,IAAI,WAAW,CAAC,gBAAgB,IAAI,WAAW,CAAC,IAAI,EAAE;AACpD,YAAA,MAAM,gBAAgB,GAAG,WAAW,CAAC,gBAAgB,CAAC;AACtD,YAAA,MAAM,kBAAkB,GAAG,IAAI,eAAe,CAAC,gBAAgB,CAAC,CAAC;AACjE,YAAA,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAC1B,gBAAA,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AAC/B,aAAA;AAAM,iBAAA;AACL,gBAAA,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC9B,aAAA;YAED,IAAI,GAAG,kBAAkB,CAAC;AAC3B,SAAA;QAED,MAAM,2BAA2B,GAAyB,MAAM,IAAI,CAAC,cAAc,CACjF,WAAW,CACZ,CAAC;AAEF,QAAA,MAAM,WAAW,GACf,MAAA,CAAA,MAAA,CAAA,EAAA,IAAI,EAAE,IAAI,EACV,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC,UAAU,EAAE,EACzC,MAAM,EAAE,WAAW,CAAC,MAAM;;;;YAI1B,MAAM,EAAEH,iBAAe,CAAC,MAAa,EACrC,QAAQ,EAAE,QAAQ,EAAA,EACf,2BAA2B,CAC/B,CAAC;AAEF,QAAA,IAAI,iBAAoD,CAAC;QACzD,IAAI;AACF,YAAA,MAAM,QAAQ,GAAmB,MAAM,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;YAEhF,MAAM,OAAO,GAAG,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAE/C,YAAA,MAAM,SAAS,GACb,CAAA,CAAA,EAAA,GAAA,WAAW,CAAC,yBAAyB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC3D,WAAW,CAAC,kBAAkB,CAAC;AAEjC,YAAA,iBAAiB,GAAG;AAClB,gBAAA,OAAO,EAAE,OAAO;AAChB,gBAAA,OAAO,EAAE,WAAW;gBACpB,MAAM,EAAE,QAAQ,CAAC,MAAM;AACvB,gBAAA,kBAAkB,EAAE,SAAS;sBACxB,QAAQ,CAAC,IAAyC;AACrD,sBAAE,SAAS;AACb,gBAAA,UAAU,EAAE,CAAC,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,GAAG,SAAS;aAC3D,CAAC;AAEF,YAAA,MAAM,kBAAkB,GAAG,WAAW,CAAC,kBAAkB,CAAC;AAC1D,YAAA,IAAI,kBAAkB,EAAE;AACtB,gBAAA,MAAM,YAAY,GAA2C,QAAQ,CAAC,IAAI,IAAI,SAAS,CAAC;AAExF,gBAAA,IAAI,gBAAgB,CAAC,YAAY,CAAC,EAAE;AAClC,oBAAA,MAAM,oBAAoB,GAAG,IAAI,eAAe,CAAC,kBAAkB,CAAC,CAAC;AACrE,oBAAA,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;AACxC,oBAAA,iBAAiB,CAAC,kBAAkB,GAAG,oBAAoB,CAAC;AAC7D,iBAAA;AAAM,qBAAA;AACL,oBAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAE,CAAC,IAAI,SAAS,CAAC;AACrE,oBAAA,IAAI,MAAM,EAAE;;AAEV,wBAAA,kBAAkB,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC,CAAC;AAC7C,qBAAA;AACF,iBAAA;AACF,aAAA;AAED,YAAA,MAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;AAE7C,YAAA,OAAO,iBAAiB,CAAC;AAC1B,SAAA;AAAC,QAAA,OAAO,KAAU,EAAE;YACnB,MAAM,UAAU,GAAe,KAAK,CAAC;AACrC,YAAA,IAAI,UAAU,CAAC,IAAI,KAAK,WAAW,EAAE;AACnC,gBAAA,MAAM,IAAI,SAAS,CACjB,UAAU,CAAC,OAAO,EAClB,SAAS,CAAC,kBAAkB,EAC5B,SAAS,EACT,WAAW,CACZ,CAAC;AACH,aAAA;AAAM,iBAAA,IAAI,UAAU,CAAC,IAAI,KAAK,SAAS,EAAE;AACxC,gBAAA,MAAM,IAAIE,0BAAU,CAAC,4BAA4B,CAAC,CAAC;AACpD,aAAA;AAED,YAAA,MAAM,UAAU,CAAC;AAClB,SAAA;AAAS,gBAAA;;AAER,YAAA,IAAI,WAAW,CAAC,WAAW,IAAI,aAAa,EAAE;AAC5C,gBAAA,IAAI,gBAAgB,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;AACzC,gBAAA,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAC1B,oBAAA,gBAAgB,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAC3C,iBAAA;AACD,gBAAA,IAAI,kBAAkB,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;gBAC3C,IAAI,gBAAgB,CAAC,iBAAiB,KAAjB,IAAA,IAAA,iBAAiB,uBAAjB,iBAAiB,CAAE,kBAAkB,CAAC,EAAE;oBAC3D,kBAAkB,GAAG,gBAAgB,CACnC,iBAAkB,CAAC,kBAAkB,EACrCF,iBAAe,CAChB,CAAC;AACH,iBAAA;gBAED,OAAO,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC;qBAChD,IAAI,CAAC,MAAK;;oBACT,CAAA,EAAA,GAAA,WAAW,CAAC,WAAW,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,mBAAmB,CAAC,OAAO,EAAE,aAAc,CAAC,CAAC;oBACtE,OAAO;AACT,iBAAC,CAAC;AACD,qBAAA,KAAK,CAAC,CAAC,CAAC,KAAI;AACX,oBAAA,MAAM,CAAC,OAAO,CAAC,qDAAqD,EAAE,CAAC,CAAC,CAAC;AAC3E,iBAAC,CAAC,CAAC;AACN,aAAA;AACF,SAAA;KACF;AAQO,IAAA,gBAAgB,CAAC,WAA4B,EAAA;;QACnD,MAAM,OAAO,GAAG,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;;;;QAK5C,IAAI,WAAW,CAAC,aAAa,EAAE;AAC7B,YAAA,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,WAAW,CAAC,aAAa,CAAC;YACrE,MAAM,GAAG,GAAG,CAAA,EAAG,IAAI,CAAA,CAAA,EAAI,IAAI,CAAA,CAAA,EAAI,QAAQ,CAAA,CAAA,EAAI,QAAQ,CAAA,CAAE,CAAC;AACtD,YAAA,MAAM,WAAW,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,EAAE,CAAC;YAEtD,IAAI,KAAK,GAAG,cAAc,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;AACjD,YAAA,IAAI,KAAK,EAAE;AACT,gBAAA,OAAO,KAAK,CAAC;AACd,aAAA;AAED,YAAA,MAAM,MAAM,GAAe,gBAAgB,CACzC,WAAW,CAAC,GAAG,EACf,WAAW,CAAC,aAAa,EACzB,WAAW,CAAC,OAAO,CACpB,CAAC;AAEF,YAAA,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;YACrB,IAAI,MAAM,CAAC,OAAO,EAAE;AAClB,gBAAA,WAAW,CAAC,UAAU,GAAG,MAAM,CAAC,KAAoB,CAAC;AACtD,aAAA;AAAM,iBAAA;AACL,gBAAA,WAAW,CAAC,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC;AACtC,aAAA;YACD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;AAEzC,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;aAAM,IAAI,WAAW,CAAC,SAAS,EAAE;YAChC,IAAI,KAAK,GAAG,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;AAC1D,YAAA,IAAI,KAAK,EAAE;AACT,gBAAA,OAAO,KAAK,CAAC;AACd,aAAA;AAED,YAAA,MAAM,YAAY,GAA2C;gBAC3D,SAAS,EAAE,WAAW,CAAC,SAAS;aACjC,CAAC;AAEF,YAAA,IAAI,OAAO,EAAE;AACX,gBAAA,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,IAAII,gBAAK,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AACzE,aAAA;AAAM,iBAAA;AACL,gBAAA,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,IAAIC,eAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AACvE,aAAA;AAED,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AAAM,aAAA;AACL,YAAA,OAAO,OAAO,GAAGD,gBAAK,CAAC,WAAW,GAAGC,eAAI,CAAC,WAAW,CAAC;AACvD,SAAA;KACF;AAED;;AAEG;;AAEH,IAAA,MAAM,KAAK,CAAC,KAAwB,EAAE,IAAwB,EAAA;AAC5D,QAAA,OAAOC,8BAAU,CAAC,KAAK,EAAE,IAAI,CAAuC,CAAC;KACtE;AAED;;AAEG;IACH,MAAM,cAAc,CAAC,WAA4B,EAAA;QAC/C,MAAM,WAAW,GAA+D,EAAE,CAAC;AAEnF,QAAA,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YACxD,MAAM,YAAY,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,KAAI;AACjE,gBAAA,IAAI,CAAC,SAAU,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,MAAM,KAAI;AAC/D,oBAAA,IAAI,GAAG,EAAE;wBACP,MAAM,CAAC,GAAG,CAAC,CAAC;AACb,qBAAA;AAAM,yBAAA;wBACL,OAAO,CAAC,MAAM,CAAC,CAAC;AACjB,qBAAA;AACH,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC,CAAC;YAEH,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;AACjD,SAAA;;QAGD,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;AAEvD,QAAA,WAAW,CAAC,QAAQ,GAAG,WAAW,CAAC,kBAAkB,CAAC;AAEtD,QAAA,OAAO,WAAW,CAAC;KACpB;AAED;;AAEG;IACH,MAAM,cAAc,CAAC,iBAAwC,EAAA;QAC3D,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,eAAe,GAAG,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YACpE,IAAI,eAAe,KAAK,SAAS,EAAE;gBACjC,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,KAAI;oBAC1C,IAAI,CAAC,SAAU,CAAC,SAAS,CACvB,eAAe,EACf,iBAAiB,CAAC,OAAO,CAAC,GAAG,EAC7B,EAAE,WAAW,EAAE,IAAI,EAAE,EACrB,CAAC,GAAG,KAAI;AACN,wBAAA,IAAI,GAAG,EAAE;4BACP,MAAM,CAAC,GAAG,CAAC,CAAC;AACb,yBAAA;AAAM,6BAAA;AACL,4BAAA,OAAO,EAAE,CAAC;AACX,yBAAA;AACH,qBAAC,CACF,CAAC;AACJ,iBAAC,CAAC,CAAC;AACJ,aAAA;AACF,SAAA;KACF;AACF;;AChaD;AACA;AAEA;;AAEG;AACSC,sCAoBX;AApBD,CAAA,UAAY,oBAAoB,EAAA;AAC9B;;AAEG;AACH,IAAA,oBAAA,CAAA,oBAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG,CAAA;AAEH;;AAEG;AACH,IAAA,oBAAA,CAAA,oBAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AAEL;;AAEG;AACH,IAAA,oBAAA,CAAA,oBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AAEP;;AAEG;AACH,IAAA,oBAAA,CAAA,oBAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACN,CAAC,EApBWA,4BAAoB,KAApBA,4BAAoB,GAoB/B,EAAA,CAAA,CAAA;;AC1BD;AAwDA;;;;AAIG;AACG,SAAU,oCAAoC,CAClD,IAAO,EAAA;AAEP,IAAA,MAAM,EAAE,cAAc,EAAE,cAAc,EAA2B,GAAA,IAAI,EAA1B,iBAAiB,GAAKC,YAAA,CAAA,IAAI,EAA/D,CAAA,gBAAA,EAAA,gBAAA,CAAwD,CAAO,CAAC;IAEtE,IAAI,MAAM,GAAuB,iBAAiB,CAAC;AAEnD,IAAA,IAAI,cAAc,EAAE;AAClB,QAAA,MAAM,GAAQ,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,MAAM,CAAK,EAAA,cAAc,CAAE,CAAC;AAC3C,KAAA;AAED,IAAA,IAAI,cAAc,EAAE;AAClB,QAAA,MAAM,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAC;;QAEtD,MAAM,CAAC,WAAW,GAAI,cAAsB,KAAA,IAAA,IAAtB,cAAc,KAAd,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,cAAc,CAAU,WAAW,CAAC;AAC3D,KAAA;AAED,IAAA,OAAO,MAAM,CAAC;AAChB;;AC/EA;AA0BA;;AAEG;MACmB,iBAAiB,CAAA;AACrC;;AAEG;AACH,IAAA,WAAA;AACE;;AAEG;IACM,WAA0B;AACnC;;AAEG;IACM,QAAkC,EAAA;QAJlC,IAAW,CAAA,WAAA,GAAX,WAAW,CAAe;QAI1B,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAA0B;KACzC;AAQJ;;;;AAIG;AACI,IAAA,SAAS,CAAC,QAA8B,EAAA;QAC7C,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;KAC1C;AAED;;;;;AAKG;IACI,GAAG,CAAC,QAA8B,EAAE,OAAe,EAAA;QACxD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;KACtC;AACF,CAAA;AAsBD;;AAEG;MACU,oBAAoB,CAAA;AAC/B,IAAA,WAAA,CAAoB,OAA4B,EAAA;QAA5B,IAAO,CAAA,OAAA,GAAP,OAAO,CAAqB;KAAI;AAEpD;;;;AAIG;AACI,IAAA,SAAS,CAAC,QAA8B,EAAA;AAC7C,QAAA,QACE,CAAC,CAAC,IAAI,CAAC,OAAO;YACd,QAAQ,KAAKD,4BAAoB,CAAC,GAAG;AACrC,YAAA,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EACxC;KACH;AAED;;;;;AAKG;IACI,GAAG,CAAC,QAA8B,EAAE,OAAe,EAAA;QACxD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;YAC5C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACrC,SAAA;KACF;AACF;;ACxHD;AAMA;AACA;AACA;AACA;AACA,MAAM,sBAAsB,GAAqB;AAC/C,IAAA,eAAe,EAAE,KAAK;AACtB,IAAA,IAAI,EAAE,KAAK;AACX,IAAA,SAAS,EAAE,KAAK;AAChB,IAAA,aAAa,EAAE,KAAK;AACpB,IAAA,OAAO,EAAE,WAAW;AACpB,IAAA,aAAa,EAAE,IAAI;AACnB,IAAA,WAAW,EAAE,KAAK;AAClB,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,YAAY,EAAE,IAAI;AAClB,IAAA,SAAS,EAAE,SAAS;AACpB,IAAA,KAAK,EAAE,KAAK;AACZ,IAAA,gBAAgB,EAAE,KAAK;AACvB,IAAA,qBAAqB,EAAE,KAAK;AAC5B,IAAA,QAAQ,EAAE,IAAI;AACd,IAAA,eAAe,EAAE,KAAK;AACtB,IAAA,iBAAiB,EAAE,KAAK;AACxB,IAAA,KAAK,EAAE,KAAK;AACZ,IAAA,MAAM,EAAE,IAAI;AACZ,IAAA,kBAAkB,EAAE,SAAS;AAC7B,IAAA,mBAAmB,EAAE,SAAS;AAC9B,IAAA,iBAAiB,EAAE,SAAS;AAC5B,IAAA,eAAe,EAAE,SAAS;AAC1B,IAAA,QAAQ,EAAE,MAAM;AAChB,IAAA,MAAM,EAAE;AACN,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,QAAQ,EAAE,OAAO;AACjB,QAAA,UAAU,EAAE,IAAI;AACjB,KAAA;AACD,IAAA,OAAO,EAAE,SAAS;AAClB,IAAA,UAAU,EAAE;AACV,QAAA,MAAM,EAAE,IAAI;AACZ,QAAA,MAAM,EAAE,IAAI;AACZ,QAAA,OAAO,EAAE,IAAI;AACd,KAAA;AACD,IAAA,QAAQ,EAAE,KAAK;AACf,IAAA,SAAS,EAAE,KAAK;AAChB,IAAA,QAAQ,EAAE,EAAE;AACZ,IAAA,KAAK,EAAE,KAAK;CACb,CAAC;AAEF;AACA,MAAM,oBAAoB,GAAQ,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,sBAAsB,CAAC,CAAC;AAC5E,oBAAoB,CAAC,aAAa,GAAG,KAAK,CAAC;AAE3C;AACA,MAAM,qBAAqB,GAAQ,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,sBAAsB,CAAC,CAAC;AAC7E,qBAAqB,CAAC,aAAa,GAAG,KAAK,CAAC;AAC5C,qBAAqB,CAAC,UAAU,GAAG;AACjC,IAAA,MAAM,EAAE,KAAK;CACd,CAAC;AAEF;;;;AAIG;SACa,YAAY,CAAC,GAAY,EAAE,OAA0B,EAAE,EAAA;;AACrE,IAAA,qBAAqB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;IAC/C,qBAAqB,CAAC,OAAO,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,WAAW,CAAC;IAC/D,MAAM,OAAO,GAAG,IAAIE,iBAAM,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAC1D,IAAA,OAAO,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AAClC,CAAC;AAED;;;;AAIG;SACa,QAAQ,CAAC,GAAW,EAAE,OAA0B,EAAE,EAAA;;IAChE,oBAAoB,CAAC,YAAY,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC;IACvD,oBAAoB,CAAC,OAAO,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,WAAW,CAAC;IAC9D,MAAM,SAAS,GAAG,IAAIA,iBAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;IAC1D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;QACrC,IAAI,CAAC,GAAG,EAAE;AACR,YAAA,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC;AACxC,SAAA;AAAM,aAAA;YACL,SAAS,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAQ,EAAE,GAAQ,KAAI;AAChD,gBAAA,IAAI,GAAG,EAAE;oBACP,MAAM,CAAC,GAAG,CAAC,CAAC;AACb,iBAAA;AAAM,qBAAA;oBACL,OAAO,CAAC,GAAG,CAAC,CAAC;AACd,iBAAA;AACH,aAAC,CAAC,CAAC;AACJ,SAAA;AACH,KAAC,CAAC,CAAC;AACL;;AChGA;AA+CA;;;AAGG;AACa,SAAA,qBAAqB,CACnC,2BAAyD,EACzD,cAAkC,EAAA;IAElC,OAAO;AACL,QAAA,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,KAAI;YACnE,OAAO,IAAI,qBAAqB,CAC9B,UAAU,EACV,OAAO,EACP,2BAA2B,EAC3B,cAAc,CACf,CAAC;SACH;KACF,CAAC;AACJ,CAAC;AAEM,MAAM,uBAAuB,GAAG,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;AAClE,MAAM,sBAAsB,GAAG,CAAC,iBAAiB,EAAE,sBAAsB,CAAC,CAAC;AAE3E,MAAM,6BAA6B,GAA2B;AACnE,IAAA,oBAAoB,EAAE;AACpB,QAAA,IAAI,EAAE,uBAAuB;AAC7B,QAAA,GAAG,EAAE,sBAAsB;AAC5B,KAAA;CACF,CAAC;AAEF;;;AAGG;AACG,MAAO,qBAAsB,SAAQ,iBAAiB,CAAA;AAK1D,IAAA,WAAA,CACE,UAAyB,EACzB,oBAA0C,EAC1C,2BAAyD,EACzD,iBAAoC,EAAE,EAAA;;AAEtC,QAAA,KAAK,CAAC,UAAU,EAAE,oBAAoB,CAAC,CAAC;AAExC,QAAA,IAAI,CAAC,gBAAgB;YACnB,CAAC,2BAA2B,IAAI,2BAA2B,CAAC,IAAI,KAAK,uBAAuB,CAAC;AAC/F,QAAA,IAAI,CAAC,eAAe;YAClB,CAAC,2BAA2B,IAAI,2BAA2B,CAAC,GAAG,KAAK,sBAAsB,CAAC;QAC7F,IAAI,CAAC,UAAU,GAAG,CAAA,EAAA,GAAA,cAAc,CAAC,UAAU,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,WAAW,CAAC;KAC5D;IAEM,MAAM,WAAW,CAAC,OAAwB,EAAA;QAC/C,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,QAA+B,KAChF,uBAAuB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,EAAE,QAAQ,EAAE;YAC7E,UAAU,EAAE,IAAI,CAAC,UAAU;AAC5B,SAAA,CAAC,CACH,CAAC;KACH;AACF,CAAA;AAED,SAAS,oBAAoB,CAC3B,cAAqC,EAAA;AAErC,IAAA,IAAI,MAAqC,CAAC;AAC1C,IAAA,MAAM,OAAO,GAAoB,cAAc,CAAC,OAAO,CAAC;AACxD,IAAA,MAAM,aAAa,GAA8B,OAAO,CAAC,aAAa,CAAC;AACvE,IAAA,IAAI,aAAa,EAAE;AACjB,QAAA,MAAM,uBAAuB,GAKa,OAAO,CAAC,uBAAuB,CAAC;QAC1E,IAAI,CAAC,uBAAuB,EAAE;YAC5B,MAAM,GAAG,aAAa,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AACzD,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,GAAG,uBAAuB,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;AACjE,SAAA;AACF,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,yBAAyB,CAAC,cAAqC,EAAA;AACtE,IAAA,MAAM,iBAAiB,GACrB,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC;AAC3C,IAAA,IAAI,MAAe,CAAC;IACpB,IAAI,iBAAiB,KAAK,SAAS,EAAE;QACnC,MAAM,GAAG,IAAI,CAAC;AACf,KAAA;AAAM,SAAA,IAAI,OAAO,iBAAiB,KAAK,SAAS,EAAE;QACjD,MAAM,GAAG,iBAAiB,CAAC;AAC5B,KAAA;AAAM,SAAA;AACL,QAAA,MAAM,GAAG,iBAAiB,CAAC,cAAc,CAAC,CAAC;AAC5C,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;;AAOG;AACG,SAAU,uBAAuB,CACrC,gBAA0B,EAC1B,eAAyB,EACzB,QAA+B,EAC/B,OAAA,GAA6B,EAAE,EAAA;;AAE/B,IAAA,MAAM,cAAc,GAAgC;AAClD,QAAA,QAAQ,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,QAAQ,mCAAI,EAAE;AAChC,QAAA,WAAW,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,WAAW,mCAAI,KAAK;AACzC,QAAA,UAAU,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,UAAU,mCAAI,WAAW;KAC9C,CAAC;AACF,IAAA,OAAO,KAAK,CAAC,gBAAgB,EAAE,eAAe,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC,IAAI,CAC5E,CAAC,cAAc,KAAI;AACjB,QAAA,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,EAAE;AAC9C,YAAA,OAAO,cAAc,CAAC;AACvB,SAAA;AAED,QAAA,MAAM,aAAa,GAAG,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC;AAC3D,QAAA,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE;AAC9C,YAAA,OAAO,cAAc,CAAC;AACvB,SAAA;AAED,QAAA,MAAM,YAAY,GAAG,oBAAoB,CAAC,cAAc,CAAC,CAAC;AAE1D,QAAA,MAAM,EAAE,KAAK,EAAE,oBAAoB,EAAE,GAAG,mBAAmB,CACzD,cAAc,EACd,aAAa,EACb,YAAY,CACb,CAAC;AACF,QAAA,IAAI,KAAK,EAAE;AACT,YAAA,MAAM,KAAK,CAAC;AACb,SAAA;AAAM,aAAA,IAAI,oBAAoB,EAAE;AAC/B,YAAA,OAAO,cAAc,CAAC;AACvB,SAAA;;;AAID,QAAA,IAAI,YAAY,EAAE;YAChB,IAAI,YAAY,CAAC,UAAU,EAAE;AAC3B,gBAAA,IAAI,kBAAkB,GAAQ,cAAc,CAAC,UAAU,CAAC;AACxD,gBAAA,IAAI,aAAa,CAAC,KAAK,IAAI,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,QAAQ,EAAE;oBACpF,kBAAkB;wBAChB,OAAO,kBAAkB,KAAK,QAAQ;8BAClC,kBAAkB,CAAC,YAAY,CAAC,UAAU,CAAC,cAAe,CAAC;8BAC3D,EAAE,CAAC;AACV,iBAAA;gBACD,IAAI;AACF,oBAAA,cAAc,CAAC,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC,WAAW,CAC9D,YAAY,CAAC,UAAU,EACvB,kBAAkB,EAClB,yBAAyB,EACzB,OAAO,CACR,CAAC;AACH,iBAAA;AAAC,gBAAA,OAAO,UAAe,EAAE;oBACxB,MAAM,SAAS,GAAG,IAAI,SAAS,CAC7B,SAAS,UAAU,CAAA,8CAAA,EAAiD,cAAc,CAAC,UAAU,CAAA,CAAE,EAC/F,SAAS,EACT,cAAc,CAAC,MAAM,EACrB,cAAc,CAAC,OAAO,EACtB,cAAc,CACf,CAAC;AACF,oBAAA,MAAM,SAAS,CAAC;AACjB,iBAAA;AACF,aAAA;AAAM,iBAAA,IAAI,aAAa,CAAC,UAAU,KAAK,MAAM,EAAE;;AAE9C,gBAAA,cAAc,CAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG,CAAC;AAC7E,aAAA;YAED,IAAI,YAAY,CAAC,aAAa,EAAE;gBAC9B,cAAc,CAAC,aAAa,GAAG,aAAa,CAAC,UAAU,CAAC,WAAW,CACjE,YAAY,CAAC,aAAa,EAC1B,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,EAC/B,4BAA4B,EAC5B,OAAO,CACR,CAAC;AACH,aAAA;AACF,SAAA;AAED,QAAA,OAAO,cAAc,CAAC;AACxB,KAAC,CACF,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAAC,aAA4B,EAAA;IACxD,MAAM,mBAAmB,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;AACjE,IAAA,QACE,mBAAmB,CAAC,MAAM,KAAK,CAAC;AAChC,SAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,IAAI,mBAAmB,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,EAC1E;AACJ,CAAC;AAED,SAAS,mBAAmB,CAC1B,cAAqC,EACrC,aAA4B,EAC5B,YAA2C,EAAA;;AAE3C,IAAA,MAAM,iBAAiB,GAAG,GAAG,IAAI,cAAc,CAAC,MAAM,IAAI,cAAc,CAAC,MAAM,GAAG,GAAG,CAAC;AACtF,IAAA,MAAM,oBAAoB,GAAY,oBAAoB,CAAC,aAAa,CAAC;AACvE,UAAE,iBAAiB;AACnB,UAAE,CAAC,CAAC,YAAY,CAAC;AAEnB,IAAA,IAAI,oBAAoB,EAAE;AACxB,QAAA,IAAI,YAAY,EAAE;AAChB,YAAA,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;gBACzB,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,oBAAoB,EAAE,KAAK,EAAE,CAAC;AACrD,aAAA;AACF,SAAA;AAAM,aAAA;YACL,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,oBAAoB,EAAE,KAAK,EAAE,CAAC;AACrD,SAAA;AACF,KAAA;AAED,IAAA,MAAM,iBAAiB,GAAG,YAAY,KAAA,IAAA,IAAZ,YAAY,KAAA,KAAA,CAAA,GAAZ,YAAY,GAAI,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC;AAC1E,IAAA,MAAM,SAAS,GACb,CAAA,CAAA,EAAA,GAAA,cAAc,CAAC,OAAO,CAAC,yBAAyB,0CAAE,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC;AAC5E,QAAA,cAAc,CAAC,OAAO,CAAC,kBAAkB,CAAC;IAC5C,MAAM,mBAAmB,GAAG,SAAS;AACnC,UAAE,CAAA,wBAAA,EAA2B,cAAc,CAAC,MAAM,CAAE,CAAA;AACpD,UAAG,cAAc,CAAC,UAAqB,CAAC;AAE1C,IAAA,MAAM,KAAK,GAAG,IAAI,SAAS,CACzB,mBAAmB,EACnB,SAAS,EACT,cAAc,CAAC,MAAM,EACrB,cAAc,CAAC,OAAO,EACtB,cAAc,CACf,CAAC;;;IAIF,IAAI,CAAC,iBAAiB,EAAE;AACtB,QAAA,MAAM,KAAK,CAAC;AACb,KAAA;AAED,IAAA,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,UAAU,CAAC;AACvD,IAAA,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,aAAa,CAAC;IAE7D,IAAI;;;QAGF,IAAI,cAAc,CAAC,UAAU,EAAE;AAC7B,YAAA,MAAM,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;AAC7C,YAAA,IAAI,WAAW,CAAC;AAChB,YAAA,IAAI,iBAAiB,EAAE;gBACrB,IAAI,kBAAkB,GAAQ,UAAU,CAAC;AACzC,gBAAA,IAAI,aAAa,CAAC,KAAK,IAAI,iBAAiB,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,QAAQ,EAAE;oBAC9E,kBAAkB;AAChB,wBAAA,OAAO,UAAU,KAAK,QAAQ,GAAG,UAAU,CAAC,iBAAiB,CAAC,cAAe,CAAC,GAAG,EAAE,CAAC;AACvF,iBAAA;AACD,gBAAA,WAAW,GAAG,aAAa,CAAC,UAAU,CAAC,WAAW,CAChD,iBAAiB,EACjB,kBAAkB,EAClB,2BAA2B,CAC5B,CAAC;AACH,aAAA;YAED,MAAM,aAAa,GAAQ,UAAU,CAAC,KAAK,IAAI,WAAW,IAAI,UAAU,CAAC;AACzE,YAAA,KAAK,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC;YAChC,IAAI,aAAa,CAAC,OAAO,EAAE;AACzB,gBAAA,KAAK,CAAC,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC;AACvC,aAAA;AAED,YAAA,IAAI,iBAAiB,EAAE;AACrB,gBAAA,KAAK,CAAC,QAAS,CAAC,UAAU,GAAG,WAAW,CAAC;AAC1C,aAAA;AACF,SAAA;;AAGD,QAAA,IAAI,cAAc,CAAC,OAAO,IAAI,oBAAoB,EAAE;YAClD,KAAK,CAAC,QAAS,CAAC,aAAa,GAAG,aAAa,CAAC,UAAU,CAAC,WAAW,CAClE,oBAAoB,EACpB,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,EAC/B,4BAA4B,CAC7B,CAAC;AACH,SAAA;AACF,KAAA;AAAC,IAAA,OAAO,YAAiB,EAAE;AAC1B,QAAA,KAAK,CAAC,OAAO,GAAG,CAAA,OAAA,EAAU,YAAY,CAAC,OAAO,CAAA,gDAAA,EAAmD,cAAc,CAAC,UAAU,CAAA,2BAAA,CAA6B,CAAC;AACzJ,KAAA;AAED,IAAA,OAAO,EAAE,KAAK,EAAE,oBAAoB,EAAE,KAAK,EAAE,CAAC;AAChD,CAAC;AAED,SAAS,KAAK,CACZ,gBAA0B,EAC1B,eAAyB,EACzB,iBAAwC,EACxC,IAAiC,EAAA;;AAEjC,IAAA,MAAM,YAAY,GAAG,CAAC,GAA6B,KAAoB;QACrE,MAAM,GAAG,GAAG,CAAU,OAAA,EAAA,GAAG,gDAAgD,iBAAiB,CAAC,UAAU,CAAA,CAAA,CAAG,CAAC;QACzG,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,IAAI,SAAS,CAAC,WAAW,CAAC;AAClD,QAAA,MAAM,CAAC,GAAG,IAAI,SAAS,CACrB,GAAG,EACH,OAAO,EACP,iBAAiB,CAAC,MAAM,EACxB,iBAAiB,CAAC,OAAO,EACzB,iBAAiB,CAClB,CAAC;AACF,QAAA,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC3B,KAAC,CAAC;AAEF,IAAA,MAAM,SAAS,GACb,CAAA,CAAA,EAAA,GAAA,iBAAiB,CAAC,OAAO,CAAC,yBAAyB,0CAAE,GAAG,CAAC,iBAAiB,CAAC,MAAM,CAAC;AAClF,QAAA,iBAAiB,CAAC,OAAO,CAAC,kBAAkB,CAAC;AAC/C,IAAA,IAAI,CAAC,SAAS,IAAI,iBAAiB,CAAC,UAAU,EAAE;AAC9C,QAAA,MAAM,IAAI,GAAG,iBAAiB,CAAC,UAAU,CAAC;AAC1C,QAAA,MAAM,WAAW,GAAW,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;QAChF,MAAM,iBAAiB,GAAa,CAAC,WAAW;AAC9C,cAAE,EAAE;cACF,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,KAAK,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;AACvE,QAAA,IACE,iBAAiB,CAAC,MAAM,KAAK,CAAC;AAC9B,YAAA,iBAAiB,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EACjF;AACA,YAAA,OAAO,IAAI,OAAO,CAAwB,CAAC,OAAO,KAAI;gBACpD,iBAAiB,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAChD,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAC7B,aAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AACxB,SAAA;AAAM,aAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;AAC3F,YAAA,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC;AACxB,iBAAA,IAAI,CAAC,CAAC,IAAI,KAAI;AACb,gBAAA,iBAAiB,CAAC,UAAU,GAAG,IAAI,CAAC;AACpC,gBAAA,OAAO,iBAAiB,CAAC;AAC3B,aAAC,CAAC;iBACD,KAAK,CAAC,YAAY,CAAC,CAAC;AACxB,SAAA;AACF,KAAA;AAED,IAAA,OAAO,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAC5C;;AC9XA;AAwBA;;AAEG;AACI,MAAM,uBAAuB,GAAqB;AACvD,IAAA,MAAM,EAAE,IAAI;CACb,CAAC;AAEF;;;;AAIG;AACG,SAAU,eAAe,CAAC,gBAAmC,EAAA;IACjE,OAAO;AACL,QAAA,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,KAAI;YACnE,OAAO,IAAI,eAAe,CAAC,UAAU,EAAE,OAAO,EAAE,gBAAgB,IAAI,uBAAuB,CAAC,CAAC;SAC9F;KACF,CAAC;AACJ,CAAC;AAED;;AAEG;AACG,MAAO,eAAgB,SAAQ,iBAAiB,CAAA;AACpD;;;;;;AAMG;AACH,IAAA,WAAA,CACE,UAAyB,EACzB,OAA6B,EACZ,gBAAkC,EAAA;AAEnD,QAAA,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAFV,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAkB;KAGpD;AAED;;;;;AAKG;IACI,MAAM,WAAW,CAAC,OAAwB,EAAA;QAC/C,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;QACjD,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;KAC9C;AACF;;ACzED;AAaA;;AAEG;AACH,MAAM,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAkBjC,MAAM,sBAAsB,GAAoB;AACrD,IAAA,eAAe,EAAE,IAAI;AACrB,IAAA,UAAU,EAAE,EAAE;CACf,CAAC;AAEF;;;;AAIG;AACa,SAAA,cAAc,CAAC,cAAc,GAAG,EAAE,EAAA;IAChD,OAAO;AACL,QAAA,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,KAAI;YACnE,OAAO,IAAI,cAAc,CAAC,UAAU,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;SAChE;KACF,CAAC;AACJ,CAAC;AAED;;AAEG;AACG,MAAO,cAAe,SAAQ,iBAAiB,CAAA;AACnD,IAAA,WAAA,CAAY,UAAyB,EAAE,OAA6B,EAAW,aAAa,EAAE,EAAA;AAC5F,QAAA,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QADkD,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;KAE7F;AAEM,IAAA,WAAW,CAAC,OAAwB,EAAA;QACzC,OAAO,IAAI,CAAC,WAAW;aACpB,WAAW,CAAC,OAAO,CAAC;AACpB,aAAA,IAAI,CAAC,CAAC,QAAQ,KAAK,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;KAC1D;AACF,CAAA;AAED,SAAS,cAAc,CACrB,MAAsB,EACtB,QAA+B,EAC/B,cAAsB,EAAA;AAEtB,IAAA,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC;IACrC,MAAM,cAAc,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACxD,IAAA,IACE,cAAc;SACb,MAAM,KAAK,GAAG;AACb,aAAC,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC5D,aAAC,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAC3D,MAAM,KAAK,GAAG,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,CAAC;YAC7C,MAAM,KAAK,GAAG,CAAC;SAChB,CAAC,MAAM,CAAC,UAAU,IAAI,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC,EAC1D;QACA,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9C,QAAA,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;AAChC,QAAA,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;;;QAIjC,IAAI,MAAM,KAAK,GAAG,EAAE;AAClB,YAAA,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC;YACvB,OAAO,OAAO,CAAC,IAAI,CAAC;AACrB,SAAA;QAED,OAAO,MAAM,CAAC,WAAW;aACtB,WAAW,CAAC,OAAO,CAAC;AACpB,aAAA,IAAI,CAAC,CAAC,GAAG,KAAK,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AACnE,KAAA;AAED,IAAA,OAAO,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnC;;ACpGA;AACA;AAIO,MAAM,0BAA0B,GAAG,CAAC,CAAC;AAC5C;AACO,MAAM,6BAA6B,GAAG,IAAI,GAAG,EAAE,CAAC;AAChD,MAAM,iCAAiC,GAAG,IAAI,GAAG,EAAE,CAAC;AACpD,MAAM,iCAAiC,GAAG,IAAI,GAAG,CAAC,CAAC;AAEpD,SAAU,QAAQ,CAAC,CAAU,EAAA;AACjC,IAAA,OAAO,OAAO,CAAC,KAAK,QAAQ,CAAC;AAC/B,CAAC;AAaD;;;;;;;;AAQG;AACG,SAAU,WAAW,CACzB,UAAkB,EAClB,SAA4E,EAC5E,SAAoB,EACpB,QAAgC,EAChC,KAAkB,EAAA;AAElB,IAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE;AAC/B,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AAED,IAAA,OAAO,SAAS,CAAC,UAAU,GAAG,UAAU,CAAC;AAC3C,CAAC;AAED;;;;;;;AAOG;SACa,eAAe,CAC7B,YAA2F,EAC3F,YAAuB,EAAE,UAAU,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,EAC1D,GAAgB,EAAA;AAEhB,IAAA,IAAI,GAAG,EAAE;QACP,IAAI,SAAS,CAAC,KAAK,EAAE;AACnB,YAAA,GAAG,CAAC,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC;AAClC,SAAA;AAED,QAAA,SAAS,CAAC,KAAK,GAAG,GAAG,CAAC;AACvB,KAAA;;IAGD,SAAS,CAAC,UAAU,EAAE,CAAC;;AAGvB,IAAA,IAAI,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC/D,IAAA,MAAM,gBAAgB,GACpB,YAAY,CAAC,aAAa,GAAG,GAAG;AAChC,QAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,YAAY,CAAC,aAAa,GAAG,GAAG,CAAC,CAAC,CAAC;IACjE,cAAc,IAAI,gBAAgB,CAAC;AAEnC,IAAA,SAAS,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAChC,YAAY,CAAC,gBAAgB,GAAG,cAAc,EAC9C,YAAY,CAAC,gBAAgB,CAC9B,CAAC;AAEF,IAAA,OAAO,SAAS,CAAC;AACnB;;ACtFA;AA0BA;;;;;AAKG;SACa,sBAAsB,CACpC,UAAmB,EACnB,aAAsB,EACtB,gBAAyB,EAAA;IAEzB,OAAO;AACL,QAAA,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,KAAI;AACnE,YAAA,OAAO,IAAI,sBAAsB,CAC/B,UAAU,EACV,OAAO,EACP,UAAU,EACV,aAAa,EACb,gBAAgB,CACjB,CAAC;SACH;KACF,CAAC;AACJ,CAAC;AAED;;AAEG;AACSC,2BAMX;AAND,CAAA,UAAY,SAAS,EAAA;AACnB;;;AAGG;AACH,IAAA,SAAA,CAAA,SAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAW,CAAA;AACb,CAAC,EANWA,iBAAS,KAATA,iBAAS,GAMpB,EAAA,CAAA,CAAA,CAAA;AA8BM,MAAM,mBAAmB,GAAiB;AAC/C,IAAA,UAAU,EAAE,0BAA0B;AACtC,IAAA,cAAc,EAAE,6BAA6B;AAC7C,IAAA,iBAAiB,EAAE,iCAAiC;CACrD,CAAC;AAEF;;AAEG;AACG,MAAO,sBAAuB,SAAQ,iBAAiB,CAAA;AAc3D;;;;;;;AAOG;IACH,WACE,CAAA,UAAyB,EACzB,OAA6B,EAC7B,UAAmB,EACnB,aAAsB,EACtB,gBAAyB,EAAA;AAEzB,QAAA,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AAC3B,QAAA,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,UAAU,GAAG,0BAA0B,CAAC;AACjF,QAAA,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,aAAa,GAAG,6BAA6B,CAAC;AAC7F,QAAA,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAC;AAChD,cAAE,gBAAgB;cAChB,iCAAiC,CAAC;KACvC;AAEM,IAAA,WAAW,CAAC,OAAwB,EAAA;QACzC,OAAO,IAAI,CAAC,WAAW;AACpB,aAAA,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;AAC5B,aAAA,IAAI,CAAC,CAAC,QAAQ,KAAKC,OAAK,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;aAClD,KAAK,CAAC,CAAC,KAAK,KAAKA,OAAK,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;KAC7E;AACF,CAAA;AAED,eAAeA,OAAK,CAClB,MAA8B,EAC9B,OAAwB,EACxB,QAAgC,EAChC,SAAqB,EACrB,YAAyB,EAAA;IAEzB,SAAS,iBAAiB,CAAC,aAAqC,EAAA;QAC9D,MAAM,UAAU,GAAG,aAAa,KAAA,IAAA,IAAb,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAE,MAAM,CAAC;QACzC,IAAI,UAAU,KAAK,GAAG,KAAI,QAAQ,KAAR,IAAA,IAAA,QAAQ,uBAAR,QAAQ,CAAE,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe,CAAC,WAAW,CAAC,CAAA,EAAE;AACtF,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;QAED,IACE,UAAU,KAAK,SAAS;AACxB,aAAC,UAAU,GAAG,GAAG,IAAI,UAAU,KAAK,GAAG,CAAC;AACxC,YAAA,UAAU,KAAK,GAAG;YAClB,UAAU,KAAK,GAAG,EAClB;AACA,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACb;IAED,SAAS,GAAG,eAAe,CACzB;QACE,aAAa,EAAE,MAAM,CAAC,aAAa;AACnC,QAAA,gBAAgB,EAAE,CAAC;QACnB,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;AAC1C,KAAA,EACD,SAAS,EACT,YAAY,CACb,CAAC;IAEF,MAAM,SAAS,GAAwB,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC;AAC1F,IAAA,IAAI,CAAC,SAAS,IAAI,WAAW,CAAC,MAAM,CAAC,UAAU,EAAE,iBAAiB,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE;QACxF,MAAM,CAAC,IAAI,CAAC,CAAA,oBAAA,EAAuB,SAAS,CAAC,aAAa,CAAE,CAAA,CAAC,CAAC;QAC9D,IAAI;AACF,YAAA,MAAMC,cAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;AACrC,YAAA,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;YAClE,OAAOD,OAAK,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;AAC/C,SAAA;AAAC,QAAA,OAAO,GAAQ,EAAE;AACjB,YAAA,OAAOA,OAAK,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;AACzD,SAAA;AACF,KAAA;AAAM,SAAA,IAAI,SAAS,IAAI,YAAY,IAAI,CAAC,QAAQ,EAAE;;AAEjD,QAAA,MAAM,GAAG,GACP,SAAS,CAAC,KAAK;YACf,IAAI,SAAS,CACX,6BAA6B,EAC7B,SAAS,CAAC,kBAAkB,EAC5B,QAAQ,IAAI,QAAQ,CAAC,MAAM,EAC3B,QAAQ,IAAI,QAAQ,CAAC,OAAO,EAC5B,QAAQ,CACT,CAAC;AACJ,QAAA,MAAM,GAAG,CAAC;AACX,KAAA;AAAM,SAAA;AACL,QAAA,OAAO,QAAQ,CAAC;AACjB,KAAA;AACH;;AC1MA;AA8CA;;;;AAIG;AACa,SAAA,SAAS,CAAC,cAAA,GAAmC,EAAE,EAAA;IAC7D,OAAO;AACL,QAAA,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,KAAI;YACnE,OAAO,IAAI,SAAS,CAAC,UAAU,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;SAC3D;KACF,CAAC;AACJ,CAAC;AAED;;AAEG;AACG,MAAO,SAAU,SAAQ,iBAAiB,CAAA;AA4C9C,IAAA,WAAA,CACE,UAAyB,EACzB,OAA6B,EAC7B,UACEE,QAAM,GAAGC,MAAU,CAAC,IAAI,EACxB,kBAAkB,GAAG,EAAE,EACvB,sBAAsB,GAAG,EAAE,MACP,EAAE,EAAA;AAExB,QAAA,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AAC3B,QAAA,IAAI,CAAC,MAAM,GAAGD,QAAM,CAAC;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,CAAC,EAAE,kBAAkB,EAAE,sBAAsB,EAAE,CAAC,CAAC;KAChF;AApDD;;;;;;AAMG;AACH,IAAA,IAAW,kBAAkB,GAAA;AAC3B,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;KAC1C;AAED;;;;;;AAMG;IACH,IAAW,kBAAkB,CAAC,kBAA+B,EAAA;AAC3D,QAAA,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;KACxD;AAED;;;;AAIG;AACH,IAAA,IAAW,sBAAsB,GAAA;AAC/B,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC;KAC9C;AAED;;;;AAIG;IACH,IAAW,sBAAsB,CAAC,sBAAmC,EAAA;AACnE,QAAA,IAAI,CAAC,SAAS,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;KAChE;AAgBM,IAAA,WAAW,CAAC,OAAwB,EAAA;AACzC,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO;YAAE,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAEvE,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;KAC7F;AAEO,IAAA,UAAU,CAAC,OAAwB,EAAA;AACzC,QAAA,IAAI,CAAC,MAAM,CAAC,CAAA,SAAA,EAAY,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA,CAAE,CAAC,CAAC;KAC7D;AAEO,IAAA,WAAW,CAAC,QAA+B,EAAA;QACjD,IAAI,CAAC,MAAM,CAAC,CAAA,sBAAA,EAAyB,QAAQ,CAAC,MAAM,CAAE,CAAA,CAAC,CAAC;AACxD,QAAA,IAAI,CAAC,MAAM,CAAC,CAAY,SAAA,EAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA,CAAE,CAAC,CAAC;AACrE,QAAA,OAAO,QAAQ,CAAC;KACjB;AACF;;ACxID;AACA;AAqDA;;;;AAIG;AACG,SAAU,0BAA0B,CAAC,SAA6B,EAAA;IACtE,OAAO,8BAA8B,CAAC,SAAS,CAAC,aAAa,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AACnF,CAAC;AAEe,SAAA,8BAA8B,CAC5C,aAA4B,EAC5B,MAAc,EAAA;AAEd,IAAA,IAAI,MAAc,CAAC;AACnB,IAAA,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;QACrC,MAAM,GAAG,aAAa,CAAC;AACxB,KAAA;AAAM,SAAA,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;AACvC,QAAA,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAClC,KAAA;AAAM,SAAA;AACL,QAAA,MAAM,GAAG,MAAM,CAAC,cAAe,CAAC;AACjC,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB;;AC5EA;AAiGA;;;AAGG;AACG,SAAU,4BAA4B,CAAC,aAA4B,EAAA;AACvE,IAAA,MAAM,MAAM,GAAG,IAAI,GAAG,EAAU,CAAC;AACjC,IAAA,KAAK,MAAM,UAAU,IAAI,aAAa,CAAC,SAAS,EAAE;QAChD,MAAM,iBAAiB,GAAG,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAC9D,IACE,iBAAiB,CAAC,UAAU;YAC5B,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,MAAM,EAC5D;YACA,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;AAChC,SAAA;AACF,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB;;ACjHA;SAOgB,sBAAsB,GAAA;AACpC,IAAA,OAAO,SAAS,CAAC,eAAe,CAAC,UAAU,CAAC;AAC9C,CAAC;SAEe,uBAAuB,GAAA;AACrC,IAAA,MAAM,WAAW,GAAG;AAClB,QAAA,GAAG,EAAE,MAAM;QACX,KAAK,EAAE,OAAO,CAAC,OAAO;KACvB,CAAC;AAEF,IAAA,MAAM,MAAM,GAAG;AACb,QAAA,GAAG,EAAE,IAAI;AACT,QAAA,KAAK,EAAE,CAAI,CAAA,EAAAE,aAAE,CAAC,IAAI,EAAE,CAAI,CAAA,EAAAA,aAAE,CAAC,IAAI,EAAE,CAAI,CAAA,EAAAA,aAAE,CAAC,OAAO,EAAE,CAAG,CAAA,CAAA;KACrD,CAAC;AAEF,IAAA,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;AAC/B;;ACvBA;AA+BA,SAAS,cAAc,GAAA;AACrB,IAAA,MAAM,aAAa,GAAG;AACpB,QAAA,GAAG,EAAE,WAAW;QAChB,KAAK,EAAE,SAAS,CAAC,eAAe;KACjC,CAAC;IAEF,OAAO,CAAC,aAAa,CAAC,CAAC;AACzB,CAAC;AAED,SAAS,kBAAkB,CACzB,aAA8B,EAC9B,YAAY,GAAG,GAAG,EAClB,cAAc,GAAG,GAAG,EAAA;AAEpB,IAAA,OAAO,aAAa;AACjB,SAAA,GAAG,CAAC,CAAC,IAAI,KAAI;AACZ,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAA,EAAG,cAAc,CAAG,EAAA,IAAI,CAAC,KAAK,CAAA,CAAE,GAAG,EAAE,CAAC;AACjE,QAAA,OAAO,GAAG,IAAI,CAAC,GAAG,CAAG,EAAA,KAAK,EAAE,CAAC;AAC/B,KAAC,CAAC;SACD,IAAI,CAAC,YAAY,CAAC,CAAC;AACxB,CAAC;AAEM,MAAM,6BAA6B,GAAG,sBAAsB,CAAC;AAEpE;;;AAGG;SACa,wBAAwB,GAAA;AACtC,IAAA,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;AACrC,IAAA,MAAM,oBAAoB,GAAG,uBAAuB,EAAE,CAAC;IACvD,MAAM,SAAS,GAAG,kBAAkB,CAAC,WAAW,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC;AAC/E,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;;;AAIG;AACG,SAAU,eAAe,CAAC,aAA6B,EAAA;AAC3D,IAAA,MAAM,GAAG,GACP,CAAC,aAAa,IAAI,aAAa,CAAC,GAAG,KAAK,SAAS,IAAI,aAAa,CAAC,GAAG,KAAK,IAAI;UAC3E,sBAAsB,EAAE;AAC1B,UAAE,aAAa,CAAC,GAAG,CAAC;AACxB,IAAA,MAAM,KAAK,GACT,CAAC,aAAa,IAAI,aAAa,CAAC,KAAK,KAAK,SAAS,IAAI,aAAa,CAAC,KAAK,KAAK,IAAI;UAC/E,wBAAwB,EAAE;AAC5B,UAAE,aAAa,CAAC,KAAK,CAAC;IAE1B,OAAO;AACL,QAAA,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,KAAI;YACnE,OAAO,IAAI,eAAe,CAAC,UAAU,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;SAC7D;KACF,CAAC;AACJ,CAAC;AAED;;AAEG;AACG,MAAO,eAAgB,SAAQ,iBAAiB,CAAA;AACpD,IAAA,WAAA,CACW,WAA0B,EAC1B,QAA8B,EAC7B,SAAiB,EACjB,WAAmB,EAAA;AAE7B,QAAA,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QALpB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAe;QAC1B,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAsB;QAC7B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAQ;QACjB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAQ;KAG9B;AAED,IAAA,WAAW,CAAC,OAAwB,EAAA;AAClC,QAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;KAC9C;AAED;;AAEG;AACH,IAAA,kBAAkB,CAAC,OAAwB,EAAA;AACzC,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AACpB,YAAA,OAAO,CAAC,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;AACrC,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE;AAC5D,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AACvD,SAAA;KACF;AACF;;ACtHD;AACA;AAEA;;AAEG;AACSC,uCAqBX;AArBD,CAAA,UAAY,qBAAqB,EAAA;AAC/B;;AAEG;AACH,IAAA,qBAAA,CAAA,KAAA,CAAA,GAAA,GAAS,CAAA;AACT;;AAEG;AACH,IAAA,qBAAA,CAAA,KAAA,CAAA,GAAA,GAAS,CAAA;AACT;;AAEG;AACH,IAAA,qBAAA,CAAA,KAAA,CAAA,GAAA,IAAU,CAAA;AACV;;AAEG;AACH,IAAA,qBAAA,CAAA,OAAA,CAAA,GAAA,GAAW,CAAA;AACX;;AAEG;AACH,IAAA,qBAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACjB,CAAC,EArBWA,6BAAqB,KAArBA,6BAAqB,GAqBhC,EAAA,CAAA,CAAA;;AC3BD;AA8CA;AACO,MAAM,sBAAsB,GAAuB;AACxD,IAAA,uBAAuB,EAAE,IAAI;AAC7B,IAAA,iBAAiB,EAAE,IAAI;AACvB,IAAA,iBAAiB,EAAE,IAAI,GAAG,EAAE,GAAG,CAAC;CACjC,CAAC;AAEF;;;;;;;;;;;;AAYG;AACH,eAAe,YAAY,CACzB,cAAiD,EACjD,iBAAyB,EACzB,WAAmB,EAAA;;;AAInB,IAAA,eAAe,iBAAiB,GAAA;AAC9B,QAAA,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,WAAW,EAAE;YAC5B,IAAI;gBACF,OAAO,MAAM,cAAc,EAAE,CAAC;AAC/B,aAAA;YAAC,OAAM,EAAA,EAAA;AACN,gBAAA,OAAO,IAAI,CAAC;AACb,aAAA;AACF,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,UAAU,GAAG,MAAM,cAAc,EAAE,CAAC;;YAG1C,IAAI,UAAU,KAAK,IAAI,EAAE;AACvB,gBAAA,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;AACpD,aAAA;AAED,YAAA,OAAO,UAAU,CAAC;AACnB,SAAA;KACF;AAED,IAAA,IAAI,KAAK,GAAuB,MAAM,iBAAiB,EAAE,CAAC;IAE1D,OAAO,KAAK,KAAK,IAAI,EAAE;AACrB,QAAA,MAAMJ,cAAK,CAAC,iBAAiB,CAAC,CAAC;AAE/B,QAAA,KAAK,GAAG,MAAM,iBAAiB,EAAE,CAAC;AACnC,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;;;;;;;;;AAcG;AACH,SAAS,iBAAiB,CACxB,UAA2B,EAC3B,MAAyB,EACzB,kBAAgD,EAAA;IAEhD,IAAI,aAAa,GAAgC,IAAI,CAAC;IACtD,IAAI,KAAK,GAAuB,IAAI,CAAC;AAErC,IAAA,MAAM,OAAO,GACR,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,sBAAsB,CACtB,EAAA,kBAAkB,CACtB,CAAC;AAEF;;;AAGG;AACH,IAAA,MAAM,MAAM,GAAG;AACb;;AAEG;AACH,QAAA,IAAI,YAAY,GAAA;YACd,OAAO,aAAa,KAAK,IAAI,CAAC;SAC/B;AACD;;;AAGG;AACH,QAAA,IAAI,aAAa,GAAA;;AACf,YAAA,QACE,CAAC,MAAM,CAAC,YAAY;gBACpB,CAAC,CAAA,EAAA,GAAA,KAAK,KAAL,IAAA,IAAA,KAAK,uBAAL,KAAK,CAAE,kBAAkB,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,CAAC,IAAI,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,EAAE,EACzE;SACH;AACD;;;AAGG;AACH,QAAA,IAAI,WAAW,GAAA;AACb,YAAA,QACE,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,kBAAkB,GAAG,OAAO,CAAC,uBAAuB,GAAG,IAAI,CAAC,GAAG,EAAE,EACzF;SACH;KACF,CAAC;AAEF;;;AAGG;IACH,SAAS,OAAO,CAAC,eAAgC,EAAA;;AAC/C,QAAA,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;;AAExB,YAAA,MAAM,iBAAiB,GAAG,MACxB,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;;;AAI/C,YAAA,aAAa,GAAG,YAAY,CAC1B,iBAAiB,EACjB,OAAO,CAAC,iBAAiB;;AAEzB,YAAA,CAAA,EAAA,GAAA,KAAK,KAAA,IAAA,IAAL,KAAK,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAL,KAAK,CAAE,kBAAkB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,IAAI,CAAC,GAAG,EAAE,CACxC;AACE,iBAAA,IAAI,CAAC,CAAC,MAAM,KAAI;gBACf,aAAa,GAAG,IAAI,CAAC;gBACrB,KAAK,GAAG,MAAM,CAAC;AACf,gBAAA,OAAO,KAAK,CAAC;AACf,aAAC,CAAC;AACD,iBAAA,KAAK,CAAC,CAAC,MAAM,KAAI;;;;gBAIhB,aAAa,GAAG,IAAI,CAAC;gBACrB,KAAK,GAAG,IAAI,CAAC;AACb,gBAAA,MAAM,MAAM,CAAC;AACf,aAAC,CAAC,CAAC;AACN,SAAA;AAED,QAAA,OAAO,aAAqC,CAAC;KAC9C;AAED,IAAA,OAAO,OAAO,YAA6B,KAA0B;;;;;;;;;;QAWnE,IAAI,MAAM,CAAC,WAAW;AAAE,YAAA,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC;QAErD,IAAI,MAAM,CAAC,aAAa,EAAE;YACxB,OAAO,CAAC,YAAY,CAAC,CAAC;AACvB,SAAA;AAED,QAAA,OAAO,KAAoB,CAAC;AAC9B,KAAC,CAAC;AACJ,CAAC;AAED;AAEA;;;;;;AAMG;AACa,SAAA,+BAA+B,CAC7C,UAA2B,EAC3B,MAAyB,EAAA;;IAGzB,MAAM,QAAQ,GAAG,iBAAiB,CAAC,UAAU,EAAE,MAAM,iBAAiB,CAAC;IAEvE,MAAM,+BAAgC,SAAQ,iBAAiB,CAAA;QAC7D,WAAmB,CAAA,UAAyB,EAAE,OAA6B,EAAA;AACzE,YAAA,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;SAC5B;QAEM,MAAM,WAAW,CAAC,WAA4B,EAAA;AACnD,YAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;AACzD,gBAAA,MAAM,IAAI,KAAK,CACb,sFAAsF,CACvF,CAAC;AACH,aAAA;AAED,YAAA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,CAAC;gBAC/B,WAAW,EAAE,WAAW,CAAC,WAAW;AACpC,gBAAA,cAAc,EAAE;oBACd,cAAc,EAAE,WAAW,CAAC,cAAc;AAC3C,iBAAA;AACF,aAAA,CAAC,CAAC;AACH,YAAA,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe,CAAC,aAAa,EAAE,CAAA,OAAA,EAAU,KAAK,CAAA,CAAE,CAAC,CAAC;YACpF,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;SAClD;AACF,KAAA;IAED,OAAO;AACL,QAAA,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,KAAI;AACnE,YAAA,OAAO,IAAI,+BAA+B,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;SACjE;KACF,CAAC;AACJ;;ACxQA;AAYA;;;AAGG;SACa,kCAAkC,GAAA;IAChD,OAAO;AACL,QAAA,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,KAAI;AACnE,YAAA,OAAO,IAAI,kCAAkC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;SACpE;KACF,CAAC;AACJ,CAAC;AAED;;;AAGG;AACG,MAAO,kCAAmC,SAAQ,iBAAiB,CAAA;AACvE;;;;;AAKG;;;IAGH,WAAY,CAAA,UAAyB,EAAE,OAA6B,EAAA;AAClE,QAAA,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;KAC5B;AAED;;;;;AAKG;IACI,MAAM,WAAW,CAAC,OAAoB,EAAA;AAC3C,QAAA,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAC;QACnC,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;KAC9C;AACF;;ACnDD;AAYA;;;AAGG;AACa,SAAA,6BAA6B,CAC3C,mBAAmB,GAAG,wBAAwB,EAAA;IAE9C,OAAO;AACL,QAAA,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,KAAI;YACnE,OAAO,IAAI,6BAA6B,CAAC,UAAU,EAAE,OAAO,EAAE,mBAAmB,CAAC,CAAC;SACpF;KACF,CAAC;AACJ,CAAC;AAEK,MAAO,6BAA8B,SAAQ,iBAAiB,CAAA;AAClE,IAAA,WAAA,CACE,UAAyB,EACzB,OAA6B,EACrB,oBAA4B,EAAA;AAEpC,QAAA,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAFnB,IAAoB,CAAA,oBAAA,GAApB,oBAAoB,CAAQ;KAGrC;AAEM,IAAA,WAAW,CAAC,OAAwB,EAAA;QACzC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE;AACxD,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AACnE,SAAA;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;KAC9C;AACF;;ACzCD;AAMA,IAAI,gBAAwC,CAAC;SAE7B,0BAA0B,GAAA;IACxC,IAAI,CAAC,gBAAgB,EAAE;AACrB,QAAA,gBAAgB,GAAG,IAAIK,mBAAiB,EAAE,CAAC;AAC5C,KAAA;AAED,IAAA,OAAO,gBAAgB,CAAC;AAC1B;;ACdA;SAegB,YAAY,GAAA;IAC1B,OAAO;AACL,QAAA,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,KAAI;AACnE,YAAA,OAAO,IAAI,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;SAC9C;KACF,CAAC;AACJ,CAAC;AAED;;AAEG;AACH,MAAM,YAAa,SAAQ,iBAAiB,CAAA;AAC1C;;AAEG;IACH,WAAY,CAAA,UAAyB,EAAE,OAA6B,EAAA;AAClE,QAAA,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;KAC5B;AAED;;AAEG;IACI,MAAM,WAAW,CAAC,OAAwB,EAAA;;AAE/C,QAAA,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACpE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACtC,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACvB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACzE,aAAA;AACF,SAAA;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;KAC9C;AACF;;AC/CD;AAgBA;;;AAGG;AACI,MAAM,iBAAiB,GAAa,EAAE,CAAC;AAC9C,IAAI,iBAAiB,GAAY,KAAK,CAAC;AAEvC;AACA,MAAM,iBAAiB,GAAyB,IAAI,GAAG,EAAE,CAAC;AAE1D,SAAS,yBAAyB,GAAA;IAChC,IAAI,CAAC,OAAO,EAAE;AACZ,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;IAED,MAAM,UAAU,GAAG,mBAAmB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;IAC9D,MAAM,QAAQ,GAAG,mBAAmB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC1D,MAAM,SAAS,GAAG,mBAAmB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AAE5D,IAAA,OAAO,UAAU,IAAI,QAAQ,IAAI,SAAS,CAAC;AAC7C,CAAC;AAED;;;;AAIG;AACH,SAAS,UAAU,CACjB,GAAW,EACX,WAAqB,EACrB,WAAkC,EAAA;AAElC,IAAA,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;AAC5B,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;IACD,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,EAAG,CAAC;IAC9C,IAAI,WAAW,KAAX,IAAA,IAAA,WAAW,KAAX,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,WAAW,CAAE,GAAG,CAAC,IAAI,CAAC,EAAE;AAC1B,QAAA,OAAO,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC9B,KAAA;IACD,IAAI,cAAc,GAAG,KAAK,CAAC;AAC3B,IAAA,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE;AACjC,QAAA,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;;;AAGtB,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;gBAC1B,cAAc,GAAG,IAAI,CAAC;AACvB,aAAA;AAAM,iBAAA;AACL,gBAAA,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;oBACnE,cAAc,GAAG,IAAI,CAAC;AACvB,iBAAA;AACF,aAAA;AACF,SAAA;AAAM,aAAA;YACL,IAAI,IAAI,KAAK,OAAO,EAAE;gBACpB,cAAc,GAAG,IAAI,CAAC;AACvB,aAAA;AACF,SAAA;AACF,KAAA;IACD,WAAW,KAAA,IAAA,IAAX,WAAW,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAX,WAAW,CAAE,GAAG,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;AACvC,IAAA,OAAO,cAAc,CAAC;AACxB,CAAC;AAED;;AAEG;SACa,WAAW,GAAA;IACzB,MAAM,OAAO,GAAG,mBAAmB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IACxD,iBAAiB,GAAG,IAAI,CAAC;AACzB,IAAA,IAAI,OAAO,EAAE;AACX,QAAA,OAAO,OAAO;aACX,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;aAC1B,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;AAClC,KAAA;AAED,IAAA,OAAO,EAAE,CAAC;AACZ,CAAC;AAED;;;;AAIG;AACG,SAAU,uBAAuB,CAAC,QAAiB,EAAA;IACvD,IAAI,CAAC,QAAQ,EAAE;QACb,QAAQ,GAAG,yBAAyB,EAAE,CAAC;QACvC,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,OAAO,SAAS,CAAC;AAClB,SAAA;AACF,KAAA;AAED,IAAA,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;IAC5E,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AACnD,IAAA,MAAM,MAAM,GAAG,SAAS,CAAC,SAAS,EAAE,GAAG,SAAS,CAAC,SAAS,EAAE,GAAG,KAAK,GAAG,EAAE,CAAC;IAC1E,OAAO;AACL,QAAA,IAAI,EAAE,MAAM,GAAG,SAAS,CAAC,OAAO,EAAE;QAClC,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC;QAClD,QAAQ;QACR,QAAQ;KACT,CAAC;AACJ,CAAC;AAED;;;;;;AAMG;AACa,SAAA,WAAW,CACzB,aAA6B,EAC7B,OAGC,EAAA;IAED,IAAI,CAAC,aAAa,EAAE;QAClB,aAAa,GAAG,uBAAuB,EAAE,CAAC;AAC3C,KAAA;IACD,IAAI,CAAC,iBAAiB,EAAE;AACtB,QAAA,iBAAiB,CAAC,IAAI,CAAC,GAAG,WAAW,EAAE,CAAC,CAAC;AAC1C,KAAA;IACD,OAAO;AACL,QAAA,MAAM,EAAE,CAAC,UAAyB,EAAE,oBAA0C,KAAI;AAChF,YAAA,OAAO,IAAI,WAAW,CACpB,UAAU,EACV,oBAAoB,EACpB,aAAc,EACd,OAAO,aAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,iBAAiB,CAC3B,CAAC;SACH;KACF,CAAC;AACJ,CAAC;AAED,SAAS,kBAAkB,CAAC,GAAW,EAAA;IAKrC,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACjC,IAAA,IAAI,OAAO,KAAK,CAAC,CAAC,EAAE;AAClB,QAAA,OAAO,EAAE,cAAc,EAAE,GAAG,EAAE,CAAC;AAChC,KAAA;IAED,MAAM,WAAW,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACvC,IAAA,MAAM,SAAS,GAAG,WAAW,KAAK,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3D,MAAM,IAAI,GAAG,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAC/C,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACrC,IAAA,MAAM,WAAW,GAAG,UAAU,KAAK,CAAC,CAAC,CAAC;AACtC,IAAA,MAAM,QAAQ,GAAG,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,GAAG,IAAI,CAAC;AACpE,IAAA,MAAM,QAAQ,GAAG,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC;AAC1E,IAAA,MAAM,cAAc,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;IAChF,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,cAAc;KACf,CAAC;AACJ,CAAC;AAEK,MAAO,WAAY,SAAQ,iBAAiB,CAAA;AAChD,IAAA,WAAA,CACE,UAAyB,EACzB,OAA6B,EACtB,aAA4B,EAC3B,iBAA4B,EAAA;AAEpC,QAAA,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAHpB,IAAa,CAAA,aAAA,GAAb,aAAa,CAAe;QAC3B,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAW;KAGrC;AAEM,IAAA,WAAW,CAAC,OAAwB,EAAA;;QACzC,IACE,CAAC,OAAO,CAAC,aAAa;YACtB,CAAC,UAAU,CACT,OAAO,CAAC,GAAG,EACX,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,iBAAiB,EAC3C,IAAI,CAAC,iBAAiB,GAAG,SAAS,GAAG,iBAAiB,CACvD,EACD;AACA,YAAA,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;AAC5C,SAAA;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;KAC9C;AACF;;ACrMD;AAcgB,SAAA,oBAAoB,CAAC,YAAY,GAAG,EAAE,EAAA;IACpD,OAAO;AACL,QAAA,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,KAAI;YACnE,OAAO,IAAI,oBAAoB,CAAC,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;SACpE;KACF,CAAC;AACJ,CAAC;AAEK,MAAO,oBAAqB,SAAQ,iBAAiB,CAAA;AACzD,IAAA,WAAA,CACE,UAAyB,EACzB,OAA6B,EACpB,gBAAgB,EAAE,EAAA;AAE3B,QAAA,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAFlB,IAAa,CAAA,aAAA,GAAb,aAAa,CAAK;KAG5B;AAEM,IAAA,WAAW,CAAC,OAAwB,EAAA;QACzC,OAAO,IAAI,CAAC,WAAW;AACpB,aAAA,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;AAC5B,aAAA,IAAI,CAAC,CAAC,QAAQ,KAAK,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;KAClE;AACF,CAAA;AAED,SAAS,gBAAgB,CACvB,MAA4B,EAC5B,OAAwB,EACxB,QAA+B,EAAA;AAE/B,IAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;QAC3B,MAAM,MAAM,GAAG,yBAAyB,CAAC,QAAQ,CAAC,UAAoB,CAAC,CAAC;AACxE,QAAA,IAAI,MAAM,EAAE;YACV,MAAM,SAAS,GAAG,sBAAsB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACtD,QACE,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;;;;AAI3C,iBAAA,KAAK,CAAC,MAAM,KAAK,CAAC;AAClB,iBAAA,IAAI,CAAC,CAAC,kBAAkB,KAAI;AAC3B,gBAAA,IAAI,kBAAkB,EAAE;;;AAGtB,oBAAA,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAEC,YAAkB,EAAE,CAAC,CAAC;oBACpE,OAAO,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;AACxD,iBAAA;AACD,gBAAA,OAAO,QAAQ,CAAC;aACjB,CAAC,EACJ;AACH,SAAA;AACF,KAAA;AAED,IAAA,OAAO,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnC,CAAC;AAED;;;;;AAKG;AACH,SAAS,oBAAoB,CAC3B,eAAgC,EAChC,WAAW,GAAG,KAAK,EAAA;AAEnB,IAAA,MAAM,UAAU,GAAoB,eAAe,CAAC,KAAK,EAAE,CAAC;AAC5D,IAAA,IAAI,WAAW,EAAE;AACf,QAAA,UAAU,CAAC,GAAG,GAAG,eAAe,CAAC,GAAG,CAAC;AACtC,KAAA;;;AAID,IAAA,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAEA,YAAkB,EAAE,CAAC,CAAC;;IAGvE,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,iCAAiC,CAAC,CAAC;AAE1E,IAAA,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;;;;AAKG;AACH,SAAS,yBAAyB,CAAC,IAAY,EAAA;IAC7C,IAAI,MAAM,EAAE,YAAY,CAAC;AACzB,IAAA,IAAI,IAAI,EAAE;QACR,IAAI;AACF,YAAA,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACjC,SAAA;AAAC,QAAA,OAAO,GAAQ,EAAE;;AAElB,SAAA;AACD,QAAA,IACE,YAAY;AACZ,YAAA,YAAY,CAAC,KAAK;YAClB,YAAY,CAAC,KAAK,CAAC,OAAO;YAC1B,YAAY,CAAC,KAAK,CAAC,IAAI;AACvB,YAAA,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK,iCAAiC,EAC7D;AACA,YAAA,MAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;AAC/D,YAAA,IAAI,QAAQ,EAAE;AACZ,gBAAA,MAAM,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;AACzB,aAAA;AACF,SAAA;AACF,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;AAKG;AACH,SAAS,sBAAsB,CAAC,GAAW,EAAA;AACzC,IAAA,IAAI,MAAM,CAAC;IACX,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;AAChE,IAAA,IAAI,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE;AAC3B,QAAA,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AACtB,KAAA;AAAM,SAAA;AACL,QAAA,MAAM,IAAI,KAAK,CAAC,yDAAyD,GAAG,CAAA,CAAA,CAAG,CAAC,CAAC;AAClF,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;;AAOG;AACH,eAAe,UAAU,CACvB,MAA4B,EAC5B,SAAiB,EACjB,QAAgB,EAChB,eAAgC,EAAA;AAEhC,IAAA,MAAM,OAAO,GAAG,CAAA,EAAG,SAAS,CAAa,UAAA,EAAA,QAAQ,kCAAkC,CAAC;AACpF,IAAA,MAAM,MAAM,GAAG,CAAA,EAAG,SAAS,CAAa,UAAA,EAAA,QAAQ,yBAAyB,CAAC;AAC1E,IAAA,MAAM,UAAU,GAAG,oBAAoB,CAAC,eAAe,CAAC,CAAC;AACzD,IAAA,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;AAC3B,IAAA,UAAU,CAAC,GAAG,GAAG,OAAO,CAAC;IAEzB,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AAClE,IAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;AAC3B,QAAA,MAAM,IAAI,KAAK,CAAC,uBAAuB,QAAQ,CAAA,yCAAA,CAA2C,CAAC,CAAC;AAC7F,KAAA;IACD,OAAO,qBAAqB,CAAC,MAAM,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;AAChE,CAAC;AAED;;;;;;;;AAQG;AACH,eAAe,qBAAqB,CAClC,MAA4B,EAC5B,GAAW,EACX,eAAgC,EAAA;AAEhC,IAAA,MAAM,UAAU,GAAQ,oBAAoB,CAAC,eAAe,CAAC,CAAC;AAC9D,IAAA,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;AACrB,IAAA,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC;IAE1B,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AAC7D,IAAA,MAAM,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC;AAC3B,IAAA,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,iBAAiB,IAAI,GAAG,CAAC,iBAAiB,KAAK,YAAY,EAAE;AACrF,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AAAM,SAAA;QACL,MAAMN,cAAK,CAAC,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,CAAC;QACzC,OAAO,qBAAqB,CAAC,MAAM,EAAE,GAAG,EAAE,eAAe,CAAC,CAAC;AAC5D,KAAA;AACH;;AClMA;AAaA;;;;AAIG;AACG,SAAU,aAAa,CAC3B,sBAAgD,EAAA;IAEhD,OAAO;AACL,QAAA,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,KAAI;YACnE,OAAO,IAAI,aAAa,CAAC,UAAU,EAAE,OAAO,EAAE,sBAAsB,CAAC,CAAC;SACvE;KACF,CAAC;AACJ,CAAC;AAED;;AAEG;AACG,MAAO,aAAc,SAAQ,iBAAiB,CAAA;AAClD,IAAA,WAAA,CACE,UAAyB,EACzB,OAA6B,EACtB,sBAAgD,EAAA;AAEvD,QAAA,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAFpB,IAAsB,CAAA,sBAAA,GAAtB,sBAAsB,CAA0B;KAGxD;AAED,IAAA,WAAW,CAAC,OAAwB,EAAA;QAClC,OAAO,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;KACzD;AAEM,IAAA,WAAW,CAAC,OAAwB,EAAA;QACzC,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,KAChD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,CAC1C,CAAC;KACH;AACF;;ACjDD;AAwBA;;;;;;;AAOG;AACG,SAAU,sBAAsB,CACpC,UAAmB,EACnB,aAAsB,EACtB,gBAAyB,EACzB,gBAAyB,EAAA;IAEzB,OAAO;AACL,QAAA,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,KAAI;AACnE,YAAA,OAAO,IAAI,sBAAsB,CAC/B,UAAU,EACV,OAAO,EACP,UAAU,EACV,aAAa,EACb,gBAAgB,EAChB,gBAAgB,CACjB,CAAC;SACH;KACF,CAAC;AACJ,CAAC;AAED;;;;;;AAMG;AACG,MAAO,sBAAuB,SAAQ,iBAAiB,CAAA;IAM3D,WACE,CAAA,UAAyB,EACzB,OAA6B,EAC7B,UAAmB,EACnB,aAAsB,EACtB,gBAAyB,EACzB,gBAAyB,EAAA;AAEzB,QAAA,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AAC3B,QAAA,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,UAAU,GAAG,0BAA0B,CAAC;AACjF,QAAA,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,aAAa,GAAG,6BAA6B,CAAC;AAC7F,QAAA,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAC;AAChD,cAAE,gBAAgB;cAChB,iCAAiC,CAAC;AACtC,QAAA,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAC;AAChD,cAAE,gBAAgB;cAChB,iCAAiC,CAAC;KACvC;AAEM,IAAA,WAAW,CAAC,OAAwB,EAAA;QACzC,OAAO,IAAI,CAAC,WAAW;AACpB,aAAA,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;AAC5B,aAAA,KAAK,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;KAClE;AACF,CAAA;AAED,eAAe,KAAK,CAClB,MAA8B,EAC9B,OAAwB,EACxB,iBAAwC,EACxC,GAAgB,EAChB,SAAqB,EAAA;IAErB,SAAS,GAAG,eAAe,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;AAEpD,IAAA,SAAS,iBAAiB,CAAC,SAAiC,EAAE,KAAkB,EAAA;AAC9E,QAAA,IACE,KAAK;AACL,YAAA,KAAK,CAAC,IAAI;AACV,aAAC,KAAK,CAAC,IAAI,KAAK,WAAW;gBACzB,KAAK,CAAC,IAAI,KAAK,iBAAiB;gBAChC,KAAK,CAAC,IAAI,KAAK,cAAc;gBAC7B,KAAK,CAAC,IAAI,KAAK,YAAY;AAC3B,gBAAA,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC,EAC1B;AACA,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AACD,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,WAAW,CAAC,MAAM,CAAC,UAAU,EAAE,iBAAiB,EAAE,SAAS,EAAE,iBAAiB,EAAE,GAAG,CAAC,EAAE;;QAExF,IAAI;AACF,YAAA,MAAMA,cAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACrC,OAAO,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;AACxD,SAAA;AAAC,QAAA,OAAO,SAAc,EAAE;AACvB,YAAA,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;AACxE,SAAA;AACF,KAAA;AAAM,SAAA;AACL,QAAA,IAAI,GAAG,EAAE;;YAEP,OAAO,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACxC,SAAA;AACD,QAAA,OAAO,iBAAiB,CAAC;AAC1B,KAAA;AACH;;AClIA;AACA;AAEA;;AAEG;AACI,MAAM,8BAA8B,GAAG,CAAC;;ACN/C;AAoBA,MAAM,WAAW,GAAG,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC;AAExD;;;;;;;;;AASG;SACa,qBAAqB,GAAA;IACnC,OAAO;AACL,QAAA,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,KAAI;AACnE,YAAA,OAAO,IAAI,qBAAqB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;SACvD;KACF,CAAC;AACJ,CAAC;AAED,MAAM,oBAAoB,GAAG,4BAA4B,CAAC;AAE1D;;;;;;;;AAQG;AACG,MAAO,qBAAsB,SAAQ,iBAAiB,CAAA;AAI1D,IAAA,WAAA,CACE,UAAyB,EACzB,OAA6B,EAC7B,eAAiC,EAAA;AAEjC,QAAA,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAPrB,IAAe,CAAA,eAAA,GAAG,CAAC,CAAC;QAQ1B,IAAI,CAAC,eAAe,GAAG,eAAe,IAAI,IAAI,CAAC,uBAAuB,CAAC;KACxE;IAEM,MAAM,WAAW,CAAC,WAA4B,EAAA;AACnD,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC;AACzE,QAAA,IACE,QAAQ,CAAC,MAAM,KAAK,WAAW,CAAC,eAAe;AAC/C,YAAA,QAAQ,CAAC,MAAM,KAAK,WAAW,CAAC,kBAAkB,EAClD;AACA,YAAA,OAAO,QAAQ,CAAC;AACjB,SAAA;AAAM,aAAA;YACL,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AACpD,SAAA;KACF;AAEO,IAAA,MAAM,uBAAuB,CACnC,WAA4B,EAC5B,YAAmC,EAAA;;AAEnC,QAAA,MAAM,gBAAgB,GAAuB,YAAY,CAAC,OAAO,CAAC,GAAG,CACnE,SAAS,CAAC,eAAe,CAAC,WAAW,CACtC,CAAC;AAEF,QAAA,IAAI,gBAAgB,EAAE;YACpB,MAAM,SAAS,GACb,qBAAqB,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,CAAC;AAChE,YAAA,IAAI,SAAS,EAAE;AACb,gBAAA,IAAI,CAAC,eAAe,IAAI,CAAC,CAAC;gBAE1B,MAAMA,cAAK,CAAC,SAAS,EAAE;oBACrB,WAAW,EAAE,WAAW,CAAC,WAAW;AACpC,oBAAA,aAAa,EAAE,oBAAoB;AACpC,iBAAA,CAAC,CAAC;AAEH,gBAAA,IAAI,MAAA,WAAW,CAAC,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,EAAE;AACpC,oBAAA,MAAM,IAAIV,0BAAU,CAAC,oBAAoB,CAAC,CAAC;AAC5C,iBAAA;AAED,gBAAA,IAAI,IAAI,CAAC,eAAe,GAAG,8BAA8B,EAAE;AACzD,oBAAA,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACtC,iBAAA;AAAM,qBAAA;oBACL,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AAClD,iBAAA;AACF,aAAA;AACF,SAAA;AAED,QAAA,OAAO,YAAY,CAAC;KACrB;IAEM,OAAO,qBAAqB,CAAC,WAAmB,EAAA;AACrD,QAAA,MAAM,mBAAmB,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;AAChD,QAAA,IAAI,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAAE;AACrC,YAAA,OAAO,qBAAqB,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;AACrE,SAAA;AAAM,aAAA;YACL,OAAO,mBAAmB,GAAG,IAAI,CAAC;AACnC,SAAA;KACF;IAEM,OAAO,yBAAyB,CAAC,WAAmB,EAAA;QACzD,IAAI;AACF,YAAA,MAAM,GAAG,GAAW,IAAI,CAAC,GAAG,EAAE,CAAC;YAC/B,MAAM,IAAI,GAAW,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;AAC7C,YAAA,MAAM,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;AAExB,YAAA,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC;AAC9C,SAAA;AAAC,QAAA,OAAO,KAAU,EAAE;AACnB,YAAA,OAAO,SAAS,CAAC;AAClB,SAAA;KACF;AACF;;AClID;AAqBA,MAAM,UAAU,GAAGiB,8BAAkB,CAAC;AACpC,IAAA,aAAa,EAAE,EAAE;AACjB,IAAA,SAAS,EAAE,EAAE;AACd,CAAA,CAAC,CAAC;AAYH;;;;AAIG;AACa,SAAA,aAAa,CAAC,cAAA,GAAuC,EAAE,EAAA;IACrE,OAAO;QACL,MAAM,CAAC,UAAyB,EAAE,OAA6B,EAAA;YAC7D,OAAO,IAAI,aAAa,CAAC,UAAU,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;SAC/D;KACF,CAAC;AACJ,CAAC;AAED;;AAEG;AACG,MAAO,aAAc,SAAQ,iBAAiB,CAAA;AAGlD,IAAA,WAAA,CACE,UAAyB,EACzB,OAA6B,EAC7B,cAAoC,EAAA;AAEpC,QAAA,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AAC3B,QAAA,IAAI,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC;KAC3C;IAEM,MAAM,WAAW,CAAC,OAAwB,EAAA;AAC/C,QAAA,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YAC3B,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAC9C,SAAA;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAEzC,IAAI,CAAC,IAAI,EAAE;YACT,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAC9C,SAAA;QAED,IAAI;YACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAC7D,YAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AACxC,YAAA,OAAO,QAAQ,CAAC;AACjB,SAAA;AAAC,QAAA,OAAO,GAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAChC,YAAA,MAAM,GAAG,CAAC;AACX,SAAA;KACF;AAED,IAAA,aAAa,CAAC,OAAwB,EAAA;;QACpC,IAAI;;;YAGF,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC,CAAA,KAAA,EAAQ,OAAO,CAAC,MAAM,CAAA,CAAE,EAAE;AACpD,gBAAA,cAAc,EAAE;oBACd,WAAW,EAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACL,OAAe,CAAC,WAAW,CAAA,EAAA,EAC/B,IAAI,EAAEC,oBAAQ,CAAC,MAAM,EACtB,CAAA;oBACD,cAAc,EAAE,OAAO,CAAC,cAAc;AACvC,iBAAA;AACF,aAAA,CAAC,CAAC;;AAGH,YAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;gBACvB,IAAI,CAAC,GAAG,EAAE,CAAC;AACX,gBAAA,OAAO,SAAS,CAAC;AAClB,aAAA;AAED,YAAA,MAAM,oBAAoB,GAAG,CAAA,EAAA,GAAA,OAAO,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC;AAE1F,YAAA,IAAI,OAAO,oBAAoB,KAAK,QAAQ,EAAE;AAC5C,gBAAA,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,oBAAoB,CAAC,CAAC;AACzD,aAAA;YAED,IAAI,CAAC,aAAa,CAAC;gBACjB,aAAa,EAAE,OAAO,CAAC,MAAM;gBAC7B,UAAU,EAAE,OAAO,CAAC,GAAG;gBACvB,SAAS,EAAE,OAAO,CAAC,SAAS;AAC7B,aAAA,CAAC,CAAC;YAEH,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AACtD,aAAA;;AAGD,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AACvC,YAAA,MAAM,iBAAiB,GAAGC,gCAAoB,CAAC,WAAW,CAAC,CAAC;AAC5D,YAAA,IAAI,iBAAiB,IAAIC,8BAAkB,CAAC,WAAW,CAAC,EAAE;gBACxD,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;AACtD,gBAAA,MAAM,UAAU,GAAG,WAAW,CAAC,UAAU,IAAI,WAAW,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;;AAEhF,gBAAA,IAAI,UAAU,EAAE;oBACd,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;AAC/C,iBAAA;AACF,aAAA;AACD,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AAAC,QAAA,OAAO,KAAU,EAAE;YACnB,MAAM,CAAC,OAAO,CAAC,CAAA,kDAAA,EAAqD,KAAK,CAAC,OAAO,CAAE,CAAA,CAAC,CAAC;AACrF,YAAA,OAAO,SAAS,CAAC;AAClB,SAAA;KACF;IAEO,eAAe,CAAC,IAAU,EAAE,GAAQ,EAAA;QAC1C,IAAI;YACF,IAAI,CAAC,SAAS,CAAC;gBACb,IAAI,EAAEC,0BAAc,CAAC,KAAK;gBAC1B,OAAO,EAAE,GAAG,CAAC,OAAO;AACrB,aAAA,CAAC,CAAC;YAEH,IAAI,GAAG,CAAC,UAAU,EAAE;gBAClB,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;AACvD,aAAA;YACD,IAAI,CAAC,GAAG,EAAE,CAAC;AACZ,SAAA;AAAC,QAAA,OAAO,KAAU,EAAE;YACnB,MAAM,CAAC,OAAO,CAAC,CAAA,kDAAA,EAAqD,KAAK,CAAC,OAAO,CAAE,CAAA,CAAC,CAAC;AACtF,SAAA;KACF;IAEO,kBAAkB,CAAC,IAAU,EAAE,QAA+B,EAAA;QACpE,IAAI;YACF,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;YACvD,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;AACjE,YAAA,IAAI,gBAAgB,EAAE;AACpB,gBAAA,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;AACzD,aAAA;YACD,IAAI,CAAC,SAAS,CAAC;gBACb,IAAI,EAAEA,0BAAc,CAAC,EAAE;AACxB,aAAA,CAAC,CAAC;YACH,IAAI,CAAC,GAAG,EAAE,CAAC;AACZ,SAAA;AAAC,QAAA,OAAO,KAAU,EAAE;YACnB,MAAM,CAAC,OAAO,CAAC,CAAA,kDAAA,EAAqD,KAAK,CAAC,OAAO,CAAE,CAAA,CAAC,CAAC;AACtF,SAAA;KACF;AACF;;AC1KD;AAiKA;;AAEG;MACU,aAAa,CAAA;AAsBxB;;;;AAIG;AACH,IAAA,WAAA,CACE,WAAwD;;IAExD,OAA8B,EAAA;QAE9B,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,GAAG,EAAE,CAAC;AACd,SAAA;QAED,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,eAAe,IAAI,KAAK,CAAC;QACzD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,IAAI,0BAA0B,EAAE,CAAC;QACtE,IAAI,CAAC,qBAAqB,GAAG,IAAI,oBAAoB,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAElF,QAAA,IAAI,sBAA8C,CAAC;QACnD,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,EAAE;AACjD,YAAA,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;AAC5D,YAAA,sBAAsB,GAAG,OAAO,CAAC,sBAAsB,CAAC;AACzD,SAAA;AAAM,aAAA;YACL,IAAI,iBAAiB,GAAqC,SAAS,CAAC;AACpE,YAAA,IAAIC,0BAAiB,CAAC,WAAW,CAAC,EAAE;AAClC,gBAAA,MAAM,CAAC,IAAI,CACT,sFAAsF,CACvF,CAAC;;;;;;;gBAOF,MAAM,oBAAoB,GAA+B,MAAK;oBAC5D,IAAI,wBAAwB,GAAqC,SAAS,CAAC;;oBAE3E,MAAM,aAAa,GAAG,IAAI,CAAC;oBAC3B,MAAM,oBAAoB,GAAG,OAAO,CAAC;oBACrC,OAAO;wBACL,MAAM,CAAC,UAAyB,EAAE,aAAmC,EAAA;4BACnE,MAAM,gBAAgB,GAAG,mBAAmB,CAC1C,oBAAoB,EACpB,aAAa,CAAC,OAAO,CACtB,CAAC;4BAEF,IAAI,CAAC,gBAAgB,EAAE;AACrB,gCAAA,MAAM,IAAI,KAAK,CACb,CAAA,iKAAA,CAAmK,CACpK,CAAC;AACH,6BAAA;AAED,4BAAA,IAAI,wBAAwB,KAAK,SAAS,IAAI,wBAAwB,KAAK,IAAI,EAAE;AAC/E,gCAAA,wBAAwB,GAAG,+BAA+B,CACxD,WAAW,EACX,gBAAgB,CACjB,CAAC;AACH,6BAAA;4BAED,OAAO,wBAAwB,CAAC,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;yBACnE;qBACF,CAAC;AACJ,iBAAC,CAAC;gBAEF,iBAAiB,GAAG,oBAAoB,EAAE,CAAC;AAC5C,aAAA;iBAAM,IAAI,WAAW,IAAI,OAAO,WAAW,CAAC,WAAW,KAAK,UAAU,EAAE;AACvE,gBAAA,MAAM,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;AAChF,gBAAA,iBAAiB,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;AAChD,aAAA;AAAM,iBAAA,IAAI,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI,EAAE;AAC5D,gBAAA,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;AAC1F,aAAA;AAED,YAAA,MAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;AAC7D,YAAA,sBAAsB,GAAG,mCAAmC,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;YACzF,IAAI,OAAO,CAAC,sBAAsB,EAAE;;;gBAGlC,MAAM,yBAAyB,GAC7B,OAAO,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,CAAC;AACzD,gBAAA,IAAI,yBAAyB,EAAE;oBAC7B,sBAAsB,GAAG,yBAAyB,CAAC;AACpD,iBAAA;AACF,aAAA;AACF,SAAA;AACD,QAAA,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;KACvD;AAED;;AAEG;AACH,IAAA,WAAW,CAAC,OAAgD,EAAA;AAC1D,QAAA,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AAC5E,YAAA,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;AACvF,SAAA;AAED,QAAA,IAAI,WAA4B,CAAC;QACjC,IAAI;AACF,YAAA,IAAI,iBAAiB,CAAC,OAAO,CAAC,EAAE;gBAC9B,OAAO,CAAC,yBAAyB,EAAE,CAAC;gBACpC,WAAW,GAAG,OAAO,CAAC;AACvB,aAAA;AAAM,iBAAA;AACL,gBAAA,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;AAChC,gBAAA,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC5C,aAAA;AACF,SAAA;AAAC,QAAA,OAAO,KAAU,EAAE;AACnB,YAAA,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC9B,SAAA;AAED,QAAA,IAAI,YAAY,GAAkB,IAAI,CAAC,WAAW,CAAC;QACnD,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,EAAE;AAC3E,YAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;AACjE,gBAAA,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,MAAM,CACnD,YAAY,EACZ,IAAI,CAAC,qBAAqB,CAC3B,CAAC;AACH,aAAA;AACF,SAAA;AACD,QAAA,OAAO,YAAY,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;KAC9C;AAED;;;;;AAKG;AACH,IAAA,MAAM,oBAAoB,CACxB,kBAAsC,EACtC,aAA4B,EAC5B,QAA+B,EAAA;;AAE/B,QAAA,IAAI,OAAO,kBAAkB,CAAC,OAAO,KAAK,UAAU,EAAE;AACpD,YAAA,QAAQ,GAAG,kBAAkB,CAAC,OAAO,CAAC;AACtC,YAAA,kBAAkB,CAAC,OAAO,GAAG,SAAS,CAAC;AACxC,SAAA;QAED,MAAM,iBAAiB,GAAG,CAAA,EAAA,GAAA,kBAAkB,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,iBAAiB,CAAC;AACxE,QAAA,MAAM,WAAW,GAAoB,IAAI,WAAW,EAAE,CAAC;AAEvD,QAAA,IAAI,MAA6B,CAAC;QAClC,IAAI;YACF,MAAM,OAAO,GAAuB,aAAa,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;YAC1E,IAAI,CAAC,OAAO,EAAE;AACZ,gBAAA,MAAM,IAAI,KAAK,CACb,0IAA0I,CAC3I,CAAC;AACH,aAAA;AAED,YAAA,WAAW,CAAC,MAAM,GAAG,aAAa,CAAC,UAAU,CAAC;AAC9C,YAAA,WAAW,CAAC,aAAa,GAAG,aAAa,CAAC;YAE1C,MAAM,UAAU,GAAe,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACzD,IAAI,aAAa,CAAC,IAAI,EAAE;AACtB,gBAAA,UAAU,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AAC3C,aAAA;YACD,IAAI,aAAa,CAAC,aAAa,IAAI,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;AACzE,gBAAA,KAAK,MAAM,YAAY,IAAI,aAAa,CAAC,aAAa,EAAE;AACtD,oBAAA,IAAI,iBAAiB,GAAW,sCAAsC,CACpE,IAAI,EACJ,kBAAkB,EAClB,YAAY,EACZ,aAAa,CAAC,UAAU,CACzB,CAAC;oBACF,iBAAiB,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CACpD,YAAY,CAAC,MAAM,EACnB,iBAAiB,EACjB,0BAA0B,CAAC,YAAY,CAAC,EACxC,iBAAiB,CAClB,CAAC;AACF,oBAAA,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE;AAC9B,wBAAA,iBAAiB,GAAG,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;AAC3D,qBAAA;AACD,oBAAA,UAAU,CAAC,UAAU,CACnB,IAAI,YAAY,CAAC,MAAM,CAAC,cAAc,IAAI,0BAA0B,CAAC,YAAY,CAAC,GAAG,EACrF,iBAAiB,CAClB,CAAC;AACH,iBAAA;AACF,aAAA;YACD,IAAI,aAAa,CAAC,eAAe,IAAI,aAAa,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;AAC7E,gBAAA,KAAK,MAAM,cAAc,IAAI,aAAa,CAAC,eAAe,EAAE;AAC1D,oBAAA,IAAI,mBAAmB,GAAQ,sCAAsC,CACnE,IAAI,EACJ,kBAAkB,EAClB,cAAc,EACd,aAAa,CAAC,UAAU,CACzB,CAAC;AACF,oBAAA,IAAI,mBAAmB,KAAK,SAAS,IAAI,mBAAmB,KAAK,IAAI,EAAE;wBACrE,mBAAmB,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CACtD,cAAc,CAAC,MAAM,EACrB,mBAAmB,EACnB,0BAA0B,CAAC,cAAc,CAAC,EAC1C,iBAAiB,CAClB,CAAC;AACF,wBAAA,IACE,cAAc,CAAC,gBAAgB,KAAK,SAAS;AAC7C,4BAAA,cAAc,CAAC,gBAAgB,KAAK,IAAI,EACxC;AACA,4BAAA,IAAI,cAAc,CAAC,gBAAgB,KAAKR,6BAAqB,CAAC,KAAK,EAAE;AACnE,gCAAA,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;;oCAEpC,SAAS;AACV,iCAAA;AAAM,qCAAA;AACL,oCAAA,KAAK,MAAM,KAAK,IAAI,mBAAmB,EAAE;AACvC,wCAAA,MAAM,IAAI,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;wCACxC,mBAAmB,CAAC,KAAK,CAAC;AACxB,4CAAA,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC9D,qCAAA;AACF,iCAAA;AACF,6BAAA;AAAM,iCAAA,IACL,cAAc,CAAC,gBAAgB,KAAKA,6BAAqB,CAAC,GAAG;AAC7D,gCAAA,cAAc,CAAC,gBAAgB,KAAKA,6BAAqB,CAAC,GAAG,EAC7D;gCACA,mBAAmB,GAAG,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;AACjF,6BAAA;AACF,yBAAA;AACD,wBAAA,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE;AAChC,4BAAA,IAAI,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE;AACtC,gCAAA,KAAK,MAAM,KAAK,IAAI,mBAAmB,EAAE;AACvC,oCAAA,IACE,mBAAmB,CAAC,KAAK,CAAC,KAAK,SAAS;AACxC,wCAAA,mBAAmB,CAAC,KAAK,CAAC,KAAK,IAAI,EACnC;wCACA,mBAAmB,CAAC,KAAK,CAAC,GAAG,kBAAkB,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7E,qCAAA;AACF,iCAAA;AACF,6BAAA;AAAM,iCAAA;AACL,gCAAA,mBAAmB,GAAG,kBAAkB,CAAC,mBAAmB,CAAC,CAAC;AAC/D,6BAAA;AACF,yBAAA;AACD,wBAAA,IACE,cAAc,CAAC,gBAAgB,KAAK,SAAS;4BAC7C,cAAc,CAAC,gBAAgB,KAAK,IAAI;AACxC,4BAAA,cAAc,CAAC,gBAAgB,KAAKA,6BAAqB,CAAC,KAAK;AAC/D,4BAAA,cAAc,CAAC,gBAAgB,KAAKA,6BAAqB,CAAC,GAAG;AAC7D,4BAAA,cAAc,CAAC,gBAAgB,KAAKA,6BAAqB,CAAC,GAAG,EAC7D;4BACA,mBAAmB,GAAG,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;AACjF,yBAAA;AACD,wBAAA,UAAU,CAAC,iBAAiB,CAC1B,cAAc,CAAC,MAAM,CAAC,cAAc,IAAI,0BAA0B,CAAC,cAAc,CAAC,EAClF,mBAAmB,CACpB,CAAC;AACH,qBAAA;AACF,iBAAA;AACF,aAAA;AACD,YAAA,WAAW,CAAC,GAAG,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;YAExC,MAAM,WAAW,GAAG,aAAa,CAAC,WAAW,IAAI,IAAI,CAAC,kBAAkB,CAAC;AACzE,YAAA,IAAI,WAAW,IAAI,aAAa,CAAC,WAAW,EAAE;gBAC5C,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;AACtD,aAAA;YAED,IAAI,aAAa,CAAC,gBAAgB,EAAE;AAClC,gBAAA,KAAK,MAAM,eAAe,IAAI,aAAa,CAAC,gBAAgB,EAAE;AAC5D,oBAAA,IAAI,WAAW,GAAQ,sCAAsC,CAC3D,IAAI,EACJ,kBAAkB,EAClB,eAAe,EACf,aAAa,CAAC,UAAU,CACzB,CAAC;AACF,oBAAA,IAAI,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI,EAAE;wBACrD,WAAW,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CAC9C,eAAe,CAAC,MAAM,EACtB,WAAW,EACX,0BAA0B,CAAC,eAAe,CAAC,EAC3C,iBAAiB,CAClB,CAAC;AACF,wBAAA,MAAM,sBAAsB,GAAI,eAAe,CAAC,MAA2B;AACxE,6BAAA,sBAAsB,CAAC;AAC1B,wBAAA,IAAI,sBAAsB,EAAE;4BAC1B,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;AAC1C,gCAAA,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,GAAG,GAAG,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;AACzE,6BAAA;AACF,yBAAA;AAAM,6BAAA;4BACL,WAAW,CAAC,OAAO,CAAC,GAAG,CACrB,eAAe,CAAC,MAAM,CAAC,cAAc;AACnC,gCAAA,0BAA0B,CAAC,eAAe,CAAC,EAC7C,WAAW,CACZ,CAAC;AACH,yBAAA;AACF,qBAAA;AACF,iBAAA;AACF,aAAA;AAED,YAAA,MAAM,OAAO,GAAmC,kBAAkB,CAAC,OAAO,CAAC;AAC3E,YAAA,IAAI,OAAO,EAAE;gBACX,IAAI,OAAO,CAAC,aAAa,EAAE;AACzB,oBAAA,KAAK,MAAM,gBAAgB,IAAI,OAAO,CAAC,aAAa,EAAE;AACpD,wBAAA,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC;AACpF,qBAAA;AACF,iBAAA;gBAED,IAAI,OAAO,CAAC,WAAW,EAAE;AACvB,oBAAA,WAAW,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;AAC/C,iBAAA;gBAED,IAAI,OAAO,CAAC,OAAO,EAAE;AACnB,oBAAA,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;AACvC,iBAAA;gBAED,IAAI,OAAO,CAAC,gBAAgB,EAAE;AAC5B,oBAAA,WAAW,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;AACzD,iBAAA;gBAED,IAAI,OAAO,CAAC,kBAAkB,EAAE;AAC9B,oBAAA,WAAW,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;AAC7D,iBAAA;gBAED,IAAI,OAAO,CAAC,WAAW,EAAE;;AAEtB,oBAAA,WAAmB,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;AACxD,iBAAA;gBAED,IAAI,OAAO,CAAC,cAAc,EAAE;AAC1B,oBAAA,WAAW,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;AACrD,iBAAA;gBAED,IAAI,OAAO,CAAC,iBAAiB,KAAK,SAAS,IAAI,OAAO,CAAC,iBAAiB,KAAK,IAAI,EAAE;AACjF,oBAAA,WAAW,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAC;AAC3D,iBAAA;AACF,aAAA;AAED,YAAA,WAAW,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAEpD,oBAAoB,CAAC,IAAI,EAAE,WAAW,EAAE,kBAAkB,EAAE,aAAa,CAAC,CAAC;AAE3E,YAAA,IAAI,WAAW,CAAC,yBAAyB,KAAK,SAAS,EAAE;AACvD,gBAAA,WAAW,CAAC,yBAAyB,GAAG,4BAA4B,CAAC,aAAa,CAAC,CAAC;AACrF,aAAA;AAED,YAAA,IAAI,WAAkC,CAAC;AACvC,YAAA,IAAI,gBAAgB,CAAC;YACrB,IAAI;gBACF,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACnD,aAAA;AAAC,YAAA,OAAO,KAAU,EAAE;gBACnB,gBAAgB,GAAG,KAAK,CAAC;AAC1B,aAAA;AACD,YAAA,IAAI,gBAAgB,EAAE;gBACpB,IAAI,gBAAgB,CAAC,QAAQ,EAAE;AAC7B,oBAAA,gBAAgB,CAAC,OAAO,GAAG,eAAe,CACxC,gBAAgB,CAAC,QAAQ,EACzB,aAAa,CAAC,SAAS,CAAC,gBAAgB,CAAC,UAAU,CAAC;AAClD,wBAAA,aAAa,CAAC,SAAS,CAAC,SAAS,CAAC,CACrC,CAAC;AACH,iBAAA;AACD,gBAAA,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;AAC3C,aAAA;AAAM,iBAAA;AACL,gBAAA,MAAM,GAAG,OAAO,CAAC,OAAO,CACtB,eAAe,CAAC,WAAY,EAAE,aAAa,CAAC,SAAS,CAAC,WAAY,CAAC,MAAM,CAAC,CAAC,CAC5E,CAAC;AACH,aAAA;AACF,SAAA;AAAC,QAAA,OAAO,KAAU,EAAE;AACnB,YAAA,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAChC,SAAA;QAED,MAAM,EAAE,GAAG,QAAQ,CAAC;AACpB,QAAA,IAAI,EAAE,EAAE;YACN,MAAM;iBACH,IAAI,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,CAAC,UAAU,EAAE,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;iBACvF,KAAK,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5B,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACf;AACF,CAAA;AAEK,SAAU,oBAAoB,CAClC,aAA4B,EAC5B,WAA4B,EAC5B,kBAAsC,EACtC,aAA4B,EAAA;;IAE5B,MAAM,iBAAiB,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,kBAAkB,CAAC,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,iBAAiB,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,EAAE,CAAC;AAC9E,IAAA,MAAM,cAAc,GAAgC;AAClD,QAAA,QAAQ,EAAE,CAAA,EAAA,GAAA,iBAAiB,CAAC,QAAQ,mCAAI,EAAE;AAC1C,QAAA,WAAW,EAAE,CAAA,EAAA,GAAA,iBAAiB,CAAC,WAAW,mCAAI,KAAK;AACnD,QAAA,UAAU,EAAE,CAAA,EAAA,GAAA,iBAAiB,CAAC,UAAU,mCAAI,WAAW;KACxD,CAAC;AAEF,IAAA,MAAM,UAAU,GAAG,iBAAiB,CAAC,UAAU,CAAC;IAChD,IAAI,aAAa,CAAC,WAAW,IAAI,aAAa,CAAC,WAAW,CAAC,MAAM,EAAE;AACjE,QAAA,WAAW,CAAC,IAAI,GAAG,sCAAsC,CACvD,aAAa,EACb,kBAAkB,EAClB,aAAa,CAAC,WAAW,EACzB,aAAa,CAAC,UAAU,CACzB,CAAC;AAEF,QAAA,MAAM,UAAU,GAAG,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC;AACpD,QAAA,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,YAAY,EAAE,kBAAkB,EAAE,GAC3F,UAAU,CAAC;AACb,QAAA,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;QAEtC,IAAI;AACF,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,SAAS,IAAI,WAAW,CAAC,IAAI,KAAK,IAAI,KAAK,QAAQ,EAAE;gBAC7E,MAAM,8BAA8B,GAAW,0BAA0B,CACvE,aAAa,CAAC,WAAW,CAC1B,CAAC;AACF,gBAAA,WAAW,CAAC,IAAI,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CACnD,UAAU,EACV,WAAW,CAAC,IAAI,EAChB,8BAA8B,EAC9B,cAAc,CACf,CAAC;AAEF,gBAAA,MAAM,QAAQ,GAAG,QAAQ,KAAK,UAAU,CAAC,MAAM,CAAC;gBAEhD,IAAI,aAAa,CAAC,KAAK,EAAE;AACvB,oBAAA,MAAM,QAAQ,GAAG,kBAAkB,GAAG,CAAS,MAAA,EAAA,kBAAkB,CAAE,CAAA,GAAG,OAAO,CAAC;AAC9E,oBAAA,MAAM,KAAK,GAAG,wBAAwB,CACpC,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,WAAW,CAAC,IAAI,EAChB,cAAc,CACf,CAAC;AACF,oBAAA,IAAI,QAAQ,KAAK,UAAU,CAAC,QAAQ,EAAE;wBACpC,WAAW,CAAC,IAAI,GAAG,YAAY,CAC7BS,kBAAwB,CACtB,KAAK,EACL,cAAc,IAAI,OAAO,IAAI,cAAe,EAC5C,QAAQ,EACR,YAAY,CACb,EACD;4BACE,QAAQ,EAAE,OAAO,IAAI,cAAc;4BACnC,UAAU;AACX,yBAAA,CACF,CAAC;AACH,qBAAA;yBAAM,IAAI,CAAC,QAAQ,EAAE;AACpB,wBAAA,WAAW,CAAC,IAAI,GAAG,YAAY,CAAC,KAAK,EAAE;4BACrC,QAAQ,EAAE,OAAO,IAAI,cAAc;4BACnC,UAAU;AACX,yBAAA,CAAC,CAAC;AACJ,qBAAA;AACF,iBAAA;AAAM,qBAAA,IACL,QAAQ,KAAK,UAAU,CAAC,MAAM;AAC9B,qBAAC,CAAA,CAAA,EAAA,GAAA,aAAa,CAAC,WAAW,0CAAE,KAAK,CAAC,YAAY,CAAC,KAAI,aAAa,CAAC,SAAS,KAAK,MAAM,CAAC,EACtF;;;oBAGA,OAAO;AACR,iBAAA;qBAAM,IAAI,CAAC,QAAQ,EAAE;oBACpB,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACrD,iBAAA;AACF,aAAA;AACF,SAAA;AAAC,QAAA,OAAO,KAAU,EAAE;YACnB,MAAM,IAAI,KAAK,CACb,CAAA,OAAA,EAAU,KAAK,CAAC,OAAO,2CAA2C,IAAI,CAAC,SAAS,CAC9E,cAAc,EACd,SAAS,EACT,IAAI,CACL,CAAA,CAAA,CAAG,CACL,CAAC;AACH,SAAA;AACF,KAAA;SAAM,IAAI,aAAa,CAAC,kBAAkB,IAAI,aAAa,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1F,QAAA,WAAW,CAAC,QAAQ,GAAG,EAAE,CAAC;AAC1B,QAAA,KAAK,MAAM,iBAAiB,IAAI,aAAa,CAAC,kBAAkB,EAAE;AAChE,YAAA,MAAM,sBAAsB,GAAQ,sCAAsC,CACxE,aAAa,EACb,kBAAkB,EAClB,iBAAiB,EACjB,aAAa,CAAC,UAAU,CACzB,CAAC;AACF,YAAA,IAAI,sBAAsB,KAAK,SAAS,IAAI,sBAAsB,KAAK,IAAI,EAAE;AAC3E,gBAAA,MAAM,6BAA6B,GACjC,iBAAiB,CAAC,MAAM,CAAC,cAAc,IAAI,0BAA0B,CAAC,iBAAiB,CAAC,CAAC;gBAC3F,WAAW,CAAC,QAAQ,CAAC,6BAA6B,CAAC,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CACtF,iBAAiB,CAAC,MAAM,EACxB,sBAAsB,EACtB,0BAA0B,CAAC,iBAAiB,CAAC,EAC7C,cAAc,CACf,CAAC;AACH,aAAA;AACF,SAAA;AACF,KAAA;AACH,CAAC;AAED;;AAEG;AACH,SAAS,wBAAwB,CAC/B,YAAgC,EAChC,QAAgB,EAChB,QAAgB,EAChB,eAAoB,EACpB,OAAoC,EAAA;;;AAIpC,IAAA,IAAI,YAAY,IAAI,CAAC,CAAC,WAAW,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;QAC/E,MAAM,MAAM,GAAQ,EAAE,CAAC;AACvB,QAAA,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,eAAe,CAAC;QAC7C,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,GAAG,YAAY,EAAE,CAAC;AACnD,QAAA,OAAO,MAAM,CAAC;AACf,KAAA;AAED,IAAA,OAAO,eAAe,CAAC;AACzB,CAAC;AAED,SAAS,wBAAwB,CAC/B,KAA8D,EAC9D,mBAAiC,EAAA;AAEjC,IAAA,IAAI,MAAc,CAAC;AACnB,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAM,GAAG,KAAK,CAAC;AAChB,KAAA;AAAM,SAAA;QACL,MAAM,GAAG,mBAAmB,EAAE,CAAC;AAC/B,QAAA,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;AAC/B,YAAA,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AACxB,SAAA;AACF,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,mCAAmC,CAC1C,iBAAmD,EACnD,OAA6B,EAAA;IAE7B,MAAM,SAAS,GAA2B,EAAE,CAAC;IAE7C,IAAI,OAAO,CAAC,6BAA6B,EAAE;QACzC,SAAS,CAAC,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC,CAAC;AAClF,KAAA;AAED,IAAA,IAAI,iBAAiB,EAAE;AACrB,QAAA,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACnC,KAAA;IAED,MAAM,mBAAmB,GAAW,wBAAwB,CAC1D,OAAO,CAAC,mBAAmB,EAC3B,6BAA6B,CAC9B,CAAC;IACF,MAAM,oBAAoB,GAAW,wBAAwB,CAC3D,OAAO,CAAC,SAAS,EACjB,wBAAwB,CACzB,CAAC;IACF,IAAI,mBAAmB,IAAI,oBAAoB,EAAE;AAC/C,QAAA,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,GAAG,EAAE,mBAAmB,EAAE,KAAK,EAAE,oBAAoB,EAAE,CAAC,CAAC,CAAC;AAC5F,KAAA;AACD,IAAA,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IACjC,SAAS,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC;AAEzE,IAAA,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;AAC1B,QAAA,SAAS,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;AACzC,QAAA,SAAS,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;AACzC,QAAA,SAAS,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC;AACzC,KAAA;IAED,SAAS,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC,CAAC;AAE3E,IAAA,IAAI,MAAM,EAAE;QACV,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;AACpD,KAAA;AAED,IAAA,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAEnD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;;;;AAKG;AACa,SAAA,yBAAyB,CACvC,eAAwC,EACxC,iBAAwC,EAAA;IAExC,MAAM,sBAAsB,GAA2B,EAAE,CAAC;IAE1D,IAAI,eAAe,CAAC,iBAAiB,EAAE;AACrC,QAAA,sBAAsB,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;AAC7C,KAAA;IAED,IAAI,cAAc,GAAG,SAAS,CAAC;IAC/B,IAAI,eAAe,CAAC,gBAAgB,IAAI,eAAe,CAAC,gBAAgB,CAAC,eAAe,EAAE;QACxF,MAAM,aAAa,GAAa,EAAE,CAAC;QACnC,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;;;AAIrE,QAAA,MAAM,oBAAoB,GAAG,wBAAwB,EAAE,CAAC;QACxD,IAAI,aAAa,CAAC,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,EAAE;AACtD,YAAA,aAAa,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;AAC1C,SAAA;AAED,QAAA,cAAc,GAAG,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1C,KAAA;IAED,MAAM,gBAAgB,mCACjB,uBAAuB,CAAA,EACvB,eAAe,CAAC,gBAAgB,CACpC,CAAC;IAEF,MAAM,YAAY,mCACb,mBAAmB,CAAA,EACnB,eAAe,CAAC,YAAY,CAChC,CAAC;IAEF,MAAM,eAAe,mCAChB,sBAAsB,CAAA,EACtB,eAAe,CAAC,eAAe,CACnC,CAAC;AAEF,IAAA,IAAI,MAAM,EAAE;QACV,sBAAsB,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC;AACxE,KAAA;IAED,MAAM,sBAAsB,mCACvB,6BAA6B,CAAA,EAC7B,eAAe,CAAC,sBAAsB,CAC1C,CAAC;AAEF,IAAA,MAAM,cAAc,GACf,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,eAAe,CAAC,cAAc,CAClC,CAAC;IAEF,sBAAsB,CAAC,IAAI,CACzB,aAAa,CAAC,EAAE,SAAS,EAAE,cAAc,EAAE,CAAC,EAC5C,eAAe,CAAC,gBAAgB,CAAC,EACjC,eAAe,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC,EAC1C,6BAA6B,EAAE,EAC/B,qBAAqB,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,EAClE,qBAAqB,EAAE,EACvB,sBAAsB,EAAE,EACxB,sBAAsB,CACpB,YAAY,CAAC,UAAU,EACvB,YAAY,CAAC,cAAc,EAC3B,YAAY,CAAC,iBAAiB,CAC/B,CACF,CAAC;IAEF,IAAI,eAAe,CAAC,eAAe,EAAE;QACnC,sBAAsB,CAAC,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC;AACzE,KAAA;AAED,IAAA,IAAI,iBAAiB,EAAE;AACrB,QAAA,sBAAsB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAChD,KAAA;IAED,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC;AAEvD,IAAA,IAAI,MAAM,IAAI,eAAe,CAAC,kBAAkB,KAAK,KAAK,EAAE;AAC1D,QAAA,sBAAsB,CAAC,IAAI,CAAC,kCAAkC,EAAE,CAAC,CAAC;AACnE,KAAA;IAED,OAAO;QACL,UAAU,EAAE,eAAe,CAAC,UAAU;QACtC,sBAAsB;KACvB,CAAC;AACJ,CAAC;AAsBD,SAAS,sCAAsC,CAC7C,aAA4B,EAC5B,kBAAsC,EACtC,SAA6B,EAC7B,UAAsB,EAAA;AAEtB,IAAA,OAAO,0CAA0C,CAC/C,aAAa,EACb,kBAAkB,EAClB,SAAS,CAAC,aAAa,EACvB,SAAS,CAAC,MAAM,EAChB,UAAU,CACX,CAAC;AACJ,CAAC;AAEK,SAAU,0CAA0C,CACxD,aAA4B,EAC5B,kBAAsC,EACtC,aAA4B,EAC5B,eAAuB,EACvB,UAAsB,EAAA;;AAEtB,IAAA,IAAI,KAAU,CAAC;AACf,IAAA,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;AACrC,QAAA,aAAa,GAAG,CAAC,aAAa,CAAC,CAAC;AACjC,KAAA;IACD,MAAM,iBAAiB,GAAG,CAAA,EAAA,GAAA,kBAAkB,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,iBAAiB,CAAC;AACxE,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;AAChC,QAAA,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5B,IAAI,eAAe,CAAC,UAAU,EAAE;AAC9B,gBAAA,KAAK,GAAG,eAAe,CAAC,YAAY,CAAC;AACtC,aAAA;AAAM,iBAAA;gBACL,IAAI,oBAAoB,GAAyB,4BAA4B,CAC3E,kBAAkB,EAClB,aAAa,CACd,CAAC;AACF,gBAAA,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE;AACvC,oBAAA,oBAAoB,GAAG,4BAA4B,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;AACnF,iBAAA;gBAED,IAAI,eAAe,GAAG,KAAK,CAAC;AAC5B,gBAAA,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE;oBACvC,eAAe;AACb,wBAAA,eAAe,CAAC,QAAQ;AACxB,6BAAC,aAAa,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;AAClE,iBAAA;AACD,gBAAA,KAAK,GAAG,eAAe,GAAG,eAAe,CAAC,YAAY,GAAG,oBAAoB,CAAC,aAAa,CAAC;AAC7F,aAAA;;YAGD,MAAM,mBAAmB,GAAW,8BAA8B,CAChE,aAAa,EACb,eAAe,CAChB,CAAC;YACF,UAAU,CAAC,SAAS,CAAC,eAAe,EAAE,KAAK,EAAE,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;AACtF,SAAA;AACF,KAAA;AAAM,SAAA;QACL,IAAI,eAAe,CAAC,QAAQ,EAAE;YAC5B,KAAK,GAAG,EAAE,CAAC;AACZ,SAAA;AAED,QAAA,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;YACxC,MAAM,cAAc,GAAY,eAAmC,CAAC,IAAI,CAAC,eAAgB,CACvF,YAAY,CACb,CAAC;AACF,YAAA,MAAM,YAAY,GAAkB,aAAa,CAAC,YAAY,CAAC,CAAC;AAChE,YAAA,MAAM,aAAa,GAAQ,0CAA0C,CACnE,aAAa,EACb,kBAAkB,EAClB,YAAY,EACZ,cAAc,EACd,UAAU,CACX,CAAC;;YAEF,MAAM,kBAAkB,GAAW,8BAA8B,CAC/D,YAAY,EACZ,cAAc,CACf,CAAC;YACF,UAAU,CAAC,SAAS,CAAC,cAAc,EAAE,aAAa,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;AAC3F,YAAA,IAAI,aAAa,KAAK,SAAS,IAAI,aAAa,KAAK,IAAI,EAAE;gBACzD,IAAI,CAAC,KAAK,EAAE;oBACV,KAAK,GAAG,EAAE,CAAC;AACZ,iBAAA;AACD,gBAAA,KAAK,CAAC,YAAY,CAAC,GAAG,aAAa,CAAC;AACrC,aAAA;AACF,SAAA;AACF,KAAA;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAOD,SAAS,4BAA4B,CACnC,MAAwC,EACxC,aAAuB,EAAA;AAEvB,IAAA,MAAM,MAAM,GAAyB,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC;IAC9D,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,OAAO,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AACpC,QAAA,MAAM,iBAAiB,GAAW,aAAa,CAAC,CAAC,CAAC,CAAC;;QAEnD,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI,IAAI,iBAAiB,IAAI,MAAM,EAAE;AAC1E,YAAA,MAAM,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;AACpC,SAAA;AAAM,aAAA;YACL,MAAM;AACP,SAAA;AACF,KAAA;AACD,IAAA,IAAI,CAAC,KAAK,aAAa,CAAC,MAAM,EAAE;AAC9B,QAAA,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC;AAC9B,QAAA,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC;AAC7B,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;AAKG;AACa,SAAA,eAAe,CAC7B,SAAgC,EAChC,YAA2C,EAAA;AAE3C,IAAA,MAAM,aAAa,GAAG,SAAS,CAAC,aAAa,CAAC;AAC9C,IAAA,MAAM,UAAU,GAAG,YAAY,IAAI,YAAY,CAAC,UAAU,CAAC;AAE3D,IAAA,MAAM,oBAAoB,GAAG,CAC3B,GAAM,KAGJ;AACF,QAAA,OAAO,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,WAAW,EAAE;AAC7C,YAAA,KAAK,EAAE,SAAS;AACjB,SAAA,CAEA,CAAC;AACJ,KAAC,CAAC;AAEF,IAAA,IAAI,UAAU,EAAE;AACd,QAAA,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;QACtC,IAAI,QAAQ,KAAK,QAAQ,EAAE;AACzB,YAAA,OAAO,oBAAoB,CACtB,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,aAAa,CAChB,EAAA,EAAA,QAAQ,EAAE,SAAS,CAAC,QAAQ,EAC5B,kBAAkB,EAAE,SAAS,CAAC,kBAAkB,IAChD,CAAC;AACJ,SAAA;AAED,QAAA,MAAM,eAAe,GACnB,CAAC,QAAQ,KAAK,WAAW,IAAK,UAA8B,CAAC,IAAI,CAAC,eAAe,KAAK,EAAE,CAAC;QAC3F,MAAM,kBAAkB,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAC1D,CAAC,CAAC,KAAK,eAAe,CAAC,CAAC,CAAC,CAAC,cAAc,KAAK,EAAE,CAChD,CAAC;AACF,QAAA,IAAI,QAAQ,KAAK,UAAU,IAAI,kBAAkB,EAAE;AACjD,YAAA,MAAM,aAAa,GAAG,CAAC,IAAI,SAAS,CAAC,UAAU,IAAI,EAAE,CAAC,CAAyB,CAAC;YAEhF,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;AAC9C,gBAAA,IAAI,eAAe,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE;oBACvC,aAAa,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAChD,iBAAA;AACF,aAAA;AAED,YAAA,IAAI,aAAa,EAAE;gBACjB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;oBAC5C,aAAa,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;AACzC,iBAAA;AACF,aAAA;YACD,oBAAoB,CAAC,aAAa,CAAC,CAAC;AACpC,YAAA,OAAO,aAAa,CAAC;AACtB,SAAA;AAED,QAAA,IAAI,QAAQ,KAAK,WAAW,IAAI,QAAQ,KAAK,YAAY,EAAE;YACzD,OAAO,oBAAoB,iCACtB,aAAa,CAAA,EACb,SAAS,CAAC,UAAU,EACvB,CAAC;AACJ,SAAA;AACF,KAAA;AAED,IAAA,IACE,UAAU;AACV,QAAA,SAAS,CAAC,OAAO,CAAC,MAAM,KAAK,MAAM;AACnC,QAAAC,eAAqB,CAAC,SAAS,CAAC,UAAU,CAAC,EAC3C;;QAEA,OAAO,oBAAoB,CACtB,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,aAAa,CAChB,EAAA,EAAA,IAAI,EAAE,SAAS,CAAC,UAAU,EAAA,CAAA,CAC1B,CAAC;AACJ,KAAA;IAED,OAAO,oBAAoB,iCACtB,aAAa,CAAA,EACb,SAAS,CAAC,UAAU,EACvB,CAAC;AACL,CAAC;AAED,SAAS,mBAAmB,CAC1B,OAA8B,EAC9B,OAAgB,EAAA;AAEhB,IAAA,IAAI,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,gBAAgB,EAAE;AAC7B,QAAA,MAAM,MAAM,GAAG,OAAO,CAAC,gBAAgB,CAAC;AACxC,QAAA,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;AAC1B,cAAE,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;cAChD,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;AAChC,KAAA;AAED,IAAA,IAAI,OAAO,EAAE;QACX,OAAO,CAAA,EAAG,OAAO,CAAA,SAAA,CAAW,CAAC;AAC9B,KAAA;AACD,IAAA,OAAO,SAAS,CAAC;AACnB;;ACrjCA;AA2BA;;;;;;;;AAQG;AACG,SAAU,kBAAkB,CAChC,IAAgB,EAAA;AAKhB,IAAA,OAAOC,8BAA6B,CAAC,IAAI,CAAC,CAAC;AAC7C;;AC3CA;AACA;AAIA;;AAEG;AACI,MAAM,oBAAoB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;AAqBlD;;;;;;AAMG;MACU,wBAAwB,CAAA;AAInC;;;AAGG;AACH,IAAA,WAAA,CAAY,uBAA+B,oBAAoB,EAAA;QANvD,IAAW,CAAA,WAAA,GAAiB,SAAS,CAAC;AAO5C,QAAA,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;KAClD;AAED;;;AAGG;AACH,IAAA,cAAc,CAAC,WAAoC,EAAA;AACjD,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;KAChC;AAED;;AAEG;IACH,cAAc,GAAA;QACZ,IACE,IAAI,CAAC,WAAW;AAChB,YAAA,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAC7E;AACA,YAAA,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;AAC9B,SAAA;QAED,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;AACF;;ACrED;AACA;AAIA;;;;AAIG;MACU,oBAAoB,CAAA;AAI/B,IAAA,WAAA,CACU,UAA2B,EAC3B,MAAyB,EACzB,uCAA+C,KAAK,EAAA;QAFpD,IAAU,CAAA,UAAA,GAAV,UAAU,CAAiB;QAC3B,IAAM,CAAA,MAAA,GAAN,MAAM,CAAmB;QACzB,IAAoC,CAAA,oCAAA,GAApC,oCAAoC,CAAgB;QALtD,IAAU,CAAA,UAAA,GAAG,CAAC,CAAC;KAMnB;AAEJ;;;AAGG;IACI,OAAO,GAAA;;AAEZ,QAAA,QACE,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,oCAAoC,EAC5F;KACH;AAED;;;;;AAKG;IACK,MAAM,QAAQ,CAAC,OAAwB,EAAA;AAC7C,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AAC7B,QAAA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AACnE,QAAA,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;QACzB,OAAO,KAAK,IAAI,SAAS,CAAC;KAC3B;AAED;;;AAGG;AACI,IAAA,OAAO,CAAC,OAAwB,EAAA;AACrC,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACvC,SAAA;QAED,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;AACF;;ACvDD;AASA,MAAM,eAAe,GAAG,SAAS,CAAC,eAAe,CAAC;AAClD,MAAM,4BAA4B,GAAG,OAAO,CAAC;AAE7C;;AAEG;MACU,8BAA8B,CAAA;AAiBzC;;;;;;AAMG;AACH,IAAA,WAAA,CACE,QAAgB,EAChB,QAAgB,EAChB,sBAA8B,4BAA4B,EAAA;AAhB5D;;;AAGG;QACH,IAAmB,CAAA,mBAAA,GAAW,4BAA4B,CAAC;AAczD,QAAA,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,SAAS,IAAI,OAAO,QAAQ,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;AACzF,YAAA,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;AACrF,SAAA;AACD,QAAA,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,SAAS,IAAI,OAAO,QAAQ,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;AACzF,YAAA,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;AACrF,SAAA;AACD,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;KAChD;AAED;;;;;AAKG;AACH,IAAA,WAAW,CAAC,WAA4B,EAAA;QACtC,MAAM,WAAW,GAAG,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAA,CAAA,EAAI,IAAI,CAAC,QAAQ,CAAA,CAAE,CAAC;AACxD,QAAA,MAAM,kBAAkB,GAAG,CAAG,EAAA,IAAI,CAAC,mBAAmB,CAAA,CAAA,EAAIC,YAAmB,CAAC,WAAW,CAAC,EAAE,CAAC;QAC7F,IAAI,CAAC,WAAW,CAAC,OAAO;AAAE,YAAA,WAAW,CAAC,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC;AAC3E,QAAA,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;KACrC;AACF;;ACpED;AAqBA;;AAEG;MACU,iBAAiB,CAAA;AAU5B;;AAEG;AACH,IAAA,WAAA,CAAY,OAAgC,EAAA;AAC1C,QAAA,IAAI,CAAC,OAAO,KAAK,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AAClE,YAAA,MAAM,IAAI,KAAK,CACb,CAAA,wHAAA,CAA0H,CAC3H,CAAC;AACH,SAAA;AACD,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AACjC,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;KAChC;AAED;;;;;AAKG;AACH,IAAA,WAAW,CAAC,WAA4B,EAAA;QACtC,IAAI,CAAC,WAAW,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,CACnB,IAAI,KAAK,CAAC,CAAA,qEAAA,CAAuE,CAAC,CACnF,CAAC;AACH,SAAA;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;AACxB,gBAAA,WAAW,CAAC,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;AACzC,aAAA;AACD,YAAA,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,QAAQ,EAAE;AACtC,gBAAA,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;AAChE,aAAA;AACF,SAAA;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE;gBACpB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,CAAA,yCAAA,CAA2C,CAAC,CAAC,CAAC;AAC/E,aAAA;YACD,IAAI,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACpC,gBAAA,WAAW,CAAC,GAAG,IAAI,GAAG,CAAC;AACxB,aAAA;AACD,YAAA,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE;gBAC9B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAClC,oBAAA,WAAW,CAAC,GAAG,IAAI,GAAG,CAAC;AACxB,iBAAA;AACD,gBAAA,WAAW,CAAC,GAAG,IAAI,CAAA,EAAG,GAAG,CAAA,CAAA,EAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;AAClD,aAAA;AACF,SAAA;AAED,QAAA,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;KACrC;AACF;;ACtFD;AAKA;;AAEG;AACG,MAAO,gBAAiB,SAAQ,iBAAiB,CAAA;AACrD;;;;AAIG;AACH,IAAA,WAAA,CAAY,QAAgB,EAAA;QAC1B,IAAI,CAAC,QAAQ,KAAK,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,CAAC,EAAE;AAC3D,YAAA,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;AACrF,SAAA;AACD,QAAA,MAAM,OAAO,GAA4B;AACvC,YAAA,QAAQ,EAAE;AACR,gBAAA,aAAa,EAAE,QAAQ;AACxB,aAAA;SACF,CAAC;QACF,KAAK,CAAC,OAAO,CAAC,CAAC;KAChB;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}