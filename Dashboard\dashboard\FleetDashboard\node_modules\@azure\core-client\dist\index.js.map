{"version": 3, "file": "index.js", "sources": ["../src/base64.ts", "../src/interfaces.ts", "../src/utils.ts", "../src/serializer.ts", "../src/operationHelpers.ts", "../src/deserializationPolicy.ts", "../src/interfaceHelpers.ts", "../src/serializationPolicy.ts", "../src/pipeline.ts", "../src/httpClientCache.ts", "../src/urlHelpers.ts", "../src/log.ts", "../src/serviceClient.ts", "../src/authorizeRequestOnClaimChallenge.ts", "../src/authorizeRequestOnTenantChallenge.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Encodes a string in base64 format.\n * @param value - the string to encode\n * @internal\n */\nexport function encodeString(value: string): string {\n  return Buffer.from(value).toString(\"base64\");\n}\n\n/**\n * Encodes a byte array in base64 format.\n * @param value - the Uint8Aray to encode\n * @internal\n */\nexport function encodeByteArray(value: Uint8Array): string {\n  // Buffer.from accepts <ArrayBuffer> | <SharedArrayBuffer>-- the TypeScript definition is off here\n  // https://nodejs.org/api/buffer.html#buffer_class_method_buffer_from_arraybuffer_byteoffset_length\n  const bufferValue = value instanceof Buffer ? value : Buffer.from(value.buffer as ArrayBuffer);\n  return bufferValue.toString(\"base64\");\n}\n\n/**\n * Decodes a base64 string into a byte array.\n * @param value - the base64 string to decode\n * @internal\n */\nexport function decodeString(value: string): Uint8Array {\n  return Buffer.from(value, \"base64\");\n}\n\n/**\n * Decodes a base64 string into a string.\n * @param value - the base64 string to decode\n * @internal\n */\nexport function decodeStringToString(value: string): string {\n  return Buffer.from(value, \"base64\").toString();\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  HttpClient,\n  HttpMethods,\n  PipelineOptions,\n  PipelinePolicy,\n  PipelineRequest,\n  PipelineResponse,\n  TransferProgressEvent,\n} from \"@azure/core-rest-pipeline\";\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { OperationTracingOptions } from \"@azure/core-tracing\";\n\n/**\n * Default key used to access the XML attributes.\n */\nexport const XML_ATTRKEY = \"$\";\n/**\n * Default key used to access the XML value content.\n */\nexport const XML_CHARKEY = \"_\";\n/**\n * Options to govern behavior of xml parser and builder.\n */\nexport interface XmlOptions {\n  /**\n   * indicates the name of the root element in the resulting XML when building XML.\n   */\n  rootName?: string;\n  /**\n   * indicates whether the root element is to be included or not in the output when parsing XML.\n   */\n  includeRoot?: boolean;\n  /**\n   * key used to access the XML value content when parsing XML.\n   */\n  xmlCharKey?: string;\n}\n/**\n * Options to configure serialization/de-serialization behavior.\n */\nexport interface SerializerOptions {\n  /**\n   * Options to configure xml parser/builder behavior.\n   */\n  xml: XmlOptions;\n  /**\n   * Normally additional properties are included in the result object, even if there is no mapper for them.\n   * This flag disables this behavior when true. It is used when parsing headers to avoid polluting the result object.\n   */\n  ignoreUnknownProperties?: boolean;\n}\n\nexport type RequiredSerializerOptions = {\n  [K in keyof SerializerOptions]: Required<SerializerOptions[K]>;\n};\n\n/**\n * A type alias for future proofing.\n */\nexport type OperationRequest = PipelineRequest;\n\n/**\n * Metadata that is used to properly parse a response.\n */\nexport interface OperationRequestInfo {\n  /**\n   * Used to parse the response.\n   */\n  operationSpec?: OperationSpec;\n\n  /**\n   * Used to encode the request.\n   */\n  operationArguments?: OperationArguments;\n\n  /**\n   * A function that returns the proper OperationResponseMap for the given OperationSpec and\n   * PipelineResponse combination. If this is undefined, then a simple status code lookup will\n   * be used.\n   */\n  operationResponseGetter?: (\n    operationSpec: OperationSpec,\n    response: PipelineResponse\n  ) => undefined | OperationResponseMap;\n\n  /**\n   * Whether or not the PipelineResponse should be deserialized. Defaults to true.\n   */\n  shouldDeserialize?: boolean | ((response: PipelineResponse) => boolean);\n}\n\n/**\n * The base options type for all operations.\n */\nexport interface OperationOptions {\n  /**\n   * The signal which can be used to abort requests.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Options used when creating and sending HTTP requests for this operation.\n   */\n  requestOptions?: OperationRequestOptions;\n  /**\n   * Options used when tracing is enabled.\n   */\n  tracingOptions?: OperationTracingOptions;\n  /**\n   * Options to override serialization/de-serialization behavior.\n   */\n  serializerOptions?: SerializerOptions;\n\n  /**\n   * A function to be called each time a response is received from the server\n   * while performing the requested operation.\n   * May be called multiple times.\n   */\n  onResponse?: RawResponseCallback;\n}\n\n/**\n * Options used when creating and sending HTTP requests for this operation.\n */\nexport interface OperationRequestOptions {\n  /**\n   * User defined custom request headers that\n   * will be applied before the request is sent.\n   */\n  customHeaders?: { [key: string]: string };\n\n  /**\n   * The number of milliseconds a request can take before automatically being terminated.\n   */\n  timeout?: number;\n\n  /**\n   * Callback which fires upon upload progress.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Callback which fires upon download progress.\n   */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Whether or not the HttpOperationResponse should be deserialized. If this is undefined, then the\n   * HttpOperationResponse should be deserialized.\n   */\n  shouldDeserialize?: boolean | ((response: PipelineResponse) => boolean);\n\n  /**\n   * Set to true if the request is sent over HTTP instead of HTTPS\n   */\n  allowInsecureConnection?: boolean;\n}\n\n/**\n * A collection of properties that apply to a single invocation of an operation.\n */\nexport interface OperationArguments {\n  /**\n   * The parameters that were passed to the operation method.\n   */\n  [parameterName: string]: unknown;\n\n  /**\n   * The optional arguments that are provided to an operation.\n   */\n  options?: OperationOptions;\n}\n\n/**\n * The format that will be used to join an array of values together for a query parameter value.\n */\nexport type QueryCollectionFormat = \"CSV\" | \"SSV\" | \"TSV\" | \"Pipes\" | \"Multi\";\n\n/**\n * Encodes how to reach a particular property on an object.\n */\nexport type ParameterPath = string | string[] | { [propertyName: string]: ParameterPath };\n\n/**\n * A common interface that all Operation parameter's extend.\n */\nexport interface OperationParameter {\n  /**\n   * The path to this parameter's value in OperationArguments or the object that contains paths for\n   * each property's value in OperationArguments.\n   */\n  parameterPath: ParameterPath;\n\n  /**\n   * The mapper that defines how to validate and serialize this parameter's value.\n   */\n  mapper: Mapper;\n}\n\n/**\n * A parameter for an operation that will be substituted into the operation's request URL.\n */\nexport interface OperationURLParameter extends OperationParameter {\n  /**\n   * Whether or not to skip encoding the URL parameter's value before adding it to the URL.\n   */\n  skipEncoding?: boolean;\n}\n\n/**\n * A parameter for an operation that will be added as a query parameter to the operation's HTTP\n * request.\n */\nexport interface OperationQueryParameter extends OperationParameter {\n  /**\n   * Whether or not to skip encoding the query parameter's value before adding it to the URL.\n   */\n  skipEncoding?: boolean;\n\n  /**\n   * If this query parameter's value is a collection, what type of format should the value be\n   * converted to.\n   */\n  collectionFormat?: QueryCollectionFormat;\n}\n\n/**\n * An OperationResponse that can be returned from an operation request for a single status code.\n */\nexport interface OperationResponseMap {\n  /**\n   * The mapper that will be used to deserialize the response headers.\n   */\n  headersMapper?: Mapper;\n\n  /**\n   * The mapper that will be used to deserialize the response body.\n   */\n  bodyMapper?: Mapper;\n\n  /**\n   * Indicates if this is an error response\n   */\n  isError?: boolean;\n}\n\n/**\n * A specification that defines an operation.\n */\nexport interface OperationSpec {\n  /**\n   * The serializer to use in this operation.\n   */\n  readonly serializer: Serializer;\n\n  /**\n   * The HTTP method that should be used by requests for this operation.\n   */\n  readonly httpMethod: HttpMethods;\n\n  /**\n   * The URL that was provided in the service's specification. This will still have all of the URL\n   * template variables in it. If this is not provided when the OperationSpec is created, then it\n   * will be populated by a \"baseUri\" property on the ServiceClient.\n   */\n  readonly baseUrl?: string;\n\n  /**\n   * The fixed path for this operation's URL. This will still have all of the URL template variables\n   * in it.\n   */\n  readonly path?: string;\n\n  /**\n   * The content type of the request body. This value will be used as the \"Content-Type\" header if\n   * it is provided.\n   */\n  readonly contentType?: string;\n\n  /**\n   * The media type of the request body.\n   * This value can be used to aide in serialization if it is provided.\n   */\n  readonly mediaType?:\n    | \"json\"\n    | \"xml\"\n    | \"form\"\n    | \"binary\"\n    | \"multipart\"\n    | \"text\"\n    | \"unknown\"\n    | string;\n  /**\n   * The parameter that will be used to construct the HTTP request's body.\n   */\n  readonly requestBody?: OperationParameter;\n\n  /**\n   * Whether or not this operation uses XML request and response bodies.\n   */\n  readonly isXML?: boolean;\n\n  /**\n   * The parameters to the operation method that will be substituted into the constructed URL.\n   */\n  readonly urlParameters?: ReadonlyArray<OperationURLParameter>;\n\n  /**\n   * The parameters to the operation method that will be added to the constructed URL's query.\n   */\n  readonly queryParameters?: ReadonlyArray<OperationQueryParameter>;\n\n  /**\n   * The parameters to the operation method that will be converted to headers on the operation's\n   * HTTP request.\n   */\n  readonly headerParameters?: ReadonlyArray<OperationParameter>;\n\n  /**\n   * The parameters to the operation method that will be used to create a formdata body for the\n   * operation's HTTP request.\n   */\n  readonly formDataParameters?: ReadonlyArray<OperationParameter>;\n\n  /**\n   * The different types of responses that this operation can return based on what status code is\n   * returned.\n   */\n  readonly responses: { [responseCode: string]: OperationResponseMap };\n}\n\n/**\n * Wrapper object for http request and response. Deserialized object is stored in\n * the `parsedBody` property when the response body is received in JSON or XML.\n */\nexport interface FullOperationResponse extends PipelineResponse {\n  /**\n   * The parsed HTTP response headers.\n   */\n  parsedHeaders?: { [key: string]: unknown };\n\n  /**\n   * The response body as parsed JSON or XML.\n   */\n  parsedBody?: any;\n\n  /**\n   * The request that generated the response.\n   */\n  request: OperationRequest;\n}\n\n/**\n * A function to be called each time a response is received from the server\n * while performing the requested operation.\n * May be called multiple times.\n */\nexport type RawResponseCallback = (\n  rawResponse: FullOperationResponse,\n  flatResponse: unknown,\n  error?: unknown\n) => void;\n\n/**\n * Used to map raw response objects to final shapes.\n * Helps packing and unpacking Dates and other encoded types that are not intrinsic to JSON.\n * Also allows pulling values from headers, as well as inserting default values and constants.\n */\nexport interface Serializer {\n  /**\n   * The provided model mapper.\n   */\n  readonly modelMappers: { [key: string]: any };\n  /**\n   * Whether the contents are XML or not.\n   */\n  readonly isXML: boolean;\n\n  /**\n   * Validates constraints, if any. This function will throw if the provided value does not respect those constraints.\n   * @param mapper - The definition of data models.\n   * @param value - The value.\n   * @param objectName - Name of the object. Used in the error messages.\n   * @deprecated Removing the constraints validation on client side.\n   */\n  validateConstraints(mapper: Mapper, value: any, objectName: string): void;\n\n  /**\n   * Serialize the given object based on its metadata defined in the mapper.\n   *\n   * @param mapper - The mapper which defines the metadata of the serializable object.\n   * @param object - A valid Javascript object to be serialized.\n   * @param objectName - Name of the serialized object.\n   * @param options - additional options to deserialization.\n   * @returns A valid serialized Javascript object.\n   */\n  serialize(mapper: Mapper, object: any, objectName?: string, options?: SerializerOptions): any;\n\n  /**\n   * Deserialize the given object based on its metadata defined in the mapper.\n   *\n   * @param mapper - The mapper which defines the metadata of the serializable object.\n   * @param responseBody - A valid Javascript entity to be deserialized.\n   * @param objectName - Name of the deserialized object.\n   * @param options - Controls behavior of XML parser and builder.\n   * @returns A valid deserialized Javascript object.\n   */\n  deserialize(\n    mapper: Mapper,\n    responseBody: any,\n    objectName: string,\n    options?: SerializerOptions\n  ): any;\n}\n\n/**\n * Description of various value constraints such as integer ranges and string regex.\n */\nexport interface MapperConstraints {\n  /**\n   * The value should be less than or equal to the `InclusiveMaximum` value.\n   */\n  InclusiveMaximum?: number;\n  /**\n   * The value should be less than the `ExclusiveMaximum` value.\n   */\n  ExclusiveMaximum?: number;\n  /**\n   * The value should be greater than or equal to the `InclusiveMinimum` value.\n   */\n  InclusiveMinimum?: number;\n  /**\n   * The value should be greater than the `InclusiveMinimum` value.\n   */\n  ExclusiveMinimum?: number;\n  /**\n   * The length should be smaller than the `MaxLength`.\n   */\n  MaxLength?: number;\n  /**\n   * The length should be bigger than the `MinLength`.\n   */\n  MinLength?: number;\n  /**\n   * The value must match the pattern.\n   */\n  Pattern?: RegExp;\n  /**\n   * The value must contain fewer items than the MaxItems value.\n   */\n  MaxItems?: number;\n  /**\n   * The value must contain more items than the `MinItems` value.\n   */\n  MinItems?: number;\n  /**\n   * The value must contain only unique items.\n   */\n  UniqueItems?: true;\n  /**\n   * The value should be exactly divisible by the `MultipleOf` value.\n   */\n  MultipleOf?: number;\n}\n\n/**\n * Type of the mapper. Includes known mappers.\n */\nexport type MapperType =\n  | SimpleMapperType\n  | CompositeMapperType\n  | SequenceMapperType\n  | DictionaryMapperType\n  | EnumMapperType;\n\n/**\n * The type of a simple mapper.\n */\nexport interface SimpleMapperType {\n  /**\n   * Name of the type of the property.\n   */\n  name:\n    | \"Base64Url\"\n    | \"Boolean\"\n    | \"ByteArray\"\n    | \"Date\"\n    | \"DateTime\"\n    | \"DateTimeRfc1123\"\n    | \"Object\"\n    | \"Stream\"\n    | \"String\"\n    | \"TimeSpan\"\n    | \"UnixTime\"\n    | \"Uuid\"\n    | \"Number\"\n    | \"any\";\n}\n\n/**\n * Helps build a mapper that describes how to map a set of properties of an object based on other mappers.\n *\n * Only one of the following properties should be present: `className`, `modelProperties` and `additionalProperties`.\n */\nexport interface CompositeMapperType {\n  /**\n   * Name of the composite mapper type.\n   */\n  name: \"Composite\";\n\n  /**\n   * Use `className` to reference another type definition.\n   */\n  className?: string;\n\n  /**\n   * Use `modelProperties` when the reference to the other type has been resolved.\n   */\n  modelProperties?: { [propertyName: string]: Mapper };\n\n  /**\n   * Used when a model has `additionalProperties: true`. Allows the generic processing of unnamed model properties on the response object.\n   */\n  additionalProperties?: Mapper;\n\n  /**\n   * The name of the top-most parent scheme, the one that has no parents.\n   */\n  uberParent?: string;\n\n  /**\n   * A polymorphic discriminator.\n   */\n  polymorphicDiscriminator?: PolymorphicDiscriminator;\n}\n\n/**\n * Helps build a mapper that describes how to parse a sequence of mapped values.\n */\nexport interface SequenceMapperType {\n  /**\n   * Name of the sequence type mapper.\n   */\n  name: \"Sequence\";\n  /**\n   * The mapper to use to map each one of the properties of the sequence.\n   */\n  element: Mapper;\n}\n\n/**\n * Helps build a mapper that describes how to parse a dictionary of mapped values.\n */\nexport interface DictionaryMapperType {\n  /**\n   * Name of the sequence type mapper.\n   */\n  name: \"Dictionary\";\n  /**\n   * The mapper to use to map the value of each property in the dictionary.\n   */\n  value: Mapper;\n}\n\n/**\n * Helps build a mapper that describes how to parse an enum value.\n */\nexport interface EnumMapperType {\n  /**\n   * Name of the enum type mapper.\n   */\n  name: \"Enum\";\n  /**\n   * Values allowed by this mapper.\n   */\n  allowedValues: any[];\n}\n\n/**\n * The base definition of a mapper. Can be used for XML and plain JavaScript objects.\n */\nexport interface BaseMapper {\n  /**\n   * Name for the xml element\n   */\n  xmlName?: string;\n  /**\n   * Xml element namespace\n   */\n  xmlNamespace?: string;\n  /**\n   * Xml element namespace prefix\n   */\n  xmlNamespacePrefix?: string;\n  /**\n   * Determines if the current property should be serialized as an attribute of the parent xml element\n   */\n  xmlIsAttribute?: boolean;\n  /**\n   * Determines if the current property should be serialized as the inner content of the xml element\n   */\n  xmlIsMsText?: boolean;\n  /**\n   * Name for the xml elements when serializing an array\n   */\n  xmlElementName?: string;\n  /**\n   * Whether or not the current property should have a wrapping XML element\n   */\n  xmlIsWrapped?: boolean;\n  /**\n   * Whether or not the current property is readonly\n   */\n  readOnly?: boolean;\n  /**\n   * Whether or not the current property is a constant\n   */\n  isConstant?: boolean;\n  /**\n   * Whether or not the current property is required\n   */\n  required?: boolean;\n  /**\n   * Whether or not the current property allows mull as a value\n   */\n  nullable?: boolean;\n  /**\n   * The name to use when serializing\n   */\n  serializedName?: string;\n  /**\n   * Type of the mapper\n   */\n  type: MapperType;\n  /**\n   * Default value when one is not explicitly provided\n   */\n  defaultValue?: any;\n  /**\n   * Constraints to test the current value against\n   */\n  constraints?: MapperConstraints;\n}\n\n/**\n * Mappers are definitions of the data models used in the library.\n * These data models are part of the Operation or Client definitions in the responses or parameters.\n */\nexport type Mapper = BaseMapper | CompositeMapper | SequenceMapper | DictionaryMapper | EnumMapper;\n\n/**\n * Used to disambiguate discriminated type unions.\n * For example, if response can have many shapes but also includes a 'kind' field (or similar),\n * that field can be used to determine how to deserialize the response to the correct type.\n */\nexport interface PolymorphicDiscriminator {\n  /**\n   * Name of the discriminant property in the original JSON payload, e.g. `@odata.kind`.\n   */\n  serializedName: string;\n  /**\n   * Name to use on the resulting object instead of the original property name.\n   * Useful since the JSON property could be difficult to work with.\n   * For example: For a field received as `@odata.kind`, the final object could instead include a property simply named `kind`.\n   */\n  clientName: string;\n  /**\n   * It may contain any other property.\n   */\n  [key: string]: string;\n}\n\n/**\n * A mapper composed of other mappers.\n */\nexport interface CompositeMapper extends BaseMapper {\n  /**\n   * The type descriptor of the `CompositeMapper`.\n   */\n  type: CompositeMapperType;\n}\n\n/**\n * A mapper describing arrays.\n */\nexport interface SequenceMapper extends BaseMapper {\n  /**\n   * The type descriptor of the `SequenceMapper`.\n   */\n  type: SequenceMapperType;\n}\n\n/**\n * A mapper describing plain JavaScript objects used as key/value pairs.\n */\nexport interface DictionaryMapper extends BaseMapper {\n  /**\n   * The type descriptor of the `DictionaryMapper`.\n   */\n  type: DictionaryMapperType;\n  /**\n   * Optionally, a prefix to add to the header collection.\n   */\n  headerCollectionPrefix?: string;\n}\n\n/**\n * A mapper describing an enum value.\n */\nexport interface EnumMapper extends BaseMapper {\n  /**\n   * The type descriptor of the `EnumMapper`.\n   */\n  type: EnumMapperType;\n}\n\nexport interface UrlParameterValue {\n  value: string;\n  skipUrlEncoding: boolean;\n}\n\n/**\n * Configuration for creating a new Tracing Span\n */\nexport interface SpanConfig {\n  /**\n   * Package name prefix\n   */\n  packagePrefix: string;\n  /**\n   * Service namespace\n   */\n  namespace: string;\n}\n\n/**\n * Used to configure additional policies added to the pipeline at construction.\n */\nexport interface AdditionalPolicyConfig {\n  /**\n   * A policy to be added.\n   */\n  policy: PipelinePolicy;\n  /**\n   * Determines if this policy be applied before or after retry logic.\n   * Only use `perRetry` if you need to modify the request again\n   * each time the operation is retried due to retryable service\n   * issues.\n   */\n  position: \"perCall\" | \"perRetry\";\n}\n\n/**\n * The common set of options that high level clients are expected to expose.\n */\nexport interface CommonClientOptions extends PipelineOptions {\n  /**\n   * The HttpClient that will be used to send HTTP requests.\n   */\n  httpClient?: HttpClient;\n  /**\n   * Set to true if the request is sent over HTTP instead of HTTPS\n   */\n  allowInsecureConnection?: boolean;\n  /**\n   * Additional policies to include in the HTTP pipeline.\n   */\n  additionalPolicies?: AdditionalPolicyConfig[];\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { CompositeMapper, FullOperationResponse, OperationResponseMap } from \"./interfaces\";\n\n/**\n * The union of all possible types for a primitive response body.\n * @internal\n */\nexport type BodyPrimitive = number | string | boolean | Date | Uint8Array | undefined | null;\n\n/**\n * A type guard for a primitive response body.\n * @param value - Value to test\n *\n * @internal\n */\nexport function isPrimitiveBody(value: unknown, mapperTypeName?: string): value is BodyPrimitive {\n  return (\n    mapperTypeName !== \"Composite\" &&\n    mapperTypeName !== \"Dictionary\" &&\n    (typeof value === \"string\" ||\n      typeof value === \"number\" ||\n      typeof value === \"boolean\" ||\n      mapperTypeName?.match(/^(Date|DateTime|DateTimeRfc1123|UnixTime|ByteArray|Base64Url)$/i) !==\n        null ||\n      value === undefined ||\n      value === null)\n  );\n}\n\nconst validateISODuration =\n  /^(-|\\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;\n\n/**\n * Returns true if the given string is in ISO 8601 format.\n * @param value - The value to be validated for ISO 8601 duration format.\n * @internal\n */\nexport function isDuration(value: string): boolean {\n  return validateISODuration.test(value);\n}\n\nconst validUuidRegex =\n  /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/i;\n\n/**\n * Returns true if the provided uuid is valid.\n *\n * @param uuid - The uuid that needs to be validated.\n *\n * @internal\n */\nexport function isValidUuid(uuid: string): boolean {\n  return validUuidRegex.test(uuid);\n}\n\n/**\n * Representation of parsed response headers and body coupled with information\n * about how to map them:\n * - whether the response body should be wrapped (typically if its type is primitive).\n * - whether the response is nullable so it can be null if the combination of\n *   the headers and the body is empty.\n */\ninterface ResponseObjectWithMetadata {\n  /** whether the mapper allows nullable body */\n  hasNullableType: boolean;\n  /** whether the response's body should be wrapped */\n  shouldWrapBody: boolean;\n  /** parsed headers of the response */\n  headers:\n    | {\n        [key: string]: unknown;\n      }\n    | undefined;\n  /** parsed body of the response */\n  body: any;\n}\n\n/**\n * Maps the response as follows:\n * - wraps the response body if needed (typically if its type is primitive).\n * - returns null if the combination of the headers and the body is empty.\n * - otherwise, returns the combination of the headers and the body.\n *\n * @param responseObject - a representation of the parsed response\n * @returns the response that will be returned to the user which can be null and/or wrapped\n *\n * @internal\n */\nfunction handleNullableResponseAndWrappableBody(\n  responseObject: ResponseObjectWithMetadata\n): unknown | null {\n  const combinedHeadersAndBody = {\n    ...responseObject.headers,\n    ...responseObject.body,\n  };\n  if (\n    responseObject.hasNullableType &&\n    Object.getOwnPropertyNames(combinedHeadersAndBody).length === 0\n  ) {\n    return responseObject.shouldWrapBody ? { body: null } : null;\n  } else {\n    return responseObject.shouldWrapBody\n      ? {\n          ...responseObject.headers,\n          body: responseObject.body,\n        }\n      : combinedHeadersAndBody;\n  }\n}\n\n/**\n * Take a `FullOperationResponse` and turn it into a flat\n * response object to hand back to the consumer.\n * @param fullResponse - The processed response from the operation request\n * @param responseSpec - The response map from the OperationSpec\n *\n * @internal\n */\nexport function flattenResponse(\n  fullResponse: FullOperationResponse,\n  responseSpec: OperationResponseMap | undefined\n): unknown {\n  const parsedHeaders = fullResponse.parsedHeaders;\n\n  // head methods never have a body, but we return a boolean set to body property\n  // to indicate presence/absence of the resource\n  if (fullResponse.request.method === \"HEAD\") {\n    return {\n      ...parsedHeaders,\n      body: fullResponse.parsedBody,\n    };\n  }\n  const bodyMapper = responseSpec && responseSpec.bodyMapper;\n  const isNullable = Boolean(bodyMapper?.nullable);\n  const expectedBodyTypeName = bodyMapper?.type.name;\n\n  /** If the body is asked for, we look at the expected body type to handle it */\n  if (expectedBodyTypeName === \"Stream\") {\n    return {\n      ...parsedHeaders,\n      blobBody: fullResponse.blobBody,\n      readableStreamBody: fullResponse.readableStreamBody,\n    };\n  }\n\n  const modelProperties =\n    (expectedBodyTypeName === \"Composite\" &&\n      (bodyMapper as CompositeMapper).type.modelProperties) ||\n    {};\n  const isPageableResponse = Object.keys(modelProperties).some(\n    (k) => modelProperties[k].serializedName === \"\"\n  );\n  if (expectedBodyTypeName === \"Sequence\" || isPageableResponse) {\n    const arrayResponse: { [key: string]: unknown } =\n      fullResponse.parsedBody ?? ([] as unknown as { [key: string]: unknown });\n\n    for (const key of Object.keys(modelProperties)) {\n      if (modelProperties[key].serializedName) {\n        arrayResponse[key] = fullResponse.parsedBody?.[key];\n      }\n    }\n\n    if (parsedHeaders) {\n      for (const key of Object.keys(parsedHeaders)) {\n        arrayResponse[key] = parsedHeaders[key];\n      }\n    }\n    return isNullable &&\n      !fullResponse.parsedBody &&\n      !parsedHeaders &&\n      Object.getOwnPropertyNames(modelProperties).length === 0\n      ? null\n      : arrayResponse;\n  }\n\n  return handleNullableResponseAndWrappableBody({\n    body: fullResponse.parsedBody,\n    headers: parsedHeaders,\n    hasNullableType: isNullable,\n    shouldWrapBody: isPrimitiveBody(fullResponse.parsedBody, expectedBodyTypeName),\n  });\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as base64 from \"./base64\";\nimport {\n  BaseMapper,\n  CompositeMapper,\n  DictionaryMapper,\n  <PERSON>umMapper,\n  Mapper,\n  MapperConstraints,\n  PolymorphicDiscriminator,\n  RequiredSerializerOptions,\n  SequenceMapper,\n  Serializer,\n  SerializerOptions,\n  XML_ATTRKEY,\n  XML_CHARKEY,\n} from \"./interfaces\";\nimport { isDuration, isValidUuid } from \"./utils\";\n\nclass SerializerImpl implements Serializer {\n  constructor(\n    public readonly modelMappers: { [key: string]: any } = {},\n    public readonly isXML: boolean = false\n  ) {}\n\n  /**\n   * @deprecated Removing the constraints validation on client side.\n   */\n  validateConstraints(mapper: Mapper, value: any, objectName: string): void {\n    const failValidation = (\n      constraintName: keyof MapperConstraints,\n      constraintValue: any\n    ): never => {\n      throw new Error(\n        `\"${objectName}\" with value \"${value}\" should satisfy the constraint \"${constraintName}\": ${constraintValue}.`\n      );\n    };\n    if (mapper.constraints && value !== undefined && value !== null) {\n      const {\n        ExclusiveMaximum,\n        ExclusiveMinimum,\n        InclusiveMaximum,\n        InclusiveMinimum,\n        MaxItems,\n        MaxLength,\n        MinItems,\n        MinLength,\n        MultipleOf,\n        Pattern,\n        UniqueItems,\n      } = mapper.constraints;\n      if (ExclusiveMaximum !== undefined && value >= ExclusiveMaximum) {\n        failValidation(\"ExclusiveMaximum\", ExclusiveMaximum);\n      }\n      if (ExclusiveMinimum !== undefined && value <= ExclusiveMinimum) {\n        failValidation(\"ExclusiveMinimum\", ExclusiveMinimum);\n      }\n      if (InclusiveMaximum !== undefined && value > InclusiveMaximum) {\n        failValidation(\"InclusiveMaximum\", InclusiveMaximum);\n      }\n      if (InclusiveMinimum !== undefined && value < InclusiveMinimum) {\n        failValidation(\"InclusiveMinimum\", InclusiveMinimum);\n      }\n      if (MaxItems !== undefined && value.length > MaxItems) {\n        failValidation(\"MaxItems\", MaxItems);\n      }\n      if (MaxLength !== undefined && value.length > MaxLength) {\n        failValidation(\"MaxLength\", MaxLength);\n      }\n      if (MinItems !== undefined && value.length < MinItems) {\n        failValidation(\"MinItems\", MinItems);\n      }\n      if (MinLength !== undefined && value.length < MinLength) {\n        failValidation(\"MinLength\", MinLength);\n      }\n      if (MultipleOf !== undefined && value % MultipleOf !== 0) {\n        failValidation(\"MultipleOf\", MultipleOf);\n      }\n      if (Pattern) {\n        const pattern: RegExp = typeof Pattern === \"string\" ? new RegExp(Pattern) : Pattern;\n        if (typeof value !== \"string\" || value.match(pattern) === null) {\n          failValidation(\"Pattern\", Pattern);\n        }\n      }\n      if (\n        UniqueItems &&\n        value.some((item: any, i: number, ar: Array<any>) => ar.indexOf(item) !== i)\n      ) {\n        failValidation(\"UniqueItems\", UniqueItems);\n      }\n    }\n  }\n\n  /**\n   * Serialize the given object based on its metadata defined in the mapper\n   *\n   * @param mapper - The mapper which defines the metadata of the serializable object\n   *\n   * @param object - A valid Javascript object to be serialized\n   *\n   * @param objectName - Name of the serialized object\n   *\n   * @param options - additional options to serialization\n   *\n   * @returns A valid serialized Javascript object\n   */\n  serialize(\n    mapper: Mapper,\n    object: any,\n    objectName?: string,\n    options: SerializerOptions = { xml: {} }\n  ): any {\n    const updatedOptions: RequiredSerializerOptions = {\n      xml: {\n        rootName: options.xml.rootName ?? \"\",\n        includeRoot: options.xml.includeRoot ?? false,\n        xmlCharKey: options.xml.xmlCharKey ?? XML_CHARKEY,\n      },\n    };\n    let payload: any = {};\n    const mapperType = mapper.type.name as string;\n    if (!objectName) {\n      objectName = mapper.serializedName!;\n    }\n    if (mapperType.match(/^Sequence$/i) !== null) {\n      payload = [];\n    }\n\n    if (mapper.isConstant) {\n      object = mapper.defaultValue;\n    }\n\n    // This table of allowed values should help explain\n    // the mapper.required and mapper.nullable properties.\n    // X means \"neither undefined or null are allowed\".\n    //           || required\n    //           || true      | false\n    //  nullable || ==========================\n    //      true || null      | undefined/null\n    //     false || X         | undefined\n    // undefined || X         | undefined/null\n\n    const { required, nullable } = mapper;\n\n    if (required && nullable && object === undefined) {\n      throw new Error(`${objectName} cannot be undefined.`);\n    }\n    if (required && !nullable && (object === undefined || object === null)) {\n      throw new Error(`${objectName} cannot be null or undefined.`);\n    }\n    if (!required && nullable === false && object === null) {\n      throw new Error(`${objectName} cannot be null.`);\n    }\n\n    if (object === undefined || object === null) {\n      payload = object;\n    } else {\n      if (mapperType.match(/^any$/i) !== null) {\n        payload = object;\n      } else if (mapperType.match(/^(Number|String|Boolean|Object|Stream|Uuid)$/i) !== null) {\n        payload = serializeBasicTypes(mapperType, objectName, object);\n      } else if (mapperType.match(/^Enum$/i) !== null) {\n        const enumMapper = mapper as EnumMapper;\n        payload = serializeEnumType(objectName, enumMapper.type.allowedValues, object);\n      } else if (\n        mapperType.match(/^(Date|DateTime|TimeSpan|DateTimeRfc1123|UnixTime)$/i) !== null\n      ) {\n        payload = serializeDateTypes(mapperType, object, objectName);\n      } else if (mapperType.match(/^ByteArray$/i) !== null) {\n        payload = serializeByteArrayType(objectName, object);\n      } else if (mapperType.match(/^Base64Url$/i) !== null) {\n        payload = serializeBase64UrlType(objectName, object);\n      } else if (mapperType.match(/^Sequence$/i) !== null) {\n        payload = serializeSequenceType(\n          this,\n          mapper as SequenceMapper,\n          object,\n          objectName,\n          Boolean(this.isXML),\n          updatedOptions\n        );\n      } else if (mapperType.match(/^Dictionary$/i) !== null) {\n        payload = serializeDictionaryType(\n          this,\n          mapper as DictionaryMapper,\n          object,\n          objectName,\n          Boolean(this.isXML),\n          updatedOptions\n        );\n      } else if (mapperType.match(/^Composite$/i) !== null) {\n        payload = serializeCompositeType(\n          this,\n          mapper as CompositeMapper,\n          object,\n          objectName,\n          Boolean(this.isXML),\n          updatedOptions\n        );\n      }\n    }\n    return payload;\n  }\n\n  /**\n   * Deserialize the given object based on its metadata defined in the mapper\n   *\n   * @param mapper - The mapper which defines the metadata of the serializable object\n   *\n   * @param responseBody - A valid Javascript entity to be deserialized\n   *\n   * @param objectName - Name of the deserialized object\n   *\n   * @param options - Controls behavior of XML parser and builder.\n   *\n   * @returns A valid deserialized Javascript object\n   */\n  deserialize(\n    mapper: Mapper,\n    responseBody: any,\n    objectName: string,\n    options: SerializerOptions = { xml: {} }\n  ): any {\n    const updatedOptions: RequiredSerializerOptions = {\n      xml: {\n        rootName: options.xml.rootName ?? \"\",\n        includeRoot: options.xml.includeRoot ?? false,\n        xmlCharKey: options.xml.xmlCharKey ?? XML_CHARKEY,\n      },\n      ignoreUnknownProperties: options.ignoreUnknownProperties ?? false,\n    };\n    if (responseBody === undefined || responseBody === null) {\n      if (this.isXML && mapper.type.name === \"Sequence\" && !mapper.xmlIsWrapped) {\n        // Edge case for empty XML non-wrapped lists. xml2js can't distinguish\n        // between the list being empty versus being missing,\n        // so let's do the more user-friendly thing and return an empty list.\n        responseBody = [];\n      }\n      // specifically check for undefined as default value can be a falsey value `0, \"\", false, null`\n      if (mapper.defaultValue !== undefined) {\n        responseBody = mapper.defaultValue;\n      }\n      return responseBody;\n    }\n\n    let payload: any;\n    const mapperType = mapper.type.name;\n    if (!objectName) {\n      objectName = mapper.serializedName!;\n    }\n\n    if (mapperType.match(/^Composite$/i) !== null) {\n      payload = deserializeCompositeType(\n        this,\n        mapper as CompositeMapper,\n        responseBody,\n        objectName,\n        updatedOptions\n      );\n    } else {\n      if (this.isXML) {\n        const xmlCharKey = updatedOptions.xml.xmlCharKey;\n        /**\n         * If the mapper specifies this as a non-composite type value but the responseBody contains\n         * both header (\"$\" i.e., XML_ATTRKEY) and body (\"#\" i.e., XML_CHARKEY) properties,\n         * then just reduce the responseBody value to the body (\"#\" i.e., XML_CHARKEY) property.\n         */\n        if (responseBody[XML_ATTRKEY] !== undefined && responseBody[xmlCharKey] !== undefined) {\n          responseBody = responseBody[xmlCharKey];\n        }\n      }\n\n      if (mapperType.match(/^Number$/i) !== null) {\n        payload = parseFloat(responseBody);\n        if (isNaN(payload)) {\n          payload = responseBody;\n        }\n      } else if (mapperType.match(/^Boolean$/i) !== null) {\n        if (responseBody === \"true\") {\n          payload = true;\n        } else if (responseBody === \"false\") {\n          payload = false;\n        } else {\n          payload = responseBody;\n        }\n      } else if (mapperType.match(/^(String|Enum|Object|Stream|Uuid|TimeSpan|any)$/i) !== null) {\n        payload = responseBody;\n      } else if (mapperType.match(/^(Date|DateTime|DateTimeRfc1123)$/i) !== null) {\n        payload = new Date(responseBody);\n      } else if (mapperType.match(/^UnixTime$/i) !== null) {\n        payload = unixTimeToDate(responseBody);\n      } else if (mapperType.match(/^ByteArray$/i) !== null) {\n        payload = base64.decodeString(responseBody);\n      } else if (mapperType.match(/^Base64Url$/i) !== null) {\n        payload = base64UrlToByteArray(responseBody);\n      } else if (mapperType.match(/^Sequence$/i) !== null) {\n        payload = deserializeSequenceType(\n          this,\n          mapper as SequenceMapper,\n          responseBody,\n          objectName,\n          updatedOptions\n        );\n      } else if (mapperType.match(/^Dictionary$/i) !== null) {\n        payload = deserializeDictionaryType(\n          this,\n          mapper as DictionaryMapper,\n          responseBody,\n          objectName,\n          updatedOptions\n        );\n      }\n    }\n\n    if (mapper.isConstant) {\n      payload = mapper.defaultValue;\n    }\n\n    return payload;\n  }\n}\n\n/**\n * Method that creates and returns a Serializer.\n * @param modelMappers - Known models to map\n * @param isXML - If XML should be supported\n */\nexport function createSerializer(\n  modelMappers: { [key: string]: any } = {},\n  isXML: boolean = false\n): Serializer {\n  return new SerializerImpl(modelMappers, isXML);\n}\n\nfunction trimEnd(str: string, ch: string): string {\n  let len = str.length;\n  while (len - 1 >= 0 && str[len - 1] === ch) {\n    --len;\n  }\n  return str.substr(0, len);\n}\n\nfunction bufferToBase64Url(buffer: Uint8Array): string | undefined {\n  if (!buffer) {\n    return undefined;\n  }\n  if (!(buffer instanceof Uint8Array)) {\n    throw new Error(`Please provide an input of type Uint8Array for converting to Base64Url.`);\n  }\n  // Uint8Array to Base64.\n  const str = base64.encodeByteArray(buffer);\n  // Base64 to Base64Url.\n  return trimEnd(str, \"=\").replace(/\\+/g, \"-\").replace(/\\//g, \"_\");\n}\n\nfunction base64UrlToByteArray(str: string): Uint8Array | undefined {\n  if (!str) {\n    return undefined;\n  }\n  if (str && typeof str.valueOf() !== \"string\") {\n    throw new Error(\"Please provide an input of type string for converting to Uint8Array\");\n  }\n  // Base64Url to Base64.\n  str = str.replace(/-/g, \"+\").replace(/_/g, \"/\");\n  // Base64 to Uint8Array.\n  return base64.decodeString(str);\n}\n\nfunction splitSerializeName(prop: string | undefined): string[] {\n  const classes: string[] = [];\n  let partialclass = \"\";\n  if (prop) {\n    const subwords = prop.split(\".\");\n\n    for (const item of subwords) {\n      if (item.charAt(item.length - 1) === \"\\\\\") {\n        partialclass += item.substr(0, item.length - 1) + \".\";\n      } else {\n        partialclass += item;\n        classes.push(partialclass);\n        partialclass = \"\";\n      }\n    }\n  }\n\n  return classes;\n}\n\nfunction dateToUnixTime(d: string | Date): number | undefined {\n  if (!d) {\n    return undefined;\n  }\n\n  if (typeof d.valueOf() === \"string\") {\n    d = new Date(d as string);\n  }\n  return Math.floor((d as Date).getTime() / 1000);\n}\n\nfunction unixTimeToDate(n: number): Date | undefined {\n  if (!n) {\n    return undefined;\n  }\n  return new Date(n * 1000);\n}\n\nfunction serializeBasicTypes(typeName: string, objectName: string, value: any): any {\n  if (value !== null && value !== undefined) {\n    if (typeName.match(/^Number$/i) !== null) {\n      if (typeof value !== \"number\") {\n        throw new Error(`${objectName} with value ${value} must be of type number.`);\n      }\n    } else if (typeName.match(/^String$/i) !== null) {\n      if (typeof value.valueOf() !== \"string\") {\n        throw new Error(`${objectName} with value \"${value}\" must be of type string.`);\n      }\n    } else if (typeName.match(/^Uuid$/i) !== null) {\n      if (!(typeof value.valueOf() === \"string\" && isValidUuid(value))) {\n        throw new Error(\n          `${objectName} with value \"${value}\" must be of type string and a valid uuid.`\n        );\n      }\n    } else if (typeName.match(/^Boolean$/i) !== null) {\n      if (typeof value !== \"boolean\") {\n        throw new Error(`${objectName} with value ${value} must be of type boolean.`);\n      }\n    } else if (typeName.match(/^Stream$/i) !== null) {\n      const objectType = typeof value;\n      if (\n        objectType !== \"string\" &&\n        typeof value.pipe !== \"function\" &&\n        !(value instanceof ArrayBuffer) &&\n        !ArrayBuffer.isView(value) &&\n        // File objects count as a type of Blob, so we want to use instanceof explicitly\n        !((typeof Blob === \"function\" || typeof Blob === \"object\") && value instanceof Blob) &&\n        objectType !== \"function\"\n      ) {\n        throw new Error(\n          `${objectName} must be a string, Blob, ArrayBuffer, ArrayBufferView, NodeJS.ReadableStream, or () => NodeJS.ReadableStream.`\n        );\n      }\n    }\n  }\n  return value;\n}\n\nfunction serializeEnumType(objectName: string, allowedValues: Array<any>, value: any): any {\n  if (!allowedValues) {\n    throw new Error(\n      `Please provide a set of allowedValues to validate ${objectName} as an Enum Type.`\n    );\n  }\n  const isPresent = allowedValues.some((item) => {\n    if (typeof item.valueOf() === \"string\") {\n      return item.toLowerCase() === value.toLowerCase();\n    }\n    return item === value;\n  });\n  if (!isPresent) {\n    throw new Error(\n      `${value} is not a valid value for ${objectName}. The valid values are: ${JSON.stringify(\n        allowedValues\n      )}.`\n    );\n  }\n  return value;\n}\n\nfunction serializeByteArrayType(objectName: string, value: any): any {\n  if (value !== undefined && value !== null) {\n    if (!(value instanceof Uint8Array)) {\n      throw new Error(`${objectName} must be of type Uint8Array.`);\n    }\n    value = base64.encodeByteArray(value);\n  }\n  return value;\n}\n\nfunction serializeBase64UrlType(objectName: string, value: any): any {\n  if (value !== undefined && value !== null) {\n    if (!(value instanceof Uint8Array)) {\n      throw new Error(`${objectName} must be of type Uint8Array.`);\n    }\n    value = bufferToBase64Url(value);\n  }\n  return value;\n}\n\nfunction serializeDateTypes(typeName: string, value: any, objectName: string): any {\n  if (value !== undefined && value !== null) {\n    if (typeName.match(/^Date$/i) !== null) {\n      if (\n        !(\n          value instanceof Date ||\n          (typeof value.valueOf() === \"string\" && !isNaN(Date.parse(value)))\n        )\n      ) {\n        throw new Error(`${objectName} must be an instanceof Date or a string in ISO8601 format.`);\n      }\n      value =\n        value instanceof Date\n          ? value.toISOString().substring(0, 10)\n          : new Date(value).toISOString().substring(0, 10);\n    } else if (typeName.match(/^DateTime$/i) !== null) {\n      if (\n        !(\n          value instanceof Date ||\n          (typeof value.valueOf() === \"string\" && !isNaN(Date.parse(value)))\n        )\n      ) {\n        throw new Error(`${objectName} must be an instanceof Date or a string in ISO8601 format.`);\n      }\n      value = value instanceof Date ? value.toISOString() : new Date(value).toISOString();\n    } else if (typeName.match(/^DateTimeRfc1123$/i) !== null) {\n      if (\n        !(\n          value instanceof Date ||\n          (typeof value.valueOf() === \"string\" && !isNaN(Date.parse(value)))\n        )\n      ) {\n        throw new Error(`${objectName} must be an instanceof Date or a string in RFC-1123 format.`);\n      }\n      value = value instanceof Date ? value.toUTCString() : new Date(value).toUTCString();\n    } else if (typeName.match(/^UnixTime$/i) !== null) {\n      if (\n        !(\n          value instanceof Date ||\n          (typeof value.valueOf() === \"string\" && !isNaN(Date.parse(value)))\n        )\n      ) {\n        throw new Error(\n          `${objectName} must be an instanceof Date or a string in RFC-1123/ISO8601 format ` +\n            `for it to be serialized in UnixTime/Epoch format.`\n        );\n      }\n      value = dateToUnixTime(value);\n    } else if (typeName.match(/^TimeSpan$/i) !== null) {\n      if (!isDuration(value)) {\n        throw new Error(\n          `${objectName} must be a string in ISO 8601 format. Instead was \"${value}\".`\n        );\n      }\n    }\n  }\n  return value;\n}\n\nfunction serializeSequenceType(\n  serializer: Serializer,\n  mapper: SequenceMapper,\n  object: any,\n  objectName: string,\n  isXml: boolean,\n  options: RequiredSerializerOptions\n): any {\n  if (!Array.isArray(object)) {\n    throw new Error(`${objectName} must be of type Array.`);\n  }\n  let elementType = mapper.type.element;\n  if (!elementType || typeof elementType !== \"object\") {\n    throw new Error(\n      `element\" metadata for an Array must be defined in the ` +\n        `mapper and it must of type \"object\" in ${objectName}.`\n    );\n  }\n  // Quirk: Composite mappers referenced by `element` might\n  // not have *all* properties declared (like uberParent),\n  // so let's try to look up the full definition by name.\n  if (elementType.type.name === \"Composite\" && elementType.type.className) {\n    elementType = serializer.modelMappers[elementType.type.className] ?? elementType;\n  }\n  const tempArray = [];\n  for (let i = 0; i < object.length; i++) {\n    const serializedValue = serializer.serialize(elementType, object[i], objectName, options);\n    if (isXml && elementType.xmlNamespace) {\n      const xmlnsKey = elementType.xmlNamespacePrefix\n        ? `xmlns:${elementType.xmlNamespacePrefix}`\n        : \"xmlns\";\n      if (elementType.type.name === \"Composite\") {\n        tempArray[i] = { ...serializedValue };\n        tempArray[i][XML_ATTRKEY] = { [xmlnsKey]: elementType.xmlNamespace };\n      } else {\n        tempArray[i] = {};\n        tempArray[i][options.xml.xmlCharKey] = serializedValue;\n        tempArray[i][XML_ATTRKEY] = { [xmlnsKey]: elementType.xmlNamespace };\n      }\n    } else {\n      tempArray[i] = serializedValue;\n    }\n  }\n  return tempArray;\n}\n\nfunction serializeDictionaryType(\n  serializer: Serializer,\n  mapper: DictionaryMapper,\n  object: any,\n  objectName: string,\n  isXml: boolean,\n  options: RequiredSerializerOptions\n): any {\n  if (typeof object !== \"object\") {\n    throw new Error(`${objectName} must be of type object.`);\n  }\n  const valueType = mapper.type.value;\n  if (!valueType || typeof valueType !== \"object\") {\n    throw new Error(\n      `\"value\" metadata for a Dictionary must be defined in the ` +\n        `mapper and it must of type \"object\" in ${objectName}.`\n    );\n  }\n  const tempDictionary: { [key: string]: any } = {};\n  for (const key of Object.keys(object)) {\n    const serializedValue = serializer.serialize(valueType, object[key], objectName, options);\n    // If the element needs an XML namespace we need to add it within the $ property\n    tempDictionary[key] = getXmlObjectValue(valueType, serializedValue, isXml, options);\n  }\n\n  // Add the namespace to the root element if needed\n  if (isXml && mapper.xmlNamespace) {\n    const xmlnsKey = mapper.xmlNamespacePrefix ? `xmlns:${mapper.xmlNamespacePrefix}` : \"xmlns\";\n    const result = tempDictionary;\n    result[XML_ATTRKEY] = { [xmlnsKey]: mapper.xmlNamespace };\n    return result;\n  }\n\n  return tempDictionary;\n}\n\n/**\n * Resolves the additionalProperties property from a referenced mapper\n * @param serializer - the serializer containing the entire set of mappers\n * @param mapper - the composite mapper to resolve\n * @param objectName - name of the object being serialized\n */\nfunction resolveAdditionalProperties(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  objectName: string\n): SequenceMapper | BaseMapper | CompositeMapper | DictionaryMapper | EnumMapper | undefined {\n  const additionalProperties = mapper.type.additionalProperties;\n\n  if (!additionalProperties && mapper.type.className) {\n    const modelMapper = resolveReferencedMapper(serializer, mapper, objectName);\n    return modelMapper?.type.additionalProperties;\n  }\n\n  return additionalProperties;\n}\n\n/**\n * Finds the mapper referenced by className\n * @param serializer - the serializer containing the entire set of mappers\n * @param mapper - the composite mapper to resolve\n * @param objectName - name of the object being serialized\n */\nfunction resolveReferencedMapper(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  objectName: string\n): CompositeMapper | undefined {\n  const className = mapper.type.className;\n  if (!className) {\n    throw new Error(\n      `Class name for model \"${objectName}\" is not provided in the mapper \"${JSON.stringify(\n        mapper,\n        undefined,\n        2\n      )}\".`\n    );\n  }\n\n  return serializer.modelMappers[className];\n}\n\n/**\n * Resolves a composite mapper's modelProperties.\n * @param serializer - the serializer containing the entire set of mappers\n * @param mapper - the composite mapper to resolve\n */\nfunction resolveModelProperties(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  objectName: string\n): { [propertyName: string]: Mapper } {\n  let modelProps = mapper.type.modelProperties;\n  if (!modelProps) {\n    const modelMapper = resolveReferencedMapper(serializer, mapper, objectName);\n    if (!modelMapper) {\n      throw new Error(`mapper() cannot be null or undefined for model \"${mapper.type.className}\".`);\n    }\n    modelProps = modelMapper?.type.modelProperties;\n    if (!modelProps) {\n      throw new Error(\n        `modelProperties cannot be null or undefined in the ` +\n          `mapper \"${JSON.stringify(modelMapper)}\" of type \"${\n            mapper.type.className\n          }\" for object \"${objectName}\".`\n      );\n    }\n  }\n\n  return modelProps;\n}\n\nfunction serializeCompositeType(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  object: any,\n  objectName: string,\n  isXml: boolean,\n  options: RequiredSerializerOptions\n): any {\n  if (getPolymorphicDiscriminatorRecursively(serializer, mapper)) {\n    mapper = getPolymorphicMapper(serializer, mapper, object, \"clientName\");\n  }\n\n  if (object !== undefined && object !== null) {\n    const payload: any = {};\n    const modelProps = resolveModelProperties(serializer, mapper, objectName);\n    for (const key of Object.keys(modelProps)) {\n      const propertyMapper = modelProps[key];\n      if (propertyMapper.readOnly) {\n        continue;\n      }\n\n      let propName: string | undefined;\n      let parentObject: any = payload;\n      if (serializer.isXML) {\n        if (propertyMapper.xmlIsWrapped) {\n          propName = propertyMapper.xmlName;\n        } else {\n          propName = propertyMapper.xmlElementName || propertyMapper.xmlName;\n        }\n      } else {\n        const paths = splitSerializeName(propertyMapper.serializedName!);\n        propName = paths.pop();\n\n        for (const pathName of paths) {\n          const childObject = parentObject[pathName];\n          if (\n            (childObject === undefined || childObject === null) &&\n            ((object[key] !== undefined && object[key] !== null) ||\n              propertyMapper.defaultValue !== undefined)\n          ) {\n            parentObject[pathName] = {};\n          }\n          parentObject = parentObject[pathName];\n        }\n      }\n\n      if (parentObject !== undefined && parentObject !== null) {\n        if (isXml && mapper.xmlNamespace) {\n          const xmlnsKey = mapper.xmlNamespacePrefix\n            ? `xmlns:${mapper.xmlNamespacePrefix}`\n            : \"xmlns\";\n          parentObject[XML_ATTRKEY] = {\n            ...parentObject[XML_ATTRKEY],\n            [xmlnsKey]: mapper.xmlNamespace,\n          };\n        }\n        const propertyObjectName =\n          propertyMapper.serializedName !== \"\"\n            ? objectName + \".\" + propertyMapper.serializedName\n            : objectName;\n\n        let toSerialize = object[key];\n        const polymorphicDiscriminator = getPolymorphicDiscriminatorRecursively(serializer, mapper);\n        if (\n          polymorphicDiscriminator &&\n          polymorphicDiscriminator.clientName === key &&\n          (toSerialize === undefined || toSerialize === null)\n        ) {\n          toSerialize = mapper.serializedName;\n        }\n\n        const serializedValue = serializer.serialize(\n          propertyMapper,\n          toSerialize,\n          propertyObjectName,\n          options\n        );\n        if (serializedValue !== undefined && propName !== undefined && propName !== null) {\n          const value = getXmlObjectValue(propertyMapper, serializedValue, isXml, options);\n          if (isXml && propertyMapper.xmlIsAttribute) {\n            // XML_ATTRKEY, i.e., $ is the key attributes are kept under in xml2js.\n            // This keeps things simple while preventing name collision\n            // with names in user documents.\n            parentObject[XML_ATTRKEY] = parentObject[XML_ATTRKEY] || {};\n            parentObject[XML_ATTRKEY][propName] = serializedValue;\n          } else if (isXml && propertyMapper.xmlIsWrapped) {\n            parentObject[propName] = { [propertyMapper.xmlElementName!]: value };\n          } else {\n            parentObject[propName] = value;\n          }\n        }\n      }\n    }\n\n    const additionalPropertiesMapper = resolveAdditionalProperties(serializer, mapper, objectName);\n    if (additionalPropertiesMapper) {\n      const propNames = Object.keys(modelProps);\n      for (const clientPropName in object) {\n        const isAdditionalProperty = propNames.every((pn) => pn !== clientPropName);\n        if (isAdditionalProperty) {\n          payload[clientPropName] = serializer.serialize(\n            additionalPropertiesMapper,\n            object[clientPropName],\n            objectName + '[\"' + clientPropName + '\"]',\n            options\n          );\n        }\n      }\n    }\n\n    return payload;\n  }\n  return object;\n}\n\nfunction getXmlObjectValue(\n  propertyMapper: Mapper,\n  serializedValue: any,\n  isXml: boolean,\n  options: RequiredSerializerOptions\n): any {\n  if (!isXml || !propertyMapper.xmlNamespace) {\n    return serializedValue;\n  }\n\n  const xmlnsKey = propertyMapper.xmlNamespacePrefix\n    ? `xmlns:${propertyMapper.xmlNamespacePrefix}`\n    : \"xmlns\";\n  const xmlNamespace = { [xmlnsKey]: propertyMapper.xmlNamespace };\n\n  if ([\"Composite\"].includes(propertyMapper.type.name)) {\n    if (serializedValue[XML_ATTRKEY]) {\n      return serializedValue;\n    } else {\n      const result: any = { ...serializedValue };\n      result[XML_ATTRKEY] = xmlNamespace;\n      return result;\n    }\n  }\n  const result: any = {};\n  result[options.xml.xmlCharKey] = serializedValue;\n  result[XML_ATTRKEY] = xmlNamespace;\n  return result;\n}\n\nfunction isSpecialXmlProperty(propertyName: string, options: RequiredSerializerOptions): boolean {\n  return [XML_ATTRKEY, options.xml.xmlCharKey].includes(propertyName);\n}\n\nfunction deserializeCompositeType(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  responseBody: any,\n  objectName: string,\n  options: RequiredSerializerOptions\n): any {\n  const xmlCharKey = options.xml.xmlCharKey ?? XML_CHARKEY;\n  if (getPolymorphicDiscriminatorRecursively(serializer, mapper)) {\n    mapper = getPolymorphicMapper(serializer, mapper, responseBody, \"serializedName\");\n  }\n\n  const modelProps = resolveModelProperties(serializer, mapper, objectName);\n  let instance: { [key: string]: any } = {};\n  const handledPropertyNames: string[] = [];\n\n  for (const key of Object.keys(modelProps)) {\n    const propertyMapper = modelProps[key];\n    const paths = splitSerializeName(modelProps[key].serializedName!);\n    handledPropertyNames.push(paths[0]);\n    const { serializedName, xmlName, xmlElementName } = propertyMapper;\n    let propertyObjectName = objectName;\n    if (serializedName !== \"\" && serializedName !== undefined) {\n      propertyObjectName = objectName + \".\" + serializedName;\n    }\n\n    const headerCollectionPrefix = (propertyMapper as DictionaryMapper).headerCollectionPrefix;\n    if (headerCollectionPrefix) {\n      const dictionary: any = {};\n      for (const headerKey of Object.keys(responseBody)) {\n        if (headerKey.startsWith(headerCollectionPrefix)) {\n          dictionary[headerKey.substring(headerCollectionPrefix.length)] = serializer.deserialize(\n            (propertyMapper as DictionaryMapper).type.value,\n            responseBody[headerKey],\n            propertyObjectName,\n            options\n          );\n        }\n\n        handledPropertyNames.push(headerKey);\n      }\n      instance[key] = dictionary;\n    } else if (serializer.isXML) {\n      if (propertyMapper.xmlIsAttribute && responseBody[XML_ATTRKEY]) {\n        instance[key] = serializer.deserialize(\n          propertyMapper,\n          responseBody[XML_ATTRKEY][xmlName!],\n          propertyObjectName,\n          options\n        );\n      } else if (propertyMapper.xmlIsMsText) {\n        if (responseBody[xmlCharKey] !== undefined) {\n          instance[key] = responseBody[xmlCharKey];\n        } else if (typeof responseBody === \"string\") {\n          // The special case where xml parser parses \"<Name>content</Name>\" into JSON of\n          //   `{ name: \"content\"}` instead of `{ name: { \"_\": \"content\" }}`\n          instance[key] = responseBody;\n        }\n      } else {\n        const propertyName = xmlElementName || xmlName || serializedName;\n        if (propertyMapper.xmlIsWrapped) {\n          /* a list of <xmlElementName> wrapped by <xmlName>\n            For the xml example below\n              <Cors>\n                <CorsRule>...</CorsRule>\n                <CorsRule>...</CorsRule>\n              </Cors>\n            the responseBody has\n              {\n                Cors: {\n                  CorsRule: [{...}, {...}]\n                }\n              }\n            xmlName is \"Cors\" and xmlElementName is\"CorsRule\".\n          */\n          const wrapped = responseBody[xmlName!];\n          const elementList = wrapped?.[xmlElementName!] ?? [];\n          instance[key] = serializer.deserialize(\n            propertyMapper,\n            elementList,\n            propertyObjectName,\n            options\n          );\n          handledPropertyNames.push(xmlName!);\n        } else {\n          const property = responseBody[propertyName!];\n          instance[key] = serializer.deserialize(\n            propertyMapper,\n            property,\n            propertyObjectName,\n            options\n          );\n          handledPropertyNames.push(propertyName!);\n        }\n      }\n    } else {\n      // deserialize the property if it is present in the provided responseBody instance\n      let propertyInstance;\n      let res = responseBody;\n      // traversing the object step by step.\n      let steps = 0;\n      for (const item of paths) {\n        if (!res) break;\n        steps++;\n        res = res[item];\n      }\n      // only accept null when reaching the last position of object otherwise it would be undefined\n      if (res === null && steps < paths.length) {\n        res = undefined;\n      }\n      propertyInstance = res;\n      const polymorphicDiscriminator = mapper.type.polymorphicDiscriminator;\n      // checking that the model property name (key)(ex: \"fishtype\") and the\n      // clientName of the polymorphicDiscriminator {metadata} (ex: \"fishtype\")\n      // instead of the serializedName of the polymorphicDiscriminator (ex: \"fish.type\")\n      // is a better approach. The generator is not consistent with escaping '\\.' in the\n      // serializedName of the property (ex: \"fish\\.type\") that is marked as polymorphic discriminator\n      // and the serializedName of the metadata polymorphicDiscriminator (ex: \"fish.type\"). However,\n      // the clientName transformation of the polymorphicDiscriminator (ex: \"fishtype\") and\n      // the transformation of model property name (ex: \"fishtype\") is done consistently.\n      // Hence, it is a safer bet to rely on the clientName of the polymorphicDiscriminator.\n      if (\n        polymorphicDiscriminator &&\n        key === polymorphicDiscriminator.clientName &&\n        (propertyInstance === undefined || propertyInstance === null)\n      ) {\n        propertyInstance = mapper.serializedName;\n      }\n\n      let serializedValue;\n      // paging\n      if (Array.isArray(responseBody[key]) && modelProps[key].serializedName === \"\") {\n        propertyInstance = responseBody[key];\n        const arrayInstance = serializer.deserialize(\n          propertyMapper,\n          propertyInstance,\n          propertyObjectName,\n          options\n        );\n        // Copy over any properties that have already been added into the instance, where they do\n        // not exist on the newly de-serialized array\n        for (const [k, v] of Object.entries(instance)) {\n          if (!Object.prototype.hasOwnProperty.call(arrayInstance, k)) {\n            arrayInstance[k] = v;\n          }\n        }\n        instance = arrayInstance;\n      } else if (propertyInstance !== undefined || propertyMapper.defaultValue !== undefined) {\n        serializedValue = serializer.deserialize(\n          propertyMapper,\n          propertyInstance,\n          propertyObjectName,\n          options\n        );\n        instance[key] = serializedValue;\n      }\n    }\n  }\n\n  const additionalPropertiesMapper = mapper.type.additionalProperties;\n  if (additionalPropertiesMapper) {\n    const isAdditionalProperty = (responsePropName: string): boolean => {\n      for (const clientPropName in modelProps) {\n        const paths = splitSerializeName(modelProps[clientPropName].serializedName);\n        if (paths[0] === responsePropName) {\n          return false;\n        }\n      }\n      return true;\n    };\n\n    for (const responsePropName in responseBody) {\n      if (isAdditionalProperty(responsePropName)) {\n        instance[responsePropName] = serializer.deserialize(\n          additionalPropertiesMapper,\n          responseBody[responsePropName],\n          objectName + '[\"' + responsePropName + '\"]',\n          options\n        );\n      }\n    }\n  } else if (responseBody && !options.ignoreUnknownProperties) {\n    for (const key of Object.keys(responseBody)) {\n      if (\n        instance[key] === undefined &&\n        !handledPropertyNames.includes(key) &&\n        !isSpecialXmlProperty(key, options)\n      ) {\n        instance[key] = responseBody[key];\n      }\n    }\n  }\n\n  return instance;\n}\n\nfunction deserializeDictionaryType(\n  serializer: Serializer,\n  mapper: DictionaryMapper,\n  responseBody: any,\n  objectName: string,\n  options: RequiredSerializerOptions\n): any {\n  /* jshint validthis: true */\n  const value = mapper.type.value;\n  if (!value || typeof value !== \"object\") {\n    throw new Error(\n      `\"value\" metadata for a Dictionary must be defined in the ` +\n        `mapper and it must of type \"object\" in ${objectName}`\n    );\n  }\n  if (responseBody) {\n    const tempDictionary: { [key: string]: any } = {};\n    for (const key of Object.keys(responseBody)) {\n      tempDictionary[key] = serializer.deserialize(value, responseBody[key], objectName, options);\n    }\n    return tempDictionary;\n  }\n  return responseBody;\n}\n\nfunction deserializeSequenceType(\n  serializer: Serializer,\n  mapper: SequenceMapper,\n  responseBody: any,\n  objectName: string,\n  options: RequiredSerializerOptions\n): any {\n  let element = mapper.type.element;\n  if (!element || typeof element !== \"object\") {\n    throw new Error(\n      `element\" metadata for an Array must be defined in the ` +\n        `mapper and it must of type \"object\" in ${objectName}`\n    );\n  }\n  if (responseBody) {\n    if (!Array.isArray(responseBody)) {\n      // xml2js will interpret a single element array as just the element, so force it to be an array\n      responseBody = [responseBody];\n    }\n\n    // Quirk: Composite mappers referenced by `element` might\n    // not have *all* properties declared (like uberParent),\n    // so let's try to look up the full definition by name.\n    if (element.type.name === \"Composite\" && element.type.className) {\n      element = serializer.modelMappers[element.type.className] ?? element;\n    }\n\n    const tempArray = [];\n    for (let i = 0; i < responseBody.length; i++) {\n      tempArray[i] = serializer.deserialize(\n        element,\n        responseBody[i],\n        `${objectName}[${i}]`,\n        options\n      );\n    }\n    return tempArray;\n  }\n  return responseBody;\n}\n\nfunction getIndexDiscriminator(\n  discriminators: Record<string, CompositeMapper>,\n  discriminatorValue: string,\n  typeName: string\n): CompositeMapper | undefined {\n  const typeNamesToCheck = [typeName];\n  while (typeNamesToCheck.length) {\n    const currentName = typeNamesToCheck.shift();\n    const indexDiscriminator =\n      discriminatorValue === currentName\n        ? discriminatorValue\n        : currentName + \".\" + discriminatorValue;\n    if (Object.prototype.hasOwnProperty.call(discriminators, indexDiscriminator)) {\n      return discriminators[indexDiscriminator];\n    } else {\n      for (const [name, mapper] of Object.entries(discriminators)) {\n        if (\n          name.startsWith(currentName + \".\") &&\n          mapper.type.uberParent === currentName &&\n          mapper.type.className\n        ) {\n          typeNamesToCheck.push(mapper.type.className);\n        }\n      }\n    }\n  }\n\n  return undefined;\n}\n\nfunction getPolymorphicMapper(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  object: any,\n  polymorphicPropertyName: \"clientName\" | \"serializedName\"\n): CompositeMapper {\n  const polymorphicDiscriminator = getPolymorphicDiscriminatorRecursively(serializer, mapper);\n\n  if (polymorphicDiscriminator) {\n    let discriminatorName = polymorphicDiscriminator[polymorphicPropertyName];\n    if (discriminatorName) {\n      // The serializedName might have \\\\, which we just want to ignore\n      if (polymorphicPropertyName === \"serializedName\") {\n        discriminatorName = discriminatorName.replace(/\\\\/gi, \"\");\n      }\n      const discriminatorValue = object[discriminatorName];\n      const typeName = mapper.type.uberParent ?? mapper.type.className;\n\n      if (typeof discriminatorValue === \"string\" && typeName) {\n        const polymorphicMapper = getIndexDiscriminator(\n          serializer.modelMappers.discriminators,\n          discriminatorValue,\n          typeName\n        );\n        if (polymorphicMapper) {\n          mapper = polymorphicMapper;\n        }\n      }\n    }\n  }\n  return mapper;\n}\n\nfunction getPolymorphicDiscriminatorRecursively(\n  serializer: Serializer,\n  mapper: CompositeMapper\n): PolymorphicDiscriminator | undefined {\n  return (\n    mapper.type.polymorphicDiscriminator ||\n    getPolymorphicDiscriminatorSafely(serializer, mapper.type.uberParent) ||\n    getPolymorphicDiscriminatorSafely(serializer, mapper.type.className)\n  );\n}\n\nfunction getPolymorphicDiscriminatorSafely(\n  serializer: Serializer,\n  typeName?: string\n): PolymorphicDiscriminator | undefined {\n  return (\n    typeName &&\n    serializer.modelMappers[typeName] &&\n    serializer.modelMappers[typeName].type.polymorphicDiscriminator\n  );\n}\n\n/**\n * Known types of Mappers\n */\nexport const MapperTypeNames = {\n  Base64Url: \"Base64Url\",\n  Boolean: \"Boolean\",\n  ByteArray: \"ByteArray\",\n  Composite: \"Composite\",\n  Date: \"Date\",\n  DateTime: \"DateTime\",\n  DateTimeRfc1123: \"DateTimeRfc1123\",\n  Dictionary: \"Dictionary\",\n  Enum: \"Enum\",\n  Number: \"Number\",\n  Object: \"Object\",\n  Sequence: \"Sequence\",\n  String: \"String\",\n  Stream: \"Stream\",\n  TimeSpan: \"TimeSpan\",\n  UnixTime: \"UnixTime\",\n} as const;\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  CompositeMapper,\n  Mapper,\n  OperationArguments,\n  OperationParameter,\n  OperationRequest,\n  OperationRequestInfo,\n  ParameterPath,\n} from \"./interfaces\";\n\n/**\n * @internal\n * Retrieves the value to use for a given operation argument\n * @param operationArguments - The arguments passed from the generated client\n * @param parameter - The parameter description\n * @param fallbackObject - If something isn't found in the arguments bag, look here.\n *  Generally used to look at the service client properties.\n */\nexport function getOperationArgumentValueFromParameter(\n  operationArguments: OperationArguments,\n  parameter: OperationParameter,\n  fallbackObject?: { [parameterName: string]: any }\n): any {\n  let parameterPath = parameter.parameterPath;\n  const parameterMapper = parameter.mapper;\n  let value: any;\n  if (typeof parameterPath === \"string\") {\n    parameterPath = [parameterPath];\n  }\n  if (Array.isArray(parameterPath)) {\n    if (parameterPath.length > 0) {\n      if (parameterMapper.isConstant) {\n        value = parameterMapper.defaultValue;\n      } else {\n        let propertySearchResult = getPropertyFromParameterPath(operationArguments, parameterPath);\n\n        if (!propertySearchResult.propertyFound && fallbackObject) {\n          propertySearchResult = getPropertyFromParameterPath(fallbackObject, parameterPath);\n        }\n\n        let useDefaultValue = false;\n        if (!propertySearchResult.propertyFound) {\n          useDefaultValue =\n            parameterMapper.required ||\n            (parameterPath[0] === \"options\" && parameterPath.length === 2);\n        }\n        value = useDefaultValue ? parameterMapper.defaultValue : propertySearchResult.propertyValue;\n      }\n    }\n  } else {\n    if (parameterMapper.required) {\n      value = {};\n    }\n\n    for (const propertyName in parameterPath) {\n      const propertyMapper: Mapper = (parameterMapper as CompositeMapper).type.modelProperties![\n        propertyName\n      ];\n      const propertyPath: ParameterPath = parameterPath[propertyName];\n      const propertyValue: any = getOperationArgumentValueFromParameter(\n        operationArguments,\n        {\n          parameterPath: propertyPath,\n          mapper: propertyMapper,\n        },\n        fallbackObject\n      );\n      if (propertyValue !== undefined) {\n        if (!value) {\n          value = {};\n        }\n        value[propertyName] = propertyValue;\n      }\n    }\n  }\n  return value;\n}\n\ninterface PropertySearchResult {\n  propertyValue?: any;\n  propertyFound: boolean;\n}\n\nfunction getPropertyFromParameterPath(\n  parent: { [parameterName: string]: any },\n  parameterPath: string[]\n): PropertySearchResult {\n  const result: PropertySearchResult = { propertyFound: false };\n  let i = 0;\n  for (; i < parameterPath.length; ++i) {\n    const parameterPathPart: string = parameterPath[i];\n    // Make sure to check inherited properties too, so don't use hasOwnProperty().\n    if (parent && parameterPathPart in parent) {\n      parent = parent[parameterPathPart];\n    } else {\n      break;\n    }\n  }\n  if (i === parameterPath.length) {\n    result.propertyValue = parent;\n    result.propertyFound = true;\n  }\n  return result;\n}\n\nconst operationRequestMap = new WeakMap<OperationRequest, OperationRequestInfo>();\nconst originalRequestSymbol = Symbol.for(\"@azure/core-client original request\");\n\nfunction hasOriginalRequest(\n  request: OperationRequest\n): request is OperationRequest & { [originalRequestSymbol]: OperationRequest } {\n  return originalRequestSymbol in request;\n}\n\nexport function getOperationRequestInfo(request: OperationRequest): OperationRequestInfo {\n  if (hasOriginalRequest(request)) {\n    return getOperationRequestInfo(request[originalRequestSymbol]);\n  }\n  let info = operationRequestMap.get(request);\n\n  if (!info) {\n    info = {};\n    operationRequestMap.set(request, info);\n  }\n  return info;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  FullOperationResponse,\n  OperationRequest,\n  OperationResponseMap,\n  OperationSpec,\n  RequiredSerializerOptions,\n  SerializerOptions,\n  XML_CHARKEY,\n  XmlOptions,\n} from \"./interfaces\";\nimport {\n  PipelinePolicy,\n  PipelineRequest,\n  PipelineResponse,\n  RestError,\n  SendRequest,\n} from \"@azure/core-rest-pipeline\";\nimport { MapperTypeNames } from \"./serializer\";\nimport { getOperationRequestInfo } from \"./operationHelpers\";\n\nconst defaultJsonContentTypes = [\"application/json\", \"text/json\"];\nconst defaultXmlContentTypes = [\"application/xml\", \"application/atom+xml\"];\n\n/**\n * The programmatic identifier of the deserializationPolicy.\n */\nexport const deserializationPolicyName = \"deserializationPolicy\";\n\n/**\n * Options to configure API response deserialization.\n */\nexport interface DeserializationPolicyOptions {\n  /**\n   * Configures the expected content types for the deserialization of\n   * JSON and XML response bodies.\n   */\n  expectedContentTypes?: DeserializationContentTypes;\n\n  /**\n   * A function that is able to parse XML. Required for XML support.\n   */\n  parseXML?: (str: string, opts?: XmlOptions) => Promise<any>;\n\n  /**\n   * Configures behavior of xml parser and builder.\n   */\n  serializerOptions?: SerializerOptions;\n}\n\n/**\n * The content-types that will indicate that an operation response should be deserialized in a\n * particular way.\n */\nexport interface DeserializationContentTypes {\n  /**\n   * The content-types that indicate that an operation response should be deserialized as JSON.\n   * Defaults to [ \"application/json\", \"text/json\" ].\n   */\n  json?: string[];\n\n  /**\n   * The content-types that indicate that an operation response should be deserialized as XML.\n   * Defaults to [ \"application/xml\", \"application/atom+xml\" ].\n   */\n  xml?: string[];\n}\n\n/**\n * This policy handles parsing out responses according to OperationSpecs on the request.\n */\nexport function deserializationPolicy(options: DeserializationPolicyOptions = {}): PipelinePolicy {\n  const jsonContentTypes = options.expectedContentTypes?.json ?? defaultJsonContentTypes;\n  const xmlContentTypes = options.expectedContentTypes?.xml ?? defaultXmlContentTypes;\n  const parseXML = options.parseXML;\n  const serializerOptions = options.serializerOptions;\n  const updatedOptions: RequiredSerializerOptions = {\n    xml: {\n      rootName: serializerOptions?.xml.rootName ?? \"\",\n      includeRoot: serializerOptions?.xml.includeRoot ?? false,\n      xmlCharKey: serializerOptions?.xml.xmlCharKey ?? XML_CHARKEY,\n    },\n  };\n\n  return {\n    name: deserializationPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      const response = await next(request);\n      return deserializeResponseBody(\n        jsonContentTypes,\n        xmlContentTypes,\n        response,\n        updatedOptions,\n        parseXML\n      );\n    },\n  };\n}\n\nfunction getOperationResponseMap(\n  parsedResponse: PipelineResponse\n): undefined | OperationResponseMap {\n  let result: OperationResponseMap | undefined;\n  const request: OperationRequest = parsedResponse.request;\n  const operationInfo = getOperationRequestInfo(request);\n  const operationSpec = operationInfo?.operationSpec;\n  if (operationSpec) {\n    if (!operationInfo?.operationResponseGetter) {\n      result = operationSpec.responses[parsedResponse.status];\n    } else {\n      result = operationInfo?.operationResponseGetter(operationSpec, parsedResponse);\n    }\n  }\n  return result;\n}\n\nfunction shouldDeserializeResponse(parsedResponse: PipelineResponse): boolean {\n  const request: OperationRequest = parsedResponse.request;\n  const operationInfo = getOperationRequestInfo(request);\n  const shouldDeserialize = operationInfo?.shouldDeserialize;\n  let result: boolean;\n  if (shouldDeserialize === undefined) {\n    result = true;\n  } else if (typeof shouldDeserialize === \"boolean\") {\n    result = shouldDeserialize;\n  } else {\n    result = shouldDeserialize(parsedResponse);\n  }\n  return result;\n}\n\nasync function deserializeResponseBody(\n  jsonContentTypes: string[],\n  xmlContentTypes: string[],\n  response: PipelineResponse,\n  options: RequiredSerializerOptions,\n  parseXML?: (str: string, opts?: XmlOptions) => Promise<any>\n): Promise<PipelineResponse> {\n  const parsedResponse = await parse(\n    jsonContentTypes,\n    xmlContentTypes,\n    response,\n    options,\n    parseXML\n  );\n  if (!shouldDeserializeResponse(parsedResponse)) {\n    return parsedResponse;\n  }\n\n  const operationInfo = getOperationRequestInfo(parsedResponse.request);\n  const operationSpec = operationInfo?.operationSpec;\n  if (!operationSpec || !operationSpec.responses) {\n    return parsedResponse;\n  }\n\n  const responseSpec = getOperationResponseMap(parsedResponse);\n  const { error, shouldReturnResponse } = handleErrorResponse(\n    parsedResponse,\n    operationSpec,\n    responseSpec,\n    options\n  );\n  if (error) {\n    throw error;\n  } else if (shouldReturnResponse) {\n    return parsedResponse;\n  }\n\n  // An operation response spec does exist for current status code, so\n  // use it to deserialize the response.\n  if (responseSpec) {\n    if (responseSpec.bodyMapper) {\n      let valueToDeserialize: any = parsedResponse.parsedBody;\n      if (operationSpec.isXML && responseSpec.bodyMapper.type.name === MapperTypeNames.Sequence) {\n        valueToDeserialize =\n          typeof valueToDeserialize === \"object\"\n            ? valueToDeserialize[responseSpec.bodyMapper.xmlElementName!]\n            : [];\n      }\n      try {\n        parsedResponse.parsedBody = operationSpec.serializer.deserialize(\n          responseSpec.bodyMapper,\n          valueToDeserialize,\n          \"operationRes.parsedBody\",\n          options\n        );\n      } catch (deserializeError: any) {\n        const restError = new RestError(\n          `Error ${deserializeError} occurred in deserializing the responseBody - ${parsedResponse.bodyAsText}`,\n          {\n            statusCode: parsedResponse.status,\n            request: parsedResponse.request,\n            response: parsedResponse,\n          }\n        );\n        throw restError;\n      }\n    } else if (operationSpec.httpMethod === \"HEAD\") {\n      // head methods never have a body, but we return a boolean to indicate presence/absence of the resource\n      parsedResponse.parsedBody = response.status >= 200 && response.status < 300;\n    }\n\n    if (responseSpec.headersMapper) {\n      parsedResponse.parsedHeaders = operationSpec.serializer.deserialize(\n        responseSpec.headersMapper,\n        parsedResponse.headers.toJSON(),\n        \"operationRes.parsedHeaders\",\n        { xml: {}, ignoreUnknownProperties: true }\n      );\n    }\n  }\n\n  return parsedResponse;\n}\n\nfunction isOperationSpecEmpty(operationSpec: OperationSpec): boolean {\n  const expectedStatusCodes = Object.keys(operationSpec.responses);\n  return (\n    expectedStatusCodes.length === 0 ||\n    (expectedStatusCodes.length === 1 && expectedStatusCodes[0] === \"default\")\n  );\n}\n\nfunction handleErrorResponse(\n  parsedResponse: FullOperationResponse,\n  operationSpec: OperationSpec,\n  responseSpec: OperationResponseMap | undefined,\n  options: RequiredSerializerOptions\n): { error: RestError | null; shouldReturnResponse: boolean } {\n  const isSuccessByStatus = 200 <= parsedResponse.status && parsedResponse.status < 300;\n  const isExpectedStatusCode: boolean = isOperationSpecEmpty(operationSpec)\n    ? isSuccessByStatus\n    : !!responseSpec;\n\n  if (isExpectedStatusCode) {\n    if (responseSpec) {\n      if (!responseSpec.isError) {\n        return { error: null, shouldReturnResponse: false };\n      }\n    } else {\n      return { error: null, shouldReturnResponse: false };\n    }\n  }\n\n  const errorResponseSpec = responseSpec ?? operationSpec.responses.default;\n\n  const initialErrorMessage = parsedResponse.request.streamResponseStatusCodes?.has(\n    parsedResponse.status\n  )\n    ? `Unexpected status code: ${parsedResponse.status}`\n    : (parsedResponse.bodyAsText as string);\n\n  const error = new RestError(initialErrorMessage, {\n    statusCode: parsedResponse.status,\n    request: parsedResponse.request,\n    response: parsedResponse,\n  });\n\n  // If the item failed but there's no error spec or default spec to deserialize the error,\n  // we should fail so we just throw the parsed response\n  if (!errorResponseSpec) {\n    throw error;\n  }\n\n  const defaultBodyMapper = errorResponseSpec.bodyMapper;\n  const defaultHeadersMapper = errorResponseSpec.headersMapper;\n\n  try {\n    // If error response has a body, try to deserialize it using default body mapper.\n    // Then try to extract error code & message from it\n    if (parsedResponse.parsedBody) {\n      const parsedBody = parsedResponse.parsedBody;\n      let deserializedError;\n\n      if (defaultBodyMapper) {\n        let valueToDeserialize: any = parsedBody;\n        if (operationSpec.isXML && defaultBodyMapper.type.name === MapperTypeNames.Sequence) {\n          valueToDeserialize = [];\n          const elementName = defaultBodyMapper.xmlElementName;\n          if (typeof parsedBody === \"object\" && elementName) {\n            valueToDeserialize = parsedBody[elementName];\n          }\n        }\n        deserializedError = operationSpec.serializer.deserialize(\n          defaultBodyMapper,\n          valueToDeserialize,\n          \"error.response.parsedBody\",\n          options\n        );\n      }\n\n      const internalError: any = parsedBody.error || deserializedError || parsedBody;\n      error.code = internalError.code;\n      if (internalError.message) {\n        error.message = internalError.message;\n      }\n\n      if (defaultBodyMapper) {\n        (error.response! as FullOperationResponse).parsedBody = deserializedError;\n      }\n    }\n\n    // If error response has headers, try to deserialize it using default header mapper\n    if (parsedResponse.headers && defaultHeadersMapper) {\n      (error.response! as FullOperationResponse).parsedHeaders =\n        operationSpec.serializer.deserialize(\n          defaultHeadersMapper,\n          parsedResponse.headers.toJSON(),\n          \"operationRes.parsedHeaders\"\n        );\n    }\n  } catch (defaultError: any) {\n    error.message = `Error \"${defaultError.message}\" occurred in deserializing the responseBody - \"${parsedResponse.bodyAsText}\" for the default response.`;\n  }\n\n  return { error, shouldReturnResponse: false };\n}\n\nasync function parse(\n  jsonContentTypes: string[],\n  xmlContentTypes: string[],\n  operationResponse: FullOperationResponse,\n  opts: RequiredSerializerOptions,\n  parseXML?: (str: string, opts?: XmlOptions) => Promise<any>\n): Promise<FullOperationResponse> {\n  if (\n    !operationResponse.request.streamResponseStatusCodes?.has(operationResponse.status) &&\n    operationResponse.bodyAsText\n  ) {\n    const text = operationResponse.bodyAsText;\n    const contentType: string = operationResponse.headers.get(\"Content-Type\") || \"\";\n    const contentComponents: string[] = !contentType\n      ? []\n      : contentType.split(\";\").map((component) => component.toLowerCase());\n\n    try {\n      if (\n        contentComponents.length === 0 ||\n        contentComponents.some((component) => jsonContentTypes.indexOf(component) !== -1)\n      ) {\n        operationResponse.parsedBody = JSON.parse(text);\n        return operationResponse;\n      } else if (contentComponents.some((component) => xmlContentTypes.indexOf(component) !== -1)) {\n        if (!parseXML) {\n          throw new Error(\"Parsing XML not supported.\");\n        }\n        const body = await parseXML(text, opts.xml);\n        operationResponse.parsedBody = body;\n        return operationResponse;\n      }\n    } catch (err: any) {\n      const msg = `Error \"${err}\" occurred while parsing the response body - ${operationResponse.bodyAsText}.`;\n      const errCode = err.code || RestError.PARSE_ERROR;\n      const e = new RestError(msg, {\n        code: errCode,\n        statusCode: operationResponse.status,\n        request: operationResponse.request,\n        response: operationResponse,\n      });\n      throw e;\n    }\n  }\n\n  return operationResponse;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { OperationParameter, OperationSpec } from \"./interfaces\";\nimport { MapperTypeNames } from \"./serializer\";\n\n/**\n * Gets the list of status codes for streaming responses.\n * @internal\n */\nexport function getStreamingResponseStatusCodes(operationSpec: OperationSpec): Set<number> {\n  const result = new Set<number>();\n  for (const statusCode in operationSpec.responses) {\n    const operationResponse = operationSpec.responses[statusCode];\n    if (\n      operationResponse.bodyMapper &&\n      operationResponse.bodyMapper.type.name === MapperTypeNames.Stream\n    ) {\n      result.add(Number(statusCode));\n    }\n  }\n  return result;\n}\n\n/**\n * Get the path to this parameter's value as a dotted string (a.b.c).\n * @param parameter - The parameter to get the path string for.\n * @returns The path to this parameter's value as a dotted string.\n * @internal\n */\nexport function getPathStringFromParameter(parameter: OperationParameter): string {\n  const { parameterPath, mapper } = parameter;\n  let result: string;\n  if (typeof parameterPath === \"string\") {\n    result = parameterPath;\n  } else if (Array.isArray(parameterPath)) {\n    result = parameterPath.join(\".\");\n  } else {\n    result = mapper.serializedName!;\n  }\n  return result;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  DictionaryMapper,\n  OperationArguments,\n  OperationRequest,\n  OperationSpec,\n  RequiredSerializerOptions,\n  SerializerOptions,\n  XML_ATTRKEY,\n  XML_CHARKEY,\n  XmlOptions,\n} from \"./interfaces\";\nimport { PipelinePolicy, PipelineResponse, SendRequest } from \"@azure/core-rest-pipeline\";\nimport {\n  getOperationArgumentValueFromParameter,\n  getOperationRequestInfo,\n} from \"./operationHelpers\";\nimport { MapperTypeNames } from \"./serializer\";\nimport { getPathStringFromParameter } from \"./interfaceHelpers\";\n\n/**\n * The programmatic identifier of the serializationPolicy.\n */\nexport const serializationPolicyName = \"serializationPolicy\";\n\n/**\n * Options to configure API request serialization.\n */\nexport interface SerializationPolicyOptions {\n  /**\n   * A function that is able to write XML. Required for XML support.\n   */\n  stringifyXML?: (obj: any, opts?: XmlOptions) => string;\n\n  /**\n   * Configures behavior of xml parser and builder.\n   */\n  serializerOptions?: SerializerOptions;\n}\n\n/**\n * This policy handles assembling the request body and headers using\n * an OperationSpec and OperationArguments on the request.\n */\nexport function serializationPolicy(options: SerializationPolicyOptions = {}): PipelinePolicy {\n  const stringifyXML = options.stringifyXML;\n\n  return {\n    name: serializationPolicyName,\n    async sendRequest(request: OperationRequest, next: SendRequest): Promise<PipelineResponse> {\n      const operationInfo = getOperationRequestInfo(request);\n      const operationSpec = operationInfo?.operationSpec;\n      const operationArguments = operationInfo?.operationArguments;\n      if (operationSpec && operationArguments) {\n        serializeHeaders(request, operationArguments, operationSpec);\n        serializeRequestBody(request, operationArguments, operationSpec, stringifyXML);\n      }\n      return next(request);\n    },\n  };\n}\n\n/**\n * @internal\n */\nexport function serializeHeaders(\n  request: OperationRequest,\n  operationArguments: OperationArguments,\n  operationSpec: OperationSpec\n): void {\n  if (operationSpec.headerParameters) {\n    for (const headerParameter of operationSpec.headerParameters) {\n      let headerValue = getOperationArgumentValueFromParameter(operationArguments, headerParameter);\n      if ((headerValue !== null && headerValue !== undefined) || headerParameter.mapper.required) {\n        headerValue = operationSpec.serializer.serialize(\n          headerParameter.mapper,\n          headerValue,\n          getPathStringFromParameter(headerParameter)\n        );\n        const headerCollectionPrefix = (headerParameter.mapper as DictionaryMapper)\n          .headerCollectionPrefix;\n        if (headerCollectionPrefix) {\n          for (const key of Object.keys(headerValue)) {\n            request.headers.set(headerCollectionPrefix + key, headerValue[key]);\n          }\n        } else {\n          request.headers.set(\n            headerParameter.mapper.serializedName || getPathStringFromParameter(headerParameter),\n            headerValue\n          );\n        }\n      }\n    }\n  }\n  const customHeaders = operationArguments.options?.requestOptions?.customHeaders;\n  if (customHeaders) {\n    for (const customHeaderName of Object.keys(customHeaders)) {\n      request.headers.set(customHeaderName, customHeaders[customHeaderName]);\n    }\n  }\n}\n\n/**\n * @internal\n */\nexport function serializeRequestBody(\n  request: OperationRequest,\n  operationArguments: OperationArguments,\n  operationSpec: OperationSpec,\n  stringifyXML: (obj: any, opts?: XmlOptions) => string = function () {\n    throw new Error(\"XML serialization unsupported!\");\n  }\n): void {\n  const serializerOptions = operationArguments.options?.serializerOptions;\n  const updatedOptions: RequiredSerializerOptions = {\n    xml: {\n      rootName: serializerOptions?.xml.rootName ?? \"\",\n      includeRoot: serializerOptions?.xml.includeRoot ?? false,\n      xmlCharKey: serializerOptions?.xml.xmlCharKey ?? XML_CHARKEY,\n    },\n  };\n\n  const xmlCharKey = updatedOptions.xml.xmlCharKey;\n  if (operationSpec.requestBody && operationSpec.requestBody.mapper) {\n    request.body = getOperationArgumentValueFromParameter(\n      operationArguments,\n      operationSpec.requestBody\n    );\n\n    const bodyMapper = operationSpec.requestBody.mapper;\n    const {\n      required,\n      serializedName,\n      xmlName,\n      xmlElementName,\n      xmlNamespace,\n      xmlNamespacePrefix,\n      nullable,\n    } = bodyMapper;\n    const typeName = bodyMapper.type.name;\n\n    try {\n      if (\n        (request.body !== undefined && request.body !== null) ||\n        (nullable && request.body === null) ||\n        required\n      ) {\n        const requestBodyParameterPathString: string = getPathStringFromParameter(\n          operationSpec.requestBody\n        );\n        request.body = operationSpec.serializer.serialize(\n          bodyMapper,\n          request.body,\n          requestBodyParameterPathString,\n          updatedOptions\n        );\n\n        const isStream = typeName === MapperTypeNames.Stream;\n\n        if (operationSpec.isXML) {\n          const xmlnsKey = xmlNamespacePrefix ? `xmlns:${xmlNamespacePrefix}` : \"xmlns\";\n          const value = getXmlValueWithNamespace(\n            xmlNamespace,\n            xmlnsKey,\n            typeName,\n            request.body,\n            updatedOptions\n          );\n\n          if (typeName === MapperTypeNames.Sequence) {\n            request.body = stringifyXML(\n              prepareXMLRootList(\n                value,\n                xmlElementName || xmlName || serializedName!,\n                xmlnsKey,\n                xmlNamespace\n              ),\n              { rootName: xmlName || serializedName, xmlCharKey }\n            );\n          } else if (!isStream) {\n            request.body = stringifyXML(value, {\n              rootName: xmlName || serializedName,\n              xmlCharKey,\n            });\n          }\n        } else if (\n          typeName === MapperTypeNames.String &&\n          (operationSpec.contentType?.match(\"text/plain\") || operationSpec.mediaType === \"text\")\n        ) {\n          // the String serializer has validated that request body is a string\n          // so just send the string.\n          return;\n        } else if (!isStream) {\n          request.body = JSON.stringify(request.body);\n        }\n      }\n    } catch (error: any) {\n      throw new Error(\n        `Error \"${error.message}\" occurred in serializing the payload - ${JSON.stringify(\n          serializedName,\n          undefined,\n          \"  \"\n        )}.`\n      );\n    }\n  } else if (operationSpec.formDataParameters && operationSpec.formDataParameters.length > 0) {\n    request.formData = {};\n    for (const formDataParameter of operationSpec.formDataParameters) {\n      const formDataParameterValue = getOperationArgumentValueFromParameter(\n        operationArguments,\n        formDataParameter\n      );\n      if (formDataParameterValue !== undefined && formDataParameterValue !== null) {\n        const formDataParameterPropertyName: string =\n          formDataParameter.mapper.serializedName || getPathStringFromParameter(formDataParameter);\n        request.formData[formDataParameterPropertyName] = operationSpec.serializer.serialize(\n          formDataParameter.mapper,\n          formDataParameterValue,\n          getPathStringFromParameter(formDataParameter),\n          updatedOptions\n        );\n      }\n    }\n  }\n}\n\n/**\n * Adds an xml namespace to the xml serialized object if needed, otherwise it just returns the value itself\n */\nfunction getXmlValueWithNamespace(\n  xmlNamespace: string | undefined,\n  xmlnsKey: string,\n  typeName: string,\n  serializedValue: any,\n  options: RequiredSerializerOptions\n): any {\n  // Composite and Sequence schemas already got their root namespace set during serialization\n  // We just need to add xmlns to the other schema types\n  if (xmlNamespace && ![\"Composite\", \"Sequence\", \"Dictionary\"].includes(typeName)) {\n    const result: any = {};\n    result[options.xml.xmlCharKey] = serializedValue;\n    result[XML_ATTRKEY] = { [xmlnsKey]: xmlNamespace };\n    return result;\n  }\n\n  return serializedValue;\n}\n\nfunction prepareXMLRootList(\n  obj: any,\n  elementName: string,\n  xmlNamespaceKey?: string,\n  xmlNamespace?: string\n): { [key: string]: any[] } {\n  if (!Array.isArray(obj)) {\n    obj = [obj];\n  }\n  if (!xmlNamespaceKey || !xmlNamespace) {\n    return { [elementName]: obj };\n  }\n\n  const result = { [elementName]: obj };\n  result[XML_ATTRKEY] = { [xmlNamespaceKey]: xmlNamespace };\n  return result;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { DeserializationPolicyOptions, deserializationPolicy } from \"./deserializationPolicy\";\nimport {\n  InternalPipelineOptions,\n  Pipeline,\n  bearerTokenAuthenticationPolicy,\n  createPipelineFromOptions,\n} from \"@azure/core-rest-pipeline\";\nimport { SerializationPolicyOptions, serializationPolicy } from \"./serializationPolicy\";\nimport { TokenCredential } from \"@azure/core-auth\";\n\n/**\n * Options for creating a Pipeline to use with ServiceClient.\n * Mostly for customizing the auth policy (if using token auth) or\n * the deserialization options when using XML.\n */\nexport interface InternalClientPipelineOptions extends InternalPipelineOptions {\n  /**\n   * Options to customize bearerTokenAuthenticationPolicy.\n   */\n  credentialOptions?: { credentialScopes: string | string[]; credential: TokenCredential };\n  /**\n   * Options to customize deserializationPolicy.\n   */\n  deserializationOptions?: DeserializationPolicyOptions;\n  /**\n   * Options to customize serializationPolicy.\n   */\n  serializationOptions?: SerializationPolicyOptions;\n}\n\n/**\n * Creates a new Pipeline for use with a Service Client.\n * Adds in deserializationPolicy by default.\n * Also adds in bearerTokenAuthenticationPolicy if passed a TokenCredential.\n * @param options - Options to customize the created pipeline.\n */\nexport function createClientPipeline(options: InternalClientPipelineOptions = {}): Pipeline {\n  const pipeline = createPipelineFromOptions(options ?? {});\n  if (options.credentialOptions) {\n    pipeline.addPolicy(\n      bearerTokenAuthenticationPolicy({\n        credential: options.credentialOptions.credential,\n        scopes: options.credentialOptions.credentialScopes,\n      })\n    );\n  }\n\n  pipeline.addPolicy(serializationPolicy(options.serializationOptions), { phase: \"Serialize\" });\n  pipeline.addPolicy(deserializationPolicy(options.deserializationOptions), {\n    phase: \"Deserialize\",\n  });\n\n  return pipeline;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpClient, createDefaultHttpClient } from \"@azure/core-rest-pipeline\";\n\nlet cachedHttpClient: HttpClient | undefined;\n\nexport function getCachedDefaultHttpClient(): HttpClient {\n  if (!cachedHttpClient) {\n    cachedHttpClient = createDefaultHttpClient();\n  }\n\n  return cachedHttpClient;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { OperationArguments, OperationSpec, QueryCollectionFormat } from \"./interfaces\";\nimport { getOperationArgumentValueFromParameter } from \"./operationHelpers\";\nimport { getPathStringFromParameter } from \"./interfaceHelpers\";\n\nconst CollectionFormatToDelimiterMap: { [key in QueryCollectionFormat]: string } = {\n  CSV: \",\",\n  SSV: \" \",\n  Multi: \"Multi\",\n  TSV: \"\\t\",\n  Pipes: \"|\",\n};\n\nexport function getRequestUrl(\n  baseUri: string,\n  operationSpec: OperationSpec,\n  operationArguments: OperationArguments,\n  fallbackObject: { [parameterName: string]: any }\n): string {\n  const urlReplacements = calculateUrlReplacements(\n    operationSpec,\n    operationArguments,\n    fallbackObject\n  );\n\n  let isAbsolutePath = false;\n\n  let requestUrl = replaceAll(baseUri, urlReplacements);\n  if (operationSpec.path) {\n    let path = replaceAll(operationSpec.path, urlReplacements);\n    // QUIRK: sometimes we get a path component like /{nextLink}\n    // which may be a fully formed URL with a leading /. In that case, we should\n    // remove the leading /\n    if (operationSpec.path === \"/{nextLink}\" && path.startsWith(\"/\")) {\n      path = path.substring(1);\n    }\n    // QUIRK: sometimes we get a path component like {nextLink}\n    // which may be a fully formed URL. In that case, we should\n    // ignore the baseUri.\n    if (isAbsoluteUrl(path)) {\n      requestUrl = path;\n      isAbsolutePath = true;\n    } else {\n      requestUrl = appendPath(requestUrl, path);\n    }\n  }\n\n  const { queryParams, sequenceParams } = calculateQueryParameters(\n    operationSpec,\n    operationArguments,\n    fallbackObject\n  );\n  /**\n   * Notice that this call sets the `noOverwrite` parameter to true if the `requestUrl`\n   * is an absolute path. This ensures that existing query parameter values in `requestUrl`\n   * do not get overwritten. On the other hand when `requestUrl` is not absolute path, it\n   * is still being built so there is nothing to overwrite.\n   */\n  requestUrl = appendQueryParams(requestUrl, queryParams, sequenceParams, isAbsolutePath);\n\n  return requestUrl;\n}\n\nfunction replaceAll(input: string, replacements: Map<string, string>): string {\n  let result = input;\n  for (const [searchValue, replaceValue] of replacements) {\n    result = result.split(searchValue).join(replaceValue);\n  }\n  return result;\n}\n\nfunction calculateUrlReplacements(\n  operationSpec: OperationSpec,\n  operationArguments: OperationArguments,\n  fallbackObject: { [parameterName: string]: any }\n): Map<string, string> {\n  const result = new Map<string, string>();\n  if (operationSpec.urlParameters?.length) {\n    for (const urlParameter of operationSpec.urlParameters) {\n      let urlParameterValue: string = getOperationArgumentValueFromParameter(\n        operationArguments,\n        urlParameter,\n        fallbackObject\n      );\n      const parameterPathString = getPathStringFromParameter(urlParameter);\n      urlParameterValue = operationSpec.serializer.serialize(\n        urlParameter.mapper,\n        urlParameterValue,\n        parameterPathString\n      );\n      if (!urlParameter.skipEncoding) {\n        urlParameterValue = encodeURIComponent(urlParameterValue);\n      }\n      result.set(\n        `{${urlParameter.mapper.serializedName || parameterPathString}}`,\n        urlParameterValue\n      );\n    }\n  }\n  return result;\n}\n\nfunction isAbsoluteUrl(url: string): boolean {\n  return url.includes(\"://\");\n}\n\nfunction appendPath(url: string, pathToAppend?: string): string {\n  if (!pathToAppend) {\n    return url;\n  }\n\n  const parsedUrl = new URL(url);\n  let newPath = parsedUrl.pathname;\n\n  if (!newPath.endsWith(\"/\")) {\n    newPath = `${newPath}/`;\n  }\n\n  if (pathToAppend.startsWith(\"/\")) {\n    pathToAppend = pathToAppend.substring(1);\n  }\n\n  const searchStart = pathToAppend.indexOf(\"?\");\n  if (searchStart !== -1) {\n    const path = pathToAppend.substring(0, searchStart);\n    const search = pathToAppend.substring(searchStart + 1);\n    newPath = newPath + path;\n    if (search) {\n      parsedUrl.search = parsedUrl.search ? `${parsedUrl.search}&${search}` : search;\n    }\n  } else {\n    newPath = newPath + pathToAppend;\n  }\n\n  parsedUrl.pathname = newPath;\n\n  return parsedUrl.toString();\n}\n\nfunction calculateQueryParameters(\n  operationSpec: OperationSpec,\n  operationArguments: OperationArguments,\n  fallbackObject: { [parameterName: string]: any }\n): {\n  queryParams: Map<string, string | string[]>;\n  sequenceParams: Set<string>;\n} {\n  const result = new Map<string, string | string[]>();\n  const sequenceParams: Set<string> = new Set<string>();\n\n  if (operationSpec.queryParameters?.length) {\n    for (const queryParameter of operationSpec.queryParameters) {\n      if (queryParameter.mapper.type.name === \"Sequence\" && queryParameter.mapper.serializedName) {\n        sequenceParams.add(queryParameter.mapper.serializedName);\n      }\n      let queryParameterValue: string | string[] = getOperationArgumentValueFromParameter(\n        operationArguments,\n        queryParameter,\n        fallbackObject\n      );\n      if (\n        (queryParameterValue !== undefined && queryParameterValue !== null) ||\n        queryParameter.mapper.required\n      ) {\n        queryParameterValue = operationSpec.serializer.serialize(\n          queryParameter.mapper,\n          queryParameterValue,\n          getPathStringFromParameter(queryParameter)\n        );\n\n        const delimiter = queryParameter.collectionFormat\n          ? CollectionFormatToDelimiterMap[queryParameter.collectionFormat]\n          : \"\";\n        if (Array.isArray(queryParameterValue)) {\n          // replace null and undefined\n          queryParameterValue = queryParameterValue.map((item) => {\n            if (item === null || item === undefined) {\n              return \"\";\n            }\n\n            return item;\n          });\n        }\n        if (queryParameter.collectionFormat === \"Multi\" && queryParameterValue.length === 0) {\n          continue;\n        } else if (\n          Array.isArray(queryParameterValue) &&\n          (queryParameter.collectionFormat === \"SSV\" || queryParameter.collectionFormat === \"TSV\")\n        ) {\n          queryParameterValue = queryParameterValue.join(delimiter);\n        }\n        if (!queryParameter.skipEncoding) {\n          if (Array.isArray(queryParameterValue)) {\n            queryParameterValue = queryParameterValue.map((item: string) => {\n              return encodeURIComponent(item);\n            });\n          } else {\n            queryParameterValue = encodeURIComponent(queryParameterValue);\n          }\n        }\n\n        // Join pipes and CSV *after* encoding, or the server will be upset.\n        if (\n          Array.isArray(queryParameterValue) &&\n          (queryParameter.collectionFormat === \"CSV\" || queryParameter.collectionFormat === \"Pipes\")\n        ) {\n          queryParameterValue = queryParameterValue.join(delimiter);\n        }\n\n        result.set(\n          queryParameter.mapper.serializedName || getPathStringFromParameter(queryParameter),\n          queryParameterValue\n        );\n      }\n    }\n  }\n  return {\n    queryParams: result,\n    sequenceParams,\n  };\n}\n\nfunction simpleParseQueryParams(queryString: string): Map<string, string | string[] | undefined> {\n  const result: Map<string, string | string[] | undefined> = new Map<\n    string,\n    string | string[] | undefined\n  >();\n  if (!queryString || queryString[0] !== \"?\") {\n    return result;\n  }\n\n  // remove the leading ?\n  queryString = queryString.slice(1);\n  const pairs = queryString.split(\"&\");\n\n  for (const pair of pairs) {\n    const [name, value] = pair.split(\"=\", 2);\n    const existingValue = result.get(name);\n    if (existingValue) {\n      if (Array.isArray(existingValue)) {\n        existingValue.push(value);\n      } else {\n        result.set(name, [existingValue, value]);\n      }\n    } else {\n      result.set(name, value);\n    }\n  }\n\n  return result;\n}\n\n/** @internal */\nexport function appendQueryParams(\n  url: string,\n  queryParams: Map<string, string | string[]>,\n  sequenceParams: Set<string>,\n  noOverwrite: boolean = false\n): string {\n  if (queryParams.size === 0) {\n    return url;\n  }\n\n  const parsedUrl = new URL(url);\n\n  // QUIRK: parsedUrl.searchParams will have their name/value pairs decoded, which\n  // can change their meaning to the server, such as in the case of a SAS signature.\n  // To avoid accidentally un-encoding a query param, we parse the key/values ourselves\n  const combinedParams = simpleParseQueryParams(parsedUrl.search);\n\n  for (const [name, value] of queryParams) {\n    const existingValue = combinedParams.get(name);\n    if (Array.isArray(existingValue)) {\n      if (Array.isArray(value)) {\n        existingValue.push(...value);\n        const valueSet = new Set(existingValue);\n        combinedParams.set(name, Array.from(valueSet));\n      } else {\n        existingValue.push(value);\n      }\n    } else if (existingValue) {\n      if (Array.isArray(value)) {\n        value.unshift(existingValue);\n      } else if (sequenceParams.has(name)) {\n        combinedParams.set(name, [existingValue, value]);\n      }\n      if (!noOverwrite) {\n        combinedParams.set(name, value);\n      }\n    } else {\n      combinedParams.set(name, value);\n    }\n  }\n\n  const searchPieces: string[] = [];\n  for (const [name, value] of combinedParams) {\n    if (typeof value === \"string\") {\n      searchPieces.push(`${name}=${value}`);\n    } else if (Array.isArray(value)) {\n      // QUIRK: If we get an array of values, include multiple key/value pairs\n      for (const subValue of value) {\n        searchPieces.push(`${name}=${subValue}`);\n      }\n    } else {\n      searchPieces.push(`${name}=${value}`);\n    }\n  }\n\n  // QUIRK: we have to set search manually as searchParams will encode comma when it shouldn't.\n  parsedUrl.search = searchPieces.length ? `?${searchPieces.join(\"&\")}` : \"\";\n  return parsedUrl.toString();\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { createClientLogger } from \"@azure/logger\";\nexport const logger = createClientLogger(\"core-client\");\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  CommonClientOptions,\n  OperationArguments,\n  OperationRequest,\n  OperationSpec,\n} from \"./interfaces\";\nimport {\n  HttpClient,\n  Pipeline,\n  PipelineRequest,\n  PipelineResponse,\n  createPipelineRequest,\n} from \"@azure/core-rest-pipeline\";\nimport { TokenCredential } from \"@azure/core-auth\";\nimport { createClientPipeline } from \"./pipeline\";\nimport { flattenResponse } from \"./utils\";\nimport { getCachedDefaultHttpClient } from \"./httpClientCache\";\nimport { getOperationRequestInfo } from \"./operationHelpers\";\nimport { getRequestUrl } from \"./urlHelpers\";\nimport { getStreamingResponseStatusCodes } from \"./interfaceHelpers\";\nimport { logger } from \"./log\";\n\n/**\n * Options to be provided while creating the client.\n */\nexport interface ServiceClientOptions extends CommonClientOptions {\n  /**\n   * If specified, this is the base URI that requests will be made against for this ServiceClient.\n   * If it is not specified, then all OperationSpecs must contain a baseUrl property.\n   * @deprecated This property is deprecated and will be removed soon, please use endpoint instead\n   */\n  baseUri?: string;\n  /**\n   * If specified, this is the endpoint that requests will be made against for this ServiceClient.\n   * If it is not specified, then all OperationSpecs must contain a baseUrl property.\n   * to encourage customer to use endpoint, we mark the baseUri as deprecated.\n   */\n  endpoint?: string;\n  /**\n   * If specified, will be used to build the BearerTokenAuthenticationPolicy.\n   */\n  credentialScopes?: string | string[];\n  /**\n   * The default request content type for the service.\n   * Used if no requestContentType is present on an OperationSpec.\n   */\n  requestContentType?: string;\n  /**\n   * Credential used to authenticate the request.\n   */\n  credential?: TokenCredential;\n  /**\n   * A customized pipeline to use, otherwise a default one will be created.\n   */\n  pipeline?: Pipeline;\n}\n\n/**\n * Initializes a new instance of the ServiceClient.\n */\nexport class ServiceClient {\n  /**\n   * If specified, this is the base URI that requests will be made against for this ServiceClient.\n   * If it is not specified, then all OperationSpecs must contain a baseUrl property.\n   */\n  private readonly _endpoint?: string;\n\n  /**\n   * The default request content type for the service.\n   * Used if no requestContentType is present on an OperationSpec.\n   */\n  private readonly _requestContentType?: string;\n\n  /**\n   * Set to true if the request is sent over HTTP instead of HTTPS\n   */\n  private readonly _allowInsecureConnection?: boolean;\n\n  /**\n   * The HTTP client that will be used to send requests.\n   */\n  private readonly _httpClient: HttpClient;\n\n  /**\n   * The pipeline used by this client to make requests\n   */\n  public readonly pipeline: Pipeline;\n\n  /**\n   * The ServiceClient constructor\n   * @param credential - The credentials used for authentication with the service.\n   * @param options - The service client options that govern the behavior of the client.\n   */\n  constructor(options: ServiceClientOptions = {}) {\n    this._requestContentType = options.requestContentType;\n    this._endpoint = options.endpoint ?? options.baseUri;\n    if (options.baseUri) {\n      logger.warning(\n        \"The baseUri option for SDK Clients has been deprecated, please use endpoint instead.\"\n      );\n    }\n    this._allowInsecureConnection = options.allowInsecureConnection;\n    this._httpClient = options.httpClient || getCachedDefaultHttpClient();\n\n    this.pipeline = options.pipeline || createDefaultPipeline(options);\n    if (options.additionalPolicies?.length) {\n      for (const { policy, position } of options.additionalPolicies) {\n        // Sign happens after Retry and is commonly needed to occur\n        // before policies that intercept post-retry.\n        const afterPhase = position === \"perRetry\" ? \"Sign\" : undefined;\n        this.pipeline.addPolicy(policy, {\n          afterPhase,\n        });\n      }\n    }\n  }\n\n  /**\n   * Send the provided httpRequest.\n   */\n  async sendRequest(request: PipelineRequest): Promise<PipelineResponse> {\n    return this.pipeline.sendRequest(this._httpClient, request);\n  }\n\n  /**\n   * Send an HTTP request that is populated using the provided OperationSpec.\n   * @typeParam T - The typed result of the request, based on the OperationSpec.\n   * @param operationArguments - The arguments that the HTTP request's templated values will be populated from.\n   * @param operationSpec - The OperationSpec to use to populate the httpRequest.\n   */\n  async sendOperationRequest<T>(\n    operationArguments: OperationArguments,\n    operationSpec: OperationSpec\n  ): Promise<T> {\n    const endpoint: string | undefined = operationSpec.baseUrl || this._endpoint;\n    if (!endpoint) {\n      throw new Error(\n        \"If operationSpec.baseUrl is not specified, then the ServiceClient must have a endpoint string property that contains the base URL to use.\"\n      );\n    }\n\n    // Templatized URLs sometimes reference properties on the ServiceClient child class,\n    // so we have to pass `this` below in order to search these properties if they're\n    // not part of OperationArguments\n    const url = getRequestUrl(endpoint, operationSpec, operationArguments, this);\n\n    const request: OperationRequest = createPipelineRequest({\n      url,\n    });\n    request.method = operationSpec.httpMethod;\n    const operationInfo = getOperationRequestInfo(request);\n    operationInfo.operationSpec = operationSpec;\n    operationInfo.operationArguments = operationArguments;\n\n    const contentType = operationSpec.contentType || this._requestContentType;\n    if (contentType && operationSpec.requestBody) {\n      request.headers.set(\"Content-Type\", contentType);\n    }\n\n    const options = operationArguments.options;\n    if (options) {\n      const requestOptions = options.requestOptions;\n\n      if (requestOptions) {\n        if (requestOptions.timeout) {\n          request.timeout = requestOptions.timeout;\n        }\n\n        if (requestOptions.onUploadProgress) {\n          request.onUploadProgress = requestOptions.onUploadProgress;\n        }\n\n        if (requestOptions.onDownloadProgress) {\n          request.onDownloadProgress = requestOptions.onDownloadProgress;\n        }\n\n        if (requestOptions.shouldDeserialize !== undefined) {\n          operationInfo.shouldDeserialize = requestOptions.shouldDeserialize;\n        }\n\n        if (requestOptions.allowInsecureConnection) {\n          request.allowInsecureConnection = true;\n        }\n      }\n\n      if (options.abortSignal) {\n        request.abortSignal = options.abortSignal;\n      }\n\n      if (options.tracingOptions) {\n        request.tracingOptions = options.tracingOptions;\n      }\n    }\n\n    if (this._allowInsecureConnection) {\n      request.allowInsecureConnection = true;\n    }\n\n    if (request.streamResponseStatusCodes === undefined) {\n      request.streamResponseStatusCodes = getStreamingResponseStatusCodes(operationSpec);\n    }\n\n    try {\n      const rawResponse = await this.sendRequest(request);\n      const flatResponse = flattenResponse(\n        rawResponse,\n        operationSpec.responses[rawResponse.status]\n      ) as T;\n      if (options?.onResponse) {\n        options.onResponse(rawResponse, flatResponse);\n      }\n      return flatResponse;\n    } catch (error: any) {\n      if (typeof error === \"object\" && error?.response) {\n        const rawResponse = error.response;\n        const flatResponse = flattenResponse(\n          rawResponse,\n          operationSpec.responses[error.statusCode] || operationSpec.responses[\"default\"]\n        );\n        error.details = flatResponse;\n        if (options?.onResponse) {\n          options.onResponse(rawResponse, flatResponse, error);\n        }\n      }\n      throw error;\n    }\n  }\n}\n\nfunction createDefaultPipeline(options: ServiceClientOptions): Pipeline {\n  const credentialScopes = getCredentialScopes(options);\n  const credentialOptions =\n    options.credential && credentialScopes\n      ? { credentialScopes, credential: options.credential }\n      : undefined;\n\n  return createClientPipeline({\n    ...options,\n    credentialOptions,\n  });\n}\n\nfunction getCredentialScopes(options: ServiceClientOptions): string | string[] | undefined {\n  if (options.credentialScopes) {\n    const scopes = options.credentialScopes;\n    return Array.isArray(scopes)\n      ? scopes.map((scope) => new URL(scope).toString())\n      : new URL(scopes).toString();\n  }\n\n  if (options.endpoint) {\n    return `${options.endpoint}/.default`;\n  }\n\n  if (options.baseUri) {\n    return `${options.baseUri}/.default`;\n  }\n\n  if (options.credential && !options.credentialScopes) {\n    throw new Error(\n      `When using credentials, the ServiceClientOptions must contain either a endpoint or a credentialScopes. Unable to create a bearerTokenAuthenticationPolicy`\n    );\n  }\n\n  return undefined;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AuthorizeRequestOnChallengeOptions } from \"@azure/core-rest-pipeline\";\nimport { logger as coreClientLogger } from \"./log\";\nimport { decodeStringToString } from \"./base64\";\n\n/**\n * Converts: `Bearer a=\"b\", c=\"d\", Bearer d=\"e\", f=\"g\"`.\n * Into: `[ { a: 'b', c: 'd' }, { d: 'e', f: 'g' } ]`.\n *\n * @internal\n */\nexport function parseCAEChallenge(challenges: string): any[] {\n  const bearerChallenges = `, ${challenges.trim()}`.split(\", Bearer \").filter((x) => x);\n  return bearerChallenges.map((challenge) => {\n    const challengeParts = `${challenge.trim()}, `.split('\", ').filter((x) => x);\n    const keyValuePairs = challengeParts.map((keyValue) =>\n      (([key, value]) => ({ [key]: value }))(keyValue.trim().split('=\"'))\n    );\n    // Key-value pairs to plain object:\n    return keyValuePairs.reduce((a, b) => ({ ...a, ...b }), {});\n  });\n}\n\n/**\n * CAE Challenge structure\n */\nexport interface CAEChallenge {\n  scope: string;\n  claims: string;\n}\n\n/**\n * This function can be used as a callback for the `bearerTokenAuthenticationPolicy` of `@azure/core-rest-pipeline`, to support CAE challenges:\n * [Continuous Access Evaluation](https://docs.microsoft.com/azure/active-directory/conditional-access/concept-continuous-access-evaluation).\n *\n * Call the `bearerTokenAuthenticationPolicy` with the following options:\n *\n * ```ts\n * import { bearerTokenAuthenticationPolicy } from \"@azure/core-rest-pipeline\";\n * import { authorizeRequestOnClaimChallenge } from \"@azure/core-client\";\n *\n * const bearerTokenAuthenticationPolicy = bearerTokenAuthenticationPolicy({\n *   authorizeRequestOnChallenge: authorizeRequestOnClaimChallenge\n * });\n * ```\n *\n * Once provided, the `bearerTokenAuthenticationPolicy` policy will internally handle Continuous Access Evaluation (CAE) challenges.\n * When it can't complete a challenge it will return the 401 (unauthorized) response from ARM.\n *\n * Example challenge with claims:\n *\n * ```\n * Bearer authorization_uri=\"https://login.windows-ppe.net/\", error=\"invalid_token\",\n * error_description=\"User session has been revoked\",\n * claims=\"eyJhY2Nlc3NfdG9rZW4iOnsibmJmIjp7ImVzc2VudGlhbCI6dHJ1ZSwgInZhbHVlIjoiMTYwMzc0MjgwMCJ9fX0=\"\n * ```\n */\nexport async function authorizeRequestOnClaimChallenge(\n  onChallengeOptions: AuthorizeRequestOnChallengeOptions\n): Promise<boolean> {\n  const { scopes, response } = onChallengeOptions;\n  const logger = onChallengeOptions.logger || coreClientLogger;\n\n  const challenge = response.headers.get(\"WWW-Authenticate\");\n  if (!challenge) {\n    logger.info(\n      `The WWW-Authenticate header was missing. Failed to perform the Continuous Access Evaluation authentication flow.`\n    );\n    return false;\n  }\n  const challenges: CAEChallenge[] = parseCAEChallenge(challenge) || [];\n\n  const parsedChallenge = challenges.find((x) => x.claims);\n  if (!parsedChallenge) {\n    logger.info(\n      `The WWW-Authenticate header was missing the necessary \"claims\" to perform the Continuous Access Evaluation authentication flow.`\n    );\n    return false;\n  }\n\n  const accessToken = await onChallengeOptions.getAccessToken(\n    parsedChallenge.scope ? [parsedChallenge.scope] : scopes,\n    {\n      claims: decodeStringToString(parsedChallenge.claims),\n    }\n  );\n\n  if (!accessToken) {\n    return false;\n  }\n\n  onChallengeOptions.request.headers.set(\"Authorization\", `Bearer ${accessToken.token}`);\n  return true;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  AuthorizeRequestOnChallengeOptions,\n  PipelineRequest,\n  PipelineResponse,\n} from \"@azure/core-rest-pipeline\";\n\nimport { GetTokenOptions } from \"@azure/core-auth\";\n\n/**\n * A set of constants used internally when processing requests.\n */\nconst Constants = {\n  DefaultScope: \"/.default\",\n  /**\n   * Defines constants for use with HTTP headers.\n   */\n  HeaderConstants: {\n    /**\n     * The Authorization header.\n     */\n    AUTHORIZATION: \"authorization\",\n  },\n};\n\n/**\n * Defines a callback to handle auth challenge for Storage APIs.\n * This implements the bearer challenge process described here: https://docs.microsoft.com/rest/api/storageservices/authorize-with-azure-active-directory#bearer-challenge\n * Handling has specific features for storage that departs to the general AAD challenge docs.\n **/\nexport const authorizeRequestOnTenantChallenge: (\n  challengeOptions: AuthorizeRequestOnChallengeOptions\n) => Promise<boolean> = async (challengeOptions) => {\n  const requestOptions = requestToOptions(challengeOptions.request);\n  const challenge = getChallenge(challengeOptions.response);\n  if (challenge) {\n    const challengeInfo: Challenge = parseChallenge(challenge);\n    const challengeScopes = buildScopes(challengeOptions, challengeInfo);\n    const tenantId = extractTenantId(challengeInfo);\n    const accessToken = await challengeOptions.getAccessToken(challengeScopes, {\n      ...requestOptions,\n      tenantId,\n    });\n\n    if (!accessToken) {\n      return false;\n    }\n\n    challengeOptions.request.headers.set(\n      Constants.HeaderConstants.AUTHORIZATION,\n      `Bearer ${accessToken.token}`\n    );\n    return true;\n  }\n  return false;\n};\n\n/**\n * Extracts the tenant id from the challenge information\n * The tenant id is contained in the authorization_uri as the first\n * path part.\n */\nfunction extractTenantId(challengeInfo: Challenge): string {\n  const parsedAuthUri = new URL(challengeInfo.authorization_uri);\n  const pathSegments = parsedAuthUri.pathname.split(\"/\");\n  const tenantId = pathSegments[1];\n\n  return tenantId;\n}\n\n/**\n * Builds the authentication scopes based on the information that comes in the\n * challenge information. Scopes url is present in the resource_id, if it is empty\n * we keep using the original scopes.\n */\nfunction buildScopes(\n  challengeOptions: AuthorizeRequestOnChallengeOptions,\n  challengeInfo: Challenge\n): string[] {\n  if (!challengeInfo.resource_uri) {\n    return challengeOptions.scopes;\n  }\n\n  const challengeScopes = new URL(challengeInfo.resource_uri);\n  challengeScopes.pathname = Constants.DefaultScope;\n  return [challengeScopes.toString()];\n}\n\n/**\n * We will retrieve the challenge only if the response status code was 401,\n * and if the response contained the header \"WWW-Authenticate\" with a non-empty value.\n */\nfunction getChallenge(response: PipelineResponse): string | undefined {\n  const challenge = response.headers.get(\"WWW-Authenticate\");\n  if (response.status === 401 && challenge) {\n    return challenge;\n  }\n  return;\n}\n\n/**\n * Challenge structure\n */\ninterface Challenge {\n  authorization_uri: string;\n  resource_uri?: string;\n}\n\n/**\n * Converts: `Bearer a=\"b\" c=\"d\"`.\n * Into: `[ { a: 'b', c: 'd' }]`.\n *\n * @internal\n */\nfunction parseChallenge(challenge: string): Challenge {\n  const bearerChallenge = challenge.slice(\"Bearer \".length);\n  const challengeParts = `${bearerChallenge.trim()} `.split(\" \").filter((x) => x);\n  const keyValuePairs = challengeParts.map((keyValue) =>\n    (([key, value]) => ({ [key]: value }))(keyValue.trim().split(\"=\"))\n  );\n  // Key-value pairs to plain object:\n  return keyValuePairs.reduce((a, b) => ({ ...a, ...b }), {} as Challenge);\n}\n\n/**\n * Extracts the options form a Pipeline Request for later re-use\n */\nfunction requestToOptions(request: PipelineRequest): GetTokenOptions {\n  return {\n    abortSignal: request.abortSignal,\n    requestOptions: {\n      timeout: request.timeout,\n    },\n    tracingOptions: request.tracingOptions,\n  };\n}\n"], "names": ["base64.decodeString", "base64.encodeByteArray", "RestError", "createPipelineFromOptions", "bearerTokenAuthenticationPolicy", "createDefaultHttpClient", "createClientLogger", "createPipelineRequest", "logger", "coreClientLogger"], "mappings": ";;;;;;;AAAA;AAYA;;;;AAIG;AACG,SAAU,eAAe,CAAC,KAAiB,EAAA;;;IAG/C,MAAM,WAAW,GAAG,KAAK,YAAY,MAAM,GAAG,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC;AAC/F,IAAA,OAAO,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACxC,CAAC;AAED;;;;AAIG;AACG,SAAU,YAAY,CAAC,KAAa,EAAA;IACxC,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AACtC,CAAC;AAED;;;;AAIG;AACG,SAAU,oBAAoB,CAAC,KAAa,EAAA;IAChD,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;AACjD;;ACxCA;AACA;AAcA;;AAEG;AACI,MAAM,WAAW,GAAG,IAAI;AAC/B;;AAEG;AACI,MAAM,WAAW,GAAG;;ACtB3B;AACA;AAUA;;;;;AAKG;AACa,SAAA,eAAe,CAAC,KAAc,EAAE,cAAuB,EAAA;IACrE,QACE,cAAc,KAAK,WAAW;AAC9B,QAAA,cAAc,KAAK,YAAY;SAC9B,OAAO,KAAK,KAAK,QAAQ;YACxB,OAAO,KAAK,KAAK,QAAQ;YACzB,OAAO,KAAK,KAAK,SAAS;YAC1B,CAAA,cAAc,aAAd,cAAc,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAd,cAAc,CAAE,KAAK,CAAC,iEAAiE,CAAC;gBACtF,IAAI;AACN,YAAA,KAAK,KAAK,SAAS;AACnB,YAAA,KAAK,KAAK,IAAI,CAAC,EACjB;AACJ,CAAC;AAED,MAAM,mBAAmB,GACvB,qKAAqK,CAAC;AAExK;;;;AAIG;AACG,SAAU,UAAU,CAAC,KAAa,EAAA;AACtC,IAAA,OAAO,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzC,CAAC;AAED,MAAM,cAAc,GAClB,gFAAgF,CAAC;AAEnF;;;;;;AAMG;AACG,SAAU,WAAW,CAAC,IAAY,EAAA;AACtC,IAAA,OAAO,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnC,CAAC;AAwBD;;;;;;;;;;AAUG;AACH,SAAS,sCAAsC,CAC7C,cAA0C,EAAA;IAE1C,MAAM,sBAAsB,GACvB,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,cAAc,CAAC,OAAO,GACtB,cAAc,CAAC,IAAI,CACvB,CAAC;IACF,IACE,cAAc,CAAC,eAAe;QAC9B,MAAM,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,CAAC,MAAM,KAAK,CAAC,EAC/D;AACA,QAAA,OAAO,cAAc,CAAC,cAAc,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;AAC9D,KAAA;AAAM,SAAA;QACL,OAAO,cAAc,CAAC,cAAc;AAClC,cACO,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,cAAc,CAAC,OAAO,KACzB,IAAI,EAAE,cAAc,CAAC,IAAI,EAAA,CAAA,GAE3B,sBAAsB,CAAC;AAC5B,KAAA;AACH,CAAC;AAED;;;;;;;AAOG;AACa,SAAA,eAAe,CAC7B,YAAmC,EACnC,YAA8C,EAAA;;AAE9C,IAAA,MAAM,aAAa,GAAG,YAAY,CAAC,aAAa,CAAC;;;AAIjD,IAAA,IAAI,YAAY,CAAC,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE;AAC1C,QAAA,OAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACK,aAAa,CAChB,EAAA,EAAA,IAAI,EAAE,YAAY,CAAC,UAAU,EAC7B,CAAA,CAAA;AACH,KAAA;AACD,IAAA,MAAM,UAAU,GAAG,YAAY,IAAI,YAAY,CAAC,UAAU,CAAC;AAC3D,IAAA,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,KAAA,IAAA,IAAV,UAAU,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAV,UAAU,CAAE,QAAQ,CAAC,CAAC;IACjD,MAAM,oBAAoB,GAAG,UAAU,KAAV,IAAA,IAAA,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,IAAI,CAAC,IAAI,CAAC;;IAGnD,IAAI,oBAAoB,KAAK,QAAQ,EAAE;AACrC,QAAA,OAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACK,aAAa,CAAA,EAAA,EAChB,QAAQ,EAAE,YAAY,CAAC,QAAQ,EAC/B,kBAAkB,EAAE,YAAY,CAAC,kBAAkB,EACnD,CAAA,CAAA;AACH,KAAA;AAED,IAAA,MAAM,eAAe,GACnB,CAAC,oBAAoB,KAAK,WAAW;AAClC,QAAA,UAA8B,CAAC,IAAI,CAAC,eAAe;AACtD,QAAA,EAAE,CAAC;IACL,MAAM,kBAAkB,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAC1D,CAAC,CAAC,KAAK,eAAe,CAAC,CAAC,CAAC,CAAC,cAAc,KAAK,EAAE,CAChD,CAAC;AACF,IAAA,IAAI,oBAAoB,KAAK,UAAU,IAAI,kBAAkB,EAAE;QAC7D,MAAM,aAAa,GACjB,CAAA,EAAA,GAAA,YAAY,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAK,EAA4C,CAAC;QAE3E,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;AAC9C,YAAA,IAAI,eAAe,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE;gBACvC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAA,EAAA,GAAA,YAAY,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAG,GAAG,CAAC,CAAC;AACrD,aAAA;AACF,SAAA;AAED,QAAA,IAAI,aAAa,EAAE;YACjB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;gBAC5C,aAAa,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;AACzC,aAAA;AACF,SAAA;AACD,QAAA,OAAO,UAAU;YACf,CAAC,YAAY,CAAC,UAAU;AACxB,YAAA,CAAC,aAAa;YACd,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,MAAM,KAAK,CAAC;AACxD,cAAE,IAAI;cACJ,aAAa,CAAC;AACnB,KAAA;AAED,IAAA,OAAO,sCAAsC,CAAC;QAC5C,IAAI,EAAE,YAAY,CAAC,UAAU;AAC7B,QAAA,OAAO,EAAE,aAAa;AACtB,QAAA,eAAe,EAAE,UAAU;QAC3B,cAAc,EAAE,eAAe,CAAC,YAAY,CAAC,UAAU,EAAE,oBAAoB,CAAC;AAC/E,KAAA,CAAC,CAAC;AACL;;ACvLA;AAqBA,MAAM,cAAc,CAAA;AAClB,IAAA,WAAA,CACkB,YAAuC,GAAA,EAAE,EACzC,KAAA,GAAiB,KAAK,EAAA;QADtB,IAAY,CAAA,YAAA,GAAZ,YAAY,CAA6B;QACzC,IAAK,CAAA,KAAA,GAAL,KAAK,CAAiB;KACpC;AAEJ;;AAEG;AACH,IAAA,mBAAmB,CAAC,MAAc,EAAE,KAAU,EAAE,UAAkB,EAAA;AAChE,QAAA,MAAM,cAAc,GAAG,CACrB,cAAuC,EACvC,eAAoB,KACX;AACT,YAAA,MAAM,IAAI,KAAK,CACb,CAAA,CAAA,EAAI,UAAU,CAAA,cAAA,EAAiB,KAAK,CAAA,iCAAA,EAAoC,cAAc,CAAA,GAAA,EAAM,eAAe,CAAA,CAAA,CAAG,CAC/G,CAAC;AACJ,SAAC,CAAC;QACF,IAAI,MAAM,CAAC,WAAW,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE;YAC/D,MAAM,EACJ,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,UAAU,EACV,OAAO,EACP,WAAW,GACZ,GAAG,MAAM,CAAC,WAAW,CAAC;AACvB,YAAA,IAAI,gBAAgB,KAAK,SAAS,IAAI,KAAK,IAAI,gBAAgB,EAAE;AAC/D,gBAAA,cAAc,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;AACtD,aAAA;AACD,YAAA,IAAI,gBAAgB,KAAK,SAAS,IAAI,KAAK,IAAI,gBAAgB,EAAE;AAC/D,gBAAA,cAAc,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;AACtD,aAAA;AACD,YAAA,IAAI,gBAAgB,KAAK,SAAS,IAAI,KAAK,GAAG,gBAAgB,EAAE;AAC9D,gBAAA,cAAc,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;AACtD,aAAA;AACD,YAAA,IAAI,gBAAgB,KAAK,SAAS,IAAI,KAAK,GAAG,gBAAgB,EAAE;AAC9D,gBAAA,cAAc,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;AACtD,aAAA;YACD,IAAI,QAAQ,KAAK,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,QAAQ,EAAE;AACrD,gBAAA,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;AACtC,aAAA;YACD,IAAI,SAAS,KAAK,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,SAAS,EAAE;AACvD,gBAAA,cAAc,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;AACxC,aAAA;YACD,IAAI,QAAQ,KAAK,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,QAAQ,EAAE;AACrD,gBAAA,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;AACtC,aAAA;YACD,IAAI,SAAS,KAAK,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,SAAS,EAAE;AACvD,gBAAA,cAAc,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;AACxC,aAAA;YACD,IAAI,UAAU,KAAK,SAAS,IAAI,KAAK,GAAG,UAAU,KAAK,CAAC,EAAE;AACxD,gBAAA,cAAc,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;AAC1C,aAAA;AACD,YAAA,IAAI,OAAO,EAAE;AACX,gBAAA,MAAM,OAAO,GAAW,OAAO,OAAO,KAAK,QAAQ,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;AACpF,gBAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;AAC9D,oBAAA,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AACpC,iBAAA;AACF,aAAA;AACD,YAAA,IACE,WAAW;gBACX,KAAK,CAAC,IAAI,CAAC,CAAC,IAAS,EAAE,CAAS,EAAE,EAAc,KAAK,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAC5E;AACA,gBAAA,cAAc,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;AAC5C,aAAA;AACF,SAAA;KACF;AAED;;;;;;;;;;;;AAYG;AACH,IAAA,SAAS,CACP,MAAc,EACd,MAAW,EACX,UAAmB,EACnB,OAAA,GAA6B,EAAE,GAAG,EAAE,EAAE,EAAE,EAAA;;AAExC,QAAA,MAAM,cAAc,GAA8B;AAChD,YAAA,GAAG,EAAE;gBACH,QAAQ,EAAE,MAAA,OAAO,CAAC,GAAG,CAAC,QAAQ,mCAAI,EAAE;gBACpC,WAAW,EAAE,MAAA,OAAO,CAAC,GAAG,CAAC,WAAW,mCAAI,KAAK;gBAC7C,UAAU,EAAE,MAAA,OAAO,CAAC,GAAG,CAAC,UAAU,mCAAI,WAAW;AAClD,aAAA;SACF,CAAC;QACF,IAAI,OAAO,GAAQ,EAAE,CAAC;AACtB,QAAA,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,IAAc,CAAC;QAC9C,IAAI,CAAC,UAAU,EAAE;AACf,YAAA,UAAU,GAAG,MAAM,CAAC,cAAe,CAAC;AACrC,SAAA;QACD,IAAI,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;YAC5C,OAAO,GAAG,EAAE,CAAC;AACd,SAAA;QAED,IAAI,MAAM,CAAC,UAAU,EAAE;AACrB,YAAA,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC;AAC9B,SAAA;;;;;;;;;;AAYD,QAAA,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;AAEtC,QAAA,IAAI,QAAQ,IAAI,QAAQ,IAAI,MAAM,KAAK,SAAS,EAAE;AAChD,YAAA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,CAAA,qBAAA,CAAuB,CAAC,CAAC;AACvD,SAAA;AACD,QAAA,IAAI,QAAQ,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI,CAAC,EAAE;AACtE,YAAA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,CAAA,6BAAA,CAA+B,CAAC,CAAC;AAC/D,SAAA;QACD,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,IAAI,MAAM,KAAK,IAAI,EAAE;AACtD,YAAA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,CAAA,gBAAA,CAAkB,CAAC,CAAC;AAClD,SAAA;AAED,QAAA,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI,EAAE;YAC3C,OAAO,GAAG,MAAM,CAAC;AAClB,SAAA;AAAM,aAAA;YACL,IAAI,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE;gBACvC,OAAO,GAAG,MAAM,CAAC;AAClB,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,+CAA+C,CAAC,KAAK,IAAI,EAAE;gBACrF,OAAO,GAAG,mBAAmB,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;AAC/D,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;gBAC/C,MAAM,UAAU,GAAG,MAAoB,CAAC;AACxC,gBAAA,OAAO,GAAG,iBAAiB,CAAC,UAAU,EAAE,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AAChF,aAAA;iBAAM,IACL,UAAU,CAAC,KAAK,CAAC,sDAAsD,CAAC,KAAK,IAAI,EACjF;gBACA,OAAO,GAAG,kBAAkB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;AAC9D,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;AACpD,gBAAA,OAAO,GAAG,sBAAsB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AACtD,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;AACpD,gBAAA,OAAO,GAAG,sBAAsB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AACtD,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;gBACnD,OAAO,GAAG,qBAAqB,CAC7B,IAAI,EACJ,MAAwB,EACxB,MAAM,EACN,UAAU,EACV,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EACnB,cAAc,CACf,CAAC;AACH,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,IAAI,EAAE;gBACrD,OAAO,GAAG,uBAAuB,CAC/B,IAAI,EACJ,MAA0B,EAC1B,MAAM,EACN,UAAU,EACV,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EACnB,cAAc,CACf,CAAC;AACH,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;gBACpD,OAAO,GAAG,sBAAsB,CAC9B,IAAI,EACJ,MAAyB,EACzB,MAAM,EACN,UAAU,EACV,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EACnB,cAAc,CACf,CAAC;AACH,aAAA;AACF,SAAA;AACD,QAAA,OAAO,OAAO,CAAC;KAChB;AAED;;;;;;;;;;;;AAYG;AACH,IAAA,WAAW,CACT,MAAc,EACd,YAAiB,EACjB,UAAkB,EAClB,OAAA,GAA6B,EAAE,GAAG,EAAE,EAAE,EAAE,EAAA;;AAExC,QAAA,MAAM,cAAc,GAA8B;AAChD,YAAA,GAAG,EAAE;gBACH,QAAQ,EAAE,MAAA,OAAO,CAAC,GAAG,CAAC,QAAQ,mCAAI,EAAE;gBACpC,WAAW,EAAE,MAAA,OAAO,CAAC,GAAG,CAAC,WAAW,mCAAI,KAAK;gBAC7C,UAAU,EAAE,MAAA,OAAO,CAAC,GAAG,CAAC,UAAU,mCAAI,WAAW;AAClD,aAAA;AACD,YAAA,uBAAuB,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,uBAAuB,mCAAI,KAAK;SAClE,CAAC;AACF,QAAA,IAAI,YAAY,KAAK,SAAS,IAAI,YAAY,KAAK,IAAI,EAAE;AACvD,YAAA,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;;;;gBAIzE,YAAY,GAAG,EAAE,CAAC;AACnB,aAAA;;AAED,YAAA,IAAI,MAAM,CAAC,YAAY,KAAK,SAAS,EAAE;AACrC,gBAAA,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;AACpC,aAAA;AACD,YAAA,OAAO,YAAY,CAAC;AACrB,SAAA;AAED,QAAA,IAAI,OAAY,CAAC;AACjB,QAAA,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QACpC,IAAI,CAAC,UAAU,EAAE;AACf,YAAA,UAAU,GAAG,MAAM,CAAC,cAAe,CAAC;AACrC,SAAA;QAED,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;AAC7C,YAAA,OAAO,GAAG,wBAAwB,CAChC,IAAI,EACJ,MAAyB,EACzB,YAAY,EACZ,UAAU,EACV,cAAc,CACf,CAAC;AACH,SAAA;AAAM,aAAA;YACL,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,gBAAA,MAAM,UAAU,GAAG,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC;AACjD;;;;AAIG;AACH,gBAAA,IAAI,YAAY,CAAC,WAAW,CAAC,KAAK,SAAS,IAAI,YAAY,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE;AACrF,oBAAA,YAAY,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;AACzC,iBAAA;AACF,aAAA;YAED,IAAI,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;AAC1C,gBAAA,OAAO,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC;AACnC,gBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE;oBAClB,OAAO,GAAG,YAAY,CAAC;AACxB,iBAAA;AACF,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI,EAAE;gBAClD,IAAI,YAAY,KAAK,MAAM,EAAE;oBAC3B,OAAO,GAAG,IAAI,CAAC;AAChB,iBAAA;qBAAM,IAAI,YAAY,KAAK,OAAO,EAAE;oBACnC,OAAO,GAAG,KAAK,CAAC;AACjB,iBAAA;AAAM,qBAAA;oBACL,OAAO,GAAG,YAAY,CAAC;AACxB,iBAAA;AACF,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,kDAAkD,CAAC,KAAK,IAAI,EAAE;gBACxF,OAAO,GAAG,YAAY,CAAC;AACxB,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,oCAAoC,CAAC,KAAK,IAAI,EAAE;AAC1E,gBAAA,OAAO,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC;AAClC,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;AACnD,gBAAA,OAAO,GAAG,cAAc,CAAC,YAAY,CAAC,CAAC;AACxC,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;AACpD,gBAAA,OAAO,GAAGA,YAAmB,CAAC,YAAY,CAAC,CAAC;AAC7C,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;AACpD,gBAAA,OAAO,GAAG,oBAAoB,CAAC,YAAY,CAAC,CAAC;AAC9C,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;AACnD,gBAAA,OAAO,GAAG,uBAAuB,CAC/B,IAAI,EACJ,MAAwB,EACxB,YAAY,EACZ,UAAU,EACV,cAAc,CACf,CAAC;AACH,aAAA;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,IAAI,EAAE;AACrD,gBAAA,OAAO,GAAG,yBAAyB,CACjC,IAAI,EACJ,MAA0B,EAC1B,YAAY,EACZ,UAAU,EACV,cAAc,CACf,CAAC;AACH,aAAA;AACF,SAAA;QAED,IAAI,MAAM,CAAC,UAAU,EAAE;AACrB,YAAA,OAAO,GAAG,MAAM,CAAC,YAAY,CAAC;AAC/B,SAAA;AAED,QAAA,OAAO,OAAO,CAAC;KAChB;AACF,CAAA;AAED;;;;AAIG;SACa,gBAAgB,CAC9B,eAAuC,EAAE,EACzC,QAAiB,KAAK,EAAA;AAEtB,IAAA,OAAO,IAAI,cAAc,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;AACjD,CAAC;AAED,SAAS,OAAO,CAAC,GAAW,EAAE,EAAU,EAAA;AACtC,IAAA,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;AACrB,IAAA,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;AAC1C,QAAA,EAAE,GAAG,CAAC;AACP,KAAA;IACD,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,iBAAiB,CAAC,MAAkB,EAAA;IAC3C,IAAI,CAAC,MAAM,EAAE;AACX,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;AACD,IAAA,IAAI,EAAE,MAAM,YAAY,UAAU,CAAC,EAAE;AACnC,QAAA,MAAM,IAAI,KAAK,CAAC,CAAA,uEAAA,CAAyE,CAAC,CAAC;AAC5F,KAAA;;IAED,MAAM,GAAG,GAAGC,eAAsB,CAAC,MAAM,CAAC,CAAC;;IAE3C,OAAO,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACnE,CAAC;AAED,SAAS,oBAAoB,CAAC,GAAW,EAAA;IACvC,IAAI,CAAC,GAAG,EAAE;AACR,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;IACD,IAAI,GAAG,IAAI,OAAO,GAAG,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;AAC5C,QAAA,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;AACxF,KAAA;;AAED,IAAA,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;;AAEhD,IAAA,OAAOD,YAAmB,CAAC,GAAG,CAAC,CAAC;AAClC,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAwB,EAAA;IAClD,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,IAAI,YAAY,GAAG,EAAE,CAAC;AACtB,IAAA,IAAI,IAAI,EAAE;QACR,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAEjC,QAAA,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE;AAC3B,YAAA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;AACzC,gBAAA,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;AACvD,aAAA;AAAM,iBAAA;gBACL,YAAY,IAAI,IAAI,CAAC;AACrB,gBAAA,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC3B,YAAY,GAAG,EAAE,CAAC;AACnB,aAAA;AACF,SAAA;AACF,KAAA;AAED,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,cAAc,CAAC,CAAgB,EAAA;IACtC,IAAI,CAAC,CAAC,EAAE;AACN,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;AAED,IAAA,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;AACnC,QAAA,CAAC,GAAG,IAAI,IAAI,CAAC,CAAW,CAAC,CAAC;AAC3B,KAAA;IACD,OAAO,IAAI,CAAC,KAAK,CAAE,CAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;AAClD,CAAC;AAED,SAAS,cAAc,CAAC,CAAS,EAAA;IAC/B,IAAI,CAAC,CAAC,EAAE;AACN,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;AACD,IAAA,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,mBAAmB,CAAC,QAAgB,EAAE,UAAkB,EAAE,KAAU,EAAA;AAC3E,IAAA,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;QACzC,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;AACxC,YAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,MAAM,IAAI,KAAK,CAAC,CAAA,EAAG,UAAU,CAAe,YAAA,EAAA,KAAK,CAA0B,wBAAA,CAAA,CAAC,CAAC;AAC9E,aAAA;AACF,SAAA;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;AAC/C,YAAA,IAAI,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;gBACvC,MAAM,IAAI,KAAK,CAAC,CAAA,EAAG,UAAU,CAAgB,aAAA,EAAA,KAAK,CAA2B,yBAAA,CAAA,CAAC,CAAC;AAChF,aAAA;AACF,SAAA;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;AAC7C,YAAA,IAAI,EAAE,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;gBAChE,MAAM,IAAI,KAAK,CACb,CAAA,EAAG,UAAU,CAAgB,aAAA,EAAA,KAAK,CAA4C,0CAAA,CAAA,CAC/E,CAAC;AACH,aAAA;AACF,SAAA;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI,EAAE;AAChD,YAAA,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE;gBAC9B,MAAM,IAAI,KAAK,CAAC,CAAA,EAAG,UAAU,CAAe,YAAA,EAAA,KAAK,CAA2B,yBAAA,CAAA,CAAC,CAAC;AAC/E,aAAA;AACF,SAAA;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;AAC/C,YAAA,MAAM,UAAU,GAAG,OAAO,KAAK,CAAC;YAChC,IACE,UAAU,KAAK,QAAQ;AACvB,gBAAA,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU;AAChC,gBAAA,EAAE,KAAK,YAAY,WAAW,CAAC;AAC/B,gBAAA,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;;AAE1B,gBAAA,EAAE,CAAC,OAAO,IAAI,KAAK,UAAU,IAAI,OAAO,IAAI,KAAK,QAAQ,KAAK,KAAK,YAAY,IAAI,CAAC;gBACpF,UAAU,KAAK,UAAU,EACzB;AACA,gBAAA,MAAM,IAAI,KAAK,CACb,GAAG,UAAU,CAAA,6GAAA,CAA+G,CAC7H,CAAC;AACH,aAAA;AACF,SAAA;AACF,KAAA;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,iBAAiB,CAAC,UAAkB,EAAE,aAAyB,EAAE,KAAU,EAAA;IAClF,IAAI,CAAC,aAAa,EAAE;AAClB,QAAA,MAAM,IAAI,KAAK,CACb,qDAAqD,UAAU,CAAA,iBAAA,CAAmB,CACnF,CAAC;AACH,KAAA;IACD,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,KAAI;AAC5C,QAAA,IAAI,OAAO,IAAI,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;YACtC,OAAO,IAAI,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,WAAW,EAAE,CAAC;AACnD,SAAA;QACD,OAAO,IAAI,KAAK,KAAK,CAAC;AACxB,KAAC,CAAC,CAAC;IACH,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,MAAM,IAAI,KAAK,CACb,CAAG,EAAA,KAAK,6BAA6B,UAAU,CAAA,wBAAA,EAA2B,IAAI,CAAC,SAAS,CACtF,aAAa,CACd,CAAA,CAAA,CAAG,CACL,CAAC;AACH,KAAA;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,sBAAsB,CAAC,UAAkB,EAAE,KAAU,EAAA;AAC5D,IAAA,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE;AACzC,QAAA,IAAI,EAAE,KAAK,YAAY,UAAU,CAAC,EAAE;AAClC,YAAA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,CAAA,4BAAA,CAA8B,CAAC,CAAC;AAC9D,SAAA;AACD,QAAA,KAAK,GAAGC,eAAsB,CAAC,KAAK,CAAC,CAAC;AACvC,KAAA;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,sBAAsB,CAAC,UAAkB,EAAE,KAAU,EAAA;AAC5D,IAAA,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE;AACzC,QAAA,IAAI,EAAE,KAAK,YAAY,UAAU,CAAC,EAAE;AAClC,YAAA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,CAAA,4BAAA,CAA8B,CAAC,CAAC;AAC9D,SAAA;AACD,QAAA,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAClC,KAAA;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,kBAAkB,CAAC,QAAgB,EAAE,KAAU,EAAE,UAAkB,EAAA;AAC1E,IAAA,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE;QACzC,IAAI,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;AACtC,YAAA,IACE,EACE,KAAK,YAAY,IAAI;iBACpB,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACnE,EACD;AACA,gBAAA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,CAAA,0DAAA,CAA4D,CAAC,CAAC;AAC5F,aAAA;YACD,KAAK;AACH,gBAAA,KAAK,YAAY,IAAI;sBACjB,KAAK,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;AACtC,sBAAE,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACtD,SAAA;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;AACjD,YAAA,IACE,EACE,KAAK,YAAY,IAAI;iBACpB,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACnE,EACD;AACA,gBAAA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,CAAA,0DAAA,CAA4D,CAAC,CAAC;AAC5F,aAAA;YACD,KAAK,GAAG,KAAK,YAAY,IAAI,GAAG,KAAK,CAAC,WAAW,EAAE,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;AACrF,SAAA;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,oBAAoB,CAAC,KAAK,IAAI,EAAE;AACxD,YAAA,IACE,EACE,KAAK,YAAY,IAAI;iBACpB,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACnE,EACD;AACA,gBAAA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,CAAA,2DAAA,CAA6D,CAAC,CAAC;AAC7F,aAAA;YACD,KAAK,GAAG,KAAK,YAAY,IAAI,GAAG,KAAK,CAAC,WAAW,EAAE,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;AACrF,SAAA;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;AACjD,YAAA,IACE,EACE,KAAK,YAAY,IAAI;iBACpB,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACnE,EACD;AACA,gBAAA,MAAM,IAAI,KAAK,CACb,CAAA,EAAG,UAAU,CAAqE,mEAAA,CAAA;AAChF,oBAAA,CAAA,iDAAA,CAAmD,CACtD,CAAC;AACH,aAAA;AACD,YAAA,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;AAC/B,SAAA;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;AACjD,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;gBACtB,MAAM,IAAI,KAAK,CACb,CAAA,EAAG,UAAU,CAAsD,mDAAA,EAAA,KAAK,CAAI,EAAA,CAAA,CAC7E,CAAC;AACH,aAAA;AACF,SAAA;AACF,KAAA;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,qBAAqB,CAC5B,UAAsB,EACtB,MAAsB,EACtB,MAAW,EACX,UAAkB,EAClB,KAAc,EACd,OAAkC,EAAA;;AAElC,IAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AAC1B,QAAA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,CAAA,uBAAA,CAAyB,CAAC,CAAC;AACzD,KAAA;AACD,IAAA,IAAI,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;AACtC,IAAA,IAAI,CAAC,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;QACnD,MAAM,IAAI,KAAK,CACb,CAAwD,sDAAA,CAAA;YACtD,CAA0C,uCAAA,EAAA,UAAU,CAAG,CAAA,CAAA,CAC1D,CAAC;AACH,KAAA;;;;AAID,IAAA,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE;AACvE,QAAA,WAAW,GAAG,CAAA,EAAA,GAAA,UAAU,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,WAAW,CAAC;AAClF,KAAA;IACD,MAAM,SAAS,GAAG,EAAE,CAAC;AACrB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,QAAA,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;AAC1F,QAAA,IAAI,KAAK,IAAI,WAAW,CAAC,YAAY,EAAE;AACrC,YAAA,MAAM,QAAQ,GAAG,WAAW,CAAC,kBAAkB;AAC7C,kBAAE,CAAA,MAAA,EAAS,WAAW,CAAC,kBAAkB,CAAE,CAAA;kBACzC,OAAO,CAAC;AACZ,YAAA,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE;AACzC,gBAAA,SAAS,CAAC,CAAC,CAAC,GAAQ,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,eAAe,CAAE,CAAC;AACtC,gBAAA,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC;AACtE,aAAA;AAAM,iBAAA;AACL,gBAAA,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AAClB,gBAAA,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,eAAe,CAAC;AACvD,gBAAA,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC;AACtE,aAAA;AACF,SAAA;AAAM,aAAA;AACL,YAAA,SAAS,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC;AAChC,SAAA;AACF,KAAA;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,uBAAuB,CAC9B,UAAsB,EACtB,MAAwB,EACxB,MAAW,EACX,UAAkB,EAClB,KAAc,EACd,OAAkC,EAAA;AAElC,IAAA,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAC9B,QAAA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,CAAA,wBAAA,CAA0B,CAAC,CAAC;AAC1D,KAAA;AACD,IAAA,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;AACpC,IAAA,IAAI,CAAC,SAAS,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;QAC/C,MAAM,IAAI,KAAK,CACb,CAA2D,yDAAA,CAAA;YACzD,CAA0C,uCAAA,EAAA,UAAU,CAAG,CAAA,CAAA,CAC1D,CAAC;AACH,KAAA;IACD,MAAM,cAAc,GAA2B,EAAE,CAAC;IAClD,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AACrC,QAAA,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;;AAE1F,QAAA,cAAc,CAAC,GAAG,CAAC,GAAG,iBAAiB,CAAC,SAAS,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AACrF,KAAA;;AAGD,IAAA,IAAI,KAAK,IAAI,MAAM,CAAC,YAAY,EAAE;AAChC,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,kBAAkB,GAAG,CAAS,MAAA,EAAA,MAAM,CAAC,kBAAkB,CAAA,CAAE,GAAG,OAAO,CAAC;QAC5F,MAAM,MAAM,GAAG,cAAc,CAAC;AAC9B,QAAA,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;AAC1D,QAAA,OAAO,MAAM,CAAC;AACf,KAAA;AAED,IAAA,OAAO,cAAc,CAAC;AACxB,CAAC;AAED;;;;;AAKG;AACH,SAAS,2BAA2B,CAClC,UAAsB,EACtB,MAAuB,EACvB,UAAkB,EAAA;AAElB,IAAA,MAAM,oBAAoB,GAAG,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;IAE9D,IAAI,CAAC,oBAAoB,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE;QAClD,MAAM,WAAW,GAAG,uBAAuB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QAC5E,OAAO,WAAW,aAAX,WAAW,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAX,WAAW,CAAE,IAAI,CAAC,oBAAoB,CAAC;AAC/C,KAAA;AAED,IAAA,OAAO,oBAAoB,CAAC;AAC9B,CAAC;AAED;;;;;AAKG;AACH,SAAS,uBAAuB,CAC9B,UAAsB,EACtB,MAAuB,EACvB,UAAkB,EAAA;AAElB,IAAA,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;IACxC,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,MAAM,IAAI,KAAK,CACb,yBAAyB,UAAU,CAAA,iCAAA,EAAoC,IAAI,CAAC,SAAS,CACnF,MAAM,EACN,SAAS,EACT,CAAC,CACF,CAAA,EAAA,CAAI,CACN,CAAC;AACH,KAAA;AAED,IAAA,OAAO,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AAC5C,CAAC;AAED;;;;AAIG;AACH,SAAS,sBAAsB,CAC7B,UAAsB,EACtB,MAAuB,EACvB,UAAkB,EAAA;AAElB,IAAA,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC;IAC7C,IAAI,CAAC,UAAU,EAAE;QACf,MAAM,WAAW,GAAG,uBAAuB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QAC5E,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,CAAmD,gDAAA,EAAA,MAAM,CAAC,IAAI,CAAC,SAAS,CAAI,EAAA,CAAA,CAAC,CAAC;AAC/F,SAAA;QACD,UAAU,GAAG,WAAW,KAAA,IAAA,IAAX,WAAW,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAX,WAAW,CAAE,IAAI,CAAC,eAAe,CAAC;QAC/C,IAAI,CAAC,UAAU,EAAE;YACf,MAAM,IAAI,KAAK,CACb,CAAqD,mDAAA,CAAA;AACnD,gBAAA,CAAA,QAAA,EAAW,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CACpC,WAAA,EAAA,MAAM,CAAC,IAAI,CAAC,SACd,CAAA,cAAA,EAAiB,UAAU,CAAA,EAAA,CAAI,CAClC,CAAC;AACH,SAAA;AACF,KAAA;AAED,IAAA,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,SAAS,sBAAsB,CAC7B,UAAsB,EACtB,MAAuB,EACvB,MAAW,EACX,UAAkB,EAClB,KAAc,EACd,OAAkC,EAAA;AAElC,IAAA,IAAI,sCAAsC,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE;QAC9D,MAAM,GAAG,oBAAoB,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;AACzE,KAAA;AAED,IAAA,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI,EAAE;QAC3C,MAAM,OAAO,GAAQ,EAAE,CAAC;QACxB,MAAM,UAAU,GAAG,sBAAsB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QAC1E,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACzC,YAAA,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;YACvC,IAAI,cAAc,CAAC,QAAQ,EAAE;gBAC3B,SAAS;AACV,aAAA;AAED,YAAA,IAAI,QAA4B,CAAC;YACjC,IAAI,YAAY,GAAQ,OAAO,CAAC;YAChC,IAAI,UAAU,CAAC,KAAK,EAAE;gBACpB,IAAI,cAAc,CAAC,YAAY,EAAE;AAC/B,oBAAA,QAAQ,GAAG,cAAc,CAAC,OAAO,CAAC;AACnC,iBAAA;AAAM,qBAAA;oBACL,QAAQ,GAAG,cAAc,CAAC,cAAc,IAAI,cAAc,CAAC,OAAO,CAAC;AACpE,iBAAA;AACF,aAAA;AAAM,iBAAA;gBACL,MAAM,KAAK,GAAG,kBAAkB,CAAC,cAAc,CAAC,cAAe,CAAC,CAAC;AACjE,gBAAA,QAAQ,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;AAEvB,gBAAA,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE;AAC5B,oBAAA,MAAM,WAAW,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;oBAC3C,IACE,CAAC,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI;AAClD,yBAAC,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,SAAS,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI;AACjD,4BAAA,cAAc,CAAC,YAAY,KAAK,SAAS,CAAC,EAC5C;AACA,wBAAA,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;AAC7B,qBAAA;AACD,oBAAA,YAAY,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;AACvC,iBAAA;AACF,aAAA;AAED,YAAA,IAAI,YAAY,KAAK,SAAS,IAAI,YAAY,KAAK,IAAI,EAAE;AACvD,gBAAA,IAAI,KAAK,IAAI,MAAM,CAAC,YAAY,EAAE;AAChC,oBAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,kBAAkB;AACxC,0BAAE,CAAA,MAAA,EAAS,MAAM,CAAC,kBAAkB,CAAE,CAAA;0BACpC,OAAO,CAAC;AACZ,oBAAA,YAAY,CAAC,WAAW,CAAC,GACpB,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,YAAY,CAAC,WAAW,CAAC,CAC5B,EAAA,EAAA,CAAC,QAAQ,GAAG,MAAM,CAAC,YAAY,GAChC,CAAC;AACH,iBAAA;AACD,gBAAA,MAAM,kBAAkB,GACtB,cAAc,CAAC,cAAc,KAAK,EAAE;AAClC,sBAAE,UAAU,GAAG,GAAG,GAAG,cAAc,CAAC,cAAc;sBAChD,UAAU,CAAC;AAEjB,gBAAA,IAAI,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC9B,MAAM,wBAAwB,GAAG,sCAAsC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AAC5F,gBAAA,IACE,wBAAwB;oBACxB,wBAAwB,CAAC,UAAU,KAAK,GAAG;qBAC1C,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI,CAAC,EACnD;AACA,oBAAA,WAAW,GAAG,MAAM,CAAC,cAAc,CAAC;AACrC,iBAAA;AAED,gBAAA,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAC1C,cAAc,EACd,WAAW,EACX,kBAAkB,EAClB,OAAO,CACR,CAAC;gBACF,IAAI,eAAe,KAAK,SAAS,IAAI,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,IAAI,EAAE;AAChF,oBAAA,MAAM,KAAK,GAAG,iBAAiB,CAAC,cAAc,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AACjF,oBAAA,IAAI,KAAK,IAAI,cAAc,CAAC,cAAc,EAAE;;;;wBAI1C,YAAY,CAAC,WAAW,CAAC,GAAG,YAAY,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;wBAC5D,YAAY,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,GAAG,eAAe,CAAC;AACvD,qBAAA;AAAM,yBAAA,IAAI,KAAK,IAAI,cAAc,CAAC,YAAY,EAAE;AAC/C,wBAAA,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,cAAc,CAAC,cAAe,GAAG,KAAK,EAAE,CAAC;AACtE,qBAAA;AAAM,yBAAA;AACL,wBAAA,YAAY,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;AAChC,qBAAA;AACF,iBAAA;AACF,aAAA;AACF,SAAA;QAED,MAAM,0BAA0B,GAAG,2BAA2B,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;AAC/F,QAAA,IAAI,0BAA0B,EAAE;YAC9B,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC1C,YAAA,KAAK,MAAM,cAAc,IAAI,MAAM,EAAE;AACnC,gBAAA,MAAM,oBAAoB,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,cAAc,CAAC,CAAC;AAC5E,gBAAA,IAAI,oBAAoB,EAAE;oBACxB,OAAO,CAAC,cAAc,CAAC,GAAG,UAAU,CAAC,SAAS,CAC5C,0BAA0B,EAC1B,MAAM,CAAC,cAAc,CAAC,EACtB,UAAU,GAAG,IAAI,GAAG,cAAc,GAAG,IAAI,EACzC,OAAO,CACR,CAAC;AACH,iBAAA;AACF,aAAA;AACF,SAAA;AAED,QAAA,OAAO,OAAO,CAAC;AAChB,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,iBAAiB,CACxB,cAAsB,EACtB,eAAoB,EACpB,KAAc,EACd,OAAkC,EAAA;AAElC,IAAA,IAAI,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE;AAC1C,QAAA,OAAO,eAAe,CAAC;AACxB,KAAA;AAED,IAAA,MAAM,QAAQ,GAAG,cAAc,CAAC,kBAAkB;AAChD,UAAE,CAAA,MAAA,EAAS,cAAc,CAAC,kBAAkB,CAAE,CAAA;UAC5C,OAAO,CAAC;IACZ,MAAM,YAAY,GAAG,EAAE,CAAC,QAAQ,GAAG,cAAc,CAAC,YAAY,EAAE,CAAC;AAEjE,IAAA,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AACpD,QAAA,IAAI,eAAe,CAAC,WAAW,CAAC,EAAE;AAChC,YAAA,OAAO,eAAe,CAAC;AACxB,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,MAAM,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAa,eAAe,CAAE,CAAC;AAC3C,YAAA,MAAM,CAAC,WAAW,CAAC,GAAG,YAAY,CAAC;AACnC,YAAA,OAAO,MAAM,CAAC;AACf,SAAA;AACF,KAAA;IACD,MAAM,MAAM,GAAQ,EAAE,CAAC;IACvB,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,eAAe,CAAC;AACjD,IAAA,MAAM,CAAC,WAAW,CAAC,GAAG,YAAY,CAAC;AACnC,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,oBAAoB,CAAC,YAAoB,EAAE,OAAkC,EAAA;AACpF,IAAA,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;AACtE,CAAC;AAED,SAAS,wBAAwB,CAC/B,UAAsB,EACtB,MAAuB,EACvB,YAAiB,EACjB,UAAkB,EAClB,OAAkC,EAAA;;IAElC,MAAM,UAAU,GAAG,CAAA,EAAA,GAAA,OAAO,CAAC,GAAG,CAAC,UAAU,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,WAAW,CAAC;AACzD,IAAA,IAAI,sCAAsC,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE;QAC9D,MAAM,GAAG,oBAAoB,CAAC,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,gBAAgB,CAAC,CAAC;AACnF,KAAA;IAED,MAAM,UAAU,GAAG,sBAAsB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;IAC1E,IAAI,QAAQ,GAA2B,EAAE,CAAC;IAC1C,MAAM,oBAAoB,GAAa,EAAE,CAAC;IAE1C,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACzC,QAAA,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;QACvC,MAAM,KAAK,GAAG,kBAAkB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,cAAe,CAAC,CAAC;QAClE,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,cAAc,CAAC;QACnE,IAAI,kBAAkB,GAAG,UAAU,CAAC;AACpC,QAAA,IAAI,cAAc,KAAK,EAAE,IAAI,cAAc,KAAK,SAAS,EAAE;AACzD,YAAA,kBAAkB,GAAG,UAAU,GAAG,GAAG,GAAG,cAAc,CAAC;AACxD,SAAA;AAED,QAAA,MAAM,sBAAsB,GAAI,cAAmC,CAAC,sBAAsB,CAAC;AAC3F,QAAA,IAAI,sBAAsB,EAAE;YAC1B,MAAM,UAAU,GAAQ,EAAE,CAAC;YAC3B,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;AACjD,gBAAA,IAAI,SAAS,CAAC,UAAU,CAAC,sBAAsB,CAAC,EAAE;AAChD,oBAAA,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,WAAW,CACpF,cAAmC,CAAC,IAAI,CAAC,KAAK,EAC/C,YAAY,CAAC,SAAS,CAAC,EACvB,kBAAkB,EAClB,OAAO,CACR,CAAC;AACH,iBAAA;AAED,gBAAA,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACtC,aAAA;AACD,YAAA,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;AAC5B,SAAA;aAAM,IAAI,UAAU,CAAC,KAAK,EAAE;YAC3B,IAAI,cAAc,CAAC,cAAc,IAAI,YAAY,CAAC,WAAW,CAAC,EAAE;gBAC9D,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CACpC,cAAc,EACd,YAAY,CAAC,WAAW,CAAC,CAAC,OAAQ,CAAC,EACnC,kBAAkB,EAClB,OAAO,CACR,CAAC;AACH,aAAA;iBAAM,IAAI,cAAc,CAAC,WAAW,EAAE;AACrC,gBAAA,IAAI,YAAY,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE;oBAC1C,QAAQ,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;AAC1C,iBAAA;AAAM,qBAAA,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;;;AAG3C,oBAAA,QAAQ,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;AAC9B,iBAAA;AACF,aAAA;AAAM,iBAAA;AACL,gBAAA,MAAM,YAAY,GAAG,cAAc,IAAI,OAAO,IAAI,cAAc,CAAC;gBACjE,IAAI,cAAc,CAAC,YAAY,EAAE;AAC/B;;;;;;;;;;;;;AAaE;AACF,oBAAA,MAAM,OAAO,GAAG,YAAY,CAAC,OAAQ,CAAC,CAAC;AACvC,oBAAA,MAAM,WAAW,GAAG,CAAA,EAAA,GAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAG,cAAe,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,EAAE,CAAC;AACrD,oBAAA,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CACpC,cAAc,EACd,WAAW,EACX,kBAAkB,EAClB,OAAO,CACR,CAAC;AACF,oBAAA,oBAAoB,CAAC,IAAI,CAAC,OAAQ,CAAC,CAAC;AACrC,iBAAA;AAAM,qBAAA;AACL,oBAAA,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAa,CAAC,CAAC;AAC7C,oBAAA,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CACpC,cAAc,EACd,QAAQ,EACR,kBAAkB,EAClB,OAAO,CACR,CAAC;AACF,oBAAA,oBAAoB,CAAC,IAAI,CAAC,YAAa,CAAC,CAAC;AAC1C,iBAAA;AACF,aAAA;AACF,SAAA;AAAM,aAAA;;AAEL,YAAA,IAAI,gBAAgB,CAAC;YACrB,IAAI,GAAG,GAAG,YAAY,CAAC;;YAEvB,IAAI,KAAK,GAAG,CAAC,CAAC;AACd,YAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AACxB,gBAAA,IAAI,CAAC,GAAG;oBAAE,MAAM;AAChB,gBAAA,KAAK,EAAE,CAAC;AACR,gBAAA,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AACjB,aAAA;;YAED,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE;gBACxC,GAAG,GAAG,SAAS,CAAC;AACjB,aAAA;YACD,gBAAgB,GAAG,GAAG,CAAC;AACvB,YAAA,MAAM,wBAAwB,GAAG,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC;;;;;;;;;;AAUtE,YAAA,IACE,wBAAwB;gBACxB,GAAG,KAAK,wBAAwB,CAAC,UAAU;iBAC1C,gBAAgB,KAAK,SAAS,IAAI,gBAAgB,KAAK,IAAI,CAAC,EAC7D;AACA,gBAAA,gBAAgB,GAAG,MAAM,CAAC,cAAc,CAAC;AAC1C,aAAA;AAED,YAAA,IAAI,eAAe,CAAC;;AAEpB,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,cAAc,KAAK,EAAE,EAAE;AAC7E,gBAAA,gBAAgB,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;AACrC,gBAAA,MAAM,aAAa,GAAG,UAAU,CAAC,WAAW,CAC1C,cAAc,EACd,gBAAgB,EAChB,kBAAkB,EAClB,OAAO,CACR,CAAC;;;AAGF,gBAAA,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAC7C,oBAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE;AAC3D,wBAAA,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACtB,qBAAA;AACF,iBAAA;gBACD,QAAQ,GAAG,aAAa,CAAC;AAC1B,aAAA;iBAAM,IAAI,gBAAgB,KAAK,SAAS,IAAI,cAAc,CAAC,YAAY,KAAK,SAAS,EAAE;AACtF,gBAAA,eAAe,GAAG,UAAU,CAAC,WAAW,CACtC,cAAc,EACd,gBAAgB,EAChB,kBAAkB,EAClB,OAAO,CACR,CAAC;AACF,gBAAA,QAAQ,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC;AACjC,aAAA;AACF,SAAA;AACF,KAAA;AAED,IAAA,MAAM,0BAA0B,GAAG,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;AACpE,IAAA,IAAI,0BAA0B,EAAE;AAC9B,QAAA,MAAM,oBAAoB,GAAG,CAAC,gBAAwB,KAAa;AACjE,YAAA,KAAK,MAAM,cAAc,IAAI,UAAU,EAAE;gBACvC,MAAM,KAAK,GAAG,kBAAkB,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,cAAc,CAAC,CAAC;AAC5E,gBAAA,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,gBAAgB,EAAE;AACjC,oBAAA,OAAO,KAAK,CAAC;AACd,iBAAA;AACF,aAAA;AACD,YAAA,OAAO,IAAI,CAAC;AACd,SAAC,CAAC;AAEF,QAAA,KAAK,MAAM,gBAAgB,IAAI,YAAY,EAAE;AAC3C,YAAA,IAAI,oBAAoB,CAAC,gBAAgB,CAAC,EAAE;gBAC1C,QAAQ,CAAC,gBAAgB,CAAC,GAAG,UAAU,CAAC,WAAW,CACjD,0BAA0B,EAC1B,YAAY,CAAC,gBAAgB,CAAC,EAC9B,UAAU,GAAG,IAAI,GAAG,gBAAgB,GAAG,IAAI,EAC3C,OAAO,CACR,CAAC;AACH,aAAA;AACF,SAAA;AACF,KAAA;AAAM,SAAA,IAAI,YAAY,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE;QAC3D,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;AAC3C,YAAA,IACE,QAAQ,CAAC,GAAG,CAAC,KAAK,SAAS;AAC3B,gBAAA,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC;AACnC,gBAAA,CAAC,oBAAoB,CAAC,GAAG,EAAE,OAAO,CAAC,EACnC;gBACA,QAAQ,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;AACnC,aAAA;AACF,SAAA;AACF,KAAA;AAED,IAAA,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,yBAAyB,CAChC,UAAsB,EACtB,MAAwB,EACxB,YAAiB,EACjB,UAAkB,EAClB,OAAkC,EAAA;;AAGlC,IAAA,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;AAChC,IAAA,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QACvC,MAAM,IAAI,KAAK,CACb,CAA2D,yDAAA,CAAA;YACzD,CAA0C,uCAAA,EAAA,UAAU,CAAE,CAAA,CACzD,CAAC;AACH,KAAA;AACD,IAAA,IAAI,YAAY,EAAE;QAChB,MAAM,cAAc,GAA2B,EAAE,CAAC;QAClD,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;AAC3C,YAAA,cAAc,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,YAAY,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;AAC7F,SAAA;AACD,QAAA,OAAO,cAAc,CAAC;AACvB,KAAA;AACD,IAAA,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,SAAS,uBAAuB,CAC9B,UAAsB,EACtB,MAAsB,EACtB,YAAiB,EACjB,UAAkB,EAClB,OAAkC,EAAA;;AAElC,IAAA,IAAI,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;AAClC,IAAA,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC3C,MAAM,IAAI,KAAK,CACb,CAAwD,sDAAA,CAAA;YACtD,CAA0C,uCAAA,EAAA,UAAU,CAAE,CAAA,CACzD,CAAC;AACH,KAAA;AACD,IAAA,IAAI,YAAY,EAAE;AAChB,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;;AAEhC,YAAA,YAAY,GAAG,CAAC,YAAY,CAAC,CAAC;AAC/B,SAAA;;;;AAKD,QAAA,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE;AAC/D,YAAA,OAAO,GAAG,CAAA,EAAA,GAAA,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,OAAO,CAAC;AACtE,SAAA;QAED,MAAM,SAAS,GAAG,EAAE,CAAC;AACrB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,SAAS,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,WAAW,CACnC,OAAO,EACP,YAAY,CAAC,CAAC,CAAC,EACf,CAAA,EAAG,UAAU,CAAA,CAAA,EAAI,CAAC,CAAG,CAAA,CAAA,EACrB,OAAO,CACR,CAAC;AACH,SAAA;AACD,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;AACD,IAAA,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,SAAS,qBAAqB,CAC5B,cAA+C,EAC/C,kBAA0B,EAC1B,QAAgB,EAAA;AAEhB,IAAA,MAAM,gBAAgB,GAAG,CAAC,QAAQ,CAAC,CAAC;IACpC,OAAO,gBAAgB,CAAC,MAAM,EAAE;AAC9B,QAAA,MAAM,WAAW,GAAG,gBAAgB,CAAC,KAAK,EAAE,CAAC;AAC7C,QAAA,MAAM,kBAAkB,GACtB,kBAAkB,KAAK,WAAW;AAChC,cAAE,kBAAkB;AACpB,cAAE,WAAW,GAAG,GAAG,GAAG,kBAAkB,CAAC;AAC7C,QAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,kBAAkB,CAAC,EAAE;AAC5E,YAAA,OAAO,cAAc,CAAC,kBAAkB,CAAC,CAAC;AAC3C,SAAA;AAAM,aAAA;AACL,YAAA,KAAK,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;AAC3D,gBAAA,IACE,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,GAAG,CAAC;AAClC,oBAAA,MAAM,CAAC,IAAI,CAAC,UAAU,KAAK,WAAW;AACtC,oBAAA,MAAM,CAAC,IAAI,CAAC,SAAS,EACrB;oBACA,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC9C,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;AAED,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,oBAAoB,CAC3B,UAAsB,EACtB,MAAuB,EACvB,MAAW,EACX,uBAAwD,EAAA;;IAExD,MAAM,wBAAwB,GAAG,sCAAsC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AAE5F,IAAA,IAAI,wBAAwB,EAAE;AAC5B,QAAA,IAAI,iBAAiB,GAAG,wBAAwB,CAAC,uBAAuB,CAAC,CAAC;AAC1E,QAAA,IAAI,iBAAiB,EAAE;;YAErB,IAAI,uBAAuB,KAAK,gBAAgB,EAAE;gBAChD,iBAAiB,GAAG,iBAAiB,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AAC3D,aAAA;AACD,YAAA,MAAM,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;AACrD,YAAA,MAAM,QAAQ,GAAG,CAAA,EAAA,GAAA,MAAM,CAAC,IAAI,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;AAEjE,YAAA,IAAI,OAAO,kBAAkB,KAAK,QAAQ,IAAI,QAAQ,EAAE;AACtD,gBAAA,MAAM,iBAAiB,GAAG,qBAAqB,CAC7C,UAAU,CAAC,YAAY,CAAC,cAAc,EACtC,kBAAkB,EAClB,QAAQ,CACT,CAAC;AACF,gBAAA,IAAI,iBAAiB,EAAE;oBACrB,MAAM,GAAG,iBAAiB,CAAC;AAC5B,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,sCAAsC,CAC7C,UAAsB,EACtB,MAAuB,EAAA;AAEvB,IAAA,QACE,MAAM,CAAC,IAAI,CAAC,wBAAwB;QACpC,iCAAiC,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QACrE,iCAAiC,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EACpE;AACJ,CAAC;AAED,SAAS,iCAAiC,CACxC,UAAsB,EACtB,QAAiB,EAAA;AAEjB,IAAA,QACE,QAAQ;AACR,QAAA,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC;QACjC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,wBAAwB,EAC/D;AACJ,CAAC;AAED;;AAEG;AACU,MAAA,eAAe,GAAG;AAC7B,IAAA,SAAS,EAAE,WAAW;AACtB,IAAA,OAAO,EAAE,SAAS;AAClB,IAAA,SAAS,EAAE,WAAW;AACtB,IAAA,SAAS,EAAE,WAAW;AACtB,IAAA,IAAI,EAAE,MAAM;AACZ,IAAA,QAAQ,EAAE,UAAU;AACpB,IAAA,eAAe,EAAE,iBAAiB;AAClC,IAAA,UAAU,EAAE,YAAY;AACxB,IAAA,IAAI,EAAE,MAAM;AACZ,IAAA,MAAM,EAAE,QAAQ;AAChB,IAAA,MAAM,EAAE,QAAQ;AAChB,IAAA,QAAQ,EAAE,UAAU;AACpB,IAAA,MAAM,EAAE,QAAQ;AAChB,IAAA,MAAM,EAAE,QAAQ;AAChB,IAAA,QAAQ,EAAE,UAAU;AACpB,IAAA,QAAQ,EAAE,UAAU;;;ACtsCtB;AACA;AAYA;;;;;;;AAOG;SACa,sCAAsC,CACpD,kBAAsC,EACtC,SAA6B,EAC7B,cAAiD,EAAA;AAEjD,IAAA,IAAI,aAAa,GAAG,SAAS,CAAC,aAAa,CAAC;AAC5C,IAAA,MAAM,eAAe,GAAG,SAAS,CAAC,MAAM,CAAC;AACzC,IAAA,IAAI,KAAU,CAAC;AACf,IAAA,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;AACrC,QAAA,aAAa,GAAG,CAAC,aAAa,CAAC,CAAC;AACjC,KAAA;AACD,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;AAChC,QAAA,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5B,IAAI,eAAe,CAAC,UAAU,EAAE;AAC9B,gBAAA,KAAK,GAAG,eAAe,CAAC,YAAY,CAAC;AACtC,aAAA;AAAM,iBAAA;gBACL,IAAI,oBAAoB,GAAG,4BAA4B,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;AAE3F,gBAAA,IAAI,CAAC,oBAAoB,CAAC,aAAa,IAAI,cAAc,EAAE;AACzD,oBAAA,oBAAoB,GAAG,4BAA4B,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;AACpF,iBAAA;gBAED,IAAI,eAAe,GAAG,KAAK,CAAC;AAC5B,gBAAA,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE;oBACvC,eAAe;AACb,wBAAA,eAAe,CAAC,QAAQ;AACxB,6BAAC,aAAa,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;AAClE,iBAAA;AACD,gBAAA,KAAK,GAAG,eAAe,GAAG,eAAe,CAAC,YAAY,GAAG,oBAAoB,CAAC,aAAa,CAAC;AAC7F,aAAA;AACF,SAAA;AACF,KAAA;AAAM,SAAA;QACL,IAAI,eAAe,CAAC,QAAQ,EAAE;YAC5B,KAAK,GAAG,EAAE,CAAC;AACZ,SAAA;AAED,QAAA,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;YACxC,MAAM,cAAc,GAAY,eAAmC,CAAC,IAAI,CAAC,eAAgB,CACvF,YAAY,CACb,CAAC;AACF,YAAA,MAAM,YAAY,GAAkB,aAAa,CAAC,YAAY,CAAC,CAAC;AAChE,YAAA,MAAM,aAAa,GAAQ,sCAAsC,CAC/D,kBAAkB,EAClB;AACE,gBAAA,aAAa,EAAE,YAAY;AAC3B,gBAAA,MAAM,EAAE,cAAc;aACvB,EACD,cAAc,CACf,CAAC;YACF,IAAI,aAAa,KAAK,SAAS,EAAE;gBAC/B,IAAI,CAAC,KAAK,EAAE;oBACV,KAAK,GAAG,EAAE,CAAC;AACZ,iBAAA;AACD,gBAAA,KAAK,CAAC,YAAY,CAAC,GAAG,aAAa,CAAC;AACrC,aAAA;AACF,SAAA;AACF,KAAA;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAOD,SAAS,4BAA4B,CACnC,MAAwC,EACxC,aAAuB,EAAA;AAEvB,IAAA,MAAM,MAAM,GAAyB,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC;IAC9D,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,OAAO,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AACpC,QAAA,MAAM,iBAAiB,GAAW,aAAa,CAAC,CAAC,CAAC,CAAC;;AAEnD,QAAA,IAAI,MAAM,IAAI,iBAAiB,IAAI,MAAM,EAAE;AACzC,YAAA,MAAM,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;AACpC,SAAA;AAAM,aAAA;YACL,MAAM;AACP,SAAA;AACF,KAAA;AACD,IAAA,IAAI,CAAC,KAAK,aAAa,CAAC,MAAM,EAAE;AAC9B,QAAA,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC;AAC9B,QAAA,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC;AAC7B,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,mBAAmB,GAAG,IAAI,OAAO,EAA0C,CAAC;AAClF,MAAM,qBAAqB,GAAG,MAAM,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;AAEhF,SAAS,kBAAkB,CACzB,OAAyB,EAAA;IAEzB,OAAO,qBAAqB,IAAI,OAAO,CAAC;AAC1C,CAAC;AAEK,SAAU,uBAAuB,CAAC,OAAyB,EAAA;AAC/D,IAAA,IAAI,kBAAkB,CAAC,OAAO,CAAC,EAAE;AAC/B,QAAA,OAAO,uBAAuB,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC;AAChE,KAAA;IACD,IAAI,IAAI,GAAG,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IAE5C,IAAI,CAAC,IAAI,EAAE;QACT,IAAI,GAAG,EAAE,CAAC;AACV,QAAA,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACxC,KAAA;AACD,IAAA,OAAO,IAAI,CAAC;AACd;;AChIA;AAuBA,MAAM,uBAAuB,GAAG,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;AAClE,MAAM,sBAAsB,GAAG,CAAC,iBAAiB,EAAE,sBAAsB,CAAC,CAAC;AAE3E;;AAEG;AACI,MAAM,yBAAyB,GAAG,wBAAwB;AAyCjE;;AAEG;AACa,SAAA,qBAAqB,CAAC,OAAA,GAAwC,EAAE,EAAA;;IAC9E,MAAM,gBAAgB,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,OAAO,CAAC,oBAAoB,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,uBAAuB,CAAC;IACvF,MAAM,eAAe,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,OAAO,CAAC,oBAAoB,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAG,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,sBAAsB,CAAC;AACpF,IAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AAClC,IAAA,MAAM,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAC;AACpD,IAAA,MAAM,cAAc,GAA8B;AAChD,QAAA,GAAG,EAAE;AACH,YAAA,QAAQ,EAAE,CAAA,EAAA,GAAA,iBAAiB,KAAA,IAAA,IAAjB,iBAAiB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAjB,iBAAiB,CAAE,GAAG,CAAC,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,EAAE;AAC/C,YAAA,WAAW,EAAE,CAAA,EAAA,GAAA,iBAAiB,KAAA,IAAA,IAAjB,iBAAiB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAjB,iBAAiB,CAAE,GAAG,CAAC,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,KAAK;AACxD,YAAA,UAAU,EAAE,CAAA,EAAA,GAAA,iBAAiB,KAAA,IAAA,IAAjB,iBAAiB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAjB,iBAAiB,CAAE,GAAG,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,WAAW;AAC7D,SAAA;KACF,CAAC;IAEF,OAAO;AACL,QAAA,IAAI,EAAE,yBAAyB;AAC/B,QAAA,MAAM,WAAW,CAAC,OAAwB,EAAE,IAAiB,EAAA;AAC3D,YAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;AACrC,YAAA,OAAO,uBAAuB,CAC5B,gBAAgB,EAChB,eAAe,EACf,QAAQ,EACR,cAAc,EACd,QAAQ,CACT,CAAC;SACH;KACF,CAAC;AACJ,CAAC;AAED,SAAS,uBAAuB,CAC9B,cAAgC,EAAA;AAEhC,IAAA,IAAI,MAAwC,CAAC;AAC7C,IAAA,MAAM,OAAO,GAAqB,cAAc,CAAC,OAAO,CAAC;AACzD,IAAA,MAAM,aAAa,GAAG,uBAAuB,CAAC,OAAO,CAAC,CAAC;IACvD,MAAM,aAAa,GAAG,aAAa,KAAA,IAAA,IAAb,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAE,aAAa,CAAC;AACnD,IAAA,IAAI,aAAa,EAAE;QACjB,IAAI,EAAC,aAAa,KAAb,IAAA,IAAA,aAAa,uBAAb,aAAa,CAAE,uBAAuB,CAAA,EAAE;YAC3C,MAAM,GAAG,aAAa,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AACzD,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,GAAG,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAE,uBAAuB,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;AAChF,SAAA;AACF,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,yBAAyB,CAAC,cAAgC,EAAA;AACjE,IAAA,MAAM,OAAO,GAAqB,cAAc,CAAC,OAAO,CAAC;AACzD,IAAA,MAAM,aAAa,GAAG,uBAAuB,CAAC,OAAO,CAAC,CAAC;IACvD,MAAM,iBAAiB,GAAG,aAAa,KAAA,IAAA,IAAb,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAE,iBAAiB,CAAC;AAC3D,IAAA,IAAI,MAAe,CAAC;IACpB,IAAI,iBAAiB,KAAK,SAAS,EAAE;QACnC,MAAM,GAAG,IAAI,CAAC;AACf,KAAA;AAAM,SAAA,IAAI,OAAO,iBAAiB,KAAK,SAAS,EAAE;QACjD,MAAM,GAAG,iBAAiB,CAAC;AAC5B,KAAA;AAAM,SAAA;AACL,QAAA,MAAM,GAAG,iBAAiB,CAAC,cAAc,CAAC,CAAC;AAC5C,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,eAAe,uBAAuB,CACpC,gBAA0B,EAC1B,eAAyB,EACzB,QAA0B,EAC1B,OAAkC,EAClC,QAA2D,EAAA;AAE3D,IAAA,MAAM,cAAc,GAAG,MAAM,KAAK,CAChC,gBAAgB,EAChB,eAAe,EACf,QAAQ,EACR,OAAO,EACP,QAAQ,CACT,CAAC;AACF,IAAA,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,EAAE;AAC9C,QAAA,OAAO,cAAc,CAAC;AACvB,KAAA;IAED,MAAM,aAAa,GAAG,uBAAuB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IACtE,MAAM,aAAa,GAAG,aAAa,KAAA,IAAA,IAAb,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAE,aAAa,CAAC;AACnD,IAAA,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE;AAC9C,QAAA,OAAO,cAAc,CAAC;AACvB,KAAA;AAED,IAAA,MAAM,YAAY,GAAG,uBAAuB,CAAC,cAAc,CAAC,CAAC;AAC7D,IAAA,MAAM,EAAE,KAAK,EAAE,oBAAoB,EAAE,GAAG,mBAAmB,CACzD,cAAc,EACd,aAAa,EACb,YAAY,EACZ,OAAO,CACR,CAAC;AACF,IAAA,IAAI,KAAK,EAAE;AACT,QAAA,MAAM,KAAK,CAAC;AACb,KAAA;AAAM,SAAA,IAAI,oBAAoB,EAAE;AAC/B,QAAA,OAAO,cAAc,CAAC;AACvB,KAAA;;;AAID,IAAA,IAAI,YAAY,EAAE;QAChB,IAAI,YAAY,CAAC,UAAU,EAAE;AAC3B,YAAA,IAAI,kBAAkB,GAAQ,cAAc,CAAC,UAAU,CAAC;AACxD,YAAA,IAAI,aAAa,CAAC,KAAK,IAAI,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,eAAe,CAAC,QAAQ,EAAE;gBACzF,kBAAkB;oBAChB,OAAO,kBAAkB,KAAK,QAAQ;0BAClC,kBAAkB,CAAC,YAAY,CAAC,UAAU,CAAC,cAAe,CAAC;0BAC3D,EAAE,CAAC;AACV,aAAA;YACD,IAAI;AACF,gBAAA,cAAc,CAAC,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC,WAAW,CAC9D,YAAY,CAAC,UAAU,EACvB,kBAAkB,EAClB,yBAAyB,EACzB,OAAO,CACR,CAAC;AACH,aAAA;AAAC,YAAA,OAAO,gBAAqB,EAAE;AAC9B,gBAAA,MAAM,SAAS,GAAG,IAAIC,0BAAS,CAC7B,CAAA,MAAA,EAAS,gBAAgB,CAAA,8CAAA,EAAiD,cAAc,CAAC,UAAU,CAAA,CAAE,EACrG;oBACE,UAAU,EAAE,cAAc,CAAC,MAAM;oBACjC,OAAO,EAAE,cAAc,CAAC,OAAO;AAC/B,oBAAA,QAAQ,EAAE,cAAc;AACzB,iBAAA,CACF,CAAC;AACF,gBAAA,MAAM,SAAS,CAAC;AACjB,aAAA;AACF,SAAA;AAAM,aAAA,IAAI,aAAa,CAAC,UAAU,KAAK,MAAM,EAAE;;AAE9C,YAAA,cAAc,CAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG,CAAC;AAC7E,SAAA;QAED,IAAI,YAAY,CAAC,aAAa,EAAE;AAC9B,YAAA,cAAc,CAAC,aAAa,GAAG,aAAa,CAAC,UAAU,CAAC,WAAW,CACjE,YAAY,CAAC,aAAa,EAC1B,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,EAC/B,4BAA4B,EAC5B,EAAE,GAAG,EAAE,EAAE,EAAE,uBAAuB,EAAE,IAAI,EAAE,CAC3C,CAAC;AACH,SAAA;AACF,KAAA;AAED,IAAA,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,SAAS,oBAAoB,CAAC,aAA4B,EAAA;IACxD,MAAM,mBAAmB,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;AACjE,IAAA,QACE,mBAAmB,CAAC,MAAM,KAAK,CAAC;AAChC,SAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,IAAI,mBAAmB,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,EAC1E;AACJ,CAAC;AAED,SAAS,mBAAmB,CAC1B,cAAqC,EACrC,aAA4B,EAC5B,YAA8C,EAC9C,OAAkC,EAAA;;AAElC,IAAA,MAAM,iBAAiB,GAAG,GAAG,IAAI,cAAc,CAAC,MAAM,IAAI,cAAc,CAAC,MAAM,GAAG,GAAG,CAAC;AACtF,IAAA,MAAM,oBAAoB,GAAY,oBAAoB,CAAC,aAAa,CAAC;AACvE,UAAE,iBAAiB;AACnB,UAAE,CAAC,CAAC,YAAY,CAAC;AAEnB,IAAA,IAAI,oBAAoB,EAAE;AACxB,QAAA,IAAI,YAAY,EAAE;AAChB,YAAA,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;gBACzB,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,oBAAoB,EAAE,KAAK,EAAE,CAAC;AACrD,aAAA;AACF,SAAA;AAAM,aAAA;YACL,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,oBAAoB,EAAE,KAAK,EAAE,CAAC;AACrD,SAAA;AACF,KAAA;AAED,IAAA,MAAM,iBAAiB,GAAG,YAAY,KAAA,IAAA,IAAZ,YAAY,KAAA,KAAA,CAAA,GAAZ,YAAY,GAAI,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC;AAE1E,IAAA,MAAM,mBAAmB,GAAG,CAAA,CAAA,EAAA,GAAA,cAAc,CAAC,OAAO,CAAC,yBAAyB,0CAAE,GAAG,CAC/E,cAAc,CAAC,MAAM,CACtB;AACC,UAAE,CAAA,wBAAA,EAA2B,cAAc,CAAC,MAAM,CAAE,CAAA;AACpD,UAAG,cAAc,CAAC,UAAqB,CAAC;AAE1C,IAAA,MAAM,KAAK,GAAG,IAAIA,0BAAS,CAAC,mBAAmB,EAAE;QAC/C,UAAU,EAAE,cAAc,CAAC,MAAM;QACjC,OAAO,EAAE,cAAc,CAAC,OAAO;AAC/B,QAAA,QAAQ,EAAE,cAAc;AACzB,KAAA,CAAC,CAAC;;;IAIH,IAAI,CAAC,iBAAiB,EAAE;AACtB,QAAA,MAAM,KAAK,CAAC;AACb,KAAA;AAED,IAAA,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,UAAU,CAAC;AACvD,IAAA,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,aAAa,CAAC;IAE7D,IAAI;;;QAGF,IAAI,cAAc,CAAC,UAAU,EAAE;AAC7B,YAAA,MAAM,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;AAC7C,YAAA,IAAI,iBAAiB,CAAC;AAEtB,YAAA,IAAI,iBAAiB,EAAE;gBACrB,IAAI,kBAAkB,GAAQ,UAAU,CAAC;AACzC,gBAAA,IAAI,aAAa,CAAC,KAAK,IAAI,iBAAiB,CAAC,IAAI,CAAC,IAAI,KAAK,eAAe,CAAC,QAAQ,EAAE;oBACnF,kBAAkB,GAAG,EAAE,CAAC;AACxB,oBAAA,MAAM,WAAW,GAAG,iBAAiB,CAAC,cAAc,CAAC;AACrD,oBAAA,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,WAAW,EAAE;AACjD,wBAAA,kBAAkB,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC;AAC9C,qBAAA;AACF,iBAAA;AACD,gBAAA,iBAAiB,GAAG,aAAa,CAAC,UAAU,CAAC,WAAW,CACtD,iBAAiB,EACjB,kBAAkB,EAClB,2BAA2B,EAC3B,OAAO,CACR,CAAC;AACH,aAAA;YAED,MAAM,aAAa,GAAQ,UAAU,CAAC,KAAK,IAAI,iBAAiB,IAAI,UAAU,CAAC;AAC/E,YAAA,KAAK,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC;YAChC,IAAI,aAAa,CAAC,OAAO,EAAE;AACzB,gBAAA,KAAK,CAAC,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC;AACvC,aAAA;AAED,YAAA,IAAI,iBAAiB,EAAE;AACpB,gBAAA,KAAK,CAAC,QAAmC,CAAC,UAAU,GAAG,iBAAiB,CAAC;AAC3E,aAAA;AACF,SAAA;;AAGD,QAAA,IAAI,cAAc,CAAC,OAAO,IAAI,oBAAoB,EAAE;YACjD,KAAK,CAAC,QAAmC,CAAC,aAAa;AACtD,gBAAA,aAAa,CAAC,UAAU,CAAC,WAAW,CAClC,oBAAoB,EACpB,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,EAC/B,4BAA4B,CAC7B,CAAC;AACL,SAAA;AACF,KAAA;AAAC,IAAA,OAAO,YAAiB,EAAE;AAC1B,QAAA,KAAK,CAAC,OAAO,GAAG,CAAA,OAAA,EAAU,YAAY,CAAC,OAAO,CAAA,gDAAA,EAAmD,cAAc,CAAC,UAAU,CAAA,2BAAA,CAA6B,CAAC;AACzJ,KAAA;AAED,IAAA,OAAO,EAAE,KAAK,EAAE,oBAAoB,EAAE,KAAK,EAAE,CAAC;AAChD,CAAC;AAED,eAAe,KAAK,CAClB,gBAA0B,EAC1B,eAAyB,EACzB,iBAAwC,EACxC,IAA+B,EAC/B,QAA2D,EAAA;;AAE3D,IAAA,IACE,EAAC,CAAA,EAAA,GAAA,iBAAiB,CAAC,OAAO,CAAC,yBAAyB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,GAAG,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;QACnF,iBAAiB,CAAC,UAAU,EAC5B;AACA,QAAA,MAAM,IAAI,GAAG,iBAAiB,CAAC,UAAU,CAAC;AAC1C,QAAA,MAAM,WAAW,GAAW,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;QAChF,MAAM,iBAAiB,GAAa,CAAC,WAAW;AAC9C,cAAE,EAAE;cACF,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,KAAK,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;QAEvE,IAAI;AACF,YAAA,IACE,iBAAiB,CAAC,MAAM,KAAK,CAAC;AAC9B,gBAAA,iBAAiB,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EACjF;gBACA,iBAAiB,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAChD,gBAAA,OAAO,iBAAiB,CAAC;AAC1B,aAAA;AAAM,iBAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;gBAC3F,IAAI,CAAC,QAAQ,EAAE;AACb,oBAAA,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;AAC/C,iBAAA;gBACD,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AAC5C,gBAAA,iBAAiB,CAAC,UAAU,GAAG,IAAI,CAAC;AACpC,gBAAA,OAAO,iBAAiB,CAAC;AAC1B,aAAA;AACF,SAAA;AAAC,QAAA,OAAO,GAAQ,EAAE;YACjB,MAAM,GAAG,GAAG,CAAU,OAAA,EAAA,GAAG,gDAAgD,iBAAiB,CAAC,UAAU,CAAA,CAAA,CAAG,CAAC;YACzG,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,IAAIA,0BAAS,CAAC,WAAW,CAAC;AAClD,YAAA,MAAM,CAAC,GAAG,IAAIA,0BAAS,CAAC,GAAG,EAAE;AAC3B,gBAAA,IAAI,EAAE,OAAO;gBACb,UAAU,EAAE,iBAAiB,CAAC,MAAM;gBACpC,OAAO,EAAE,iBAAiB,CAAC,OAAO;AAClC,gBAAA,QAAQ,EAAE,iBAAiB;AAC5B,aAAA,CAAC,CAAC;AACH,YAAA,MAAM,CAAC,CAAC;AACT,SAAA;AACF,KAAA;AAED,IAAA,OAAO,iBAAiB,CAAC;AAC3B;;AC9WA;AAMA;;;AAGG;AACG,SAAU,+BAA+B,CAAC,aAA4B,EAAA;AAC1E,IAAA,MAAM,MAAM,GAAG,IAAI,GAAG,EAAU,CAAC;AACjC,IAAA,KAAK,MAAM,UAAU,IAAI,aAAa,CAAC,SAAS,EAAE;QAChD,MAAM,iBAAiB,GAAG,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAC9D,IACE,iBAAiB,CAAC,UAAU;YAC5B,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,eAAe,CAAC,MAAM,EACjE;YACA,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;AAChC,SAAA;AACF,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;AAKG;AACG,SAAU,0BAA0B,CAAC,SAA6B,EAAA;AACtE,IAAA,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,GAAG,SAAS,CAAC;AAC5C,IAAA,IAAI,MAAc,CAAC;AACnB,IAAA,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;QACrC,MAAM,GAAG,aAAa,CAAC;AACxB,KAAA;AAAM,SAAA,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;AACvC,QAAA,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAClC,KAAA;AAAM,SAAA;AACL,QAAA,MAAM,GAAG,MAAM,CAAC,cAAe,CAAC;AACjC,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB;;ACzCA;AAsBA;;AAEG;AACI,MAAM,uBAAuB,GAAG,sBAAsB;AAiB7D;;;AAGG;AACa,SAAA,mBAAmB,CAAC,OAAA,GAAsC,EAAE,EAAA;AAC1E,IAAA,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;IAE1C,OAAO;AACL,QAAA,IAAI,EAAE,uBAAuB;AAC7B,QAAA,MAAM,WAAW,CAAC,OAAyB,EAAE,IAAiB,EAAA;AAC5D,YAAA,MAAM,aAAa,GAAG,uBAAuB,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM,aAAa,GAAG,aAAa,KAAA,IAAA,IAAb,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAE,aAAa,CAAC;YACnD,MAAM,kBAAkB,GAAG,aAAa,KAAA,IAAA,IAAb,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAE,kBAAkB,CAAC;YAC7D,IAAI,aAAa,IAAI,kBAAkB,EAAE;AACvC,gBAAA,gBAAgB,CAAC,OAAO,EAAE,kBAAkB,EAAE,aAAa,CAAC,CAAC;gBAC7D,oBAAoB,CAAC,OAAO,EAAE,kBAAkB,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC;AAChF,aAAA;AACD,YAAA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;SACtB;KACF,CAAC;AACJ,CAAC;AAED;;AAEG;SACa,gBAAgB,CAC9B,OAAyB,EACzB,kBAAsC,EACtC,aAA4B,EAAA;;IAE5B,IAAI,aAAa,CAAC,gBAAgB,EAAE;AAClC,QAAA,KAAK,MAAM,eAAe,IAAI,aAAa,CAAC,gBAAgB,EAAE;YAC5D,IAAI,WAAW,GAAG,sCAAsC,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;AAC9F,YAAA,IAAI,CAAC,WAAW,KAAK,IAAI,IAAI,WAAW,KAAK,SAAS,KAAK,eAAe,CAAC,MAAM,CAAC,QAAQ,EAAE;AAC1F,gBAAA,WAAW,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CAC9C,eAAe,CAAC,MAAM,EACtB,WAAW,EACX,0BAA0B,CAAC,eAAe,CAAC,CAC5C,CAAC;AACF,gBAAA,MAAM,sBAAsB,GAAI,eAAe,CAAC,MAA2B;AACxE,qBAAA,sBAAsB,CAAC;AAC1B,gBAAA,IAAI,sBAAsB,EAAE;oBAC1B,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;AAC1C,wBAAA,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,GAAG,GAAG,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;AACrE,qBAAA;AACF,iBAAA;AAAM,qBAAA;AACL,oBAAA,OAAO,CAAC,OAAO,CAAC,GAAG,CACjB,eAAe,CAAC,MAAM,CAAC,cAAc,IAAI,0BAA0B,CAAC,eAAe,CAAC,EACpF,WAAW,CACZ,CAAC;AACH,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;IACD,MAAM,aAAa,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,kBAAkB,CAAC,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,aAAa,CAAC;AAChF,IAAA,IAAI,aAAa,EAAE;QACjB,KAAK,MAAM,gBAAgB,IAAI,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;AACzD,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC;AACxE,SAAA;AACF,KAAA;AACH,CAAC;AAED;;AAEG;AACG,SAAU,oBAAoB,CAClC,OAAyB,EACzB,kBAAsC,EACtC,aAA4B,EAC5B,YAAwD,GAAA,YAAA;AACtD,IAAA,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;AACpD,CAAC,EAAA;;IAED,MAAM,iBAAiB,GAAG,CAAA,EAAA,GAAA,kBAAkB,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,iBAAiB,CAAC;AACxE,IAAA,MAAM,cAAc,GAA8B;AAChD,QAAA,GAAG,EAAE;AACH,YAAA,QAAQ,EAAE,CAAA,EAAA,GAAA,iBAAiB,KAAA,IAAA,IAAjB,iBAAiB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAjB,iBAAiB,CAAE,GAAG,CAAC,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,EAAE;AAC/C,YAAA,WAAW,EAAE,CAAA,EAAA,GAAA,iBAAiB,KAAA,IAAA,IAAjB,iBAAiB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAjB,iBAAiB,CAAE,GAAG,CAAC,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,KAAK;AACxD,YAAA,UAAU,EAAE,CAAA,EAAA,GAAA,iBAAiB,KAAA,IAAA,IAAjB,iBAAiB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAjB,iBAAiB,CAAE,GAAG,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,WAAW;AAC7D,SAAA;KACF,CAAC;AAEF,IAAA,MAAM,UAAU,GAAG,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC;IACjD,IAAI,aAAa,CAAC,WAAW,IAAI,aAAa,CAAC,WAAW,CAAC,MAAM,EAAE;QACjE,OAAO,CAAC,IAAI,GAAG,sCAAsC,CACnD,kBAAkB,EAClB,aAAa,CAAC,WAAW,CAC1B,CAAC;AAEF,QAAA,MAAM,UAAU,GAAG,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC;AACpD,QAAA,MAAM,EACJ,QAAQ,EACR,cAAc,EACd,OAAO,EACP,cAAc,EACd,YAAY,EACZ,kBAAkB,EAClB,QAAQ,GACT,GAAG,UAAU,CAAC;AACf,QAAA,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;QAEtC,IAAI;AACF,YAAA,IACE,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI;AACpD,iBAAC,QAAQ,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC;AACnC,gBAAA,QAAQ,EACR;gBACA,MAAM,8BAA8B,GAAW,0BAA0B,CACvE,aAAa,CAAC,WAAW,CAC1B,CAAC;AACF,gBAAA,OAAO,CAAC,IAAI,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CAC/C,UAAU,EACV,OAAO,CAAC,IAAI,EACZ,8BAA8B,EAC9B,cAAc,CACf,CAAC;AAEF,gBAAA,MAAM,QAAQ,GAAG,QAAQ,KAAK,eAAe,CAAC,MAAM,CAAC;gBAErD,IAAI,aAAa,CAAC,KAAK,EAAE;AACvB,oBAAA,MAAM,QAAQ,GAAG,kBAAkB,GAAG,CAAS,MAAA,EAAA,kBAAkB,CAAE,CAAA,GAAG,OAAO,CAAC;AAC9E,oBAAA,MAAM,KAAK,GAAG,wBAAwB,CACpC,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,OAAO,CAAC,IAAI,EACZ,cAAc,CACf,CAAC;AAEF,oBAAA,IAAI,QAAQ,KAAK,eAAe,CAAC,QAAQ,EAAE;AACzC,wBAAA,OAAO,CAAC,IAAI,GAAG,YAAY,CACzB,kBAAkB,CAChB,KAAK,EACL,cAAc,IAAI,OAAO,IAAI,cAAe,EAC5C,QAAQ,EACR,YAAY,CACb,EACD,EAAE,QAAQ,EAAE,OAAO,IAAI,cAAc,EAAE,UAAU,EAAE,CACpD,CAAC;AACH,qBAAA;yBAAM,IAAI,CAAC,QAAQ,EAAE;AACpB,wBAAA,OAAO,CAAC,IAAI,GAAG,YAAY,CAAC,KAAK,EAAE;4BACjC,QAAQ,EAAE,OAAO,IAAI,cAAc;4BACnC,UAAU;AACX,yBAAA,CAAC,CAAC;AACJ,qBAAA;AACF,iBAAA;AAAM,qBAAA,IACL,QAAQ,KAAK,eAAe,CAAC,MAAM;AACnC,qBAAC,CAAA,CAAA,EAAA,GAAA,aAAa,CAAC,WAAW,0CAAE,KAAK,CAAC,YAAY,CAAC,KAAI,aAAa,CAAC,SAAS,KAAK,MAAM,CAAC,EACtF;;;oBAGA,OAAO;AACR,iBAAA;qBAAM,IAAI,CAAC,QAAQ,EAAE;oBACpB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC7C,iBAAA;AACF,aAAA;AACF,SAAA;AAAC,QAAA,OAAO,KAAU,EAAE;YACnB,MAAM,IAAI,KAAK,CACb,CAAA,OAAA,EAAU,KAAK,CAAC,OAAO,2CAA2C,IAAI,CAAC,SAAS,CAC9E,cAAc,EACd,SAAS,EACT,IAAI,CACL,CAAA,CAAA,CAAG,CACL,CAAC;AACH,SAAA;AACF,KAAA;SAAM,IAAI,aAAa,CAAC,kBAAkB,IAAI,aAAa,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1F,QAAA,OAAO,CAAC,QAAQ,GAAG,EAAE,CAAC;AACtB,QAAA,KAAK,MAAM,iBAAiB,IAAI,aAAa,CAAC,kBAAkB,EAAE;YAChE,MAAM,sBAAsB,GAAG,sCAAsC,CACnE,kBAAkB,EAClB,iBAAiB,CAClB,CAAC;AACF,YAAA,IAAI,sBAAsB,KAAK,SAAS,IAAI,sBAAsB,KAAK,IAAI,EAAE;AAC3E,gBAAA,MAAM,6BAA6B,GACjC,iBAAiB,CAAC,MAAM,CAAC,cAAc,IAAI,0BAA0B,CAAC,iBAAiB,CAAC,CAAC;gBAC3F,OAAO,CAAC,QAAQ,CAAC,6BAA6B,CAAC,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CAClF,iBAAiB,CAAC,MAAM,EACxB,sBAAsB,EACtB,0BAA0B,CAAC,iBAAiB,CAAC,EAC7C,cAAc,CACf,CAAC;AACH,aAAA;AACF,SAAA;AACF,KAAA;AACH,CAAC;AAED;;AAEG;AACH,SAAS,wBAAwB,CAC/B,YAAgC,EAChC,QAAgB,EAChB,QAAgB,EAChB,eAAoB,EACpB,OAAkC,EAAA;;;AAIlC,IAAA,IAAI,YAAY,IAAI,CAAC,CAAC,WAAW,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;QAC/E,MAAM,MAAM,GAAQ,EAAE,CAAC;QACvB,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,eAAe,CAAC;QACjD,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,GAAG,YAAY,EAAE,CAAC;AACnD,QAAA,OAAO,MAAM,CAAC;AACf,KAAA;AAED,IAAA,OAAO,eAAe,CAAC;AACzB,CAAC;AAED,SAAS,kBAAkB,CACzB,GAAQ,EACR,WAAmB,EACnB,eAAwB,EACxB,YAAqB,EAAA;AAErB,IAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AACvB,QAAA,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AACb,KAAA;AACD,IAAA,IAAI,CAAC,eAAe,IAAI,CAAC,YAAY,EAAE;AACrC,QAAA,OAAO,EAAE,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;AAC/B,KAAA;IAED,MAAM,MAAM,GAAG,EAAE,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;IACtC,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,eAAe,GAAG,YAAY,EAAE,CAAC;AAC1D,IAAA,OAAO,MAAM,CAAC;AAChB;;AC1QA;AAiCA;;;;;AAKG;AACa,SAAA,oBAAoB,CAAC,OAAA,GAAyC,EAAE,EAAA;AAC9E,IAAA,MAAM,QAAQ,GAAGC,0CAAyB,CAAC,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAP,OAAO,GAAI,EAAE,CAAC,CAAC;IAC1D,IAAI,OAAO,CAAC,iBAAiB,EAAE;AAC7B,QAAA,QAAQ,CAAC,SAAS,CAChBC,gDAA+B,CAAC;AAC9B,YAAA,UAAU,EAAE,OAAO,CAAC,iBAAiB,CAAC,UAAU;AAChD,YAAA,MAAM,EAAE,OAAO,CAAC,iBAAiB,CAAC,gBAAgB;AACnD,SAAA,CAAC,CACH,CAAC;AACH,KAAA;AAED,IAAA,QAAQ,CAAC,SAAS,CAAC,mBAAmB,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC,CAAC;IAC9F,QAAQ,CAAC,SAAS,CAAC,qBAAqB,CAAC,OAAO,CAAC,sBAAsB,CAAC,EAAE;AACxE,QAAA,KAAK,EAAE,aAAa;AACrB,KAAA,CAAC,CAAC;AAEH,IAAA,OAAO,QAAQ,CAAC;AAClB;;ACxDA;AAKA,IAAI,gBAAwC,CAAC;SAE7B,0BAA0B,GAAA;IACxC,IAAI,CAAC,gBAAgB,EAAE;QACrB,gBAAgB,GAAGC,wCAAuB,EAAE,CAAC;AAC9C,KAAA;AAED,IAAA,OAAO,gBAAgB,CAAC;AAC1B;;ACbA;AAOA,MAAM,8BAA8B,GAA+C;AACjF,IAAA,GAAG,EAAE,GAAG;AACR,IAAA,GAAG,EAAE,GAAG;AACR,IAAA,KAAK,EAAE,OAAO;AACd,IAAA,GAAG,EAAE,IAAI;AACT,IAAA,KAAK,EAAE,GAAG;CACX,CAAC;AAEI,SAAU,aAAa,CAC3B,OAAe,EACf,aAA4B,EAC5B,kBAAsC,EACtC,cAAgD,EAAA;IAEhD,MAAM,eAAe,GAAG,wBAAwB,CAC9C,aAAa,EACb,kBAAkB,EAClB,cAAc,CACf,CAAC;IAEF,IAAI,cAAc,GAAG,KAAK,CAAC;IAE3B,IAAI,UAAU,GAAG,UAAU,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;IACtD,IAAI,aAAa,CAAC,IAAI,EAAE;QACtB,IAAI,IAAI,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;;;;AAI3D,QAAA,IAAI,aAAa,CAAC,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AAChE,YAAA,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAC1B,SAAA;;;;AAID,QAAA,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE;YACvB,UAAU,GAAG,IAAI,CAAC;YAClB,cAAc,GAAG,IAAI,CAAC;AACvB,SAAA;AAAM,aAAA;AACL,YAAA,UAAU,GAAG,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AAC3C,SAAA;AACF,KAAA;AAED,IAAA,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,wBAAwB,CAC9D,aAAa,EACb,kBAAkB,EAClB,cAAc,CACf,CAAC;AACF;;;;;AAKG;IACH,UAAU,GAAG,iBAAiB,CAAC,UAAU,EAAE,WAAW,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;AAExF,IAAA,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,SAAS,UAAU,CAAC,KAAa,EAAE,YAAiC,EAAA;IAClE,IAAI,MAAM,GAAG,KAAK,CAAC;IACnB,KAAK,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,IAAI,YAAY,EAAE;AACtD,QAAA,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACvD,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,wBAAwB,CAC/B,aAA4B,EAC5B,kBAAsC,EACtC,cAAgD,EAAA;;AAEhD,IAAA,MAAM,MAAM,GAAG,IAAI,GAAG,EAAkB,CAAC;AACzC,IAAA,IAAI,MAAA,aAAa,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,EAAE;AACvC,QAAA,KAAK,MAAM,YAAY,IAAI,aAAa,CAAC,aAAa,EAAE;YACtD,IAAI,iBAAiB,GAAW,sCAAsC,CACpE,kBAAkB,EAClB,YAAY,EACZ,cAAc,CACf,CAAC;AACF,YAAA,MAAM,mBAAmB,GAAG,0BAA0B,CAAC,YAAY,CAAC,CAAC;AACrE,YAAA,iBAAiB,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CACpD,YAAY,CAAC,MAAM,EACnB,iBAAiB,EACjB,mBAAmB,CACpB,CAAC;AACF,YAAA,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE;AAC9B,gBAAA,iBAAiB,GAAG,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;AAC3D,aAAA;AACD,YAAA,MAAM,CAAC,GAAG,CACR,CAAA,CAAA,EAAI,YAAY,CAAC,MAAM,CAAC,cAAc,IAAI,mBAAmB,CAAA,CAAA,CAAG,EAChE,iBAAiB,CAClB,CAAC;AACH,SAAA;AACF,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,aAAa,CAAC,GAAW,EAAA;AAChC,IAAA,OAAO,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC7B,CAAC;AAED,SAAS,UAAU,CAAC,GAAW,EAAE,YAAqB,EAAA;IACpD,IAAI,CAAC,YAAY,EAAE;AACjB,QAAA,OAAO,GAAG,CAAC;AACZ,KAAA;AAED,IAAA,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;AAC/B,IAAA,IAAI,OAAO,GAAG,SAAS,CAAC,QAAQ,CAAC;AAEjC,IAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC1B,QAAA,OAAO,GAAG,CAAA,EAAG,OAAO,CAAA,CAAA,CAAG,CAAC;AACzB,KAAA;AAED,IAAA,IAAI,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AAChC,QAAA,YAAY,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAC1C,KAAA;IAED,MAAM,WAAW,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9C,IAAA,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE;QACtB,MAAM,IAAI,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;QACpD,MAAM,MAAM,GAAG,YAAY,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;AACvD,QAAA,OAAO,GAAG,OAAO,GAAG,IAAI,CAAC;AACzB,QAAA,IAAI,MAAM,EAAE;YACV,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,CAAA,EAAG,SAAS,CAAC,MAAM,IAAI,MAAM,CAAA,CAAE,GAAG,MAAM,CAAC;AAChF,SAAA;AACF,KAAA;AAAM,SAAA;AACL,QAAA,OAAO,GAAG,OAAO,GAAG,YAAY,CAAC;AAClC,KAAA;AAED,IAAA,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC;AAE7B,IAAA,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC9B,CAAC;AAED,SAAS,wBAAwB,CAC/B,aAA4B,EAC5B,kBAAsC,EACtC,cAAgD,EAAA;;AAKhD,IAAA,MAAM,MAAM,GAAG,IAAI,GAAG,EAA6B,CAAC;AACpD,IAAA,MAAM,cAAc,GAAgB,IAAI,GAAG,EAAU,CAAC;AAEtD,IAAA,IAAI,MAAA,aAAa,CAAC,eAAe,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,EAAE;AACzC,QAAA,KAAK,MAAM,cAAc,IAAI,aAAa,CAAC,eAAe,EAAE;AAC1D,YAAA,IAAI,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,IAAI,cAAc,CAAC,MAAM,CAAC,cAAc,EAAE;gBAC1F,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;AAC1D,aAAA;YACD,IAAI,mBAAmB,GAAsB,sCAAsC,CACjF,kBAAkB,EAClB,cAAc,EACd,cAAc,CACf,CAAC;YACF,IACE,CAAC,mBAAmB,KAAK,SAAS,IAAI,mBAAmB,KAAK,IAAI;AAClE,gBAAA,cAAc,CAAC,MAAM,CAAC,QAAQ,EAC9B;AACA,gBAAA,mBAAmB,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CACtD,cAAc,CAAC,MAAM,EACrB,mBAAmB,EACnB,0BAA0B,CAAC,cAAc,CAAC,CAC3C,CAAC;AAEF,gBAAA,MAAM,SAAS,GAAG,cAAc,CAAC,gBAAgB;AAC/C,sBAAE,8BAA8B,CAAC,cAAc,CAAC,gBAAgB,CAAC;sBAC/D,EAAE,CAAC;AACP,gBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE;;oBAEtC,mBAAmB,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC,IAAI,KAAI;AACrD,wBAAA,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE;AACvC,4BAAA,OAAO,EAAE,CAAC;AACX,yBAAA;AAED,wBAAA,OAAO,IAAI,CAAC;AACd,qBAAC,CAAC,CAAC;AACJ,iBAAA;gBACD,IAAI,cAAc,CAAC,gBAAgB,KAAK,OAAO,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;oBACnF,SAAS;AACV,iBAAA;AAAM,qBAAA,IACL,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC;AAClC,qBAAC,cAAc,CAAC,gBAAgB,KAAK,KAAK,IAAI,cAAc,CAAC,gBAAgB,KAAK,KAAK,CAAC,EACxF;AACA,oBAAA,mBAAmB,GAAG,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC3D,iBAAA;AACD,gBAAA,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE;AAChC,oBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE;wBACtC,mBAAmB,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC,IAAY,KAAI;AAC7D,4BAAA,OAAO,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAClC,yBAAC,CAAC,CAAC;AACJ,qBAAA;AAAM,yBAAA;AACL,wBAAA,mBAAmB,GAAG,kBAAkB,CAAC,mBAAmB,CAAC,CAAC;AAC/D,qBAAA;AACF,iBAAA;;AAGD,gBAAA,IACE,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC;AAClC,qBAAC,cAAc,CAAC,gBAAgB,KAAK,KAAK,IAAI,cAAc,CAAC,gBAAgB,KAAK,OAAO,CAAC,EAC1F;AACA,oBAAA,mBAAmB,GAAG,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC3D,iBAAA;AAED,gBAAA,MAAM,CAAC,GAAG,CACR,cAAc,CAAC,MAAM,CAAC,cAAc,IAAI,0BAA0B,CAAC,cAAc,CAAC,EAClF,mBAAmB,CACpB,CAAC;AACH,aAAA;AACF,SAAA;AACF,KAAA;IACD,OAAO;AACL,QAAA,WAAW,EAAE,MAAM;QACnB,cAAc;KACf,CAAC;AACJ,CAAC;AAED,SAAS,sBAAsB,CAAC,WAAmB,EAAA;AACjD,IAAA,MAAM,MAAM,GAA+C,IAAI,GAAG,EAG/D,CAAC;IACJ,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AAC1C,QAAA,OAAO,MAAM,CAAC;AACf,KAAA;;AAGD,IAAA,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACnC,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAErC,IAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AACxB,QAAA,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACzC,MAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACvC,QAAA,IAAI,aAAa,EAAE;AACjB,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;AAChC,gBAAA,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3B,aAAA;AAAM,iBAAA;gBACL,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC;AAC1C,aAAA;AACF,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACzB,SAAA;AACF,KAAA;AAED,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;AACM,SAAU,iBAAiB,CAC/B,GAAW,EACX,WAA2C,EAC3C,cAA2B,EAC3B,WAAA,GAAuB,KAAK,EAAA;AAE5B,IAAA,IAAI,WAAW,CAAC,IAAI,KAAK,CAAC,EAAE;AAC1B,QAAA,OAAO,GAAG,CAAC;AACZ,KAAA;AAED,IAAA,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;;;;IAK/B,MAAM,cAAc,GAAG,sBAAsB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAEhE,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,EAAE;QACvC,MAAM,aAAa,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC/C,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;AAChC,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACxB,gBAAA,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;AAC7B,gBAAA,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC;AACxC,gBAAA,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;AAChD,aAAA;AAAM,iBAAA;AACL,gBAAA,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3B,aAAA;AACF,SAAA;AAAM,aAAA,IAAI,aAAa,EAAE;AACxB,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACxB,gBAAA,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAC9B,aAAA;AAAM,iBAAA,IAAI,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACnC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC;AAClD,aAAA;YACD,IAAI,CAAC,WAAW,EAAE;AAChB,gBAAA,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACjC,aAAA;AACF,SAAA;AAAM,aAAA;AACL,YAAA,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACjC,SAAA;AACF,KAAA;IAED,MAAM,YAAY,GAAa,EAAE,CAAC;IAClC,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,cAAc,EAAE;AAC1C,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,YAAY,CAAC,IAAI,CAAC,CAAA,EAAG,IAAI,CAAI,CAAA,EAAA,KAAK,CAAE,CAAA,CAAC,CAAC;AACvC,SAAA;AAAM,aAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;;AAE/B,YAAA,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE;gBAC5B,YAAY,CAAC,IAAI,CAAC,CAAA,EAAG,IAAI,CAAI,CAAA,EAAA,QAAQ,CAAE,CAAA,CAAC,CAAC;AAC1C,aAAA;AACF,SAAA;AAAM,aAAA;YACL,YAAY,CAAC,IAAI,CAAC,CAAA,EAAG,IAAI,CAAI,CAAA,EAAA,KAAK,CAAE,CAAA,CAAC,CAAC;AACvC,SAAA;AACF,KAAA;;IAGD,SAAS,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,GAAG,CAAA,CAAA,EAAI,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;AAC3E,IAAA,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC9B;;ACzTA;AAIO,MAAM,MAAM,GAAGC,2BAAkB,CAAC,aAAa,CAAC;;ACJvD;AA4DA;;AAEG;MACU,aAAa,CAAA;AA4BxB;;;;AAIG;AACH,IAAA,WAAA,CAAY,UAAgC,EAAE,EAAA;;AAC5C,QAAA,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,kBAAkB,CAAC;QACtD,IAAI,CAAC,SAAS,GAAG,CAAA,EAAA,GAAA,OAAO,CAAC,QAAQ,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,OAAO,CAAC,OAAO,CAAC;QACrD,IAAI,OAAO,CAAC,OAAO,EAAE;AACnB,YAAA,MAAM,CAAC,OAAO,CACZ,sFAAsF,CACvF,CAAC;AACH,SAAA;AACD,QAAA,IAAI,CAAC,wBAAwB,GAAG,OAAO,CAAC,uBAAuB,CAAC;QAChE,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,IAAI,0BAA0B,EAAE,CAAC;QAEtE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAC;AACnE,QAAA,IAAI,MAAA,OAAO,CAAC,kBAAkB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,EAAE;YACtC,KAAK,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,OAAO,CAAC,kBAAkB,EAAE;;;AAG7D,gBAAA,MAAM,UAAU,GAAG,QAAQ,KAAK,UAAU,GAAG,MAAM,GAAG,SAAS,CAAC;AAChE,gBAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE;oBAC9B,UAAU;AACX,iBAAA,CAAC,CAAC;AACJ,aAAA;AACF,SAAA;KACF;AAED;;AAEG;IACH,MAAM,WAAW,CAAC,OAAwB,EAAA;AACxC,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;KAC7D;AAED;;;;;AAKG;AACH,IAAA,MAAM,oBAAoB,CACxB,kBAAsC,EACtC,aAA4B,EAAA;QAE5B,MAAM,QAAQ,GAAuB,aAAa,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC;QAC7E,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,MAAM,IAAI,KAAK,CACb,2IAA2I,CAC5I,CAAC;AACH,SAAA;;;;AAKD,QAAA,MAAM,GAAG,GAAG,aAAa,CAAC,QAAQ,EAAE,aAAa,EAAE,kBAAkB,EAAE,IAAI,CAAC,CAAC;QAE7E,MAAM,OAAO,GAAqBC,sCAAqB,CAAC;YACtD,GAAG;AACJ,SAAA,CAAC,CAAC;AACH,QAAA,OAAO,CAAC,MAAM,GAAG,aAAa,CAAC,UAAU,CAAC;AAC1C,QAAA,MAAM,aAAa,GAAG,uBAAuB,CAAC,OAAO,CAAC,CAAC;AACvD,QAAA,aAAa,CAAC,aAAa,GAAG,aAAa,CAAC;AAC5C,QAAA,aAAa,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAEtD,MAAM,WAAW,GAAG,aAAa,CAAC,WAAW,IAAI,IAAI,CAAC,mBAAmB,CAAC;AAC1E,QAAA,IAAI,WAAW,IAAI,aAAa,CAAC,WAAW,EAAE;YAC5C,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;AAClD,SAAA;AAED,QAAA,MAAM,OAAO,GAAG,kBAAkB,CAAC,OAAO,CAAC;AAC3C,QAAA,IAAI,OAAO,EAAE;AACX,YAAA,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;AAE9C,YAAA,IAAI,cAAc,EAAE;gBAClB,IAAI,cAAc,CAAC,OAAO,EAAE;AAC1B,oBAAA,OAAO,CAAC,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC;AAC1C,iBAAA;gBAED,IAAI,cAAc,CAAC,gBAAgB,EAAE;AACnC,oBAAA,OAAO,CAAC,gBAAgB,GAAG,cAAc,CAAC,gBAAgB,CAAC;AAC5D,iBAAA;gBAED,IAAI,cAAc,CAAC,kBAAkB,EAAE;AACrC,oBAAA,OAAO,CAAC,kBAAkB,GAAG,cAAc,CAAC,kBAAkB,CAAC;AAChE,iBAAA;AAED,gBAAA,IAAI,cAAc,CAAC,iBAAiB,KAAK,SAAS,EAAE;AAClD,oBAAA,aAAa,CAAC,iBAAiB,GAAG,cAAc,CAAC,iBAAiB,CAAC;AACpE,iBAAA;gBAED,IAAI,cAAc,CAAC,uBAAuB,EAAE;AAC1C,oBAAA,OAAO,CAAC,uBAAuB,GAAG,IAAI,CAAC;AACxC,iBAAA;AACF,aAAA;YAED,IAAI,OAAO,CAAC,WAAW,EAAE;AACvB,gBAAA,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;AAC3C,aAAA;YAED,IAAI,OAAO,CAAC,cAAc,EAAE;AAC1B,gBAAA,OAAO,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;AACjD,aAAA;AACF,SAAA;QAED,IAAI,IAAI,CAAC,wBAAwB,EAAE;AACjC,YAAA,OAAO,CAAC,uBAAuB,GAAG,IAAI,CAAC;AACxC,SAAA;AAED,QAAA,IAAI,OAAO,CAAC,yBAAyB,KAAK,SAAS,EAAE;AACnD,YAAA,OAAO,CAAC,yBAAyB,GAAG,+BAA+B,CAAC,aAAa,CAAC,CAAC;AACpF,SAAA;QAED,IAAI;YACF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACpD,YAAA,MAAM,YAAY,GAAG,eAAe,CAClC,WAAW,EACX,aAAa,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CACvC,CAAC;AACP,YAAA,IAAI,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,UAAU,EAAE;AACvB,gBAAA,OAAO,CAAC,UAAU,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;AAC/C,aAAA;AACD,YAAA,OAAO,YAAY,CAAC;AACrB,SAAA;AAAC,QAAA,OAAO,KAAU,EAAE;AACnB,YAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,KAAI,KAAK,KAAL,IAAA,IAAA,KAAK,KAAL,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,KAAK,CAAE,QAAQ,CAAA,EAAE;AAChD,gBAAA,MAAM,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC;gBACnC,MAAM,YAAY,GAAG,eAAe,CAClC,WAAW,EACX,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,aAAa,CAAC,SAAS,CAAC,SAAS,CAAC,CAChF,CAAC;AACF,gBAAA,KAAK,CAAC,OAAO,GAAG,YAAY,CAAC;AAC7B,gBAAA,IAAI,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,UAAU,EAAE;oBACvB,OAAO,CAAC,UAAU,CAAC,WAAW,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;AACtD,iBAAA;AACF,aAAA;AACD,YAAA,MAAM,KAAK,CAAC;AACb,SAAA;KACF;AACF,CAAA;AAED,SAAS,qBAAqB,CAAC,OAA6B,EAAA;AAC1D,IAAA,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,OAAO,CAAC,CAAC;AACtD,IAAA,MAAM,iBAAiB,GACrB,OAAO,CAAC,UAAU,IAAI,gBAAgB;UAClC,EAAE,gBAAgB,EAAE,UAAU,EAAE,OAAO,CAAC,UAAU,EAAE;UACpD,SAAS,CAAC;AAEhB,IAAA,OAAO,oBAAoB,CACtB,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,OAAO,CACV,EAAA,EAAA,iBAAiB,IACjB,CAAC;AACL,CAAC;AAED,SAAS,mBAAmB,CAAC,OAA6B,EAAA;IACxD,IAAI,OAAO,CAAC,gBAAgB,EAAE;AAC5B,QAAA,MAAM,MAAM,GAAG,OAAO,CAAC,gBAAgB,CAAC;AACxC,QAAA,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;AAC1B,cAAE,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;cAChD,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;AAChC,KAAA;IAED,IAAI,OAAO,CAAC,QAAQ,EAAE;AACpB,QAAA,OAAO,CAAG,EAAA,OAAO,CAAC,QAAQ,WAAW,CAAC;AACvC,KAAA;IAED,IAAI,OAAO,CAAC,OAAO,EAAE;AACnB,QAAA,OAAO,CAAG,EAAA,OAAO,CAAC,OAAO,WAAW,CAAC;AACtC,KAAA;IAED,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;AACnD,QAAA,MAAM,IAAI,KAAK,CACb,CAAA,yJAAA,CAA2J,CAC5J,CAAC;AACH,KAAA;AAED,IAAA,OAAO,SAAS,CAAC;AACnB;;AC5QA;AAOA;;;;;AAKG;AACG,SAAU,iBAAiB,CAAC,UAAkB,EAAA;IAClD,MAAM,gBAAgB,GAAG,CAAK,EAAA,EAAA,UAAU,CAAC,IAAI,EAAE,CAAE,CAAA,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AACtF,IAAA,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAC,SAAS,KAAI;QACxC,MAAM,cAAc,GAAG,CAAG,EAAA,SAAS,CAAC,IAAI,EAAE,CAAI,EAAA,CAAA,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7E,QAAA,MAAM,aAAa,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,QAAQ,KAChD,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CACpE,CAAC;;AAEF,QAAA,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,MAAK,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAM,CAAC,CAAK,EAAA,CAAC,EAAG,EAAE,EAAE,CAAC,CAAC;AAC9D,KAAC,CAAC,CAAC;AACL,CAAC;AAUD;;;;;;;;;;;;;;;;;;;;;;;;;AAyBG;AACI,eAAe,gCAAgC,CACpD,kBAAsD,EAAA;AAEtD,IAAA,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,kBAAkB,CAAC;AAChD,IAAA,MAAMC,QAAM,GAAG,kBAAkB,CAAC,MAAM,IAAIC,MAAgB,CAAC;IAE7D,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAC3D,IAAI,CAAC,SAAS,EAAE;AACd,QAAAD,QAAM,CAAC,IAAI,CACT,CAAA,gHAAA,CAAkH,CACnH,CAAC;AACF,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;IACD,MAAM,UAAU,GAAmB,iBAAiB,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;AAEtE,IAAA,MAAM,eAAe,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;IACzD,IAAI,CAAC,eAAe,EAAE;AACpB,QAAAA,QAAM,CAAC,IAAI,CACT,CAAA,+HAAA,CAAiI,CAClI,CAAC;AACF,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;IAED,MAAM,WAAW,GAAG,MAAM,kBAAkB,CAAC,cAAc,CACzD,eAAe,CAAC,KAAK,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,MAAM,EACxD;AACE,QAAA,MAAM,EAAE,oBAAoB,CAAC,eAAe,CAAC,MAAM,CAAC;AACrD,KAAA,CACF,CAAC;IAEF,IAAI,CAAC,WAAW,EAAE;AAChB,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AAED,IAAA,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,WAAW,CAAC,KAAK,CAAA,CAAE,CAAC,CAAC;AACvF,IAAA,OAAO,IAAI,CAAC;AACd;;AC/FA;AACA;AAUA;;AAEG;AACH,MAAM,SAAS,GAAG;AAChB,IAAA,YAAY,EAAE,WAAW;AACzB;;AAEG;AACH,IAAA,eAAe,EAAE;AACf;;AAEG;AACH,QAAA,aAAa,EAAE,eAAe;AAC/B,KAAA;CACF,CAAC;AAEF;;;;AAII;MACS,iCAAiC,GAEtB,OAAO,gBAAgB,KAAI;IACjD,MAAM,cAAc,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAClE,MAAM,SAAS,GAAG,YAAY,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AAC1D,IAAA,IAAI,SAAS,EAAE;AACb,QAAA,MAAM,aAAa,GAAc,cAAc,CAAC,SAAS,CAAC,CAAC;QAC3D,MAAM,eAAe,GAAG,WAAW,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;AACrE,QAAA,MAAM,QAAQ,GAAG,eAAe,CAAC,aAAa,CAAC,CAAC;AAChD,QAAA,MAAM,WAAW,GAAG,MAAM,gBAAgB,CAAC,cAAc,CAAC,eAAe,EACpE,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,cAAc,CACjB,EAAA,EAAA,QAAQ,IACR,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE;AAChB,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AAED,QAAA,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAClC,SAAS,CAAC,eAAe,CAAC,aAAa,EACvC,CAAU,OAAA,EAAA,WAAW,CAAC,KAAK,CAAA,CAAE,CAC9B,CAAC;AACF,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AACD,IAAA,OAAO,KAAK,CAAC;AACf,EAAE;AAEF;;;;AAIG;AACH,SAAS,eAAe,CAAC,aAAwB,EAAA;IAC/C,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;IAC/D,MAAM,YAAY,GAAG,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACvD,IAAA,MAAM,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;AAEjC,IAAA,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;AAIG;AACH,SAAS,WAAW,CAClB,gBAAoD,EACpD,aAAwB,EAAA;AAExB,IAAA,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE;QAC/B,OAAO,gBAAgB,CAAC,MAAM,CAAC;AAChC,KAAA;IAED,MAAM,eAAe,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;AAC5D,IAAA,eAAe,CAAC,QAAQ,GAAG,SAAS,CAAC,YAAY,CAAC;AAClD,IAAA,OAAO,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC;AACtC,CAAC;AAED;;;AAGG;AACH,SAAS,YAAY,CAAC,QAA0B,EAAA;IAC9C,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AAC3D,IAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,IAAI,SAAS,EAAE;AACxC,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;IACD,OAAO;AACT,CAAC;AAUD;;;;;AAKG;AACH,SAAS,cAAc,CAAC,SAAiB,EAAA;IACvC,MAAM,eAAe,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC1D,MAAM,cAAc,GAAG,CAAG,EAAA,eAAe,CAAC,IAAI,EAAE,CAAG,CAAA,CAAA,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AAChF,IAAA,MAAM,aAAa,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,QAAQ,KAChD,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CACnE,CAAC;;AAEF,IAAA,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,MAAK,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAM,CAAC,CAAK,EAAA,CAAC,EAAG,EAAE,EAAe,CAAC,CAAC;AAC3E,CAAC;AAED;;AAEG;AACH,SAAS,gBAAgB,CAAC,OAAwB,EAAA;IAChD,OAAO;QACL,WAAW,EAAE,OAAO,CAAC,WAAW;AAChC,QAAA,cAAc,EAAE;YACd,OAAO,EAAE,OAAO,CAAC,OAAO;AACzB,SAAA;QACD,cAAc,EAAE,OAAO,CAAC,cAAc;KACvC,CAAC;AACJ;;;;;;;;;;;;;;;"}