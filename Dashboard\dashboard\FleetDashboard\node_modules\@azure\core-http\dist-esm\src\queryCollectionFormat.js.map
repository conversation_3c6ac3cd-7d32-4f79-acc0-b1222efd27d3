{"version": 3, "file": "queryCollectionFormat.js", "sourceRoot": "", "sources": ["../../src/queryCollectionFormat.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;GAEG;AACH,MAAM,CAAN,IAAY,qBAqBX;AArBD,WAAY,qBAAqB;IAC/B;;OAEG;IACH,kCAAS,CAAA;IACT;;OAEG;IACH,kCAAS,CAAA;IACT;;OAEG;IACH,mCAAU,CAAA;IACV;;OAEG;IACH,oCAAW,CAAA;IACX;;OAEG;IACH,wCAAe,CAAA;AACjB,CAAC,EArBW,qBAAqB,KAArB,qBAAqB,QAqBhC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * The format that will be used to join an array of values together for a query parameter value.\n */\nexport enum QueryCollectionFormat {\n  /**\n   * CSV: Each pair of segments joined by a single comma.\n   */\n  Csv = \",\",\n  /**\n   * SSV: Each pair of segments joined by a single space character.\n   */\n  Ssv = \" \",\n  /**\n   * TSV: Each pair of segments joined by a single tab character.\n   */\n  Tsv = \"\\t\",\n  /**\n   * Pipes: Each pair of segments joined by a single pipe character.\n   */\n  Pipes = \"|\",\n  /**\n   * Denotes this is an array of values that should be passed to the server in multiple key/value pairs, e.g. `?queryParam=value1&queryParam=value2`\n   */\n  Multi = \"Multi\",\n}\n"]}