{"version": 3, "file": "eventRoutes.js", "sourceRoot": "", "sources": ["../../../../src/generated/operations/eventRoutes.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,OAAO,KAAK,QAAQ,MAAM,kBAAkB,CAAC;AAC7C,OAAO,KAAK,OAAO,MAAM,mBAAmB,CAAC;AAC7C,OAAO,KAAK,UAAU,MAAM,sBAAsB,CAAC;AAanD;;GAEG;AACH,MAAM,OAAO,WAAW;IAGtB;;;OAGG;IACH,YAAY,MAA4B;QACtC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;;;;OAKG;IACH,IAAI,CACF,OAAuC;QAEvC,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAC7B,iBAAiB,CACkB,CAAC;IACxC,CAAC;IAED;;;;;;;;OAQG;IACH,OAAO,CACL,EAAU,EACV,OAA0C;QAE1C,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjC,oBAAoB,CACkB,CAAC;IAC3C,CAAC;IAED;;;;;;;;;;;OAWG;IACH,GAAG,CACD,EAAU,EACV,OAAsC;QAEtC,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjC,gBAAgB,CACiB,CAAC;IACtC,CAAC;IAED;;;;;;;;OAQG;IACH,MAAM,CACJ,EAAU,EACV,OAAyC;QAEzC,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjC,mBAAmB,CACc,CAAC;IACtC,CAAC;IAED;;;;OAIG;IACH,QAAQ,CACN,QAAgB,EAChB,OAA2C;QAE3C,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,QAAQ,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACvC,qBAAqB,CACkB,CAAC;IAC5C,CAAC;CACF;AACD,2BAA2B;AAE3B,MAAM,UAAU,GAAG,IAAI,QAAQ,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;AAEvE,MAAM,iBAAiB,GAA2B;IAChD,IAAI,EAAE,cAAc;IACpB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,oBAAoB;SACzC;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC;IACjC,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,eAAe;KAC3B;IACD,UAAU;CACX,CAAC;AACF,MAAM,oBAAoB,GAA2B;IACnD,IAAI,EAAE,mBAAmB;IACzB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,UAAU;SAC/B;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC;IAChD,gBAAgB,EAAE,CAAC,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,UAAU,CAAC;IACjE,UAAU;CACX,CAAC;AACF,MAAM,gBAAgB,GAA2B;IAC/C,IAAI,EAAE,mBAAmB;IACzB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE,EAAE;QACP,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,WAAW,EAAE,UAAU,CAAC,UAAU;IAClC,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC;IAChD,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,UAAU;KACtB;IACD,SAAS,EAAE,MAAM;IACjB,UAAU;CACX,CAAC;AACF,MAAM,mBAAmB,GAA2B;IAClD,IAAI,EAAE,mBAAmB;IACzB,UAAU,EAAE,QAAQ;IACpB,SAAS,EAAE;QACT,GAAG,EAAE,EAAE;QACP,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC;IAChD,gBAAgB,EAAE,CAAC,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,UAAU,CAAC;IACjE,UAAU;CACX,CAAC;AACF,MAAM,qBAAqB,GAA2B;IACpD,IAAI,EAAE,YAAY;IAClB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,oBAAoB;SACzC;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,QAAQ,CAAC;IACtD,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,eAAe;KAC3B;IACD,UAAU;CACX,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreHttp from \"@azure/core-http\";\nimport * as Mappers from \"../models/mappers\";\nimport * as Parameters from \"../models/parameters\";\nimport { AzureDigitalTwinsAPI } from \"../azureDigitalTwinsAPI\";\nimport {\n  EventRoutesListOptionalParams,\n  EventRoutesListResponse,\n  EventRoutesGetByIdOptionalParams,\n  EventRoutesGetByIdResponse,\n  EventRoutesAddOptionalParams,\n  EventRoutesDeleteOptionalParams,\n  EventRoutesListNextOptionalParams,\n  EventRoutesListNextResponse\n} from \"../models\";\n\n/**\n * Class representing a EventRoutes.\n */\nexport class EventRoutes {\n  private readonly client: AzureDigitalTwinsAPI;\n\n  /**\n   * Initialize a new instance of the class EventRoutes class.\n   * @param client Reference to the service client\n   */\n  constructor(client: AzureDigitalTwinsAPI) {\n    this.client = client;\n  }\n\n  /**\n   * Retrieves all event routes.\n   * Status codes:\n   * * 200 OK\n   * @param options The options parameters.\n   */\n  list(\n    options?: EventRoutesListOptionalParams\n  ): Promise<EventRoutesListResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { options: operationOptions },\n      listOperationSpec\n    ) as Promise<EventRoutesListResponse>;\n  }\n\n  /**\n   * Retrieves an event route.\n   * Status codes:\n   * * 200 OK\n   * * 404 Not Found\n   *   * EventRouteNotFound - The event route was not found.\n   * @param id The id for an event route. The id is unique within event routes and case sensitive.\n   * @param options The options parameters.\n   */\n  getById(\n    id: string,\n    options?: EventRoutesGetByIdOptionalParams\n  ): Promise<EventRoutesGetByIdResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, options: operationOptions },\n      getByIdOperationSpec\n    ) as Promise<EventRoutesGetByIdResponse>;\n  }\n\n  /**\n   * Adds or replaces an event route.\n   * Status codes:\n   * * 204 No Content\n   * * 400 Bad Request\n   *   * EventRouteEndpointInvalid - The endpoint provided does not exist or is not active.\n   *   * EventRouteFilterInvalid - The event route filter is invalid.\n   *   * EventRouteIdInvalid - The event route id is invalid.\n   *   * LimitExceeded - The maximum number of event routes allowed has been reached.\n   * @param id The id for an event route. The id is unique within event routes and case sensitive.\n   * @param options The options parameters.\n   */\n  add(\n    id: string,\n    options?: EventRoutesAddOptionalParams\n  ): Promise<coreHttp.RestResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, options: operationOptions },\n      addOperationSpec\n    ) as Promise<coreHttp.RestResponse>;\n  }\n\n  /**\n   * Deletes an event route.\n   * Status codes:\n   * * 204 No Content\n   * * 404 Not Found\n   *   * EventRouteNotFound - The event route was not found.\n   * @param id The id for an event route. The id is unique within event routes and case sensitive.\n   * @param options The options parameters.\n   */\n  delete(\n    id: string,\n    options?: EventRoutesDeleteOptionalParams\n  ): Promise<coreHttp.RestResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, options: operationOptions },\n      deleteOperationSpec\n    ) as Promise<coreHttp.RestResponse>;\n  }\n\n  /**\n   * ListNext\n   * @param nextLink The nextLink from the previous successful call to the List method.\n   * @param options The options parameters.\n   */\n  listNext(\n    nextLink: string,\n    options?: EventRoutesListNextOptionalParams\n  ): Promise<EventRoutesListNextResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { nextLink, options: operationOptions },\n      listNextOperationSpec\n    ) as Promise<EventRoutesListNextResponse>;\n  }\n}\n// Operation Specifications\n\nconst serializer = new coreHttp.Serializer(Mappers, /* isXml */ false);\n\nconst listOperationSpec: coreHttp.OperationSpec = {\n  path: \"/eventroutes\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.EventRouteCollection\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host],\n  headerParameters: [\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.maxItemsPerPage\n  ],\n  serializer\n};\nconst getByIdOperationSpec: coreHttp.OperationSpec = {\n  path: \"/eventroutes/{id}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.EventRoute\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\nconst addOperationSpec: coreHttp.OperationSpec = {\n  path: \"/eventroutes/{id}\",\n  httpMethod: \"PUT\",\n  responses: {\n    204: {},\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.eventRoute,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.traceparent,\n    Parameters.tracestate\n  ],\n  mediaType: \"json\",\n  serializer\n};\nconst deleteOperationSpec: coreHttp.OperationSpec = {\n  path: \"/eventroutes/{id}\",\n  httpMethod: \"DELETE\",\n  responses: {\n    204: {},\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\nconst listNextOperationSpec: coreHttp.OperationSpec = {\n  path: \"{nextLink}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.EventRouteCollection\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.nextLink],\n  headerParameters: [\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.maxItemsPerPage\n  ],\n  serializer\n};\n"]}