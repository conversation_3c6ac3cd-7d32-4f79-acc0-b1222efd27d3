{"version": 3, "file": "xml.browser.js", "sourceRoot": "", "sources": ["../../../src/util/xml.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAqB,WAAW,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAElF,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;IAC1E,MAAM,IAAI,KAAK,CACb,oUAAoU,CACrU,CAAC;CACH;AAED,IAAI,SAA+B,CAAC;AACpC,SAAS,MAAM;IACb,IAAI,CAAC,SAAS,EAAE;QACd,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;KACtE;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,IAAI,YAAmC,CAAC;AACxC,SAAS,SAAS;IAChB,IAAI,CAAC,YAAY,EAAE;QACjB,YAAY,GAAG,IAAI,SAAS,EAAE,CAAC;KAChC;IACD,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,IAAI,gBAA2C,CAAC;AAChD,SAAS,aAAa;IACpB,IAAI,CAAC,gBAAgB,EAAE;QACrB,gBAAgB,GAAG,IAAI,aAAa,EAAE,CAAC;KACxC;IACD,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED,mDAAmD;AACnD,mDAAmD;AACnD,uFAAuF;AACvF,qFAAqF;AACrF,kFAAkF;AAClF,0BAA0B;AAC1B,IAAI,QAA2D,CAAC;AAChE,IAAI,OAAO,IAAI,CAAC,YAAY,KAAK,WAAW,EAAE;IAC5C,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,8BAA8B,EAAE;QACxE,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;KACrB,CAAC,CAAC;CACJ;AAED,MAAM,UAAU,QAAQ,CAAC,GAAW,EAAE,OAA0B,EAAE;;IAChE,IAAI;QACF,MAAM,cAAc,GAAgC;YAClD,QAAQ,EAAE,MAAA,IAAI,CAAC,QAAQ,mCAAI,EAAE;YAC7B,WAAW,EAAE,MAAA,IAAI,CAAC,WAAW,mCAAI,KAAK;YACtC,UAAU,EAAE,MAAA,IAAI,CAAC,UAAU,mCAAI,WAAW;SAC3C,CAAC;QACF,MAAM,GAAG,GAAG,SAAS,EAAE,CAAC,eAAe,CACrC,CAAC,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,UAAU,CAAC,GAAG,CAAC,mCAAI,GAAG,CAAW,EAC5C,iBAAiB,CAClB,CAAC;QACF,YAAY,CAAC,GAAG,CAAC,CAAC;QAElB,IAAI,GAAG,CAAC;QACR,IAAI,cAAc,CAAC,WAAW,EAAE;YAC9B,GAAG,GAAG,WAAW,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;SACxC;aAAM;YACL,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;SACtD;QAED,OAAO,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;KAC7B;IAAC,OAAO,GAAQ,EAAE;QACjB,OAAO,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;KAC5B;AACH,CAAC;AAED,IAAI,OAA2B,CAAC;AAEhC,SAAS,iBAAiB;;IACxB,IAAI,OAAO,KAAK,SAAS,EAAE;QACzB,IAAI;YACF,MAAM,UAAU,GAAG,CAAC,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,UAAU,CAAC,SAAS,CAAC,mCAAI,SAAS,CAAW,CAAC;YAC5E,OAAO;gBACL,MAAA,SAAS,EAAE,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;qBACvF,YAAa,mCAAI,EAAE,CAAC;SAC1B;QAAC,OAAO,OAAY,EAAE;YACrB,oFAAoF;YACpF,OAAO,GAAG,EAAE,CAAC;SACd;KACF;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,YAAY,CAAC,GAAa;IACjC,MAAM,YAAY,GAAG,GAAG,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;IAC7D,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,IAAI,iBAAiB,EAAE,EAAE;QAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,KAAK,OAAO,EAAE;gBAC5C,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;aAC5C;SACF;KACF;AACH,CAAC;AAED,SAAS,SAAS,CAAC,IAAU;IAC3B,OAAO,CAAC,CAAE,IAAgB,CAAC,UAAU,CAAC;AACxC,CAAC;AAED;;;GAGG;AACH,SAAS,uBAAuB,CAAC,IAAU;IACzC,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;AACpE,CAAC;AAED,SAAS,WAAW,CAAC,IAAU,EAAE,OAAoC;IACnE,IAAI,MAAM,GAAQ,EAAE,CAAC;IAErB,MAAM,cAAc,GAAW,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;IAEtD,MAAM,cAAc,GAAS,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAChD,MAAM,kBAAkB,GACtB,CAAC,cAAc;QACb,cAAc,KAAK,CAAC;QACpB,cAAc,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS;QAC1C,cAAc,CAAC,SAAS,CAAC;QAC3B,SAAS,CAAC;IAEZ,MAAM,qBAAqB,GAAwB,uBAAuB,CAAC,IAAI,CAAC,CAAC;IACjF,IAAI,qBAAqB,EAAE;QACzB,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;QAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,qBAAqB,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChE,MAAM,IAAI,GAAG,qBAAqB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;SACrD;QAED,IAAI,kBAAkB,EAAE;YACtB,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,kBAAkB,CAAC;SACjD;KACF;SAAM,IAAI,cAAc,KAAK,CAAC,EAAE;QAC/B,MAAM,GAAG,EAAE,CAAC;KACb;SAAM,IAAI,kBAAkB,EAAE;QAC7B,MAAM,GAAG,kBAAkB,CAAC;KAC7B;IAED,IAAI,CAAC,kBAAkB,EAAE;QACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE;YACvC,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACjC,2CAA2C;YAC3C,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,EAAE;gBACrC,MAAM,WAAW,GAAQ,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBACrD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;oBAC3B,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,WAAW,CAAC;iBACtC;qBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE;oBAChD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;iBAC1C;qBAAM;oBACL,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,WAAW,CAAC,CAAC;iBAChE;aACF;SACF;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,OAAgB,EAAE,OAA0B,EAAE;;IACzE,MAAM,cAAc,GAAgC;QAClD,QAAQ,EAAE,MAAA,IAAI,CAAC,QAAQ,mCAAI,MAAM;QACjC,WAAW,EAAE,MAAA,IAAI,CAAC,WAAW,mCAAI,KAAK;QACtC,UAAU,EAAE,MAAA,IAAI,CAAC,UAAU,mCAAI,WAAW;KAC3C,CAAC;IACF,MAAM,GAAG,GAAG,SAAS,CAAC,OAAO,EAAE,cAAc,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3E,OAAO,CACL,yDAAyD;QACzD,aAAa,EAAE,CAAC,iBAAiB,CAAC,GAAG,CAAC,CACvC,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,CAAC,KAAgD;IACvE,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QACpC,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;QACnC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACnB;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,SAAS,CAAC,GAAQ,EAAE,WAAmB,EAAE,OAAoC;IACpF,IACE,GAAG,KAAK,SAAS;QACjB,GAAG,KAAK,IAAI;QACZ,OAAO,GAAG,KAAK,QAAQ;QACvB,OAAO,GAAG,KAAK,QAAQ;QACvB,OAAO,GAAG,KAAK,SAAS,EACxB;QACA,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QACjD,IAAI,CAAC,WAAW,GAAG,GAAG,KAAK,SAAS,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;QAC3E,OAAO,CAAC,IAAI,CAAC,CAAC;KACf;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAC7B,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,KAAK,MAAM,SAAS,IAAI,GAAG,EAAE;YAC3B,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,EAAE;gBAC9D,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACpB;SACF;QACD,OAAO,MAAM,CAAC;KACf;SAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAClC,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QACjD,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YAClC,IAAI,GAAG,KAAK,WAAW,EAAE;gBACvB,KAAK,MAAM,IAAI,IAAI,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;oBAC5C,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;iBACpC;aACF;iBAAM,IAAI,GAAG,KAAK,OAAO,CAAC,UAAU,EAAE;gBACrC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;aACxC;iBAAM;gBACL,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE;oBACrD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;iBACzB;aACF;SACF;QACD,OAAO,CAAC,IAAI,CAAC,CAAC;KACf;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,wCAAwC,GAAG,EAAE,CAAC,CAAC;KAChE;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { SerializerOptions, XML_ATTRKEY, XML_CHARKEY } from \"./serializer.common\";\n\nif (!self.document || !self.DOMParser || !self.Node || !self.XMLSerializer) {\n  throw new Error(\n    `This library depends on the following DOM objects: [\"document\", \"DOMParser\", \"Node\", \"XMLSerializer\"] to parse XML, but some of these are undefined. You may provide a polyfill to make these globally available in order to support your environment. For more information, please refer to https://aka.ms/azsdk/js/web-workers. `\n  );\n}\n\nlet cachedDoc: Document | undefined;\nfunction getDoc(): Document {\n  if (!cachedDoc) {\n    cachedDoc = document.implementation.createDocument(null, null, null);\n  }\n  return cachedDoc;\n}\n\nlet cachedParser: DOMParser | undefined;\nfunction getParser(): DOMParser {\n  if (!cachedParser) {\n    cachedParser = new DOMParser();\n  }\n  return cachedParser;\n}\n\nlet cachedSerializer: XMLSerializer | undefined;\nfunction getSerializer(): XMLSerializer {\n  if (!cachedSerializer) {\n    cachedSerializer = new XMLSerializer();\n  }\n  return cachedSerializer;\n}\n\n// Policy to make our code Trusted Types compliant.\n//   https://github.com/w3c/webappsec-trusted-types\n// We are calling DOMParser.parseFromString() to parse XML payload from Azure services.\n// The parsed DOM object is not exposed to outside. Scripts are disabled when parsing\n// according to the spec.  There are no HTML/XSS security concerns on the usage of\n// parseFromString() here.\nlet ttPolicy: Pick<TrustedTypePolicy, \"createHTML\"> | undefined;\nif (typeof self.trustedTypes !== \"undefined\") {\n  ttPolicy = self.trustedTypes.createPolicy(\"@azure/core-http#xml.browser\", {\n    createHTML: (s) => s,\n  });\n}\n\nexport function parseXML(str: string, opts: SerializerOptions = {}): Promise<any> {\n  try {\n    const updatedOptions: Required<SerializerOptions> = {\n      rootName: opts.rootName ?? \"\",\n      includeRoot: opts.includeRoot ?? false,\n      xmlCharKey: opts.xmlCharKey ?? XML_CHARKEY,\n    };\n    const dom = getParser().parseFromString(\n      (ttPolicy?.createHTML(str) ?? str) as string,\n      \"application/xml\"\n    );\n    throwIfError(dom);\n\n    let obj;\n    if (updatedOptions.includeRoot) {\n      obj = domToObject(dom, updatedOptions);\n    } else {\n      obj = domToObject(dom.childNodes[0], updatedOptions);\n    }\n\n    return Promise.resolve(obj);\n  } catch (err: any) {\n    return Promise.reject(err);\n  }\n}\n\nlet errorNS: string | undefined;\n\nfunction getErrorNamespace(): string {\n  if (errorNS === undefined) {\n    try {\n      const invalidXML = (ttPolicy?.createHTML(\"INVALID\") ?? \"INVALID\") as string;\n      errorNS =\n        getParser().parseFromString(invalidXML, \"text/xml\").getElementsByTagName(\"parsererror\")[0]\n          .namespaceURI! ?? \"\";\n    } catch (ignored: any) {\n      // Most browsers will return a document containing <parsererror>, but IE will throw.\n      errorNS = \"\";\n    }\n  }\n  return errorNS;\n}\n\nfunction throwIfError(dom: Document): void {\n  const parserErrors = dom.getElementsByTagName(\"parsererror\");\n  if (parserErrors.length > 0 && getErrorNamespace()) {\n    for (let i = 0; i < parserErrors.length; i++) {\n      if (parserErrors[i].namespaceURI === errorNS) {\n        throw new Error(parserErrors[i].innerHTML);\n      }\n    }\n  }\n}\n\nfunction isElement(node: Node): node is Element {\n  return !!(node as Element).attributes;\n}\n\n/**\n * Get the Element-typed version of the provided Node if the provided node is an element with\n * attributes. If it isn't, then undefined is returned.\n */\nfunction asElementWithAttributes(node: Node): Element | undefined {\n  return isElement(node) && node.hasAttributes() ? node : undefined;\n}\n\nfunction domToObject(node: Node, options: Required<SerializerOptions>): any {\n  let result: any = {};\n\n  const childNodeCount: number = node.childNodes.length;\n\n  const firstChildNode: Node = node.childNodes[0];\n  const onlyChildTextValue: string | undefined =\n    (firstChildNode &&\n      childNodeCount === 1 &&\n      firstChildNode.nodeType === Node.TEXT_NODE &&\n      firstChildNode.nodeValue) ||\n    undefined;\n\n  const elementWithAttributes: Element | undefined = asElementWithAttributes(node);\n  if (elementWithAttributes) {\n    result[XML_ATTRKEY] = {};\n\n    for (let i = 0; i < elementWithAttributes.attributes.length; i++) {\n      const attr = elementWithAttributes.attributes[i];\n      result[XML_ATTRKEY][attr.nodeName] = attr.nodeValue;\n    }\n\n    if (onlyChildTextValue) {\n      result[options.xmlCharKey] = onlyChildTextValue;\n    }\n  } else if (childNodeCount === 0) {\n    result = \"\";\n  } else if (onlyChildTextValue) {\n    result = onlyChildTextValue;\n  }\n\n  if (!onlyChildTextValue) {\n    for (let i = 0; i < childNodeCount; i++) {\n      const child = node.childNodes[i];\n      // Ignore leading/trailing whitespace nodes\n      if (child.nodeType !== Node.TEXT_NODE) {\n        const childObject: any = domToObject(child, options);\n        if (!result[child.nodeName]) {\n          result[child.nodeName] = childObject;\n        } else if (Array.isArray(result[child.nodeName])) {\n          result[child.nodeName].push(childObject);\n        } else {\n          result[child.nodeName] = [result[child.nodeName], childObject];\n        }\n      }\n    }\n  }\n\n  return result;\n}\n\nexport function stringifyXML(content: unknown, opts: SerializerOptions = {}): string {\n  const updatedOptions: Required<SerializerOptions> = {\n    rootName: opts.rootName ?? \"root\",\n    includeRoot: opts.includeRoot ?? false,\n    xmlCharKey: opts.xmlCharKey ?? XML_CHARKEY,\n  };\n  const dom = buildNode(content, updatedOptions.rootName, updatedOptions)[0];\n  return (\n    '<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>' +\n    getSerializer().serializeToString(dom)\n  );\n}\n\nfunction buildAttributes(attrs: { [key: string]: { toString(): string } }): Attr[] {\n  const result = [];\n  for (const key of Object.keys(attrs)) {\n    const attr = getDoc().createAttribute(key);\n    attr.value = attrs[key].toString();\n    result.push(attr);\n  }\n  return result;\n}\n\nfunction buildNode(obj: any, elementName: string, options: Required<SerializerOptions>): Node[] {\n  if (\n    obj === undefined ||\n    obj === null ||\n    typeof obj === \"string\" ||\n    typeof obj === \"number\" ||\n    typeof obj === \"boolean\"\n  ) {\n    const elem = getDoc().createElement(elementName);\n    elem.textContent = obj === undefined || obj === null ? \"\" : obj.toString();\n    return [elem];\n  } else if (Array.isArray(obj)) {\n    const result = [];\n    for (const arrayElem of obj) {\n      for (const child of buildNode(arrayElem, elementName, options)) {\n        result.push(child);\n      }\n    }\n    return result;\n  } else if (typeof obj === \"object\") {\n    const elem = getDoc().createElement(elementName);\n    for (const key of Object.keys(obj)) {\n      if (key === XML_ATTRKEY) {\n        for (const attr of buildAttributes(obj[key])) {\n          elem.attributes.setNamedItem(attr);\n        }\n      } else if (key === options.xmlCharKey) {\n        elem.textContent = obj[key].toString();\n      } else {\n        for (const child of buildNode(obj[key], key, options)) {\n          elem.appendChild(child);\n        }\n      }\n    }\n    return [elem];\n  } else {\n    throw new Error(`Illegal value passed to buildObject: ${obj}`);\n  }\n}\n"]}