{"version": 3, "file": "azureDigitalTwinsAPIContext.js", "sourceRoot": "", "sources": ["../../../src/generated/azureDigitalTwinsAPIContext.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,OAAO,KAAK,QAAQ,MAAM,kBAAkB,CAAC;AAG7C,MAAM,WAAW,GAAG,2BAA2B,CAAC;AAChD,MAAM,cAAc,GAAG,OAAO,CAAC;AAE/B,MAAM,OAAO,2BAA4B,SAAQ,QAAQ,CAAC,aAAa;IAIrE;;;OAGG;IACH,YAAY,OAA4C;QACtD,0CAA0C;QAC1C,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,GAAG,EAAE,CAAC;SACd;QAED,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YACtB,MAAM,gBAAgB,GAAG,QAAQ,CAAC,wBAAwB,EAAE,CAAC;YAC7D,OAAO,CAAC,SAAS,GAAG,GAAG,WAAW,IAAI,cAAc,IAAI,gBAAgB,EAAE,CAAC;SAC5E;QAED,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAE1B,IAAI,CAAC,kBAAkB,GAAG,iCAAiC,CAAC;QAE5D,IAAI,CAAC,OAAO;YACV,OAAO,CAAC,QAAQ,IAAI,kDAAkD,CAAC;QAEzE,0CAA0C;QAC1C,IAAI,CAAC,KAAK;YACR,OAAO,CAAC,KAAK,IAAI,kDAAkD,CAAC;QACtE,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,YAAY,CAAC;IACvD,CAAC;CACF", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreHttp from \"@azure/core-http\";\nimport { AzureDigitalTwinsAPIOptionalParams } from \"./models\";\n\nconst packageName = \"@azure/digital-twins-core\";\nconst packageVersion = \"1.1.0\";\n\nexport class AzureDigitalTwinsAPIContext extends coreHttp.ServiceClient {\n  $host: string;\n  apiVersion: string;\n\n  /**\n   * Initializes a new instance of the AzureDigitalTwinsAPIContext class.\n   * @param options The parameter options\n   */\n  constructor(options?: AzureDigitalTwinsAPIOptionalParams) {\n    // Initializing default values for options\n    if (!options) {\n      options = {};\n    }\n\n    if (!options.userAgent) {\n      const defaultUserAgent = coreHttp.getDefaultUserAgentValue();\n      options.userAgent = `${packageName}/${packageVersion} ${defaultUserAgent}`;\n    }\n\n    super(undefined, options);\n\n    this.requestContentType = \"application/json; charset=utf-8\";\n\n    this.baseUri =\n      options.endpoint || \"https://digitaltwins-name.digitaltwins.azure.net\";\n\n    // Assigning values to Constant parameters\n    this.$host =\n      options.$host || \"https://digitaltwins-name.digitaltwins.azure.net\";\n    this.apiVersion = options.apiVersion || \"2022-05-31\";\n  }\n}\n"]}