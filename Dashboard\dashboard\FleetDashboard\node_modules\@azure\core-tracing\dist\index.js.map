{"version": 3, "file": "index.js", "sources": ["../src/tracingContext.ts", "../src/instrumenter.ts", "../src/tracingClient.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { TracingContext, TracingSpan } from \"./interfaces\";\n\n/** @internal */\nexport const knownContextKeys = {\n  span: Symbol.for(\"@azure/core-tracing span\"),\n  namespace: Symbol.for(\"@azure/core-tracing namespace\"),\n};\n\n/**\n * Creates a new {@link TracingContext} with the given options.\n * @param options - A set of known keys that may be set on the context.\n * @returns A new {@link TracingContext} with the given options.\n *\n * @internal\n */\nexport function createTracingContext(options: CreateTracingContextOptions = {}): TracingContext {\n  let context: TracingContext = new TracingContextImpl(options.parentContext);\n  if (options.span) {\n    context = context.setValue(knownContextKeys.span, options.span);\n  }\n  if (options.namespace) {\n    context = context.setValue(knownContextKeys.namespace, options.namespace);\n  }\n  return context;\n}\n\n/** @internal */\nexport class TracingContextImpl implements TracingContext {\n  private _contextMap: Map<symbol, unknown>;\n  constructor(initialContext?: TracingContext) {\n    this._contextMap =\n      initialContext instanceof TracingContextImpl\n        ? new Map<symbol, unknown>(initialContext._contextMap)\n        : new Map();\n  }\n\n  setValue(key: symbol, value: unknown): TracingContext {\n    const newContext = new TracingContextImpl(this);\n    newContext._contextMap.set(key, value);\n    return newContext;\n  }\n\n  getValue(key: symbol): unknown {\n    return this._contextMap.get(key);\n  }\n\n  deleteValue(key: symbol): TracingContext {\n    const newContext = new TracingContextImpl(this);\n    newContext._contextMap.delete(key);\n    return newContext;\n  }\n}\n\n/**\n * Represents a set of items that can be set when creating a new {@link TracingContext}.\n */\nexport interface CreateTracingContextOptions {\n  /** The {@link parentContext} - the newly created context will contain all the values of the parent context unless overridden. */\n  parentContext?: TracingContext;\n  /** An initial span to set on the context. */\n  span?: TracingSpan;\n  /** The namespace to set on any child spans. */\n  namespace?: string;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { Instrumenter, InstrumenterSpanOptions, TracingContext, TracingSpan } from \"./interfaces\";\nimport { createTracingContext } from \"./tracingContext\";\n\nexport function createDefaultTracingSpan(): TracingSpan {\n  return {\n    end: () => {\n      // noop\n    },\n    isRecording: () => false,\n    recordException: () => {\n      // noop\n    },\n    setAttribute: () => {\n      // noop\n    },\n    setStatus: () => {\n      // noop\n    },\n  };\n}\n\nexport function createDefaultInstrumenter(): Instrumenter {\n  return {\n    createRequestHeaders: (): Record<string, string> => {\n      return {};\n    },\n    parseTraceparentHeader: (): TracingContext | undefined => {\n      return undefined;\n    },\n    startSpan: (\n      _name: string,\n      spanOptions: InstrumenterSpanOptions\n    ): { span: TracingSpan; tracingContext: TracingContext } => {\n      return {\n        span: createDefaultTracingSpan(),\n        tracingContext: createTracingContext({ parentContext: spanOptions.tracingContext }),\n      };\n    },\n    withContext<\n      CallbackArgs extends unknown[],\n      Callback extends (...args: CallbackArgs) => ReturnType<Callback>\n    >(\n      _context: TracingContext,\n      callback: Callback,\n      ...callbackArgs: CallbackArgs\n    ): ReturnType<Callback> {\n      return callback(...callbackArgs);\n    },\n  };\n}\n\n/** @internal */\nlet instrumenterImplementation: Instrumenter | undefined;\n\n/**\n * Extends the Azure SDK with support for a given instrumenter implementation.\n *\n * @param instrumenter - The instrumenter implementation to use.\n */\nexport function useInstrumenter(instrumenter: Instrumenter): void {\n  instrumenterImplementation = instrumenter;\n}\n\n/**\n * Gets the currently set instrumenter, a No-Op instrumenter by default.\n *\n * @returns The currently set instrumenter\n */\nexport function getInstrumenter(): Instrumenter {\n  if (!instrumenterImplementation) {\n    instrumenterImplementation = createDefaultInstrumenter();\n  }\n  return instrumenterImplementation;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  OperationTracingOptions,\n  OptionsWithTracingContext,\n  Resolved,\n  TracingClient,\n  TracingClientOptions,\n  TracingContext,\n  TracingSpan,\n  TracingSpanOptions,\n} from \"./interfaces\";\nimport { getInstrumenter } from \"./instrumenter\";\nimport { knownContextKeys } from \"./tracingContext\";\n\n/**\n * Creates a new tracing client.\n *\n * @param options - Options used to configure the tracing client.\n * @returns - An instance of {@link TracingClient}.\n */\nexport function createTracingClient(options: TracingClientOptions): TracingClient {\n  const { namespace, packageName, packageVersion } = options;\n\n  function startSpan<Options extends { tracingOptions?: OperationTracingOptions }>(\n    name: string,\n    operationOptions?: Options,\n    spanOptions?: TracingSpanOptions\n  ): {\n    span: TracingSpan;\n    updatedOptions: OptionsWithTracingContext<Options>;\n  } {\n    const startSpanResult = getInstrumenter().startSpan(name, {\n      ...spanOptions,\n      packageName: packageName,\n      packageVersion: packageVersion,\n      tracingContext: operationOptions?.tracingOptions?.tracingContext,\n    });\n    let tracingContext = startSpanResult.tracingContext;\n    const span = startSpanResult.span;\n    if (!tracingContext.getValue(knownContextKeys.namespace)) {\n      tracingContext = tracingContext.setValue(knownContextKeys.namespace, namespace);\n    }\n    span.setAttribute(\"az.namespace\", tracingContext.getValue(knownContextKeys.namespace));\n    const updatedOptions: OptionsWithTracingContext<Options> = Object.assign({}, operationOptions, {\n      tracingOptions: { ...operationOptions?.tracingOptions, tracingContext },\n    });\n\n    return {\n      span,\n      updatedOptions,\n    };\n  }\n\n  async function withSpan<\n    Options extends { tracingOptions?: OperationTracingOptions },\n    Callback extends (\n      updatedOptions: Options,\n      span: Omit<TracingSpan, \"end\">\n    ) => ReturnType<Callback>\n  >(\n    name: string,\n    operationOptions: Options,\n    callback: Callback,\n    spanOptions?: TracingSpanOptions\n  ): Promise<Resolved<ReturnType<Callback>>> {\n    const { span, updatedOptions } = startSpan(name, operationOptions, spanOptions);\n    try {\n      const result = await withContext(updatedOptions.tracingOptions.tracingContext, () =>\n        Promise.resolve(callback(updatedOptions, span))\n      );\n      span.setStatus({ status: \"success\" });\n      return result as ReturnType<typeof withSpan>;\n    } catch (err: any) {\n      span.setStatus({ status: \"error\", error: err });\n      throw err;\n    } finally {\n      span.end();\n    }\n  }\n\n  function withContext<\n    CallbackArgs extends unknown[],\n    Callback extends (...args: CallbackArgs) => ReturnType<Callback>\n  >(\n    context: TracingContext,\n    callback: Callback,\n    ...callbackArgs: CallbackArgs\n  ): ReturnType<Callback> {\n    return getInstrumenter().withContext(context, callback, ...callbackArgs);\n  }\n\n  /**\n   * Parses a traceparent header value into a span identifier.\n   *\n   * @param traceparentHeader - The traceparent header to parse.\n   * @returns An implementation-specific identifier for the span.\n   */\n  function parseTraceparentHeader(traceparentHeader: string): TracingContext | undefined {\n    return getInstrumenter().parseTraceparentHeader(traceparentHeader);\n  }\n\n  /**\n   * Creates a set of request headers to propagate tracing information to a backend.\n   *\n   * @param tracingContext - The context containing the span to serialize.\n   * @returns The set of headers to add to a request.\n   */\n  function createRequestHeaders(tracingContext?: TracingContext): Record<string, string> {\n    return getInstrumenter().createRequestHeaders(tracingContext);\n  }\n\n  return {\n    startSpan,\n    withSpan,\n    withContext,\n    parseTraceparentHeader,\n    createRequestHeaders,\n  };\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAIA;AACO,MAAM,gBAAgB,GAAG;AAC9B,IAAA,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,0BAA0B,CAAC;AAC5C,IAAA,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,+BAA+B,CAAC;CACvD,CAAC;AAEF;;;;;;AAMG;AACa,SAAA,oBAAoB,CAAC,OAAA,GAAuC,EAAE,EAAA;IAC5E,IAAI,OAAO,GAAmB,IAAI,kBAAkB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;IAC5E,IAAI,OAAO,CAAC,IAAI,EAAE;AAChB,QAAA,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;AACjE,KAAA;IACD,IAAI,OAAO,CAAC,SAAS,EAAE;AACrB,QAAA,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AAC3E,KAAA;AACD,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;MACa,kBAAkB,CAAA;AAE7B,IAAA,WAAA,CAAY,cAA+B,EAAA;AACzC,QAAA,IAAI,CAAC,WAAW;AACd,YAAA,cAAc,YAAY,kBAAkB;AAC1C,kBAAE,IAAI,GAAG,CAAkB,cAAc,CAAC,WAAW,CAAC;AACtD,kBAAE,IAAI,GAAG,EAAE,CAAC;KACjB;IAED,QAAQ,CAAC,GAAW,EAAE,KAAc,EAAA;AAClC,QAAA,MAAM,UAAU,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAChD,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACvC,QAAA,OAAO,UAAU,CAAC;KACnB;AAED,IAAA,QAAQ,CAAC,GAAW,EAAA;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KAClC;AAED,IAAA,WAAW,CAAC,GAAW,EAAA;AACrB,QAAA,MAAM,UAAU,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAChD,QAAA,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACnC,QAAA,OAAO,UAAU,CAAC;KACnB;AACF;;ACtDD;SAMgB,wBAAwB,GAAA;IACtC,OAAO;QACL,GAAG,EAAE,MAAK;;SAET;AACD,QAAA,WAAW,EAAE,MAAM,KAAK;QACxB,eAAe,EAAE,MAAK;;SAErB;QACD,YAAY,EAAE,MAAK;;SAElB;QACD,SAAS,EAAE,MAAK;;SAEf;KACF,CAAC;AACJ,CAAC;SAEe,yBAAyB,GAAA;IACvC,OAAO;QACL,oBAAoB,EAAE,MAA6B;AACjD,YAAA,OAAO,EAAE,CAAC;SACX;QACD,sBAAsB,EAAE,MAAiC;AACvD,YAAA,OAAO,SAAS,CAAC;SAClB;AACD,QAAA,SAAS,EAAE,CACT,KAAa,EACb,WAAoC,KACqB;YACzD,OAAO;gBACL,IAAI,EAAE,wBAAwB,EAAE;gBAChC,cAAc,EAAE,oBAAoB,CAAC,EAAE,aAAa,EAAE,WAAW,CAAC,cAAc,EAAE,CAAC;aACpF,CAAC;SACH;AACD,QAAA,WAAW,CAIT,QAAwB,EACxB,QAAkB,EAClB,GAAG,YAA0B,EAAA;AAE7B,YAAA,OAAO,QAAQ,CAAC,GAAG,YAAY,CAAC,CAAC;SAClC;KACF,CAAC;AACJ,CAAC;AAED;AACA,IAAI,0BAAoD,CAAC;AAEzD;;;;AAIG;AACG,SAAU,eAAe,CAAC,YAA0B,EAAA;IACxD,0BAA0B,GAAG,YAAY,CAAC;AAC5C,CAAC;AAED;;;;AAIG;SACa,eAAe,GAAA;IAC7B,IAAI,CAAC,0BAA0B,EAAE;QAC/B,0BAA0B,GAAG,yBAAyB,EAAE,CAAC;AAC1D,KAAA;AACD,IAAA,OAAO,0BAA0B,CAAC;AACpC;;AC5EA;AAgBA;;;;;AAKG;AACG,SAAU,mBAAmB,CAAC,OAA6B,EAAA;IAC/D,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AAE3D,IAAA,SAAS,SAAS,CAChB,IAAY,EACZ,gBAA0B,EAC1B,WAAgC,EAAA;;AAKhC,QAAA,MAAM,eAAe,GAAG,eAAe,EAAE,CAAC,SAAS,CAAC,IAAI,EAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACnD,WAAW,CAAA,EAAA,EACd,WAAW,EAAE,WAAW,EACxB,cAAc,EAAE,cAAc,EAC9B,cAAc,EAAE,CAAA,EAAA,GAAA,gBAAgB,KAAhB,IAAA,IAAA,gBAAgB,KAAhB,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,gBAAgB,CAAE,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,cAAc,IAChE,CAAC;AACH,QAAA,IAAI,cAAc,GAAG,eAAe,CAAC,cAAc,CAAC;AACpD,QAAA,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC;QAClC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE;YACxD,cAAc,GAAG,cAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AACjF,SAAA;AACD,QAAA,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,cAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC;QACvF,MAAM,cAAc,GAAuC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,gBAAgB,EAAE;YAC7F,cAAc,EAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAO,gBAAgB,KAAA,IAAA,IAAhB,gBAAgB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAhB,gBAAgB,CAAE,cAAc,CAAE,EAAA,EAAA,cAAc,EAAE,CAAA;AACxE,SAAA,CAAC,CAAC;QAEH,OAAO;YACL,IAAI;YACJ,cAAc;SACf,CAAC;KACH;IAED,eAAe,QAAQ,CAOrB,IAAY,EACZ,gBAAyB,EACzB,QAAkB,EAClB,WAAgC,EAAA;AAEhC,QAAA,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,SAAS,CAAC,IAAI,EAAE,gBAAgB,EAAE,WAAW,CAAC,CAAC;QAChF,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,cAAc,CAAC,cAAc,CAAC,cAAc,EAAE,MAC7E,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,CAChD,CAAC;YACF,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;AACtC,YAAA,OAAO,MAAqC,CAAC;AAC9C,SAAA;AAAC,QAAA,OAAO,GAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AAChD,YAAA,MAAM,GAAG,CAAC;AACX,SAAA;AAAS,gBAAA;YACR,IAAI,CAAC,GAAG,EAAE,CAAC;AACZ,SAAA;KACF;AAED,IAAA,SAAS,WAAW,CAIlB,OAAuB,EACvB,QAAkB,EAClB,GAAG,YAA0B,EAAA;AAE7B,QAAA,OAAO,eAAe,EAAE,CAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,YAAY,CAAC,CAAC;KAC1E;AAED;;;;;AAKG;IACH,SAAS,sBAAsB,CAAC,iBAAyB,EAAA;AACvD,QAAA,OAAO,eAAe,EAAE,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,CAAC;KACpE;AAED;;;;;AAKG;IACH,SAAS,oBAAoB,CAAC,cAA+B,EAAA;AAC3D,QAAA,OAAO,eAAe,EAAE,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;KAC/D;IAED,OAAO;QACL,SAAS;QACT,QAAQ;QACR,WAAW;QACX,sBAAsB;QACtB,oBAAoB;KACrB,CAAC;AACJ;;;;;"}