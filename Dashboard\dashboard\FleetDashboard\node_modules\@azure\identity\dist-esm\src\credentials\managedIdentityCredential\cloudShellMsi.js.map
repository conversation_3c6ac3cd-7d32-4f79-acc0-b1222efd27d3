{"version": 3, "file": "cloudShellMsi.js", "sourceRoot": "", "sources": ["../../../../src/credentials/managedIdentityCredential/cloudShellMsi.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAEL,iBAAiB,EACjB,qBAAqB,GACtB,MAAM,2BAA2B,CAAC;AACnC,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAGtD,OAAO,EAAE,mBAAmB,EAAE,MAAM,SAAS,CAAC;AAE9C,MAAM,OAAO,GAAG,2CAA2C,CAAC;AAC5D,MAAM,CAAC,MAAM,MAAM,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAEhD;;GAEG;AACH,SAAS,qBAAqB,CAC5B,MAAyB,EACzB,QAAiB,EACjB,UAAmB;IAEnB,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAC7C,IAAI,CAAC,QAAQ,EAAE;QACb,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,sCAAsC,CAAC,CAAC;KACnE;IAED,MAAM,IAAI,GAA2B;QACnC,QAAQ;KACT,CAAC;IAEF,IAAI,QAAQ,EAAE;QACZ,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;KAC3B;IACD,IAAI,UAAU,EAAE;QACd,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;KAC9B;IAED,wIAAwI;IACxI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE;QAC7B,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,8CAA8C,CAAC,CAAC;KAC3E;IACD,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC;IACzC,OAAO;QACL,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY;QAC7B,MAAM,EAAE,MAAM;QACd,IAAI,EAAE,MAAM,CAAC,QAAQ,EAAE;QACvB,OAAO,EAAE,iBAAiB,CAAC;YACzB,MAAM,EAAE,kBAAkB;YAC1B,QAAQ,EAAE,MAAM;YAChB,cAAc,EAAE,mCAAmC;SACpD,CAAC;KACH,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,MAAM,aAAa,GAAQ;IAChC,KAAK,CAAC,WAAW,CAAC,EAAE,MAAM,EAAE;QAC1B,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,EAAE;YACb,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,mDAAmD,CAAC,CAAC;YAC3E,OAAO,KAAK,CAAC;SACd;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACjD,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,iEAAiE,CAAC,CAAC;SAC1F;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,KAAK,CAAC,QAAQ,CACZ,aAA+B,EAC/B,kBAAmC,EAAE;QAErC,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,aAAa,CAAC;QAEvE,IAAI,QAAQ,EAAE;YACZ,MAAM,CAAC,OAAO,CACZ,GAAG,OAAO,kGAAkG,CAC7G,CAAC;SACH;QAED,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,OAAO,CACZ,GAAG,OAAO,wHAAwH,CACnI,CAAC;SACH;QAED,MAAM,CAAC,IAAI,CACT,GAAG,OAAO,4EAA4E,OAAO,CAAC,GAAG,CAAC,YAAY,GAAG,CAClH,CAAC;QAEF,MAAM,OAAO,GAAG,qBAAqB,+BACnC,WAAW,EAAE,eAAe,CAAC,WAAW,IACrC,qBAAqB,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC;YACtD,0FAA0F;YAC1F,uBAAuB,EAAE,IAAI,IAC7B,CAAC;QACH,MAAM,aAAa,GAAG,MAAM,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACrE,OAAO,CAAC,aAAa,IAAI,aAAa,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC;IAC9D,CAAC;CACF,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  PipelineRequestOptions,\n  createHttpHeaders,\n  createPipelineRequest,\n} from \"@azure/core-rest-pipeline\";\nimport { credentialLogger } from \"../../util/logging\";\nimport { AccessToken, GetTokenOptions } from \"@azure/core-auth\";\nimport { MSI, MSIConfiguration } from \"./models\";\nimport { mapScopesToResource } from \"./utils\";\n\nconst msiName = \"ManagedIdentityCredential - CloudShellMSI\";\nexport const logger = credentialLogger(msiName);\n\n/**\n * Generates the options used on the request for an access token.\n */\nfunction prepareRequestOptions(\n  scopes: string | string[],\n  clientId?: string,\n  resourceId?: string\n): PipelineRequestOptions {\n  const resource = mapScopesToResource(scopes);\n  if (!resource) {\n    throw new Error(`${msiName}: Multiple scopes are not supported.`);\n  }\n\n  const body: Record<string, string> = {\n    resource,\n  };\n\n  if (clientId) {\n    body.client_id = clientId;\n  }\n  if (resourceId) {\n    body.msi_res_id = resourceId;\n  }\n\n  // This error should not bubble up, since we verify that this environment variable is defined in the isAvailable() method defined below.\n  if (!process.env.MSI_ENDPOINT) {\n    throw new Error(`${msiName}: Missing environment variable: MSI_ENDPOINT`);\n  }\n  const params = new URLSearchParams(body);\n  return {\n    url: process.env.MSI_ENDPOINT,\n    method: \"POST\",\n    body: params.toString(),\n    headers: createHttpHeaders({\n      Accept: \"application/json\",\n      Metadata: \"true\",\n      \"Content-Type\": \"application/x-www-form-urlencoded\",\n    }),\n  };\n}\n\n/**\n * Defines how to determine whether the Azure Cloud Shell MSI is available, and also how to retrieve a token from the Azure Cloud Shell MSI.\n * Since Azure Managed Identities aren't available in the Azure Cloud Shell, we log a warning for users that try to access cloud shell using user assigned identity.\n */\nexport const cloudShellMsi: MSI = {\n  async isAvailable({ scopes }): Promise<boolean> {\n    const resource = mapScopesToResource(scopes);\n    if (!resource) {\n      logger.info(`${msiName}: Unavailable. Multiple scopes are not supported.`);\n      return false;\n    }\n\n    const result = Boolean(process.env.MSI_ENDPOINT);\n    if (!result) {\n      logger.info(`${msiName}: Unavailable. The environment variable MSI_ENDPOINT is needed.`);\n    }\n    return result;\n  },\n  async getToken(\n    configuration: MSIConfiguration,\n    getTokenOptions: GetTokenOptions = {}\n  ): Promise<AccessToken | null> {\n    const { identityClient, scopes, clientId, resourceId } = configuration;\n\n    if (clientId) {\n      logger.warning(\n        `${msiName}: user-assigned identities not supported. The argument clientId might be ignored by the service.`\n      );\n    }\n\n    if (resourceId) {\n      logger.warning(\n        `${msiName}: user defined managed Identity by resource Id not supported. The argument resourceId might be ignored by the service.`\n      );\n    }\n\n    logger.info(\n      `${msiName}: Using the endpoint coming form the environment variable MSI_ENDPOINT = ${process.env.MSI_ENDPOINT}.`\n    );\n\n    const request = createPipelineRequest({\n      abortSignal: getTokenOptions.abortSignal,\n      ...prepareRequestOptions(scopes, clientId, resourceId),\n      // Generally, MSI endpoints use the HTTP protocol, without transport layer security (TLS).\n      allowInsecureConnection: true,\n    });\n    const tokenResponse = await identityClient.sendTokenRequest(request);\n    return (tokenResponse && tokenResponse.accessToken) || null;\n  },\n};\n"]}