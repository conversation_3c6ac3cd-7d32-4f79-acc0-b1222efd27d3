{"version": 3, "file": "xml.js", "sourceRoot": "", "sources": ["../../../src/util/xml.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,KAAK,MAAM,MAAM,QAAQ,CAAC;AACjC,OAAO,EAAqB,WAAW,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAElF,qIAAqI;AACrI,mGAAmG;AACnG,8EAA8E;AAC9E,+GAA+G;AAC/G,MAAM,sBAAsB,GAAqB;IAC/C,eAAe,EAAE,KAAK;IACtB,IAAI,EAAE,KAAK;IACX,SAAS,EAAE,KAAK;IAChB,aAAa,EAAE,KAAK;IACpB,OAAO,EAAE,WAAW;IACpB,aAAa,EAAE,IAAI;IACnB,WAAW,EAAE,KAAK;IAClB,UAAU,EAAE,KAAK;IACjB,YAAY,EAAE,IAAI;IAClB,SAAS,EAAE,SAAS;IACpB,KAAK,EAAE,KAAK;IACZ,gBAAgB,EAAE,KAAK;IACvB,qBAAqB,EAAE,KAAK;IAC5B,QAAQ,EAAE,IAAI;IACd,eAAe,EAAE,KAAK;IACtB,iBAAiB,EAAE,KAAK;IACxB,KAAK,EAAE,KAAK;IACZ,MAAM,EAAE,IAAI;IACZ,kBAAkB,EAAE,SAAS;IAC7B,mBAAmB,EAAE,SAAS;IAC9B,iBAAiB,EAAE,SAAS;IAC5B,eAAe,EAAE,SAAS;IAC1B,QAAQ,EAAE,MAAM;IAChB,MAAM,EAAE;QACN,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,OAAO;QACjB,UAAU,EAAE,IAAI;KACjB;IACD,OAAO,EAAE,SAAS;IAClB,UAAU,EAAE;QACV,MAAM,EAAE,IAAI;QACZ,MAAM,EAAE,IAAI;QACZ,OAAO,EAAE,IAAI;KACd;IACD,QAAQ,EAAE,KAAK;IACf,SAAS,EAAE,KAAK;IAChB,QAAQ,EAAE,EAAE;IACZ,KAAK,EAAE,KAAK;CACb,CAAC;AAEF,0DAA0D;AAC1D,MAAM,oBAAoB,GAAQ,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,sBAAsB,CAAC,CAAC;AAC5E,oBAAoB,CAAC,aAAa,GAAG,KAAK,CAAC;AAE3C,2DAA2D;AAC3D,MAAM,qBAAqB,GAAQ,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,sBAAsB,CAAC,CAAC;AAC7E,qBAAqB,CAAC,aAAa,GAAG,KAAK,CAAC;AAC5C,qBAAqB,CAAC,UAAU,GAAG;IACjC,MAAM,EAAE,KAAK;CACd,CAAC;AAEF;;;;GAIG;AACH,MAAM,UAAU,YAAY,CAAC,GAAY,EAAE,OAA0B,EAAE;;IACrE,qBAAqB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;IAC/C,qBAAqB,CAAC,OAAO,GAAG,MAAA,IAAI,CAAC,UAAU,mCAAI,WAAW,CAAC;IAC/D,MAAM,OAAO,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;IAC1D,OAAO,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AAClC,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,QAAQ,CAAC,GAAW,EAAE,OAA0B,EAAE;;IAChE,oBAAoB,CAAC,YAAY,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC;IACvD,oBAAoB,CAAC,OAAO,GAAG,MAAA,IAAI,CAAC,UAAU,mCAAI,WAAW,CAAC;IAC9D,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;IAC1D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,IAAI,CAAC,GAAG,EAAE;YACR,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC;SACxC;aAAM;YACL,SAAS,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAQ,EAAE,GAAQ,EAAE,EAAE;gBAChD,IAAI,GAAG,EAAE;oBACP,MAAM,CAAC,GAAG,CAAC,CAAC;iBACb;qBAAM;oBACL,OAAO,CAAC,GAAG,CAAC,CAAC;iBACd;YACH,CAAC,CAAC,CAAC;SACJ;IACH,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as xml2js from \"xml2js\";\nimport { SerializerOptions, XML_ATTRKEY, XML_CHARKEY } from \"./serializer.common\";\n\n// Note: The reason we re-define all of the xml2js default settings (version 2.0) here is because the default settings object exposed\n// by the xm2js library is mutable. See https://github.com/Leon<PERSON>-from-XIV/node-xml2js/issues/536\n// By creating a new copy of the settings each time we instantiate the parser,\n// we are safeguarding against the possibility of the default settings being mutated elsewhere unintentionally.\nconst xml2jsDefaultOptionsV2: xml2js.OptionsV2 = {\n  explicitCharkey: false,\n  trim: false,\n  normalize: false,\n  normalizeTags: false,\n  attrkey: XML_ATTRKEY,\n  explicitArray: true,\n  ignoreAttrs: false,\n  mergeAttrs: false,\n  explicitRoot: true,\n  validator: undefined,\n  xmlns: false,\n  explicitChildren: false,\n  preserveChildrenOrder: false,\n  childkey: \"$$\",\n  charsAsChildren: false,\n  includeWhiteChars: false,\n  async: false,\n  strict: true,\n  attrNameProcessors: undefined,\n  attrValueProcessors: undefined,\n  tagNameProcessors: undefined,\n  valueProcessors: undefined,\n  rootName: \"root\",\n  xmldec: {\n    version: \"1.0\",\n    encoding: \"UTF-8\",\n    standalone: true,\n  },\n  doctype: undefined,\n  renderOpts: {\n    pretty: true,\n    indent: \"  \",\n    newline: \"\\n\",\n  },\n  headless: false,\n  chunkSize: 10000,\n  emptyTag: \"\",\n  cdata: false,\n};\n\n// The xml2js settings for general XML parsing operations.\nconst xml2jsParserSettings: any = Object.assign({}, xml2jsDefaultOptionsV2);\nxml2jsParserSettings.explicitArray = false;\n\n// The xml2js settings for general XML building operations.\nconst xml2jsBuilderSettings: any = Object.assign({}, xml2jsDefaultOptionsV2);\nxml2jsBuilderSettings.explicitArray = false;\nxml2jsBuilderSettings.renderOpts = {\n  pretty: false,\n};\n\n/**\n * Converts given JSON object to XML string\n * @param obj - JSON object to be converted into XML string\n * @param opts - Options that govern the parsing of given JSON object\n */\nexport function stringifyXML(obj: unknown, opts: SerializerOptions = {}): string {\n  xml2jsBuilderSettings.rootName = opts.rootName;\n  xml2jsBuilderSettings.charkey = opts.xmlCharKey ?? XML_CHARKEY;\n  const builder = new xml2js.Builder(xml2jsBuilderSettings);\n  return builder.buildObject(obj);\n}\n\n/**\n * Converts given XML string into JSON\n * @param str - String containing the XML content to be parsed into JSON\n * @param opts - Options that govern the parsing of given xml string\n */\nexport function parseXML(str: string, opts: SerializerOptions = {}): Promise<any> {\n  xml2jsParserSettings.explicitRoot = !!opts.includeRoot;\n  xml2jsParserSettings.charkey = opts.xmlCharKey ?? XML_CHARKEY;\n  const xmlParser = new xml2js.Parser(xml2jsParserSettings);\n  return new Promise((resolve, reject) => {\n    if (!str) {\n      reject(new Error(\"Document is empty\"));\n    } else {\n      xmlParser.parseString(str, (err: any, res: any) => {\n        if (err) {\n          reject(err);\n        } else {\n          resolve(res);\n        }\n      });\n    }\n  });\n}\n"]}