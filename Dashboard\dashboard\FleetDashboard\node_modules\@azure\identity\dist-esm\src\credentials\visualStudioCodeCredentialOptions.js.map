{"version": 3, "file": "visualStudioCodeCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/visualStudioCodeCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions\";\n\n/**\n * Provides options to configure the Visual Studio Code credential.\n */\nexport interface VisualStudioCodeCredentialOptions extends MultiTenantTokenCredentialOptions {\n  /**\n   * Optionally pass in a Tenant ID to be used as part of the credential\n   */\n  tenantId?: string;\n}\n"]}