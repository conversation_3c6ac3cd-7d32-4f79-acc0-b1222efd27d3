{"version": 3, "file": "throttlingRetryPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/throttlingRetryPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,uBAAuB,EAAE,MAAM,4CAA4C,CAAC;AACrF,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,0BAA0B,EAAE,MAAM,cAAc,CAAC;AAE1D;;GAEG;AACH,MAAM,CAAC,MAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAYjE;;;;;;;;;GASG;AACH,MAAM,UAAU,qBAAqB,CAAC,UAAwC,EAAE;;IAC9E,OAAO;QACL,IAAI,EAAE,yBAAyB;QAC/B,WAAW,EAAE,WAAW,CAAC,CAAC,uBAAuB,EAAE,CAAC,EAAE;YACpD,UAAU,EAAE,MAAA,OAAO,CAAC,UAAU,mCAAI,0BAA0B;SAC7D,CAAC,CAAC,WAAW;KACf,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PipelinePolicy } from \"../pipeline\";\nimport { throttlingRetryStrategy } from \"../retryStrategies/throttlingRetryStrategy\";\nimport { retryPolicy } from \"./retryPolicy\";\nimport { DEFAULT_RETRY_POLICY_COUNT } from \"../constants\";\n\n/**\n * Name of the {@link throttlingRetryPolicy}\n */\nexport const throttlingRetryPolicyName = \"throttlingRetryPolicy\";\n\n/**\n * Options that control how to retry failed requests.\n */\nexport interface ThrottlingRetryPolicyOptions {\n  /**\n   * The maximum number of retry attempts. Defaults to 3.\n   */\n  maxRetries?: number;\n}\n\n/**\n * A policy that retries when the server sends a 429 response with a Retry-After header.\n *\n * To learn more, please refer to\n * https://docs.microsoft.com/en-us/azure/azure-resource-manager/resource-manager-request-limits,\n * https://docs.microsoft.com/en-us/azure/azure-subscription-service-limits and\n * https://docs.microsoft.com/en-us/azure/virtual-machines/troubleshooting/troubleshooting-throttling-errors\n *\n * @param options - Options that configure retry logic.\n */\nexport function throttlingRetryPolicy(options: ThrottlingRetryPolicyOptions = {}): PipelinePolicy {\n  return {\n    name: throttlingRetryPolicyName,\n    sendRequest: retryPolicy([throttlingRetryStrategy()], {\n      maxRetries: options.maxRetries ?? DEFAULT_RETRY_POLICY_COUNT,\n    }).sendRequest,\n  };\n}\n"]}