{"version": 3, "file": "msalClientAssertion.js", "sourceRoot": "", "sources": ["../../../../src/msal/nodeFlows/msalClientAssertion.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,QAAQ,EAAmB,MAAM,kBAAkB,CAAC;AAG7D,OAAO,EAAE,OAAO,EAAE,MAAM,kBAAkB,CAAC;AAa3C;;;GAGG;AACH,MAAM,OAAO,mBAAoB,SAAQ,QAAQ;IAE/C,YAAY,OAAmC;QAC7C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;IAC3C,CAAC;IAES,KAAK,CAAC,UAAU,CACxB,MAAgB,EAChB,UAAyC,EAAE;QAE3C,IAAI;YACF,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YAC5C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAgB,CAAC,8BAA8B,CAAC;gBACxE,MAAM;gBACN,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,eAAe,EAAE,SAAS;aAC3B,CAAC,CAAC;YACH,yDAAyD;YACzD,8FAA8F;YAC9F,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,CAAC;SACtE;QAAC,OAAO,GAAY,EAAE;YACrB,IAAI,IAAI,GAAG,GAAG,CAAC;YACf,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;gBACrC,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;aACvC;iBAAM;gBACL,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;aACpD;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAa,EAAE,OAAO,CAAC,CAAC;SACxD;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { <PERSON>al<PERSON><PERSON>, MsalNodeOptions } from \"./msalNodeCommon\";\nimport { AccessToken } from \"@azure/core-auth\";\nimport { CredentialFlowGetTokenOptions } from \"../credentials\";\nimport { isError } from \"@azure/core-util\";\n\n/**\n * Options that can be passed to configure MSAL to handle client assertions.\n * @internal\n */\nexport interface MsalClientAssertionOptions extends MsalNodeOptions {\n  /**\n   * A function that retrieves the assertion for the credential to use.\n   */\n  getAssertion: () => Promise<string>;\n}\n\n/**\n * MSAL client assertion client. Calls to MSAL's confidential application's `acquireTokenByClientCredential` during `doGetToken`.\n * @internal\n */\nexport class MsalClientAssertion extends MsalNode {\n  getAssertion: () => Promise<string>;\n  constructor(options: MsalClientAssertionOptions) {\n    super(options);\n    this.requiresConfidential = true;\n    this.getAssertion = options.getAssertion;\n  }\n\n  protected async doGetToken(\n    scopes: string[],\n    options: CredentialFlowGetTokenOptions = {}\n  ): Promise<AccessToken> {\n    try {\n      const assertion = await this.getAssertion();\n      const result = await this.confidentialApp!.acquireTokenByClientCredential({\n        scopes,\n        correlationId: options.correlationId,\n        azureRegion: this.azureRegion,\n        authority: options.authority,\n        claims: options.claims,\n        clientAssertion: assertion,\n      });\n      // The Client Credential flow does not return an account,\n      // so each time getToken gets called, we will have to acquire a new token through the service.\n      return this.handleResult(scopes, this.clientId, result || undefined);\n    } catch (err: unknown) {\n      let err2 = err;\n      if (err === null || err === undefined) {\n        err2 = new Error(JSON.stringify(err));\n      } else {\n        err2 = isError(err) ? err : new Error(String(err));\n      }\n      throw this.handleError(scopes, err2 as Error, options);\n    }\n  }\n}\n"]}