{"version": 3, "file": "tracing.js", "sourceRoot": "", "sources": ["../../src/tracing.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,mBAAmB,EAAE,MAAM,qBAAqB,CAAC;AAC1D,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;AAE1C;;;GAGG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG,mBAAmB,CAAC;IAC/C,SAAS,EAAE,wBAAwB;IACnC,WAAW,EAAE,2BAA2B;IACxC,cAAc,EAAE,WAAW;CAC5B,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { createTracingClient } from \"@azure/core-tracing\";\nimport { SDK_VERSION } from \"./constants\";\n\n/**\n * Creates a tracing client to manage tracing spans.\n * @internal\n */\nexport const tracingClient = createTracingClient({\n  namespace: \"Microsoft.DigitalTwins\",\n  packageName: \"@azure/digital-twins-core\",\n  packageVersion: SDK_VERSION,\n});\n"]}