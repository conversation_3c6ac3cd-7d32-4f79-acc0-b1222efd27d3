// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
import { __asyncDelegator, __asyncGenerator, __asyncValues, __await, __rest } from "tslib";
/// <reference lib="esnext.asynciterable" />
import { bearerTokenAuthenticationPolicy, createPipelineFromOptions, generateUuid, } from "@azure/core-http";
import { AzureDigitalTwinsAPI as GeneratedClient } from "./generated/azureDigitalTwinsAPI";
import { tracingClient } from "./tracing";
import { logger } from "./logger";
export const SDK_VERSION = "1.1.0";
const DEFAULT_DIGITALTWINS_SCOPE = "https://digitaltwins.azure.net/.default";
/**
 * Client for Azure IoT DigitalTwins API.
 */
export class DigitalTwinsClient {
    /**
     * Creates an instance of AzureDigitalTwinsAPI.
     *
     * Example usage:
     * ```ts
     * const { DigitalTwinsClient, ServiceClientCredentials } = require("@azure/digital-twins-core");
     *
     * const client = new DigitalTwinsClient(
     *   "<endpoint>",
     *   new DefaultAzureCredential();
     * );
     * ```
     * @param endpointUrl - The endpoint URL of the service.
     * @param credential - Used to authenticate requests to the service.
     * @param options - Used to configure the service client.
     */
    constructor(endpointUrl, credential, options = {}) {
        const authPolicy = bearerTokenAuthenticationPolicy(credential, DEFAULT_DIGITALTWINS_SCOPE);
        const libInfo = `azsdk-js-digital-twins-core/${SDK_VERSION}`;
        const { apiVersion } = options, pipelineOptions = __rest(options, ["apiVersion"]);
        if (!pipelineOptions.userAgentOptions) {
            pipelineOptions.userAgentOptions = {};
        }
        if (pipelineOptions.userAgentOptions.userAgentPrefix) {
            pipelineOptions.userAgentOptions.userAgentPrefix = `${pipelineOptions.userAgentOptions.userAgentPrefix} ${libInfo}`;
        }
        else {
            pipelineOptions.userAgentOptions.userAgentPrefix = libInfo;
        }
        const internalPipelineOptions = Object.assign(Object.assign({}, pipelineOptions), {
            loggingOptions: {
                logger: logger.info,
                allowedHeaderNames: ["x-ms-request-id"],
            },
        });
        const pipeline = createPipelineFromOptions(internalPipelineOptions, authPolicy);
        this.client = new GeneratedClient(Object.assign({ endpoint: endpointUrl, apiVersion }, pipeline));
    }
    /**
     * Get a digital twin
     *
     * @param digitalTwinId - The Id of the digital twin.
     * @param options - The operation options
     * @returns The application/json digital twin and the http response.
     */
    getDigitalTwin(digitalTwinId, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.getDigitalTwin", options, async (updatedOptions) => {
            return this.client.digitalTwins.getById(digitalTwinId, updatedOptions);
        });
    }
    /**
     * Create or update a digital twin
     *
     * @param digitalTwinId - The Id of the digital twin to create or update.
     * @param digitalTwinJson - The application/json digital twin to create.
     * @param options - Extended operation options including
     *  ifNoneMatch: Only perform the operation if the entity does not already exist.
     * @returns The created application/json digital twin and the http response.
     */
    upsertDigitalTwin(digitalTwinId, digitalTwinJson, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.upsertDigitalTwin", options, async (updatedOptions) => {
            const payload = JSON.parse(digitalTwinJson);
            return this.client.digitalTwins.add(digitalTwinId, payload, updatedOptions);
        });
    }
    /**
     * Update a digital twin using a json patch.
     *
     * @param digitalTwinId - The Id of the digital twin.
     * @param jsonPatch - An update specification described by JSON Patch. Updates to property values
     * and $model elements may happen in the same request. Operations are limited to add, replace and
     * remove.
     * @param options - Extended operation options including
     *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is provided.
     * @returns The http response.
     */
    updateDigitalTwin(digitalTwinId, 
    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types -- changing the type any would be a breaking change
    jsonPatch, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.updateDigitalTwin", options, async (updatedOptions) => {
            return this.client.digitalTwins.update(digitalTwinId, jsonPatch, updatedOptions);
        });
    }
    /**
     * Delete a digital twin
     *
     * @param digitalTwinId - The Id of the digital twin to delete.
     * @param options - Extended operation options including
     *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is provided.
     * @returns The http response.
     */
    deleteDigitalTwin(digitalTwinId, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.deleteDigitalTwin", options, async (updatedOptions) => {
            return this.client.digitalTwins.delete(digitalTwinId, updatedOptions);
        });
    }
    /**
     * Get a component on a digital twin.
     *
     * @param digitalTwinId - The Id of the digital twin.
     * @param componentName - The component being retrieved.
     * @param options - The operation options
     * @returns Json string representation of the component corresponding to the provided componentName and the HTTP response.
     */
    getComponent(digitalTwinId, componentName, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.getComponent", options, async (updatedOptions) => {
            return this.client.digitalTwins.getComponent(digitalTwinId, componentName, updatedOptions);
        });
    }
    /**
     * Update properties of a component on a digital twin using a JSON patch.
     *
     * @param digitalTwinId - The Id of the digital twin.
     * @param componentName - The component being updated.
     * @param jsonPatch - The application/json-patch+json operations to be performed on the specified digital twin's component.
     * @param enableUpdate - If true then update of an existing digital twin is enabled.
     * @param options - Extended operation options including
     *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is provided.
     * @returns The http response.
     */
    updateComponent(digitalTwinId, componentName, jsonPatch, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.updateComponent", options, async (updatedOptions) => {
            return this.client.digitalTwins.updateComponent(digitalTwinId, componentName, jsonPatch, updatedOptions);
        });
    }
    /**
     * Get a relationship on a digital twin.
     *
     * @param digitalTwinId - The Id of the source digital twin.
     * @param relationshipId - The Id of the relationship to retrieve.
     * @param options - The operation options
     * @returns The pageable list of application/json relationships belonging to the specified digital twin and the http response.
     */
    getRelationship(digitalTwinId, relationshipId, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.getRelationship", options, async (updatedOptions) => {
            return this.client.digitalTwins.getRelationshipById(digitalTwinId, relationshipId, updatedOptions);
        });
    }
    /**
     * Create or update a relationship on a digital twin.
     *
     * @param digitalTwinId - The Id of the source digital twin.
     * @param relationshipId - The Id of the relationship to create.
     * @param relationship - The application/json relationship to be created.
     * @param options - Extended operation options including
     *  ifNoneMatch: Only perform the operation if the entity does not already exist.
     */
    upsertRelationship(digitalTwinId, relationshipId, 
    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types -- changing the type any would be a breaking change
    relationship, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.upsertRelationship", options, async (updatedOptions) => {
            return this.client.digitalTwins.addRelationship(digitalTwinId, relationshipId, relationship, updatedOptions);
        });
    }
    /**
     * Updates the properties of a relationship on a digital twin using a JSON patch.
     *
     * @param digitalTwinId - The Id of the digital twin to delete.
     * @param relationshipId - The Id of the relationship to be updated.
     * @param jsonPatch - The application/json-patch+json operations to be performed on the specified digital twin's relationship.
     * @param options - Extended operation options
     *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is provided.
     */
    updateRelationship(digitalTwinId, relationshipId, jsonPatch, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.updateRelationship", options, async (updatedOptions) => {
            return this.client.digitalTwins.updateRelationship(digitalTwinId, relationshipId, jsonPatch, updatedOptions);
        });
    }
    /**
     * Delete a relationship on a digital twin.
     *
     * @param digitalTwinId - The Id of the source digital twin.
     * @param relationshipId - The Id of the relationship to delete.
     * @param options - The operation options
     *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is
     * @returns The http response.
     */
    deleteRelationship(digitalTwinId, relationshipId, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.deleteRelationship", options, async (updatedOptions) => {
            return this.client.digitalTwins.deleteRelationship(digitalTwinId, relationshipId, updatedOptions);
        });
    }
    /**
     * Deals with the pagination of {@link listRelationships}.
     *
     * @param digitalTwinId - The Id of the digital twin.
     * @param options - Common options for the iterative endpoints.
     * @param continuationState - An object that indicates the position of the paginated request.
     *
     */
    listRelationshipsPage(digitalTwinId, options, continuationState) {
        return __asyncGenerator(this, arguments, function* listRelationshipsPage_1() {
            if (continuationState.continuationToken == null) {
                const optionsComplete = Object.assign({}, options);
                const listRelationshipResponse = yield __await(this.client.digitalTwins.listRelationships(digitalTwinId, optionsComplete));
                continuationState.continuationToken = listRelationshipResponse.nextLink;
                yield yield __await(listRelationshipResponse);
            }
            while (continuationState.continuationToken) {
                const listRelationshipResponse = yield __await(this.client.digitalTwins.listRelationshipsNext("", continuationState.continuationToken, options));
                continuationState.continuationToken = listRelationshipResponse.nextLink;
                yield yield __await(listRelationshipResponse);
            }
        });
    }
    /**
     * Deals with the iteration of all the available results of {@link listRelationships}.
     * @param options - Common options for the iterative endpoints.
     */
    listRelationshipsAll(digitalTwinId, options) {
        return __asyncGenerator(this, arguments, function* listRelationshipsAll_1() {
            var e_1, _a;
            try {
                for (var _b = __asyncValues(this.listRelationshipsPage(digitalTwinId, options, {})), _c; _c = yield __await(_b.next()), !_c.done;) {
                    const page = _c.value;
                    const value = page.value || [];
                    for (const item of value) {
                        yield yield __await(item);
                    }
                }
            }
            catch (e_1_1) { e_1 = { error: e_1_1 }; }
            finally {
                try {
                    if (_c && !_c.done && (_a = _b.return)) yield __await(_a.call(_b));
                }
                finally { if (e_1) throw e_1.error; }
            }
        });
    }
    /**
     * Retrieve relationships for a digital twin.
     *
     * @param digitalTwinId - The Id of the digital twin.
     */
    listRelationships(digitalTwinId, options = {}) {
        const iter = this.listRelationshipsAll(digitalTwinId, options);
        return {
            next() {
                return iter.next();
            },
            [Symbol.asyncIterator]() {
                return this;
            },
            byPage: (settings = {}) => this.listRelationshipsPage(digitalTwinId, options, settings),
        };
    }
    /**
     * Deals with the pagination of {@link listIncomingRelationships}.
     *
     * @param digitalTwinId - The Id of the digital twin.
     * @param options - Common options for the iterative endpoints.
     * @param continuationState - An object that indicates the position of the paginated request.
     *
     */
    listIncomingRelationshipsPage(digitalTwinId, options, continuationState) {
        return __asyncGenerator(this, arguments, function* listIncomingRelationshipsPage_1() {
            if (continuationState.continuationToken == null) {
                const optionsComplete = Object.assign({}, options);
                const listIncomingRelationshipsResponse = yield __await(this.client.digitalTwins.listIncomingRelationships(digitalTwinId, optionsComplete));
                continuationState.continuationToken = listIncomingRelationshipsResponse.nextLink;
                yield yield __await(listIncomingRelationshipsResponse);
            }
            while (continuationState.continuationToken) {
                const listIncomingRelationshipsResponse = yield __await(this.client.digitalTwins.listIncomingRelationshipsNext("", continuationState.continuationToken, options));
                continuationState.continuationToken = listIncomingRelationshipsResponse.nextLink;
                yield yield __await(listIncomingRelationshipsResponse);
            }
        });
    }
    /**
     * Deals with the iteration of all the available results of {@link listIncomingRelationships}.
     * @param digitalTwinId - The Id of the digital twin.
     * @param options - Common options for the iterative endpoints.
     */
    listIncomingRelationshipsAll(digitalTwinId, options) {
        return __asyncGenerator(this, arguments, function* listIncomingRelationshipsAll_1() {
            var e_2, _a;
            try {
                for (var _b = __asyncValues(this.listIncomingRelationshipsPage(digitalTwinId, options, {})), _c; _c = yield __await(_b.next()), !_c.done;) {
                    const page = _c.value;
                    const value = page.value || [];
                    yield __await(yield* __asyncDelegator(__asyncValues(value)));
                }
            }
            catch (e_2_1) { e_2 = { error: e_2_1 }; }
            finally {
                try {
                    if (_c && !_c.done && (_a = _b.return)) yield __await(_a.call(_b));
                }
                finally { if (e_2) throw e_2.error; }
            }
        });
    }
    /**
     * Retrieve all incoming relationships for a digital twin.
     *
     * @param digitalTwinId - The Id of the digital twin.
     */
    listIncomingRelationships(digitalTwinId, options = {}) {
        const iter = this.listIncomingRelationshipsAll(digitalTwinId, options);
        return {
            next() {
                return iter.next();
            },
            [Symbol.asyncIterator]() {
                return this;
            },
            byPage: (settings = {}) => this.listIncomingRelationshipsPage(digitalTwinId, options, settings),
        };
    }
    /**
     * Publish telemetry from a digital twin, which is then consumed by one or many destination endpoints (subscribers) defined under.
     *
     * @param digitalTwinId - The Id of the digital twin to delete.
     * @param payload - The application/json telemetry payload to be sent.
     * @param messageId - The message Id.
     * @param options - The operation options
     * @returns The http response.
     */
    publishTelemetry(digitalTwinId, 
    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types -- changing the type any would be a breaking change
    payload, messageId, options = {}) {
        const digitalTwinsSendTelemetryOptionalParams = options;
        digitalTwinsSendTelemetryOptionalParams.telemetrySourceTime = new Date().toISOString();
        if (!messageId) {
            messageId = generateUuid();
        }
        return tracingClient.withSpan("DigitalTwinsClient.publishTelemetry", digitalTwinsSendTelemetryOptionalParams, async (updatedOptions) => {
            return this.client.digitalTwins.sendTelemetry(digitalTwinId, messageId, payload, updatedOptions);
        });
    }
    /**
     * Publish telemetry from a digital twin's component, which is then consumed by one or many destination endpoints (subscribers) defined under.
     *
     * @param digitalTwinId - The Id of the digital twin to delete.
     * @param componentName - The name of the DTDL component.
     * @param payload - The application/json telemetry payload to be sent.
     * @param messageId - The message Id.
     * @param options - The operation options
     * @returns The http response.
     */
    publishComponentTelemetry(digitalTwinId, componentName, payload, messageId, options = {}) {
        const digitalTwinsSendComponentTelemetryOptionalParams = options;
        digitalTwinsSendComponentTelemetryOptionalParams.telemetrySourceTime = new Date().toISOString();
        if (!messageId) {
            messageId = generateUuid();
        }
        return tracingClient.withSpan("DigitalTwinsClient.publishComponentTelemetry", digitalTwinsSendComponentTelemetryOptionalParams, async (updatedOptions) => {
            return this.client.digitalTwins.sendComponentTelemetry(digitalTwinId, componentName, payload, messageId, updatedOptions);
        });
    }
    /**
     * Get a model, including the model metadata and the model definition.
     *
     * @param modelId - The Id of the model.
     * @param options - Extended operation options including
     *  includeModelDefinition: When true the model definition will be returned as part of the result. Default value: false.
     * @returns The application/json model and the http response.
     */
    getModel(modelId, includeModelDefinition = false, options = {}) {
        const digitalTwinModelsGetByIdOptionalParams = options;
        digitalTwinModelsGetByIdOptionalParams.includeModelDefinition = includeModelDefinition;
        return tracingClient.withSpan("DigitalTwinsClient.getModel", digitalTwinModelsGetByIdOptionalParams, async (updatedOptions) => {
            return this.client.digitalTwinModels.getById(modelId, updatedOptions);
        });
    }
    /**
     * Deals with the pagination of {@link list}.
     *
     * @param options - Common options for the iterative endpoints.
     * @param continuationState - An object that indicates the position of the paginated request.
     *
     */
    getModelsPage(options, continuationState) {
        return __asyncGenerator(this, arguments, function* getModelsPage_1() {
            if (continuationState.continuationToken == null) {
                const optionsComplete = options;
                optionsComplete.maxItemsPerPage = continuationState.maxPageSize;
                const listResponse = yield __await(this.client.digitalTwinModels.list(optionsComplete));
                continuationState.continuationToken = listResponse.nextLink;
                yield yield __await(listResponse);
            }
            while (continuationState.continuationToken) {
                const listResponse = yield __await(this.client.digitalTwinModels.listNext(continuationState.continuationToken, options));
                continuationState.continuationToken = listResponse.nextLink;
                yield yield __await(listResponse);
            }
        });
    }
    /**
     * Deals with the iteration of all the available results of {@link list}.
     * @param options - Common options for the iterative endpoints.
     */
    getModelsAll(options) {
        return __asyncGenerator(this, arguments, function* getModelsAll_1() {
            var e_3, _a;
            const f = {};
            try {
                for (var _b = __asyncValues(this.getModelsPage(options, f)), _c; _c = yield __await(_b.next()), !_c.done;) {
                    const page = _c.value;
                    const value = page.value || [];
                    for (const item of value) {
                        yield yield __await(item);
                    }
                }
            }
            catch (e_3_1) { e_3 = { error: e_3_1 }; }
            finally {
                try {
                    if (_c && !_c.done && (_a = _b.return)) yield __await(_a.call(_b));
                }
                finally { if (e_3) throw e_3.error; }
            }
        });
    }
    /**
     * Get the list of models
     *
     * @param dependeciesFor - The model Ids to have dependencies retrieved. If omitted, all models are retrieved.
     * @param includeModelDefinition - Whether to include the model definition in the result. If false, only the model metadata will be returned.
     * @param resultsPerPage - The maximum number of items to retrieve per request. The server may choose to return less than the requested max.
     * @returns A pageable set of application/json models and the http response.
     */
    listModels(dependeciesFor, includeModelDefinition = false, resultsPerPage, options = {}) {
        let digitalTwinModelsListOptionalParams = options;
        digitalTwinModelsListOptionalParams = {
            maxItemsPerPage: resultsPerPage,
            dependenciesFor: dependeciesFor,
            includeModelDefinition: includeModelDefinition,
        };
        const iter = this.getModelsAll(digitalTwinModelsListOptionalParams);
        return {
            next() {
                return iter.next();
            },
            [Symbol.asyncIterator]() {
                return this;
            },
            byPage: (settings = {}) => this.getModelsPage(digitalTwinModelsListOptionalParams, settings),
        };
    }
    /**
     * Create one or many
     *
     * @param models - The set of models to create. Each string corresponds to exactly one model.
     * @param options - The operation options
     * @returns The created application/json models and the http response.
     */
    createModels(dtdlModels, options = {}) {
        const digitalTwinModelsAddOptionalParams = options;
        digitalTwinModelsAddOptionalParams.models = dtdlModels;
        return tracingClient.withSpan("DigitalTwinsClient.createModels", digitalTwinModelsAddOptionalParams, async (updatedOptions) => {
            return this.client.digitalTwinModels.add(updatedOptions);
        });
    }
    /**
     * Decommission a model using a json patch.
     * When a model is decommissioned, new digital twins will no longer be able to be
     * defined by this model. However, existing digital twins may continue to use this model.
     * Once a model is decommissioned, it may not be recommissioned.
     *
     * @param modelId - The Id of the model to decommission.
     * property can be replaced.
     * @param options - The operation options
     * @returns The http response.
     *
     */
    decomissionModel(modelId, options = {}) {
        const jsonPatch = [{ op: "replace", path: "/decommissioned", value: true }];
        return tracingClient.withSpan("DigitalTwinsClient.decomissionModel", options, async (updatedOptions) => {
            return this.client.digitalTwinModels.update(modelId, jsonPatch, updatedOptions);
        });
    }
    /**
     * Delete a model.
     *
     * @param modelId - The Id of the model to delete.
     * @param options - The operation options
     * @returns The http response.
     */
    deleteModel(modelId, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.deleteModel", options, async (updatedOptions) => {
            return this.client.digitalTwinModels.delete(modelId, updatedOptions);
        });
    }
    /**
     * Get an event route.
     *
     * @param modelId - The Id of the event route.
     * @param options - The operation options
     * @returns The application/json event route and the http response.
     */
    getEventRoute(eventRouteId, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.getEventRoute", options, async (updatedOptions) => {
            return this.client.eventRoutes.getById(eventRouteId, updatedOptions);
        });
    }
    /**
     * Deals with the pagination of {@link list}.
     *
     * @param options - Common options for the iterative endpoints.
     * @param continuationState - An object that indicates the position of the paginated request.
     *
     */
    getEventRoutesPage(options, continuationState) {
        return __asyncGenerator(this, arguments, function* getEventRoutesPage_1() {
            if (continuationState.continuationToken == null) {
                const optionsComplete = options;
                optionsComplete.maxItemsPerPage = continuationState.maxPageSize;
                const listResponse = yield __await(this.client.eventRoutes.list(optionsComplete));
                continuationState.continuationToken = listResponse.nextLink;
                yield yield __await(listResponse);
            }
            while (continuationState.continuationToken) {
                const listResponse = yield __await(this.client.eventRoutes.listNext(continuationState.continuationToken, options));
                continuationState.continuationToken = listResponse.nextLink;
                yield yield __await(listResponse);
            }
        });
    }
    /**
     * Deals with the iteration of all the available results of {@link list}.
     * @param options - Common options for the iterative endpoints.
     */
    getEventRoutesAll(options) {
        return __asyncGenerator(this, arguments, function* getEventRoutesAll_1() {
            var e_4, _a;
            const f = {};
            try {
                for (var _b = __asyncValues(this.getEventRoutesPage(options, f)), _c; _c = yield __await(_b.next()), !_c.done;) {
                    const page = _c.value;
                    const value = page.value || [];
                    for (const item of value) {
                        yield yield __await(item);
                    }
                }
            }
            catch (e_4_1) { e_4 = { error: e_4_1 }; }
            finally {
                try {
                    if (_c && !_c.done && (_a = _b.return)) yield __await(_a.call(_b));
                }
                finally { if (e_4) throw e_4.error; }
            }
        });
    }
    /**
     * List the event routes in a digital twins instance.
     *
     * @param resultsPerPage - The maximum number of items to retrieve per request. The server may choose to return less than
     * the requested max.
     * @returns The application/json event route and the http response.
     */
    listEventRoutes(resultsPerPage, options = {}) {
        let eventRoutesListOptionalParams = options;
        eventRoutesListOptionalParams = {
            maxItemsPerPage: resultsPerPage,
        };
        const iter = this.getEventRoutesAll(eventRoutesListOptionalParams);
        return {
            next() {
                return iter.next();
            },
            [Symbol.asyncIterator]() {
                return this;
            },
            byPage: (settings = {}) => this.getEventRoutesPage(eventRoutesListOptionalParams, settings),
        };
    }
    /**
     * Create or update an event route.
     *
     * @param eventRouteId - The Id of the event route to create or update.
     * @param endpointId - The id of the endpoint this event route is bound to.
     * @param filter - An expression which describes the events which are routed to the endpoint.
     * @param options - The operation options
     * @returns The http response.
     */
    upsertEventRoute(eventRouteId, endpointId, filter, options = {}) {
        const eventRoutesAddOptionalParams = options;
        const eventRoute = {
            endpointName: endpointId,
            filter: filter,
        };
        eventRoutesAddOptionalParams.eventRoute = eventRoute;
        return tracingClient.withSpan("DigitalTwinsClient.upsertEventRoute", eventRoutesAddOptionalParams, async (updatedOptions) => {
            return this.client.eventRoutes.add(eventRouteId, updatedOptions);
        });
    }
    /**
     * Delete an event route.
     *
     * @param eventRouteId - The Id of the eventRoute to delete.
     * @param options - The operation options
     * @returns The http response.
     */
    deleteEventRoute(eventRouteId, options = {}) {
        return tracingClient.withSpan("DigitalTwinsClient.deleteEventRoute", options, async (updatedOptions) => {
            return this.client.eventRoutes.delete(eventRouteId, updatedOptions);
        });
    }
    /**
     * Deals with the pagination of {@link query}.
     *
     * @param query - The query string, in SQL-like syntax.
     * @param options - Common options for the iterative endpoints.
     * @param continuationState - An object that indicates the position of the paginated request.
     *
     */
    queryTwinsPage(query, options, continuationState) {
        return __asyncGenerator(this, arguments, function* queryTwinsPage_1() {
            if (continuationState.continuationToken == null) {
                const querySpecification = {
                    query: query,
                    continuationToken: continuationState.continuationToken,
                };
                const queryResult = yield __await(this.client.query.queryTwins(querySpecification, options));
                continuationState.continuationToken = queryResult.continuationToken;
                yield yield __await(queryResult);
            }
            while (continuationState.continuationToken) {
                const querySpecification = {
                    query: query,
                    continuationToken: continuationState.continuationToken,
                };
                const queryResult = yield __await(this.client.query.queryTwins(querySpecification, options));
                continuationState.continuationToken = queryResult.continuationToken;
                yield yield __await(queryResult);
            }
        });
    }
    /**
     * Deals with the iteration of all the available results of {@link query}.
     * @param query - The query string, in SQL-like syntax.
     * @param options - Common options for the iterative endpoints.
     */
    queryTwinsAll(query, options) {
        return __asyncGenerator(this, arguments, function* queryTwinsAll_1() {
            var e_5, _a;
            const f = {};
            try {
                for (var _b = __asyncValues(this.queryTwinsPage(query, options, f)), _c; _c = yield __await(_b.next()), !_c.done;) {
                    const page = _c.value;
                    if (page.value) {
                        for (const item of page.value) {
                            yield yield __await(item);
                        }
                    }
                }
            }
            catch (e_5_1) { e_5 = { error: e_5_1 }; }
            finally {
                try {
                    if (_c && !_c.done && (_a = _b.return)) yield __await(_a.call(_b));
                }
                finally { if (e_5) throw e_5.error; }
            }
        });
    }
    /**
     * Query for digital twins.
     *
     * @param query - The query string, in SQL-like syntax.
     * @param resultsPerPage - The maximum number of items to retrieve per request. The server may choose to return less than the requested max.
     * @returns The pageable list of query results.
     */
    queryTwins(query, resultsPerPage, options = {}) {
        let queryQueryTwinsOptionalParams = options;
        queryQueryTwinsOptionalParams = {
            maxItemsPerPage: resultsPerPage,
        };
        const iter = this.queryTwinsAll(query, queryQueryTwinsOptionalParams);
        return {
            next() {
                return iter.next();
            },
            [Symbol.asyncIterator]() {
                return this;
            },
            byPage: (settings = {}) => this.queryTwinsPage(query, queryQueryTwinsOptionalParams, settings),
        };
    }
}
//# sourceMappingURL=digitalTwinsClient.js.map