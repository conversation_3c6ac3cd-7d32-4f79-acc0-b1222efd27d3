/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import * as coreHttp from "@azure/core-http";
import * as Mappers from "../models/mappers";
import * as Parameters from "../models/parameters";
/**
 * Class representing a EventRoutes.
 */
export class EventRoutes {
    /**
     * Initialize a new instance of the class EventRoutes class.
     * @param client Reference to the service client
     */
    constructor(client) {
        this.client = client;
    }
    /**
     * Retrieves all event routes.
     * Status codes:
     * * 200 OK
     * @param options The options parameters.
     */
    list(options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ options: operationOptions }, listOperationSpec);
    }
    /**
     * Retrieves an event route.
     * Status codes:
     * * 200 OK
     * * 404 Not Found
     *   * EventRouteNotFound - The event route was not found.
     * @param id The id for an event route. The id is unique within event routes and case sensitive.
     * @param options The options parameters.
     */
    getById(id, options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, options: operationOptions }, getByIdOperationSpec);
    }
    /**
     * Adds or replaces an event route.
     * Status codes:
     * * 204 No Content
     * * 400 Bad Request
     *   * EventRouteEndpointInvalid - The endpoint provided does not exist or is not active.
     *   * EventRouteFilterInvalid - The event route filter is invalid.
     *   * EventRouteIdInvalid - The event route id is invalid.
     *   * LimitExceeded - The maximum number of event routes allowed has been reached.
     * @param id The id for an event route. The id is unique within event routes and case sensitive.
     * @param options The options parameters.
     */
    add(id, options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, options: operationOptions }, addOperationSpec);
    }
    /**
     * Deletes an event route.
     * Status codes:
     * * 204 No Content
     * * 404 Not Found
     *   * EventRouteNotFound - The event route was not found.
     * @param id The id for an event route. The id is unique within event routes and case sensitive.
     * @param options The options parameters.
     */
    delete(id, options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ id, options: operationOptions }, deleteOperationSpec);
    }
    /**
     * ListNext
     * @param nextLink The nextLink from the previous successful call to the List method.
     * @param options The options parameters.
     */
    listNext(nextLink, options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ nextLink, options: operationOptions }, listNextOperationSpec);
    }
}
// Operation Specifications
const serializer = new coreHttp.Serializer(Mappers, /* isXml */ false);
const listOperationSpec = {
    path: "/eventroutes",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: Mappers.EventRouteCollection
        },
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    queryParameters: [Parameters.apiVersion],
    urlParameters: [Parameters.$host],
    headerParameters: [
        Parameters.traceparent,
        Parameters.tracestate,
        Parameters.maxItemsPerPage
    ],
    serializer
};
const getByIdOperationSpec = {
    path: "/eventroutes/{id}",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: Mappers.EventRoute
        },
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    queryParameters: [Parameters.apiVersion],
    urlParameters: [Parameters.$host, Parameters.id],
    headerParameters: [Parameters.traceparent, Parameters.tracestate],
    serializer
};
const addOperationSpec = {
    path: "/eventroutes/{id}",
    httpMethod: "PUT",
    responses: {
        204: {},
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    requestBody: Parameters.eventRoute,
    queryParameters: [Parameters.apiVersion],
    urlParameters: [Parameters.$host, Parameters.id],
    headerParameters: [
        Parameters.contentType,
        Parameters.traceparent,
        Parameters.tracestate
    ],
    mediaType: "json",
    serializer
};
const deleteOperationSpec = {
    path: "/eventroutes/{id}",
    httpMethod: "DELETE",
    responses: {
        204: {},
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    queryParameters: [Parameters.apiVersion],
    urlParameters: [Parameters.$host, Parameters.id],
    headerParameters: [Parameters.traceparent, Parameters.tracestate],
    serializer
};
const listNextOperationSpec = {
    path: "{nextLink}",
    httpMethod: "GET",
    responses: {
        200: {
            bodyMapper: Mappers.EventRouteCollection
        },
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    queryParameters: [Parameters.apiVersion],
    urlParameters: [Parameters.$host, Parameters.nextLink],
    headerParameters: [
        Parameters.traceparent,
        Parameters.tracestate,
        Parameters.maxItemsPerPage
    ],
    serializer
};
//# sourceMappingURL=eventRoutes.js.map