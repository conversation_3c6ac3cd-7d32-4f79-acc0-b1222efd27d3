/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
export const DigitalTwinsModelData = {
    type: {
        name: "Composite",
        className: "DigitalTwinsModelData",
        modelProperties: {
            displayName: {
                serializedName: "displayName",
                type: {
                    name: "Dictionary",
                    value: { type: { name: "String" } }
                }
            },
            description: {
                serializedName: "description",
                type: {
                    name: "Dictionary",
                    value: { type: { name: "String" } }
                }
            },
            id: {
                serializedName: "id",
                required: true,
                type: {
                    name: "String"
                }
            },
            uploadTime: {
                serializedName: "uploadTime",
                type: {
                    name: "DateTime"
                }
            },
            decommissioned: {
                serializedName: "decommissioned",
                type: {
                    name: "Boolean"
                }
            },
            model: {
                serializedName: "model",
                type: {
                    name: "any"
                }
            }
        }
    }
};
export const ErrorResponse = {
    type: {
        name: "Composite",
        className: "ErrorResponse",
        modelProperties: {
            error: {
                serializedName: "error",
                type: {
                    name: "Composite",
                    className: "ErrorModel"
                }
            }
        }
    }
};
export const ErrorModel = {
    type: {
        name: "Composite",
        className: "ErrorModel",
        modelProperties: {
            code: {
                serializedName: "code",
                readOnly: true,
                type: {
                    name: "String"
                }
            },
            message: {
                serializedName: "message",
                readOnly: true,
                type: {
                    name: "String"
                }
            },
            details: {
                serializedName: "details",
                readOnly: true,
                type: {
                    name: "Sequence",
                    element: { type: { name: "Composite", className: "ErrorModel" } }
                }
            },
            innererror: {
                serializedName: "innererror",
                type: {
                    name: "Composite",
                    className: "InnerError"
                }
            }
        }
    }
};
export const InnerError = {
    type: {
        name: "Composite",
        className: "InnerError",
        modelProperties: {
            code: {
                serializedName: "code",
                type: {
                    name: "String"
                }
            },
            innererror: {
                serializedName: "innererror",
                type: {
                    name: "Composite",
                    className: "InnerError"
                }
            }
        }
    }
};
export const PagedDigitalTwinsModelDataCollection = {
    type: {
        name: "Composite",
        className: "PagedDigitalTwinsModelDataCollection",
        modelProperties: {
            value: {
                serializedName: "value",
                type: {
                    name: "Sequence",
                    element: {
                        type: { name: "Composite", className: "DigitalTwinsModelData" }
                    }
                }
            },
            nextLink: {
                serializedName: "nextLink",
                type: {
                    name: "String"
                }
            }
        }
    }
};
export const QuerySpecification = {
    type: {
        name: "Composite",
        className: "QuerySpecification",
        modelProperties: {
            query: {
                serializedName: "query",
                type: {
                    name: "String"
                }
            },
            continuationToken: {
                serializedName: "continuationToken",
                type: {
                    name: "String"
                }
            }
        }
    }
};
export const QueryResult = {
    type: {
        name: "Composite",
        className: "QueryResult",
        modelProperties: {
            value: {
                serializedName: "value",
                type: {
                    name: "Sequence",
                    element: { type: { name: "any" } }
                }
            },
            continuationToken: {
                serializedName: "continuationToken",
                type: {
                    name: "String"
                }
            }
        }
    }
};
export const RelationshipCollection = {
    type: {
        name: "Composite",
        className: "RelationshipCollection",
        modelProperties: {
            value: {
                serializedName: "value",
                type: {
                    name: "Sequence",
                    element: { type: { name: "any" } }
                }
            },
            nextLink: {
                serializedName: "nextLink",
                type: {
                    name: "String"
                }
            }
        }
    }
};
export const IncomingRelationshipCollection = {
    type: {
        name: "Composite",
        className: "IncomingRelationshipCollection",
        modelProperties: {
            value: {
                serializedName: "value",
                type: {
                    name: "Sequence",
                    element: {
                        type: { name: "Composite", className: "IncomingRelationship" }
                    }
                }
            },
            nextLink: {
                serializedName: "nextLink",
                type: {
                    name: "String"
                }
            }
        }
    }
};
export const IncomingRelationship = {
    type: {
        name: "Composite",
        className: "IncomingRelationship",
        modelProperties: {
            relationshipId: {
                serializedName: "$relationshipId",
                type: {
                    name: "String"
                }
            },
            sourceId: {
                serializedName: "$sourceId",
                type: {
                    name: "String"
                }
            },
            relationshipName: {
                serializedName: "$relationshipName",
                type: {
                    name: "String"
                }
            },
            relationshipLink: {
                serializedName: "$relationshipLink",
                type: {
                    name: "String"
                }
            }
        }
    }
};
export const EventRouteCollection = {
    type: {
        name: "Composite",
        className: "EventRouteCollection",
        modelProperties: {
            value: {
                serializedName: "value",
                type: {
                    name: "Sequence",
                    element: { type: { name: "Composite", className: "EventRoute" } }
                }
            },
            nextLink: {
                serializedName: "nextLink",
                type: {
                    name: "String"
                }
            }
        }
    }
};
export const EventRoute = {
    type: {
        name: "Composite",
        className: "EventRoute",
        modelProperties: {
            id: {
                serializedName: "id",
                readOnly: true,
                type: {
                    name: "String"
                }
            },
            endpointName: {
                serializedName: "endpointName",
                required: true,
                type: {
                    name: "String"
                }
            },
            filter: {
                serializedName: "filter",
                required: true,
                type: {
                    name: "String"
                }
            }
        }
    }
};
export const QueryQueryTwinsHeaders = {
    type: {
        name: "Composite",
        className: "QueryQueryTwinsHeaders",
        modelProperties: {
            queryCharge: {
                serializedName: "query-charge",
                type: {
                    name: "Number"
                }
            }
        }
    }
};
export const DigitalTwinsGetByIdHeaders = {
    type: {
        name: "Composite",
        className: "DigitalTwinsGetByIdHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                type: {
                    name: "String"
                }
            }
        }
    }
};
export const DigitalTwinsAddHeaders = {
    type: {
        name: "Composite",
        className: "DigitalTwinsAddHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                type: {
                    name: "String"
                }
            }
        }
    }
};
export const DigitalTwinsUpdateHeaders = {
    type: {
        name: "Composite",
        className: "DigitalTwinsUpdateHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                type: {
                    name: "String"
                }
            }
        }
    }
};
export const DigitalTwinsGetRelationshipByIdHeaders = {
    type: {
        name: "Composite",
        className: "DigitalTwinsGetRelationshipByIdHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                type: {
                    name: "String"
                }
            }
        }
    }
};
export const DigitalTwinsAddRelationshipHeaders = {
    type: {
        name: "Composite",
        className: "DigitalTwinsAddRelationshipHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                type: {
                    name: "String"
                }
            }
        }
    }
};
export const DigitalTwinsUpdateRelationshipHeaders = {
    type: {
        name: "Composite",
        className: "DigitalTwinsUpdateRelationshipHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                type: {
                    name: "String"
                }
            }
        }
    }
};
export const DigitalTwinsGetComponentHeaders = {
    type: {
        name: "Composite",
        className: "DigitalTwinsGetComponentHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                type: {
                    name: "String"
                }
            }
        }
    }
};
export const DigitalTwinsUpdateComponentHeaders = {
    type: {
        name: "Composite",
        className: "DigitalTwinsUpdateComponentHeaders",
        modelProperties: {
            etag: {
                serializedName: "etag",
                type: {
                    name: "String"
                }
            }
        }
    }
};
//# sourceMappingURL=mappers.js.map