{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,gBAAgB,EAAE,eAAe,EAAE,MAAM,cAAc,CAAC;AACjE,OAAO,EAAE,aAAa,EAAwB,MAAM,iBAAiB,CAAC;AACtE,OAAO,EAAE,oBAAoB,EAAiC,MAAM,YAAY,CAAC;AACjF,OAAO,EA8BL,WAAW,EACX,WAAW,GAMZ,MAAM,cAAc,CAAC;AACtB,OAAO,EACL,qBAAqB,EACrB,yBAAyB,GAG1B,MAAM,yBAAyB,CAAC;AACjC,OAAO,EACL,mBAAmB,EACnB,uBAAuB,GAExB,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAE,gCAAgC,EAAE,MAAM,oCAAoC,CAAC;AACtF,OAAO,EAAE,iCAAiC,EAAE,MAAM,qCAAqC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nexport { createSerializer, MapperTypeNames } from \"./serializer\";\nexport { ServiceClient, ServiceClientOptions } from \"./serviceClient\";\nexport { createClientPipeline, InternalClientPipelineOptions } from \"./pipeline\";\nexport {\n  OperationSpec,\n  OperationArguments,\n  OperationOptions,\n  OperationResponseMap,\n  OperationParameter,\n  OperationQueryParameter,\n  OperationURLParameter,\n  Serializer,\n  BaseMapper,\n  Mapper,\n  MapperType,\n  SimpleMapperType,\n  EnumMapper,\n  EnumMapperType,\n  SequenceMapper,\n  SequenceMapperType,\n  DictionaryMapper,\n  DictionaryMapperType,\n  CompositeMapper,\n  CompositeMapperType,\n  MapperConstraints,\n  OperationRequest,\n  OperationRequestOptions,\n  OperationRequestInfo,\n  QueryCollectionFormat,\n  ParameterPath,\n  FullOperationResponse,\n  PolymorphicDiscriminator,\n  SpanConfig,\n  XML_ATTRKEY,\n  XML_CHARKEY,\n  XmlOptions,\n  SerializerOptions,\n  RawResponseCallback,\n  CommonClientOptions,\n  AdditionalPolicyConfig,\n} from \"./interfaces\";\nexport {\n  deserializationPolicy,\n  deserializationPolicyName,\n  DeserializationPolicyOptions,\n  DeserializationContentTypes,\n} from \"./deserializationPolicy\";\nexport {\n  serializationPolicy,\n  serializationPolicyName,\n  SerializationPolicyOptions,\n} from \"./serializationPolicy\";\nexport { authorizeRequestOnClaimChallenge } from \"./authorizeRequestOnClaimChallenge\";\nexport { authorizeRequestOnTenantChallenge } from \"./authorizeRequestOnTenantChallenge\";\n"]}