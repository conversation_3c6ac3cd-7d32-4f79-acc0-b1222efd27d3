{"version": 3, "file": "msalDeviceCode.js", "sourceRoot": "", "sources": ["../../../../src/msal/nodeFlows/msalDeviceCode.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,QAAQ,EAAmB,MAAM,kBAAkB,CAAC;AAa7D;;;GAGG;AACH,MAAM,OAAO,cAAe,SAAQ,QAAQ;IAG1C,YAAY,OAA8B;QACxC,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;IACvD,CAAC;IAES,KAAK,CAAC,UAAU,CACxB,MAAgB,EAChB,OAAuC;QAEvC,IAAI;YACF,MAAM,cAAc,GAA+B;gBACjD,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;gBAC3C,MAAM;gBACN,MAAM,EAAE,KAAK;gBACb,aAAa,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa;gBACrC,SAAS,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS;gBAC7B,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM;aACxB,CAAC;YACF,MAAM,OAAO,GAAG,IAAI,CAAC,SAAU,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;YACzE,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,EAAE,GAAG,EAAE;gBACrF,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC;YAC/B,CAAC,CAAC,CAAC;YACH,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,cAAc,IAAI,SAAS,CAAC,CAAC;SAC9E;QAAC,OAAO,KAAU,EAAE;YACnB,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAChD;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as msalNode from \"@azure/msal-node\";\nimport { MsalNode, MsalNodeOptions } from \"./msalNodeCommon\";\nimport { AccessToken } from \"@azure/core-auth\";\nimport { CredentialFlowGetTokenOptions } from \"../credentials\";\nimport { DeviceCodePromptCallback } from \"../../credentials/deviceCodeCredentialOptions\";\n\n/**\n * Options that can be passed to configure MSAL to handle authentication through device codes.\n * @internal\n */\nexport interface MsalDeviceCodeOptions extends MsalNodeOptions {\n  userPromptCallback: DeviceCodePromptCallback;\n}\n\n/**\n * MSAL device code client. Calls to the MSAL's public application's `acquireTokenByDeviceCode` during `doGetToken`.\n * @internal\n */\nexport class MsalDeviceCode extends MsalNode {\n  private userPromptCallback: DeviceCodePromptCallback;\n\n  constructor(options: MsalDeviceCodeOptions) {\n    super(options);\n    this.userPromptCallback = options.userPromptCallback;\n  }\n\n  protected async doGetToken(\n    scopes: string[],\n    options?: CredentialFlowGetTokenOptions\n  ): Promise<AccessToken> {\n    try {\n      const requestOptions: msalNode.DeviceCodeRequest = {\n        deviceCodeCallback: this.userPromptCallback,\n        scopes,\n        cancel: false,\n        correlationId: options?.correlationId,\n        authority: options?.authority,\n        claims: options?.claims,\n      };\n      const promise = this.publicApp!.acquireTokenByDeviceCode(requestOptions);\n      const deviceResponse = await this.withCancellation(promise, options?.abortSignal, () => {\n        requestOptions.cancel = true;\n      });\n      return this.handleResult(scopes, this.clientId, deviceResponse || undefined);\n    } catch (error: any) {\n      throw this.handleError(scopes, error, options);\n    }\n  }\n}\n"]}