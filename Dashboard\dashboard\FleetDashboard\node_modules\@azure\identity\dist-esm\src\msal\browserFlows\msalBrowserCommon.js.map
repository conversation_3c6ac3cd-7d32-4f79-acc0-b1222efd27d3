{"version": 3, "file": "msalBrowserCommon.js", "sourceRoot": "", "sources": ["../../../../src/msal/browserFlows/msalBrowserCommon.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,2BAA2B,EAAE,0BAA0B,EAAE,MAAM,cAAc,CAAC;AACvF,OAAO,EAAE,iBAAiB,EAAE,YAAY,EAAE,mBAAmB,EAAE,MAAM,UAAU,CAAC;AAEhF,OAAO,EACL,yBAAyB,EACzB,iCAAiC,EACjC,eAAe,GAChB,MAAM,0BAA0B,CAAC;AAKlC,OAAO,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAC;AAuBlD;;;GAGG;AACH,MAAM,UAAU,wBAAwB,CACtC,OAA+B;IAE/B,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,eAAe,CAAC;IACrD,MAAM,SAAS,GAAG,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;IAChE,OAAO;QACL,IAAI,EAAE;YACJ,QAAQ,EAAE,OAAO,CAAC,QAAS;YAC3B,SAAS;YACT,gBAAgB,EAAE,mBAAmB,CAAC,QAAQ,EAAE,SAAS,CAAC;YAC1D,qDAAqD;YACrD,yCAAyC;YACzC,kEAAkE;YAClE,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM;SACzD;KACF,CAAC;AACJ,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,OAAgB,WAAY,SAAQ,iBAAiB;IAWzD,YAAY,OAA+B;;QACzC,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC7B,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YACrB,MAAM,IAAI,0BAA0B,CAAC,qCAAqC,CAAC,CAAC;SAC7E;QACD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,4BAA4B,GAAG,iCAAiC,CACnE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,sBAAsB,0CAAE,0BAA0B,CAC5D,CAAC;QACF,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;QACjF,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAC3C,IAAI,CAAC,UAAU,GAAG,wBAAwB,CAAC,OAAO,CAAC,CAAC;QACpD,IAAI,CAAC,8BAA8B,GAAG,OAAO,CAAC,8BAA8B,CAAC;QAE7E,IAAI,OAAO,CAAC,oBAAoB,EAAE;YAChC,IAAI,CAAC,OAAO,mCACP,OAAO,CAAC,oBAAoB,KAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ,GACxB,CAAC;SACH;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,sBAAsB;IACxB,CAAC;IAOD;;OAEG;IACH,KAAK,CAAC,MAAM;;QACV,MAAA,IAAI,CAAC,GAAG,0CAAE,MAAM,EAAE,CAAC;IACrB,CAAC;IAsBD;;OAEG;IACI,KAAK,CAAC,QAAQ,CACnB,MAAgB,EAChB,UAAyC,EAAE;QAE3C,MAAM,QAAQ,GACZ,yBAAyB,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,4BAA4B,CAAC;YACpF,IAAI,CAAC,QAAQ,CAAC;QAEhB,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YACtB,OAAO,CAAC,SAAS,GAAG,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;SAChE;QAED,uDAAuD;QACvD,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAE5B,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,8BAA8B,EAAE;YAC5E,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;SAC1B;QACD,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;YAC/C,IAAI,GAAG,CAAC,IAAI,KAAK,6BAA6B,EAAE;gBAC9C,MAAM,GAAG,CAAC;aACX;YACD,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,8BAA8B,EAAE;gBAC3C,MAAM,IAAI,2BAA2B,CAAC;oBACpC,MAAM;oBACN,eAAe,EAAE,OAAO;oBACxB,OAAO,EACL,uFAAuF;iBAC1F,CAAC,CAAC;aACJ;YACD,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,oEAAoE,IAAI,CAAC,UAAU,EAAE,CACtF,CAAC;YACF,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;IACL,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as msalBrowser from \"@azure/msal-browser\";\nimport { AuthenticationRequiredError, CredentialUnavailableError } from \"../../errors\";\nimport { MsalBaseUtilities, getAuthority, getKnownAuthorities } from \"../utils\";\nimport { MsalFlow, MsalFlowOptions } from \"../flows\";\nimport {\n  processMultiTenantRequest,\n  resolveAddionallyAllowedTenantIds,\n  resolveTenantId,\n} from \"../../util/tenantIdUtils\";\nimport { AccessToken } from \"@azure/core-auth\";\nimport { AuthenticationRecord } from \"../types\";\nimport { BrowserLoginStyle } from \"../../credentials/interactiveBrowserCredentialOptions\";\nimport { CredentialFlowGetTokenOptions } from \"../credentials\";\nimport { DefaultTenantId } from \"../../constants\";\nimport { MultiTenantTokenCredentialOptions } from \"../../credentials/multiTenantTokenCredentialOptions\";\n\n/**\n * Union of the constructor parameters that all MSAL flow types take.\n * Some properties might not be used by some flow types.\n */\nexport interface MsalBrowserFlowOptions extends MsalFlowOptions {\n  tokenCredentialOptions: MultiTenantTokenCredentialOptions;\n  redirectUri?: string;\n  loginStyle: BrowserLoginStyle;\n  loginHint?: string;\n}\n\n/**\n * The common methods we use to work with the MSAL browser flows.\n * @internal\n */\nexport interface MsalBrowserFlow extends MsalFlow {\n  login(scopes?: string[]): Promise<AuthenticationRecord | undefined>;\n  handleRedirect(): Promise<AuthenticationRecord | undefined>;\n}\n\n/**\n * Generates a MSAL configuration that generally works for browsers\n * @internal\n */\nexport function defaultBrowserMsalConfig(\n  options: MsalBrowserFlowOptions\n): msalBrowser.Configuration {\n  const tenantId = options.tenantId || DefaultTenantId;\n  const authority = getAuthority(tenantId, options.authorityHost);\n  return {\n    auth: {\n      clientId: options.clientId!,\n      authority,\n      knownAuthorities: getKnownAuthorities(tenantId, authority),\n      // If the users picked redirect as their login style,\n      // but they didn't provide a redirectUri,\n      // we can try to use the current page we're in as a default value.\n      redirectUri: options.redirectUri || self.location.origin,\n    },\n  };\n}\n\n/**\n * MSAL partial base client for the browsers.\n *\n * It completes the input configuration with some default values.\n * It also provides with utility protected methods that can be used from any of the clients,\n * which includes handlers for successful responses and errors.\n *\n * @internal\n */\nexport abstract class MsalBrowser extends MsalBaseUtilities implements MsalBrowserFlow {\n  protected loginStyle: BrowserLoginStyle;\n  protected clientId: string;\n  protected tenantId: string;\n  protected additionallyAllowedTenantIds: string[];\n  protected authorityHost?: string;\n  protected account: AuthenticationRecord | undefined;\n  protected msalConfig: msalBrowser.Configuration;\n  protected disableAutomaticAuthentication?: boolean;\n  protected app?: msalBrowser.PublicClientApplication;\n\n  constructor(options: MsalBrowserFlowOptions) {\n    super(options);\n    this.logger = options.logger;\n    this.loginStyle = options.loginStyle;\n    if (!options.clientId) {\n      throw new CredentialUnavailableError(\"A client ID is required in browsers\");\n    }\n    this.clientId = options.clientId;\n    this.additionallyAllowedTenantIds = resolveAddionallyAllowedTenantIds(\n      options?.tokenCredentialOptions?.additionallyAllowedTenants\n    );\n    this.tenantId = resolveTenantId(this.logger, options.tenantId, options.clientId);\n    this.authorityHost = options.authorityHost;\n    this.msalConfig = defaultBrowserMsalConfig(options);\n    this.disableAutomaticAuthentication = options.disableAutomaticAuthentication;\n\n    if (options.authenticationRecord) {\n      this.account = {\n        ...options.authenticationRecord,\n        tenantId: this.tenantId,\n      };\n    }\n  }\n\n  /**\n   * In the browsers we don't need to init()\n   */\n  async init(): Promise<void> {\n    // Nothing to do here.\n  }\n\n  /**\n   * Attempts to handle a redirection request the least amount of times possible.\n   */\n  public abstract handleRedirect(): Promise<AuthenticationRecord | undefined>;\n\n  /**\n   * Clears MSAL's cache.\n   */\n  async logout(): Promise<void> {\n    this.app?.logout();\n  }\n\n  /**\n   * Uses MSAL to retrieve the active account.\n   */\n  public abstract getActiveAccount(): Promise<AuthenticationRecord | undefined>;\n\n  /**\n   * Uses MSAL to trigger a redirect or a popup login.\n   */\n  public abstract login(scopes?: string | string[]): Promise<AuthenticationRecord | undefined>;\n\n  /**\n   * Attempts to retrieve a token from cache.\n   */\n  public abstract getTokenSilent(scopes: string[]): Promise<AccessToken>;\n\n  /**\n   * Attempts to retrieve the token in the browser.\n   */\n  protected abstract doGetToken(scopes: string[]): Promise<AccessToken>;\n\n  /**\n   * Attempts to retrieve an authenticated token from MSAL.\n   */\n  public async getToken(\n    scopes: string[],\n    options: CredentialFlowGetTokenOptions = {}\n  ): Promise<AccessToken> {\n    const tenantId =\n      processMultiTenantRequest(this.tenantId, options, this.additionallyAllowedTenantIds) ||\n      this.tenantId;\n\n    if (!options.authority) {\n      options.authority = getAuthority(tenantId, this.authorityHost);\n    }\n\n    // We ensure that redirection is handled at this point.\n    await this.handleRedirect();\n\n    if (!(await this.getActiveAccount()) && !this.disableAutomaticAuthentication) {\n      await this.login(scopes);\n    }\n    return this.getTokenSilent(scopes).catch((err) => {\n      if (err.name !== \"AuthenticationRequiredError\") {\n        throw err;\n      }\n      if (options?.disableAutomaticAuthentication) {\n        throw new AuthenticationRequiredError({\n          scopes,\n          getTokenOptions: options,\n          message:\n            \"Automatic authentication has been disabled. You may call the authentication() method.\",\n        });\n      }\n      this.logger.info(\n        `Silent authentication failed, falling back to interactive method ${this.loginStyle}`\n      );\n      return this.doGetToken(scopes);\n    });\n  }\n}\n"]}