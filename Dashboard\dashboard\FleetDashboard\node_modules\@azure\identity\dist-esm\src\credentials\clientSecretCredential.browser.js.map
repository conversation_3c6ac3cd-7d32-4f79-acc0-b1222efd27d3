{"version": 3, "file": "clientSecretCredential.browser.js", "sourceRoot": "", "sources": ["../../../src/credentials/clientSecretCredential.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,MAAM,2BAA2B,CAAC;AACrF,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAC/E,OAAO,EACL,yBAAyB,EACzB,iCAAiC,GAClC,MAAM,uBAAuB,CAAC;AAE/B,OAAO,EAAE,cAAc,EAAE,MAAM,0BAA0B,CAAC;AAC1D,OAAO,EAAE,8BAA8B,EAAE,MAAM,+BAA+B,CAAC;AAC/E,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,MAAM,MAAM,GAAG,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;AAE1D,2EAA2E;AAC3E,mGAAmG;AACnG,wDAAwD;AAExD;;;;;;;GAOG;AACH,MAAM,OAAO,sBAAsB;IAOjC;;;;;;;;;OASG;IACH,YACE,QAAgB,EAChB,QAAgB,EAChB,YAAoB,EACpB,OAAuC;QAEvC,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,4BAA4B,GAAG,iCAAiC,CACnE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,0BAA0B,CACpC,CAAC;QACF,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IACnC,CAAC;IAED;;;;;;;;;OASG;IACI,KAAK,CAAC,QAAQ,CACnB,MAAyB,EACzB,UAA2B,EAAE;QAE7B,OAAO,aAAa,CAAC,QAAQ,CAC3B,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,WAAW,EACnC,OAAO,EACP,KAAK,EAAE,UAAU,EAAE,EAAE;YACnB,MAAM,QAAQ,GAAG,yBAAyB,CACxC,IAAI,CAAC,QAAQ,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,CAClC,CAAC;YAEF,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC;gBAChC,aAAa,EAAE,OAAO;gBACtB,UAAU,EAAE,oBAAoB;gBAChC,SAAS,EAAE,IAAI,CAAC,QAAQ;gBACxB,aAAa,EAAE,IAAI,CAAC,YAAY;gBAChC,KAAK,EAAE,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;aAC9D,CAAC,CAAC;YAEH,IAAI;gBACF,MAAM,SAAS,GAAG,8BAA8B,CAAC,QAAS,CAAC,CAAC;gBAC5D,MAAM,OAAO,GAAG,qBAAqB,CAAC;oBACpC,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,IAAI,QAAS,IAAI,SAAS,EAAE;oBACrE,MAAM,EAAE,MAAM;oBACd,IAAI,EAAE,KAAK,CAAC,QAAQ,EAAE;oBACtB,OAAO,EAAE,iBAAiB,CAAC;wBACzB,MAAM,EAAE,kBAAkB;wBAC1B,cAAc,EAAE,mCAAmC;qBACpD,CAAC;oBACF,WAAW,EAAE,OAAO,IAAI,OAAO,CAAC,WAAW;oBAC3C,cAAc,EAAE,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,cAAc;iBAC3C,CAAC,CAAC;gBAEH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;gBAC1E,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC5C,OAAO,CAAC,aAAa,IAAI,aAAa,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC;aAC7D;YAAC,OAAO,GAAQ,EAAE;gBACjB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC/C,MAAM,GAAG,CAAC;aACX;QACH,CAAC,CACF,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport { createHttpHeaders, createPipelineRequest } from \"@azure/core-rest-pipeline\";\nimport { credentialLogger, formatError, formatSuccess } from \"../util/logging\";\nimport {\n  processMultiTenantRequest,\n  resolveAddionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { ClientSecretCredentialOptions } from \"./clientSecretCredentialOptions\";\nimport { IdentityClient } from \"../client/identityClient\";\nimport { getIdentityTokenEndpointSuffix } from \"../util/identityTokenEndpoint\";\nimport { tracingClient } from \"../util/tracing\";\n\nconst logger = credentialLogger(\"ClientSecretCredential\");\n\n// This credential is exported on browser bundles for development purposes.\n// For this credential to work in browsers, browsers would need to have security features disabled.\n// Please do not disable your browser security features.\n\n/**\n * Enables authentication to Azure Active Directory using a client secret\n * that was generated for an App Registration.  More information on how\n * to configure a client secret can be found here:\n *\n * https://docs.microsoft.com/en-us/azure/active-directory/develop/quickstart-configure-app-access-web-apis#add-credentials-to-your-web-application\n *\n */\nexport class ClientSecretCredential implements TokenCredential {\n  private identityClient: IdentityClient;\n  private tenantId: string;\n  private additionallyAllowedTenantIds: string[];\n  private clientId: string;\n  private clientSecret: string;\n\n  /**\n   * Creates an instance of the ClientSecretCredential with the details\n   * needed to authenticate against Azure Active Directory with a client\n   * secret.\n   *\n   * @param tenantId - The Azure Active Directory tenant (directory) ID.\n   * @param clientId - The client (application) ID of an App Registration in the tenant.\n   * @param clientSecret - A client secret that was generated for the App Registration.\n   * @param options - Options for configuring the client which makes the authentication request.\n   */\n  constructor(\n    tenantId: string,\n    clientId: string,\n    clientSecret: string,\n    options?: ClientSecretCredentialOptions\n  ) {\n    this.identityClient = new IdentityClient(options);\n    this.tenantId = tenantId;\n    this.additionallyAllowedTenantIds = resolveAddionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants\n    );\n    this.clientId = clientId;\n    this.clientSecret = clientSecret;\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if\n   * successful.  If authentication cannot be performed at this time, this method may\n   * return null.  If an error occurs during authentication, an {@link AuthenticationError}\n   * containing failure details will be thrown.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  public async getToken(\n    scopes: string | string[],\n    options: GetTokenOptions = {}\n  ): Promise<AccessToken | null> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.getToken`,\n      options,\n      async (newOptions) => {\n        const tenantId = processMultiTenantRequest(\n          this.tenantId,\n          newOptions,\n          this.additionallyAllowedTenantIds\n        );\n\n        const query = new URLSearchParams({\n          response_type: \"token\",\n          grant_type: \"client_credentials\",\n          client_id: this.clientId,\n          client_secret: this.clientSecret,\n          scope: typeof scopes === \"string\" ? scopes : scopes.join(\" \"),\n        });\n\n        try {\n          const urlSuffix = getIdentityTokenEndpointSuffix(tenantId!);\n          const request = createPipelineRequest({\n            url: `${this.identityClient.authorityHost}/${tenantId!}/${urlSuffix}`,\n            method: \"POST\",\n            body: query.toString(),\n            headers: createHttpHeaders({\n              Accept: \"application/json\",\n              \"Content-Type\": \"application/x-www-form-urlencoded\",\n            }),\n            abortSignal: options && options.abortSignal,\n            tracingOptions: newOptions?.tracingOptions,\n          });\n\n          const tokenResponse = await this.identityClient.sendTokenRequest(request);\n          logger.getToken.info(formatSuccess(scopes));\n          return (tokenResponse && tokenResponse.accessToken) || null;\n        } catch (err: any) {\n          logger.getToken.info(formatError(scopes, err));\n          throw err;\n        }\n      }\n    );\n  }\n}\n"]}