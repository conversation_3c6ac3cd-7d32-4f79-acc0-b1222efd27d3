{"version": 3, "file": "operationResponse.js", "sourceRoot": "", "sources": ["../../src/operationResponse.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { Mapper } from \"./serializer\";\n\n/**\n * An OperationResponse that can be returned from an operation request for a single status code.\n */\nexport interface OperationResponse {\n  /**\n   * The mapper that will be used to deserialize the response headers.\n   */\n  headersMapper?: Mapper;\n\n  /**\n   * The mapper that will be used to deserialize the response body.\n   */\n  bodyMapper?: Mapper;\n\n  /**\n   * Indicates if this is an error response\n   */\n  isError?: boolean;\n}\n"]}