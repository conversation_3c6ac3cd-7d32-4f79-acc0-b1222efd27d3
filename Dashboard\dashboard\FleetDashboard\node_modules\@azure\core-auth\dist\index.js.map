{"version": 3, "file": "index.js", "sources": ["../src/azureKeyCredential.ts", "../src/typeguards.ts", "../src/azureNamedKeyCredential.ts", "../src/azureSASCredential.ts", "../src/tokenCredential.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Represents a credential defined by a static API key.\n */\nexport interface KeyCredential {\n  /**\n   * The value of the API key represented as a string\n   */\n  readonly key: string;\n}\n\n/**\n * A static-key-based credential that supports updating\n * the underlying key value.\n */\nexport class AzureKeyCredential implements KeyCredential {\n  private _key: string;\n\n  /**\n   * The value of the key to be used in authentication\n   */\n  public get key(): string {\n    return this._key;\n  }\n\n  /**\n   * Create an instance of an AzureKeyCredential for use\n   * with a service client.\n   *\n   * @param key - The initial value of the key to use in authentication\n   */\n  constructor(key: string) {\n    if (!key) {\n      throw new Error(\"key must be a non-empty string\");\n    }\n\n    this._key = key;\n  }\n\n  /**\n   * Change the value of the key.\n   *\n   * Updates will take effect upon the next request after\n   * updating the key value.\n   *\n   * @param newKey - The new key value to be used\n   */\n  public update(newKey: string): void {\n    this._key = newKey;\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Helper TypeGuard that checks if something is defined or not.\n * @param thing - Anything\n * @internal\n */\nfunction isDefined<T>(thing: T | undefined | null): thing is T {\n  return typeof thing !== \"undefined\" && thing !== null;\n}\n\n/**\n * Helper TypeGuard that checks if the input is an object with the specified properties.\n * Note: The properties may be inherited.\n * @param thing - Anything.\n * @param properties - The name of the properties that should appear in the object.\n * @internal\n */\nexport function isObjectWithProperties<Thing, PropertyName extends string>(\n  thing: Thing,\n  properties: PropertyName[]\n): thing is Thing & Record<PropertyName, unknown> {\n  if (!isDefined(thing) || typeof thing !== \"object\") {\n    return false;\n  }\n\n  for (const property of properties) {\n    if (!objectHasProperty(thing, property)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Helper TypeGuard that checks if the input is an object with the specified property.\n * Note: The property may be inherited.\n * @param thing - Any object.\n * @param property - The name of the property that should appear in the object.\n * @internal\n */\nfunction objectHasProperty<Thing, PropertyName extends string>(\n  thing: Thing,\n  property: PropertyName\n): thing is Thing & Record<PropertyName, unknown> {\n  return typeof thing === \"object\" && property in (thing as Record<string, unknown>);\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { isObjectWithProperties } from \"./typeguards\";\n\n/**\n * Represents a credential defined by a static API name and key.\n */\nexport interface NamedKeyCredential {\n  /**\n   * The value of the API key represented as a string\n   */\n  readonly key: string;\n  /**\n   * The value of the API name represented as a string.\n   */\n  readonly name: string;\n}\n\n/**\n * A static name/key-based credential that supports updating\n * the underlying name and key values.\n */\nexport class AzureNamedKeyCredential implements NamedKeyCredential {\n  private _key: string;\n  private _name: string;\n\n  /**\n   * The value of the key to be used in authentication.\n   */\n  public get key(): string {\n    return this._key;\n  }\n\n  /**\n   * The value of the name to be used in authentication.\n   */\n  public get name(): string {\n    return this._name;\n  }\n\n  /**\n   * Create an instance of an AzureNamedKeyCredential for use\n   * with a service client.\n   *\n   * @param name - The initial value of the name to use in authentication.\n   * @param key - The initial value of the key to use in authentication.\n   */\n  constructor(name: string, key: string) {\n    if (!name || !key) {\n      throw new TypeError(\"name and key must be non-empty strings\");\n    }\n\n    this._name = name;\n    this._key = key;\n  }\n\n  /**\n   * Change the value of the key.\n   *\n   * Updates will take effect upon the next request after\n   * updating the key value.\n   *\n   * @param newName - The new name value to be used.\n   * @param newKey - The new key value to be used.\n   */\n  public update(newName: string, newKey: string): void {\n    if (!newName || !newKey) {\n      throw new TypeError(\"newName and newKey must be non-empty strings\");\n    }\n\n    this._name = newName;\n    this._key = newKey;\n  }\n}\n\n/**\n * Tests an object to determine whether it implements NamedKeyCredential.\n *\n * @param credential - The assumed NamedKeyCredential to be tested.\n */\nexport function isNamedKeyCredential(credential: unknown): credential is NamedKeyCredential {\n  return (\n    isObjectWithProperties(credential, [\"name\", \"key\"]) &&\n    typeof credential.key === \"string\" &&\n    typeof credential.name === \"string\"\n  );\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { isObjectWithProperties } from \"./typeguards\";\n\n/**\n * Represents a credential defined by a static shared access signature.\n */\nexport interface SASCredential {\n  /**\n   * The value of the shared access signature represented as a string\n   */\n  readonly signature: string;\n}\n\n/**\n * A static-signature-based credential that supports updating\n * the underlying signature value.\n */\nexport class AzureSASCredential implements SASCredential {\n  private _signature: string;\n\n  /**\n   * The value of the shared access signature to be used in authentication\n   */\n  public get signature(): string {\n    return this._signature;\n  }\n\n  /**\n   * Create an instance of an AzureSASCredential for use\n   * with a service client.\n   *\n   * @param signature - The initial value of the shared access signature to use in authentication\n   */\n  constructor(signature: string) {\n    if (!signature) {\n      throw new Error(\"shared access signature must be a non-empty string\");\n    }\n\n    this._signature = signature;\n  }\n\n  /**\n   * Change the value of the signature.\n   *\n   * Updates will take effect upon the next request after\n   * updating the signature value.\n   *\n   * @param newSignature - The new shared access signature value to be used\n   */\n  public update(newSignature: string): void {\n    if (!newSignature) {\n      throw new Error(\"shared access signature must be a non-empty string\");\n    }\n\n    this._signature = newSignature;\n  }\n}\n\n/**\n * Tests an object to determine whether it implements SASCredential.\n *\n * @param credential - The assumed SASCredential to be tested.\n */\nexport function isSASCredential(credential: unknown): credential is SASCredential {\n  return (\n    isObjectWithProperties(credential, [\"signature\"]) && typeof credential.signature === \"string\"\n  );\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { TracingContext } from \"./tracing\";\n\n/**\n * Represents a credential capable of providing an authentication token.\n */\nexport interface TokenCredential {\n  /**\n   * Gets the token provided by this credential.\n   *\n   * This method is called automatically by Azure SDK client libraries. You may call this method\n   * directly, but you must also handle token caching and token refreshing.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  getToken(scopes: string | string[], options?: GetTokenOptions): Promise<AccessToken | null>;\n}\n\n/**\n * Defines options for TokenCredential.getToken.\n */\nexport interface GetTokenOptions {\n  /**\n   * The signal which can be used to abort requests.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Options used when creating and sending HTTP requests for this operation.\n   */\n  requestOptions?: {\n    /**\n     * The number of milliseconds a request can take before automatically being terminated.\n     */\n    timeout?: number;\n  };\n  /**\n   * Options used when tracing is enabled.\n   */\n  tracingOptions?: {\n    /**\n     * Tracing Context for the current request.\n     */\n    tracingContext?: TracingContext;\n  };\n\n  /**\n   * Allows specifying a tenantId. Useful to handle challenges that provide tenant Id hints.\n   */\n  tenantId?: string;\n\n  /**\n   * Claim details to perform the Continuous Access Evaluation authentication flow\n   */\n  claims?: string;\n}\n\n/**\n * Represents an access token with an expiration time.\n */\nexport interface AccessToken {\n  /**\n   * The access token returned by the authentication service.\n   */\n  token: string;\n\n  /**\n   * The access token's expiration timestamp in milliseconds, UNIX epoch time.\n   */\n  expiresOnTimestamp: number;\n}\n\n/**\n * Tests an object to determine whether it implements TokenCredential.\n *\n * @param credential - The assumed TokenCredential to be tested.\n */\nexport function isTokenCredential(credential: unknown): credential is TokenCredential {\n  // Check for an object with a 'getToken' function and possibly with\n  // a 'signRequest' function.  We do this check to make sure that\n  // a ServiceClientCredentials implementor (like TokenClientCredentials\n  // in ms-rest-nodeauth) doesn't get mistaken for a TokenCredential if\n  // it doesn't actually implement TokenCredential also.\n  const castCredential = credential as {\n    getToken: unknown;\n    signRequest: unknown;\n  };\n  return (\n    castCredential &&\n    typeof castCredential.getToken === \"function\" &&\n    (castCredential.signRequest === undefined || castCredential.getToken.length > 0)\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAYA;;;AAGG;MACU,kBAAkB,CAAA;AAU7B;;;;;AAKG;AACH,IAAA,WAAA,CAAY,GAAW,EAAA;QACrB,IAAI,CAAC,GAAG,EAAE;AACR,YAAA,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;AACnD,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;KACjB;AAnBD;;AAEG;AACH,IAAA,IAAW,GAAG,GAAA;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC;KAClB;AAgBD;;;;;;;AAOG;AACI,IAAA,MAAM,CAAC,MAAc,EAAA;AAC1B,QAAA,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC;KACpB;AACF;;ACpDD;AACA;AAEA;;;;AAIG;AACH,SAAS,SAAS,CAAI,KAA2B,EAAA;IAC/C,OAAO,OAAO,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK,IAAI,CAAC;AACxD,CAAC;AAED;;;;;;AAMG;AACa,SAAA,sBAAsB,CACpC,KAAY,EACZ,UAA0B,EAAA;IAE1B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAClD,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AAED,IAAA,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE;AACjC,QAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;AACvC,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACF,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;AAMG;AACH,SAAS,iBAAiB,CACxB,KAAY,EACZ,QAAsB,EAAA;IAEtB,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,QAAQ,IAAK,KAAiC,CAAC;AACrF;;AChDA;AAmBA;;;AAGG;MACU,uBAAuB,CAAA;AAkBlC;;;;;;AAMG;IACH,WAAY,CAAA,IAAY,EAAE,GAAW,EAAA;AACnC,QAAA,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE;AACjB,YAAA,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC;AAC/D,SAAA;AAED,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;KACjB;AA5BD;;AAEG;AACH,IAAA,IAAW,GAAG,GAAA;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC;KAClB;AAED;;AAEG;AACH,IAAA,IAAW,IAAI,GAAA;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;AAkBD;;;;;;;;AAQG;IACI,MAAM,CAAC,OAAe,EAAE,MAAc,EAAA;AAC3C,QAAA,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,EAAE;AACvB,YAAA,MAAM,IAAI,SAAS,CAAC,8CAA8C,CAAC,CAAC;AACrE,SAAA;AAED,QAAA,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;AACrB,QAAA,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC;KACpB;AACF,CAAA;AAED;;;;AAIG;AACG,SAAU,oBAAoB,CAAC,UAAmB,EAAA;IACtD,QACE,sBAAsB,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACnD,QAAA,OAAO,UAAU,CAAC,GAAG,KAAK,QAAQ;AAClC,QAAA,OAAO,UAAU,CAAC,IAAI,KAAK,QAAQ,EACnC;AACJ;;ACvFA;AAeA;;;AAGG;MACU,kBAAkB,CAAA;AAU7B;;;;;AAKG;AACH,IAAA,WAAA,CAAY,SAAiB,EAAA;QAC3B,IAAI,CAAC,SAAS,EAAE;AACd,YAAA,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;AACvE,SAAA;AAED,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;KAC7B;AAnBD;;AAEG;AACH,IAAA,IAAW,SAAS,GAAA;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;AAgBD;;;;;;;AAOG;AACI,IAAA,MAAM,CAAC,YAAoB,EAAA;QAChC,IAAI,CAAC,YAAY,EAAE;AACjB,YAAA,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;AACvE,SAAA;AAED,QAAA,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC;KAChC;AACF,CAAA;AAED;;;;AAIG;AACG,SAAU,eAAe,CAAC,UAAmB,EAAA;AACjD,IAAA,QACE,sBAAsB,CAAC,UAAU,EAAE,CAAC,WAAW,CAAC,CAAC,IAAI,OAAO,UAAU,CAAC,SAAS,KAAK,QAAQ,EAC7F;AACJ;;ACrEA;AACA;AA2EA;;;;AAIG;AACG,SAAU,iBAAiB,CAAC,UAAmB,EAAA;;;;;;IAMnD,MAAM,cAAc,GAAG,UAGtB,CAAC;AACF,IAAA,QACE,cAAc;AACd,QAAA,OAAO,cAAc,CAAC,QAAQ,KAAK,UAAU;AAC7C,SAAC,cAAc,CAAC,WAAW,KAAK,SAAS,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,EAChF;AACJ;;;;;;;;;"}