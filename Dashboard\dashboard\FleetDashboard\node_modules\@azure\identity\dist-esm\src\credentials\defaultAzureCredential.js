// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
import { ManagedIdentityCredential, } from "./managedIdentityCredential";
import { AzureCliCredential } from "./azureCliCredential";
import { AzurePowerShellCredential } from "./azurePowerShellCredential";
import { ChainedTokenCredential } from "./chainedTokenCredential";
import { EnvironmentCredential } from "./environmentCredential";
/**
 * A shim around ManagedIdentityCredential that adapts it to accept
 * `DefaultAzureCredentialOptions`.
 *
 * @internal
 */
export class DefaultManagedIdentityCredential extends ManagedIdentityCredential {
    // Constructor overload with just the other default options
    // Last constructor overload with Union of all options not required since the above two constructor overloads have optional properties
    constructor(options) {
        var _a;
        const managedIdentityClientId = (_a = options === null || options === void 0 ? void 0 : options.managedIdentityClientId) !== null && _a !== void 0 ? _a : process.env.AZURE_CLIENT_ID;
        const managedResourceId = options === null || options === void 0 ? void 0 : options.managedIdentityResourceId;
        // ManagedIdentityCredential throws if both the resourceId and the clientId are provided.
        if (managedResourceId) {
            const managedIdentityResourceIdOptions = Object.assign(Object.assign({}, options), { resourceId: managedResourceId });
            super(managedIdentityResourceIdOptions);
        }
        else if (managedIdentityClientId) {
            const managedIdentityClientOptions = Object.assign(Object.assign({}, options), { clientId: managedIdentityClientId });
            super(managedIdentityClientOptions);
        }
        else {
            super(options);
        }
    }
}
export const defaultCredentials = [
    EnvironmentCredential,
    DefaultManagedIdentityCredential,
    AzureCliCredential,
    AzurePowerShellCredential,
];
/**
 * Provides a default {@link ChainedTokenCredential} configuration that should
 * work for most applications that use the Azure SDK.
 */
export class DefaultAzureCredential extends ChainedTokenCredential {
    constructor(options) {
        super(...defaultCredentials.map((ctor) => new ctor(options)));
        this.UnavailableMessage =
            "DefaultAzureCredential => failed to retrieve a token from the included credentials. To troubleshoot, visit https://aka.ms/azsdk/js/identity/defaultazurecredential/troubleshoot.";
    }
}
//# sourceMappingURL=defaultAzureCredential.js.map