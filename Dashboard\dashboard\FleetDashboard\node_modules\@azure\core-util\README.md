# Azure Core Util client library for JavaScript (Experimental)

This library is intended to provide various shared utility functions for client SDK packages.

## Getting started

### Requirements

### Currently supported environments

- [LTS versions of Node.js](https://github.com/nodejs/release#release-schedule)
- Latest versions of Safari, Chrome, Edge, and Firefox.

See our [support policy](https://github.com/Azure/azure-sdk-for-js/blob/main/SUPPORT.md) for more details.

### Installation

This package is primarily used in authoring client SDKs and not meant to be consumed directly by end users.

## Key concepts

Utility methods provided by this library should be stateless.

## Examples

Examples can be found in the `samples` folder.

## Next steps

Look at usage in dependent client SDKs.

## Troubleshooting

If you run into issues while using this library, please feel free to [file an issue](https://github.com/Azure/azure-sdk-for-js/issues/new).

## Contributing

If you'd like to contribute to this library, please read the [contributing guide](https://github.com/Azure/azure-sdk-for-js/blob/main/CONTRIBUTING.md) to learn more about how to build and test the code.

![Impressions](https://azure-sdk-impressions.azurewebsites.net/api/impressions/azure-sdk-for-js%2Fsdk%2Fcore%2Fcore-util%2FREADME.png)
