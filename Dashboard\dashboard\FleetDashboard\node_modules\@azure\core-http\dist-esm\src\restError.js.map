{"version": 3, "file": "restError.js", "sourceRoot": "", "sources": ["../../src/restError.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,SAAS,EAAE,MAAM,kBAAkB,CAAC;AAE7C,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,MAAM,cAAc,GAAG,IAAI,SAAS,EAAE,CAAC;AAEvC;;GAEG;AACH,MAAM,OAAO,SAAU,SAAQ,KAAK;IA8BlC,YACE,OAAe,EACf,IAAa,EACb,UAAmB,EACnB,OAAyB,EACzB,QAAgC;QAEhC,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;QACxB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,CAAC,MAAM,CAAC;QACN,OAAO,cAAc,IAAI,CAAC,OAAO,OAAO,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;IAC1E,CAAC;;AAnDD;;GAEG;AACa,4BAAkB,GAAW,oBAAoB,CAAC;AAClE;;GAEG;AACa,qBAAW,GAAW,aAAa,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpOperationResponse } from \"./httpOperationResponse\";\nimport { Sanitizer } from \"./util/sanitizer\";\nimport { WebResourceLike } from \"./webResource\";\nimport { custom } from \"./util/inspect\";\n\nconst errorSanitizer = new Sanitizer();\n\n/**\n * An error resulting from an HTTP request to a service endpoint.\n */\nexport class RestError extends Error {\n  /**\n   * A constant string to identify errors that may arise when making an HTTP request that indicates an issue with the transport layer (e.g. the hostname of the URL cannot be resolved via DNS.)\n   */\n  static readonly REQUEST_SEND_ERROR: string = \"REQUEST_SEND_ERROR\";\n  /**\n   * A constant string to identify errors that may arise from parsing an incoming HTTP response. Usually indicates a malformed HTTP body, such as an encoded JSON payload that is incomplete.\n   */\n  static readonly PARSE_ERROR: string = \"PARSE_ERROR\";\n\n  /**\n   * The error code, if any. Can be one of the static error code properties (REQUEST_SEND_ERROR / PARSE_ERROR) or can be a string code from an underlying system call (E_NOENT).\n   */\n  code?: string;\n  /**\n   * The HTTP status code of the response, if one was returned.\n   */\n  statusCode?: number;\n  /**\n   * Outgoing request.\n   */\n  request?: WebResourceLike;\n  /**\n   * Incoming response.\n   */\n  response?: HttpOperationResponse;\n  /**\n   * Any additional details. In the case of deserialization errors, can be the processed response.\n   */\n  details?: unknown;\n  constructor(\n    message: string,\n    code?: string,\n    statusCode?: number,\n    request?: WebResourceLike,\n    response?: HttpOperationResponse\n  ) {\n    super(message);\n    this.name = \"RestError\";\n    this.code = code;\n    this.statusCode = statusCode;\n    this.request = request;\n    this.response = response;\n\n    Object.setPrototypeOf(this, RestError.prototype);\n  }\n\n  /**\n   * Logging method for util.inspect in Node\n   */\n  [custom](): string {\n    return `RestError: ${this.message} \\n ${errorSanitizer.sanitize(this)}`;\n  }\n}\n"]}