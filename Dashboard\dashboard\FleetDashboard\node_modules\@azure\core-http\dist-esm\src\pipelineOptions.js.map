{"version": 3, "file": "pipelineOptions.js", "sourceRoot": "", "sources": ["../../src/pipelineOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { DeserializationOptions } from \"./policies/deserializationPolicy\";\nimport { HttpClient } from \"./httpClient\";\nimport { KeepAliveOptions } from \"./policies/keepAlivePolicy\";\nimport { LogPolicyOptions } from \"./policies/logPolicy\";\nimport { ProxyOptions } from \"./serviceClient\";\nimport { RedirectOptions } from \"./policies/redirectPolicy\";\nimport { RetryOptions } from \"./policies/exponentialRetryPolicy\";\nimport { UserAgentOptions } from \"./policies/userAgentPolicy\";\n\n/**\n * Defines options that are used to configure the HTTP pipeline for\n * an SDK client.\n */\nexport interface PipelineOptions {\n  /**\n   * The HttpClient implementation to use for outgoing HTTP requests.  Defaults\n   * to DefaultHttpClient.\n   */\n  httpClient?: HttpClient;\n\n  /**\n   * Options that control how to retry failed requests.\n   */\n  retryOptions?: RetryOptions;\n\n  /**\n   * Options to configure a proxy for outgoing requests.\n   */\n  proxyOptions?: ProxyOptions;\n\n  /**\n   * Options for how HTTP connections should be maintained for future\n   * requests.\n   */\n  keepAliveOptions?: KeepAliveOptions;\n\n  /**\n   * Options for how redirect responses are handled.\n   */\n  redirectOptions?: RedirectOptions;\n\n  /**\n   * Options for adding user agent details to outgoing requests.\n   */\n  userAgentOptions?: UserAgentOptions;\n}\n\n/**\n * Defines options that are used to configure internal options of\n * the HTTP pipeline for an SDK client.\n */\nexport interface InternalPipelineOptions extends PipelineOptions {\n  /**\n   * Options to configure API response deserialization.\n   */\n  deserializationOptions?: DeserializationOptions;\n\n  /**\n   * Options to configure request/response logging.\n   */\n  loggingOptions?: LogPolicyOptions;\n\n  /**\n   * Configure whether to decompress response according to Accept-Encoding header (node-fetch only)\n   */\n  decompressResponse?: boolean;\n\n  /**\n   * Send JSON Array payloads as NDJSON.\n   */\n  sendStreamingJson?: boolean;\n}\n"]}