{"version": 3, "file": "index.js", "sources": ["../src/generated/models/mappers.ts", "../src/generated/models/parameters.ts", "../src/generated/operations/digitalTwinModels.ts", "../src/generated/operations/query.ts", "../src/generated/operations/digitalTwins.ts", "../src/generated/operations/eventRoutes.ts", "../src/generated/azureDigitalTwinsAPIContext.ts", "../src/generated/azureDigitalTwinsAPI.ts", "../src/constants.ts", "../src/tracing.ts", "../src/logger.ts", "../src/digitalTwinsClient.ts"], "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreHttp from \"@azure/core-http\";\n\nexport const DigitalTwinsModelData: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"DigitalTwinsModelData\",\n    modelProperties: {\n      displayName: {\n        serializedName: \"displayName\",\n        type: {\n          name: \"Dictionary\",\n          value: { type: { name: \"String\" } }\n        }\n      },\n      description: {\n        serializedName: \"description\",\n        type: {\n          name: \"Dictionary\",\n          value: { type: { name: \"String\" } }\n        }\n      },\n      id: {\n        serializedName: \"id\",\n        required: true,\n        type: {\n          name: \"String\"\n        }\n      },\n      uploadTime: {\n        serializedName: \"uploadTime\",\n        type: {\n          name: \"DateTime\"\n        }\n      },\n      decommissioned: {\n        serializedName: \"decommissioned\",\n        type: {\n          name: \"Boolean\"\n        }\n      },\n      model: {\n        serializedName: \"model\",\n        type: {\n          name: \"any\"\n        }\n      }\n    }\n  }\n};\n\nexport const ErrorResponse: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"ErrorResponse\",\n    modelProperties: {\n      error: {\n        serializedName: \"error\",\n        type: {\n          name: \"Composite\",\n          className: \"ErrorModel\"\n        }\n      }\n    }\n  }\n};\n\nexport const ErrorModel: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"ErrorModel\",\n    modelProperties: {\n      code: {\n        serializedName: \"code\",\n        readOnly: true,\n        type: {\n          name: \"String\"\n        }\n      },\n      message: {\n        serializedName: \"message\",\n        readOnly: true,\n        type: {\n          name: \"String\"\n        }\n      },\n      details: {\n        serializedName: \"details\",\n        readOnly: true,\n        type: {\n          name: \"Sequence\",\n          element: { type: { name: \"Composite\", className: \"ErrorModel\" } }\n        }\n      },\n      innererror: {\n        serializedName: \"innererror\",\n        type: {\n          name: \"Composite\",\n          className: \"InnerError\"\n        }\n      }\n    }\n  }\n};\n\nexport const InnerError: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"InnerError\",\n    modelProperties: {\n      code: {\n        serializedName: \"code\",\n        type: {\n          name: \"String\"\n        }\n      },\n      innererror: {\n        serializedName: \"innererror\",\n        type: {\n          name: \"Composite\",\n          className: \"InnerError\"\n        }\n      }\n    }\n  }\n};\n\nexport const PagedDigitalTwinsModelDataCollection: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"PagedDigitalTwinsModelDataCollection\",\n    modelProperties: {\n      value: {\n        serializedName: \"value\",\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: { name: \"Composite\", className: \"DigitalTwinsModelData\" }\n          }\n        }\n      },\n      nextLink: {\n        serializedName: \"nextLink\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const QuerySpecification: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"QuerySpecification\",\n    modelProperties: {\n      query: {\n        serializedName: \"query\",\n        type: {\n          name: \"String\"\n        }\n      },\n      continuationToken: {\n        serializedName: \"continuationToken\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const QueryResult: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"QueryResult\",\n    modelProperties: {\n      value: {\n        serializedName: \"value\",\n        type: {\n          name: \"Sequence\",\n          element: { type: { name: \"any\" } }\n        }\n      },\n      continuationToken: {\n        serializedName: \"continuationToken\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const RelationshipCollection: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"RelationshipCollection\",\n    modelProperties: {\n      value: {\n        serializedName: \"value\",\n        type: {\n          name: \"Sequence\",\n          element: { type: { name: \"any\" } }\n        }\n      },\n      nextLink: {\n        serializedName: \"nextLink\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const IncomingRelationshipCollection: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"IncomingRelationshipCollection\",\n    modelProperties: {\n      value: {\n        serializedName: \"value\",\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: { name: \"Composite\", className: \"IncomingRelationship\" }\n          }\n        }\n      },\n      nextLink: {\n        serializedName: \"nextLink\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const IncomingRelationship: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"IncomingRelationship\",\n    modelProperties: {\n      relationshipId: {\n        serializedName: \"$relationshipId\",\n        type: {\n          name: \"String\"\n        }\n      },\n      sourceId: {\n        serializedName: \"$sourceId\",\n        type: {\n          name: \"String\"\n        }\n      },\n      relationshipName: {\n        serializedName: \"$relationshipName\",\n        type: {\n          name: \"String\"\n        }\n      },\n      relationshipLink: {\n        serializedName: \"$relationshipLink\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const EventRouteCollection: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"EventRouteCollection\",\n    modelProperties: {\n      value: {\n        serializedName: \"value\",\n        type: {\n          name: \"Sequence\",\n          element: { type: { name: \"Composite\", className: \"EventRoute\" } }\n        }\n      },\n      nextLink: {\n        serializedName: \"nextLink\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const EventRoute: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"EventRoute\",\n    modelProperties: {\n      id: {\n        serializedName: \"id\",\n        readOnly: true,\n        type: {\n          name: \"String\"\n        }\n      },\n      endpointName: {\n        serializedName: \"endpointName\",\n        required: true,\n        type: {\n          name: \"String\"\n        }\n      },\n      filter: {\n        serializedName: \"filter\",\n        required: true,\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const QueryQueryTwinsHeaders: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"QueryQueryTwinsHeaders\",\n    modelProperties: {\n      queryCharge: {\n        serializedName: \"query-charge\",\n        type: {\n          name: \"Number\"\n        }\n      }\n    }\n  }\n};\n\nexport const DigitalTwinsGetByIdHeaders: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"DigitalTwinsGetByIdHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const DigitalTwinsAddHeaders: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"DigitalTwinsAddHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const DigitalTwinsUpdateHeaders: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"DigitalTwinsUpdateHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const DigitalTwinsGetRelationshipByIdHeaders: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"DigitalTwinsGetRelationshipByIdHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const DigitalTwinsAddRelationshipHeaders: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"DigitalTwinsAddRelationshipHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const DigitalTwinsUpdateRelationshipHeaders: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"DigitalTwinsUpdateRelationshipHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const DigitalTwinsGetComponentHeaders: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"DigitalTwinsGetComponentHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const DigitalTwinsUpdateComponentHeaders: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"DigitalTwinsUpdateComponentHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n", "/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport {\n  OperationParameter,\n  OperationURLParameter,\n  OperationQueryParameter,\n  QueryCollectionFormat\n} from \"@azure/core-http\";\nimport {\n  QuerySpecification as QuerySpecificationMapper,\n  EventRoute as EventRouteMapper\n} from \"../models/mappers\";\n\nexport const contentType: OperationParameter = {\n  parameterPath: [\"options\", \"contentType\"],\n  mapper: {\n    defaultValue: \"application/json\",\n    isConstant: true,\n    serializedName: \"Content-Type\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const models: OperationParameter = {\n  parameterPath: [\"options\", \"models\"],\n  mapper: {\n    constraints: {\n      MinItems: 1,\n      UniqueItems: true\n    },\n    serializedName: \"models\",\n    type: {\n      name: \"Sequence\",\n      element: { type: { name: \"any\" } }\n    }\n  }\n};\n\nexport const $host: OperationURLParameter = {\n  parameterPath: \"$host\",\n  mapper: {\n    serializedName: \"$host\",\n    required: true,\n    type: {\n      name: \"String\"\n    }\n  },\n  skipEncoding: true\n};\n\nexport const traceparent: OperationParameter = {\n  parameterPath: [\"options\", \"traceparent\"],\n  mapper: {\n    serializedName: \"traceparent\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const tracestate: OperationParameter = {\n  parameterPath: [\"options\", \"tracestate\"],\n  mapper: {\n    serializedName: \"tracestate\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const apiVersion: OperationQueryParameter = {\n  parameterPath: \"apiVersion\",\n  mapper: {\n    defaultValue: \"2022-05-31\",\n    isConstant: true,\n    serializedName: \"api-version\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const dependenciesFor: OperationQueryParameter = {\n  parameterPath: [\"options\", \"dependenciesFor\"],\n  mapper: {\n    serializedName: \"dependenciesFor\",\n    type: {\n      name: \"Sequence\",\n      element: { type: { name: \"String\" } }\n    }\n  },\n  collectionFormat: QueryCollectionFormat.Csv\n};\n\nexport const includeModelDefinition: OperationQueryParameter = {\n  parameterPath: [\"options\", \"includeModelDefinition\"],\n  mapper: {\n    serializedName: \"includeModelDefinition\",\n    type: {\n      name: \"Boolean\"\n    }\n  }\n};\n\nexport const maxItemsPerPage: OperationParameter = {\n  parameterPath: [\"options\", \"maxItemsPerPage\"],\n  mapper: {\n    serializedName: \"max-items-per-page\",\n    type: {\n      name: \"Number\"\n    }\n  }\n};\n\nexport const id: OperationURLParameter = {\n  parameterPath: \"id\",\n  mapper: {\n    serializedName: \"id\",\n    required: true,\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const contentType1: OperationParameter = {\n  parameterPath: [\"options\", \"contentType\"],\n  mapper: {\n    defaultValue: \"application/json-patch+json\",\n    isConstant: true,\n    serializedName: \"Content-Type\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const updateModel: OperationParameter = {\n  parameterPath: \"updateModel\",\n  mapper: {\n    serializedName: \"updateModel\",\n    required: true,\n    type: {\n      name: \"Sequence\",\n      element: { type: { name: \"any\" } }\n    }\n  }\n};\n\nexport const nextLink: OperationURLParameter = {\n  parameterPath: \"nextLink\",\n  mapper: {\n    serializedName: \"nextLink\",\n    required: true,\n    type: {\n      name: \"String\"\n    }\n  },\n  skipEncoding: true\n};\n\nexport const querySpecification: OperationParameter = {\n  parameterPath: \"querySpecification\",\n  mapper: QuerySpecificationMapper\n};\n\nexport const twin: OperationParameter = {\n  parameterPath: \"twin\",\n  mapper: {\n    serializedName: \"twin\",\n    required: true,\n    type: {\n      name: \"any\"\n    }\n  }\n};\n\nexport const ifNoneMatch: OperationParameter = {\n  parameterPath: [\"options\", \"ifNoneMatch\"],\n  mapper: {\n    serializedName: \"If-None-Match\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const ifMatch: OperationParameter = {\n  parameterPath: [\"options\", \"ifMatch\"],\n  mapper: {\n    serializedName: \"If-Match\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const patchDocument: OperationParameter = {\n  parameterPath: \"patchDocument\",\n  mapper: {\n    serializedName: \"patchDocument\",\n    required: true,\n    type: {\n      name: \"Sequence\",\n      element: { type: { name: \"any\" } }\n    }\n  }\n};\n\nexport const relationshipId: OperationURLParameter = {\n  parameterPath: \"relationshipId\",\n  mapper: {\n    serializedName: \"relationshipId\",\n    required: true,\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const relationship: OperationParameter = {\n  parameterPath: \"relationship\",\n  mapper: {\n    serializedName: \"relationship\",\n    required: true,\n    type: {\n      name: \"any\"\n    }\n  }\n};\n\nexport const relationshipName: OperationQueryParameter = {\n  parameterPath: [\"options\", \"relationshipName\"],\n  mapper: {\n    serializedName: \"relationshipName\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const telemetry: OperationParameter = {\n  parameterPath: \"telemetry\",\n  mapper: {\n    serializedName: \"telemetry\",\n    required: true,\n    type: {\n      name: \"any\"\n    }\n  }\n};\n\nexport const messageId: OperationParameter = {\n  parameterPath: \"messageId\",\n  mapper: {\n    serializedName: \"Message-Id\",\n    required: true,\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const telemetrySourceTime: OperationParameter = {\n  parameterPath: [\"options\", \"telemetrySourceTime\"],\n  mapper: {\n    serializedName: \"Telemetry-Source-Time\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const componentPath: OperationURLParameter = {\n  parameterPath: \"componentPath\",\n  mapper: {\n    serializedName: \"componentPath\",\n    required: true,\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const eventRoute: OperationParameter = {\n  parameterPath: [\"options\", \"eventRoute\"],\n  mapper: EventRouteMapper\n};\n", "/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreHttp from \"@azure/core-http\";\nimport * as Mappers from \"../models/mappers\";\nimport * as Parameters from \"../models/parameters\";\nimport { AzureDigitalTwinsAPI } from \"../azureDigitalTwinsAPI\";\nimport {\n  DigitalTwinModelsAddOptionalParams,\n  DigitalTwinModelsAddResponse,\n  DigitalTwinModelsListOptionalParams,\n  DigitalTwinModelsListResponse,\n  DigitalTwinModelsGetByIdOptionalParams,\n  DigitalTwinModelsGetByIdResponse,\n  DigitalTwinModelsUpdateOptionalParams,\n  DigitalTwinModelsDeleteOptionalParams,\n  DigitalTwinModelsListNextOptionalParams,\n  DigitalTwinModelsListNextResponse\n} from \"../models\";\n\n/**\n * Class representing a DigitalTwinModels.\n */\nexport class DigitalTwinModels {\n  private readonly client: AzureDigitalTwinsAPI;\n\n  /**\n   * Initialize a new instance of the class DigitalTwinModels class.\n   * @param client Reference to the service client\n   */\n  constructor(client: AzureDigitalTwinsAPI) {\n    this.client = client;\n  }\n\n  /**\n   * Uploads one or more models. When any error occurs, no models are uploaded.\n   * Status codes:\n   * * 201 Created\n   * * 400 Bad Request\n   *   * DTDLParserError - The models provided are not valid DTDL.\n   *   * InvalidArgument - The model id is invalid.\n   *   * LimitExceeded - The maximum number of model ids allowed in 'dependenciesFor' has been reached.\n   *   * ModelVersionNotSupported - The version of DTDL used is not supported.\n   * * 409 Conflict\n   *   * ModelAlreadyExists - The model provided already exists.\n   * @param options The options parameters.\n   */\n  add(\n    options?: DigitalTwinModelsAddOptionalParams\n  ): Promise<DigitalTwinModelsAddResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { options: operationOptions },\n      addOperationSpec\n    ) as Promise<DigitalTwinModelsAddResponse>;\n  }\n\n  /**\n   * Retrieves model metadata and, optionally, model definitions.\n   * Status codes:\n   * * 200 OK\n   * * 400 Bad Request\n   *   * InvalidArgument - The model id is invalid.\n   *   * LimitExceeded - The maximum number of model ids allowed in 'dependenciesFor' has been reached.\n   * * 404 Not Found\n   *   * ModelNotFound - The model was not found.\n   * @param options The options parameters.\n   */\n  list(\n    options?: DigitalTwinModelsListOptionalParams\n  ): Promise<DigitalTwinModelsListResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { options: operationOptions },\n      listOperationSpec\n    ) as Promise<DigitalTwinModelsListResponse>;\n  }\n\n  /**\n   * Retrieves model metadata and optionally the model definition.\n   * Status codes:\n   * * 200 OK\n   * * 400 Bad Request\n   *   * InvalidArgument - The model id is invalid.\n   *   * MissingArgument - The model id was not provided.\n   * * 404 Not Found\n   *   * ModelNotFound - The model was not found.\n   * @param id The id for the model. The id is globally unique and case sensitive.\n   * @param options The options parameters.\n   */\n  getById(\n    id: string,\n    options?: DigitalTwinModelsGetByIdOptionalParams\n  ): Promise<DigitalTwinModelsGetByIdResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, options: operationOptions },\n      getByIdOperationSpec\n    ) as Promise<DigitalTwinModelsGetByIdResponse>;\n  }\n\n  /**\n   * Updates the metadata for a model.\n   * Status codes:\n   * * 204 No Content\n   * * 400 Bad Request\n   *   * InvalidArgument - The model id is invalid.\n   *   * JsonPatchInvalid - The JSON Patch provided is invalid.\n   *   * MissingArgument - The model id was not provided.\n   * * 404 Not Found\n   *   * ModelNotFound - The model was not found.\n   * * 409 Conflict\n   *   * ModelReferencesNotDecommissioned - The model refers to models that are not decommissioned.\n   * @param id The id for the model. The id is globally unique and case sensitive.\n   * @param updateModel An update specification described by JSON Patch. Only the decommissioned property\n   *                    can be replaced.\n   * @param options The options parameters.\n   */\n  update(\n    id: string,\n    updateModel: any[],\n    options?: DigitalTwinModelsUpdateOptionalParams\n  ): Promise<coreHttp.RestResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, updateModel, options: operationOptions },\n      updateOperationSpec\n    ) as Promise<coreHttp.RestResponse>;\n  }\n\n  /**\n   * Deletes a model. A model can only be deleted if no other models reference it.\n   * Status codes:\n   * * 204 No Content\n   * * 400 Bad Request\n   *   * InvalidArgument - The model id is invalid.\n   *   * MissingArgument - The model id was not provided.\n   * * 404 Not Found\n   *   * ModelNotFound - The model was not found.\n   * * 409 Conflict\n   *   * ModelReferencesNotDeleted - The model refers to models that are not deleted.\n   * @param id The id for the model. The id is globally unique and case sensitive.\n   * @param options The options parameters.\n   */\n  delete(\n    id: string,\n    options?: DigitalTwinModelsDeleteOptionalParams\n  ): Promise<coreHttp.RestResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, options: operationOptions },\n      deleteOperationSpec\n    ) as Promise<coreHttp.RestResponse>;\n  }\n\n  /**\n   * ListNext\n   * @param nextLink The nextLink from the previous successful call to the List method.\n   * @param options The options parameters.\n   */\n  listNext(\n    nextLink: string,\n    options?: DigitalTwinModelsListNextOptionalParams\n  ): Promise<DigitalTwinModelsListNextResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { nextLink, options: operationOptions },\n      listNextOperationSpec\n    ) as Promise<DigitalTwinModelsListNextResponse>;\n  }\n}\n// Operation Specifications\n\nconst serializer = new coreHttp.Serializer(Mappers, /* isXml */ false);\n\nconst addOperationSpec: coreHttp.OperationSpec = {\n  path: \"/models\",\n  httpMethod: \"POST\",\n  responses: {\n    201: {\n      bodyMapper: {\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: { name: \"Composite\", className: \"DigitalTwinsModelData\" }\n          }\n        }\n      }\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.models,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.traceparent,\n    Parameters.tracestate\n  ],\n  mediaType: \"json\",\n  serializer\n};\nconst listOperationSpec: coreHttp.OperationSpec = {\n  path: \"/models\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.PagedDigitalTwinsModelDataCollection\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [\n    Parameters.apiVersion,\n    Parameters.dependenciesFor,\n    Parameters.includeModelDefinition\n  ],\n  urlParameters: [Parameters.$host],\n  headerParameters: [\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.maxItemsPerPage\n  ],\n  serializer\n};\nconst getByIdOperationSpec: coreHttp.OperationSpec = {\n  path: \"/models/{id}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.DigitalTwinsModelData\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion, Parameters.includeModelDefinition],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\nconst updateOperationSpec: coreHttp.OperationSpec = {\n  path: \"/models/{id}\",\n  httpMethod: \"PATCH\",\n  responses: {\n    204: {},\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.updateModel,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.contentType1\n  ],\n  mediaType: \"json\",\n  serializer\n};\nconst deleteOperationSpec: coreHttp.OperationSpec = {\n  path: \"/models/{id}\",\n  httpMethod: \"DELETE\",\n  responses: {\n    204: {},\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\nconst listNextOperationSpec: coreHttp.OperationSpec = {\n  path: \"{nextLink}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.PagedDigitalTwinsModelDataCollection\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [\n    Parameters.apiVersion,\n    Parameters.dependenciesFor,\n    Parameters.includeModelDefinition\n  ],\n  urlParameters: [Parameters.$host, Parameters.nextLink],\n  headerParameters: [\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.maxItemsPerPage\n  ],\n  serializer\n};\n", "/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreHttp from \"@azure/core-http\";\nimport * as Mappers from \"../models/mappers\";\nimport * as Parameters from \"../models/parameters\";\nimport { AzureDigitalTwinsAPI } from \"../azureDigitalTwinsAPI\";\nimport {\n  QuerySpecification,\n  QueryQueryTwinsOptionalParams,\n  QueryQueryTwinsResponse\n} from \"../models\";\n\n/**\n * Class representing a Query.\n */\nexport class Query {\n  private readonly client: AzureDigitalTwinsAPI;\n\n  /**\n   * Initialize a new instance of the class Query class.\n   * @param client Reference to the service client\n   */\n  constructor(client: AzureDigitalTwinsAPI) {\n    this.client = client;\n  }\n\n  /**\n   * Executes a query that allows traversing relationships and filtering by property values.\n   * Status codes:\n   * * 200 OK\n   * * 400 Bad Request\n   *   * BadRequest - The continuation token is invalid.\n   *   * SqlQueryError - The query contains some errors.\n   * * 429 Too Many Requests\n   *   * QuotaReachedError - The maximum query rate limit has been reached.\n   * @param querySpecification The query specification to execute.\n   * @param options The options parameters.\n   */\n  queryTwins(\n    querySpecification: QuerySpecification,\n    options?: QueryQueryTwinsOptionalParams\n  ): Promise<QueryQueryTwinsResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { querySpecification, options: operationOptions },\n      queryTwinsOperationSpec\n    ) as Promise<QueryQueryTwinsResponse>;\n  }\n}\n// Operation Specifications\n\nconst serializer = new coreHttp.Serializer(Mappers, /* isXml */ false);\n\nconst queryTwinsOperationSpec: coreHttp.OperationSpec = {\n  path: \"/query\",\n  httpMethod: \"POST\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.QueryResult,\n      headersMapper: Mappers.QueryQueryTwinsHeaders\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.querySpecification,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.maxItemsPerPage\n  ],\n  mediaType: \"json\",\n  serializer\n};\n", "/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreHttp from \"@azure/core-http\";\nimport * as Mappers from \"../models/mappers\";\nimport * as Parameters from \"../models/parameters\";\nimport { AzureDigitalTwinsAPI } from \"../azureDigitalTwinsAPI\";\nimport {\n  DigitalTwinsGetByIdOptionalParams,\n  DigitalTwinsGetByIdResponse,\n  DigitalTwinsAddOptionalParams,\n  DigitalTwinsAddResponse,\n  DigitalTwinsDeleteOptionalParams,\n  DigitalTwinsUpdateOptionalParams,\n  DigitalTwinsUpdateResponse,\n  DigitalTwinsGetRelationshipByIdOptionalParams,\n  DigitalTwinsGetRelationshipByIdResponse,\n  DigitalTwinsAddRelationshipOptionalParams,\n  DigitalTwinsAddRelationshipResponse,\n  DigitalTwinsDeleteRelationshipOptionalParams,\n  DigitalTwinsUpdateRelationshipOptionalParams,\n  DigitalTwinsUpdateRelationshipResponse,\n  DigitalTwinsListRelationshipsOptionalParams,\n  DigitalTwinsListRelationshipsResponse,\n  DigitalTwinsListIncomingRelationshipsOptionalParams,\n  DigitalTwinsListIncomingRelationshipsResponse,\n  DigitalTwinsSendTelemetryOptionalParams,\n  DigitalTwinsSendComponentTelemetryOptionalParams,\n  DigitalTwinsGetComponentOptionalParams,\n  DigitalTwinsGetComponentResponse,\n  DigitalTwinsUpdateComponentOptionalParams,\n  DigitalTwinsUpdateComponentResponse,\n  DigitalTwinsListRelationshipsNextOptionalParams,\n  DigitalTwinsListRelationshipsNextResponse,\n  DigitalTwinsListIncomingRelationshipsNextOptionalParams,\n  DigitalTwinsListIncomingRelationshipsNextResponse\n} from \"../models\";\n\n/**\n * Class representing a DigitalTwins.\n */\nexport class DigitalTwins {\n  private readonly client: AzureDigitalTwinsAPI;\n\n  /**\n   * Initialize a new instance of the class DigitalTwins class.\n   * @param client Reference to the service client\n   */\n  constructor(client: AzureDigitalTwinsAPI) {\n    this.client = client;\n  }\n\n  /**\n   * Retrieves a digital twin.\n   * Status codes:\n   * * 200 OK\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id is invalid.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param options The options parameters.\n   */\n  getById(\n    id: string,\n    options?: DigitalTwinsGetByIdOptionalParams\n  ): Promise<DigitalTwinsGetByIdResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, options: operationOptions },\n      getByIdOperationSpec\n    ) as Promise<DigitalTwinsGetByIdResponse>;\n  }\n\n  /**\n   * Adds or replaces a digital twin.\n   * Status codes:\n   * * 200 OK\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id or payload is invalid.\n   *   * ModelDecommissioned - The model for the digital twin is decommissioned.\n   *   * TwinLimitReached - The maximum number of digital twins allowed has been reached.\n   *   * ValidationFailed - The digital twin payload is not valid.\n   * * 412 Precondition Failed\n   *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param twin The digital twin instance being added. If provided, the $dtId property is ignored.\n   * @param options The options parameters.\n   */\n  add(\n    id: string,\n    twin: any,\n    options?: DigitalTwinsAddOptionalParams\n  ): Promise<DigitalTwinsAddResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, twin, options: operationOptions },\n      addOperationSpec\n    ) as Promise<DigitalTwinsAddResponse>;\n  }\n\n  /**\n   * Deletes a digital twin. All relationships referencing the digital twin must already be deleted.\n   * Status codes:\n   * * 204 No Content\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id is invalid.\n   *   * RelationshipsNotDeleted - The digital twin contains relationships.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   * * 412 Precondition Failed\n   *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param options The options parameters.\n   */\n  delete(\n    id: string,\n    options?: DigitalTwinsDeleteOptionalParams\n  ): Promise<coreHttp.RestResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, options: operationOptions },\n      deleteOperationSpec\n    ) as Promise<coreHttp.RestResponse>;\n  }\n\n  /**\n   * Updates a digital twin.\n   * Status codes:\n   * * 204 No Content\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id or payload is invalid.\n   *   * JsonPatchInvalid - The JSON Patch provided is invalid.\n   *   * ValidationFailed - Applying the patch results in an invalid digital twin.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   * * 412 Precondition Failed\n   *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param patchDocument An update specification described by JSON Patch. Updates to property values and\n   *                      $model elements may happen in the same request. Operations are limited to add, replace and remove.\n   * @param options The options parameters.\n   */\n  update(\n    id: string,\n    patchDocument: any[],\n    options?: DigitalTwinsUpdateOptionalParams\n  ): Promise<DigitalTwinsUpdateResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, patchDocument, options: operationOptions },\n      updateOperationSpec\n    ) as Promise<DigitalTwinsUpdateResponse>;\n  }\n\n  /**\n   * Retrieves a relationship between two digital twins.\n   * Status codes:\n   * * 200 OK\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id or relationship id is invalid.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   *   * RelationshipNotFound - The relationship was not found.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param relationshipId The id of the relationship. The id is unique within the digital twin and case\n   *                       sensitive.\n   * @param options The options parameters.\n   */\n  getRelationshipById(\n    id: string,\n    relationshipId: string,\n    options?: DigitalTwinsGetRelationshipByIdOptionalParams\n  ): Promise<DigitalTwinsGetRelationshipByIdResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, relationshipId, options: operationOptions },\n      getRelationshipByIdOperationSpec\n    ) as Promise<DigitalTwinsGetRelationshipByIdResponse>;\n  }\n\n  /**\n   * Adds a relationship between two digital twins.\n   * Status codes:\n   * * 200 OK\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id, relationship id, or payload is invalid.\n   *   * InvalidRelationship - The relationship is invalid.\n   *   * OperationNotAllowed - The relationship cannot connect to the same digital twin.\n   *   * ValidationFailed - The relationship content is invalid.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   *   * TargetTwinNotFound - The digital twin target of the relationship was not found.\n   * * 412 Precondition Failed\n   *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param relationshipId The id of the relationship. The id is unique within the digital twin and case\n   *                       sensitive.\n   * @param relationship The data for the relationship.\n   * @param options The options parameters.\n   */\n  addRelationship(\n    id: string,\n    relationshipId: string,\n    relationship: any,\n    options?: DigitalTwinsAddRelationshipOptionalParams\n  ): Promise<DigitalTwinsAddRelationshipResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, relationshipId, relationship, options: operationOptions },\n      addRelationshipOperationSpec\n    ) as Promise<DigitalTwinsAddRelationshipResponse>;\n  }\n\n  /**\n   * Deletes a relationship between two digital twins.\n   * Status codes:\n   * * 204 No Content\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id or relationship id is invalid.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   *   * RelationshipNotFound - The relationship was not found.\n   * * 412 Precondition Failed\n   *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param relationshipId The id of the relationship. The id is unique within the digital twin and case\n   *                       sensitive.\n   * @param options The options parameters.\n   */\n  deleteRelationship(\n    id: string,\n    relationshipId: string,\n    options?: DigitalTwinsDeleteRelationshipOptionalParams\n  ): Promise<coreHttp.RestResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, relationshipId, options: operationOptions },\n      deleteRelationshipOperationSpec\n    ) as Promise<coreHttp.RestResponse>;\n  }\n\n  /**\n   * Updates the properties on a relationship between two digital twins.\n   * Status codes:\n   * * 204 No Content\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id or relationship id is invalid.\n   *   * InvalidRelationship - The relationship is invalid.\n   *   * JsonPatchInvalid - The JSON Patch provided is invalid.\n   *   * ValidationFailed - The relationship content is invalid.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   *   * RelationshipNotFound - The relationship was not found.\n   * * 409 Conflict\n   *   * RelationshipAlreadyExists - The relationship already exists.\n   * * 412 Precondition Failed\n   *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param relationshipId The id of the relationship. The id is unique within the digital twin and case\n   *                       sensitive.\n   * @param patchDocument JSON Patch description of the update to the relationship properties.\n   * @param options The options parameters.\n   */\n  updateRelationship(\n    id: string,\n    relationshipId: string,\n    patchDocument: any[],\n    options?: DigitalTwinsUpdateRelationshipOptionalParams\n  ): Promise<DigitalTwinsUpdateRelationshipResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, relationshipId, patchDocument, options: operationOptions },\n      updateRelationshipOperationSpec\n    ) as Promise<DigitalTwinsUpdateRelationshipResponse>;\n  }\n\n  /**\n   * Retrieves the relationships from a digital twin.\n   * Status codes:\n   * * 200 OK\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id is invalid.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param options The options parameters.\n   */\n  listRelationships(\n    id: string,\n    options?: DigitalTwinsListRelationshipsOptionalParams\n  ): Promise<DigitalTwinsListRelationshipsResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, options: operationOptions },\n      listRelationshipsOperationSpec\n    ) as Promise<DigitalTwinsListRelationshipsResponse>;\n  }\n\n  /**\n   * Retrieves all incoming relationship for a digital twin.\n   * Status codes:\n   * * 200 OK\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id is invalid.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param options The options parameters.\n   */\n  listIncomingRelationships(\n    id: string,\n    options?: DigitalTwinsListIncomingRelationshipsOptionalParams\n  ): Promise<DigitalTwinsListIncomingRelationshipsResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, options: operationOptions },\n      listIncomingRelationshipsOperationSpec\n    ) as Promise<DigitalTwinsListIncomingRelationshipsResponse>;\n  }\n\n  /**\n   * Sends telemetry on behalf of a digital twin.\n   * Status codes:\n   * * 204 No Content\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id or message id is invalid.\n   *   * ValidationFailed - The telemetry content is invalid.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param messageId A unique message identifier (in the scope of the digital twin id) that is commonly\n   *                  used for de-duplicating messages.\n   * @param telemetry The telemetry measurements to send from the digital twin.\n   * @param options The options parameters.\n   */\n  sendTelemetry(\n    id: string,\n    messageId: string,\n    telemetry: any,\n    options?: DigitalTwinsSendTelemetryOptionalParams\n  ): Promise<coreHttp.RestResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, messageId, telemetry, options: operationOptions },\n      sendTelemetryOperationSpec\n    ) as Promise<coreHttp.RestResponse>;\n  }\n\n  /**\n   * Sends telemetry on behalf of a component in a digital twin.\n   * Status codes:\n   * * 204 No Content\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id, message id, or component path is invalid.\n   *   * ValidationFailed - The telemetry content is invalid.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   *   * ComponentNotFound - The component path was not found.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param componentPath The name of the DTDL component.\n   * @param messageId A unique message identifier (in the scope of the digital twin id) that is commonly\n   *                  used for de-duplicating messages.\n   * @param telemetry The telemetry measurements to send from the digital twin's component.\n   * @param options The options parameters.\n   */\n  sendComponentTelemetry(\n    id: string,\n    componentPath: string,\n    messageId: string,\n    telemetry: any,\n    options?: DigitalTwinsSendComponentTelemetryOptionalParams\n  ): Promise<coreHttp.RestResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, componentPath, messageId, telemetry, options: operationOptions },\n      sendComponentTelemetryOperationSpec\n    ) as Promise<coreHttp.RestResponse>;\n  }\n\n  /**\n   * Retrieves a component from a digital twin.\n   * Status codes:\n   * * 200 OK\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id or component path is invalid.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   *   * ComponentNotFound - The component path was not found.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param componentPath The name of the DTDL component.\n   * @param options The options parameters.\n   */\n  getComponent(\n    id: string,\n    componentPath: string,\n    options?: DigitalTwinsGetComponentOptionalParams\n  ): Promise<DigitalTwinsGetComponentResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, componentPath, options: operationOptions },\n      getComponentOperationSpec\n    ) as Promise<DigitalTwinsGetComponentResponse>;\n  }\n\n  /**\n   * Updates a component on a digital twin.\n   * Status codes:\n   * * 204 No Content\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id, component path, or payload is invalid.\n   *   * JsonPatchInvalid - The JSON Patch provided is invalid.\n   *   * ValidationFailed - Applying the patch results in an invalid digital twin.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   * * 412 Precondition Failed\n   *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param componentPath The name of the DTDL component.\n   * @param patchDocument An update specification described by JSON Patch. Updates to property values and\n   *                      $model elements may happen in the same request. Operations are limited to add, replace and remove.\n   * @param options The options parameters.\n   */\n  updateComponent(\n    id: string,\n    componentPath: string,\n    patchDocument: any[],\n    options?: DigitalTwinsUpdateComponentOptionalParams\n  ): Promise<DigitalTwinsUpdateComponentResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, componentPath, patchDocument, options: operationOptions },\n      updateComponentOperationSpec\n    ) as Promise<DigitalTwinsUpdateComponentResponse>;\n  }\n\n  /**\n   * ListRelationshipsNext\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param nextLink The nextLink from the previous successful call to the ListRelationships method.\n   * @param options The options parameters.\n   */\n  listRelationshipsNext(\n    id: string,\n    nextLink: string,\n    options?: DigitalTwinsListRelationshipsNextOptionalParams\n  ): Promise<DigitalTwinsListRelationshipsNextResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, nextLink, options: operationOptions },\n      listRelationshipsNextOperationSpec\n    ) as Promise<DigitalTwinsListRelationshipsNextResponse>;\n  }\n\n  /**\n   * ListIncomingRelationshipsNext\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param nextLink The nextLink from the previous successful call to the ListIncomingRelationships\n   *                 method.\n   * @param options The options parameters.\n   */\n  listIncomingRelationshipsNext(\n    id: string,\n    nextLink: string,\n    options?: DigitalTwinsListIncomingRelationshipsNextOptionalParams\n  ): Promise<DigitalTwinsListIncomingRelationshipsNextResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, nextLink, options: operationOptions },\n      listIncomingRelationshipsNextOperationSpec\n    ) as Promise<DigitalTwinsListIncomingRelationshipsNextResponse>;\n  }\n}\n// Operation Specifications\n\nconst serializer = new coreHttp.Serializer(Mappers, /* isXml */ false);\n\nconst getByIdOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: { type: { name: \"any\" } },\n      headersMapper: Mappers.DigitalTwinsGetByIdHeaders\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\nconst addOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      bodyMapper: { type: { name: \"any\" } },\n      headersMapper: Mappers.DigitalTwinsAddHeaders\n    },\n    202: {},\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.twin,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.ifNoneMatch\n  ],\n  mediaType: \"json\",\n  serializer\n};\nconst deleteOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}\",\n  httpMethod: \"DELETE\",\n  responses: {\n    204: {},\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.ifMatch\n  ],\n  serializer\n};\nconst updateOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}\",\n  httpMethod: \"PATCH\",\n  responses: {\n    202: {},\n    204: {\n      headersMapper: Mappers.DigitalTwinsUpdateHeaders\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.patchDocument,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.contentType1,\n    Parameters.ifMatch\n  ],\n  mediaType: \"json\",\n  serializer\n};\nconst getRelationshipByIdOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}/relationships/{relationshipId}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: { type: { name: \"any\" } },\n      headersMapper: Mappers.DigitalTwinsGetRelationshipByIdHeaders\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id, Parameters.relationshipId],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\nconst addRelationshipOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}/relationships/{relationshipId}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      bodyMapper: { type: { name: \"any\" } },\n      headersMapper: Mappers.DigitalTwinsAddRelationshipHeaders\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.relationship,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id, Parameters.relationshipId],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.ifNoneMatch\n  ],\n  mediaType: \"json\",\n  serializer\n};\nconst deleteRelationshipOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}/relationships/{relationshipId}\",\n  httpMethod: \"DELETE\",\n  responses: {\n    204: {},\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id, Parameters.relationshipId],\n  headerParameters: [\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.ifMatch\n  ],\n  serializer\n};\nconst updateRelationshipOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}/relationships/{relationshipId}\",\n  httpMethod: \"PATCH\",\n  responses: {\n    204: {\n      headersMapper: Mappers.DigitalTwinsUpdateRelationshipHeaders\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.patchDocument,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id, Parameters.relationshipId],\n  headerParameters: [\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.contentType1,\n    Parameters.ifMatch\n  ],\n  mediaType: \"json\",\n  serializer\n};\nconst listRelationshipsOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}/relationships\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.RelationshipCollection\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion, Parameters.relationshipName],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\nconst listIncomingRelationshipsOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}/incomingrelationships\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.IncomingRelationshipCollection\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\nconst sendTelemetryOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}/telemetry\",\n  httpMethod: \"POST\",\n  responses: {\n    204: {},\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.telemetry,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.messageId,\n    Parameters.telemetrySourceTime\n  ],\n  mediaType: \"json\",\n  serializer\n};\nconst sendComponentTelemetryOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}/components/{componentPath}/telemetry\",\n  httpMethod: \"POST\",\n  responses: {\n    204: {},\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.telemetry,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id, Parameters.componentPath],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.messageId,\n    Parameters.telemetrySourceTime\n  ],\n  mediaType: \"json\",\n  serializer\n};\nconst getComponentOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}/components/{componentPath}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: { type: { name: \"any\" } },\n      headersMapper: Mappers.DigitalTwinsGetComponentHeaders\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id, Parameters.componentPath],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\nconst updateComponentOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}/components/{componentPath}\",\n  httpMethod: \"PATCH\",\n  responses: {\n    202: {},\n    204: {\n      headersMapper: Mappers.DigitalTwinsUpdateComponentHeaders\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.patchDocument,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id, Parameters.componentPath],\n  headerParameters: [\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.contentType1,\n    Parameters.ifMatch\n  ],\n  mediaType: \"json\",\n  serializer\n};\nconst listRelationshipsNextOperationSpec: coreHttp.OperationSpec = {\n  path: \"{nextLink}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.RelationshipCollection\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion, Parameters.relationshipName],\n  urlParameters: [Parameters.$host, Parameters.id, Parameters.nextLink],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\nconst listIncomingRelationshipsNextOperationSpec: coreHttp.OperationSpec = {\n  path: \"{nextLink}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.IncomingRelationshipCollection\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id, Parameters.nextLink],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\n", "/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreHttp from \"@azure/core-http\";\nimport * as Mappers from \"../models/mappers\";\nimport * as Parameters from \"../models/parameters\";\nimport { AzureDigitalTwinsAPI } from \"../azureDigitalTwinsAPI\";\nimport {\n  EventRoutesListOptionalParams,\n  EventRoutesListResponse,\n  EventRoutesGetByIdOptionalParams,\n  EventRoutesGetByIdResponse,\n  EventRoutesAddOptionalParams,\n  EventRoutesDeleteOptionalParams,\n  EventRoutesListNextOptionalParams,\n  EventRoutesListNextResponse\n} from \"../models\";\n\n/**\n * Class representing a EventRoutes.\n */\nexport class EventRoutes {\n  private readonly client: AzureDigitalTwinsAPI;\n\n  /**\n   * Initialize a new instance of the class EventRoutes class.\n   * @param client Reference to the service client\n   */\n  constructor(client: AzureDigitalTwinsAPI) {\n    this.client = client;\n  }\n\n  /**\n   * Retrieves all event routes.\n   * Status codes:\n   * * 200 OK\n   * @param options The options parameters.\n   */\n  list(\n    options?: EventRoutesListOptionalParams\n  ): Promise<EventRoutesListResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { options: operationOptions },\n      listOperationSpec\n    ) as Promise<EventRoutesListResponse>;\n  }\n\n  /**\n   * Retrieves an event route.\n   * Status codes:\n   * * 200 OK\n   * * 404 Not Found\n   *   * EventRouteNotFound - The event route was not found.\n   * @param id The id for an event route. The id is unique within event routes and case sensitive.\n   * @param options The options parameters.\n   */\n  getById(\n    id: string,\n    options?: EventRoutesGetByIdOptionalParams\n  ): Promise<EventRoutesGetByIdResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, options: operationOptions },\n      getByIdOperationSpec\n    ) as Promise<EventRoutesGetByIdResponse>;\n  }\n\n  /**\n   * Adds or replaces an event route.\n   * Status codes:\n   * * 204 No Content\n   * * 400 Bad Request\n   *   * EventRouteEndpointInvalid - The endpoint provided does not exist or is not active.\n   *   * EventRouteFilterInvalid - The event route filter is invalid.\n   *   * EventRouteIdInvalid - The event route id is invalid.\n   *   * LimitExceeded - The maximum number of event routes allowed has been reached.\n   * @param id The id for an event route. The id is unique within event routes and case sensitive.\n   * @param options The options parameters.\n   */\n  add(\n    id: string,\n    options?: EventRoutesAddOptionalParams\n  ): Promise<coreHttp.RestResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, options: operationOptions },\n      addOperationSpec\n    ) as Promise<coreHttp.RestResponse>;\n  }\n\n  /**\n   * Deletes an event route.\n   * Status codes:\n   * * 204 No Content\n   * * 404 Not Found\n   *   * EventRouteNotFound - The event route was not found.\n   * @param id The id for an event route. The id is unique within event routes and case sensitive.\n   * @param options The options parameters.\n   */\n  delete(\n    id: string,\n    options?: EventRoutesDeleteOptionalParams\n  ): Promise<coreHttp.RestResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, options: operationOptions },\n      deleteOperationSpec\n    ) as Promise<coreHttp.RestResponse>;\n  }\n\n  /**\n   * ListNext\n   * @param nextLink The nextLink from the previous successful call to the List method.\n   * @param options The options parameters.\n   */\n  listNext(\n    nextLink: string,\n    options?: EventRoutesListNextOptionalParams\n  ): Promise<EventRoutesListNextResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { nextLink, options: operationOptions },\n      listNextOperationSpec\n    ) as Promise<EventRoutesListNextResponse>;\n  }\n}\n// Operation Specifications\n\nconst serializer = new coreHttp.Serializer(Mappers, /* isXml */ false);\n\nconst listOperationSpec: coreHttp.OperationSpec = {\n  path: \"/eventroutes\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.EventRouteCollection\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host],\n  headerParameters: [\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.maxItemsPerPage\n  ],\n  serializer\n};\nconst getByIdOperationSpec: coreHttp.OperationSpec = {\n  path: \"/eventroutes/{id}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.EventRoute\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\nconst addOperationSpec: coreHttp.OperationSpec = {\n  path: \"/eventroutes/{id}\",\n  httpMethod: \"PUT\",\n  responses: {\n    204: {},\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.eventRoute,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.traceparent,\n    Parameters.tracestate\n  ],\n  mediaType: \"json\",\n  serializer\n};\nconst deleteOperationSpec: coreHttp.OperationSpec = {\n  path: \"/eventroutes/{id}\",\n  httpMethod: \"DELETE\",\n  responses: {\n    204: {},\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\nconst listNextOperationSpec: coreHttp.OperationSpec = {\n  path: \"{nextLink}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.EventRouteCollection\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.nextLink],\n  headerParameters: [\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.maxItemsPerPage\n  ],\n  serializer\n};\n", "/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreHttp from \"@azure/core-http\";\nimport { AzureDigitalTwinsAPIOptionalParams } from \"./models\";\n\nconst packageName = \"@azure/digital-twins-core\";\nconst packageVersion = \"1.1.0\";\n\nexport class AzureDigitalTwinsAPIContext extends coreHttp.ServiceClient {\n  $host: string;\n  apiVersion: string;\n\n  /**\n   * Initializes a new instance of the AzureDigitalTwinsAPIContext class.\n   * @param options The parameter options\n   */\n  constructor(options?: AzureDigitalTwinsAPIOptionalParams) {\n    // Initializing default values for options\n    if (!options) {\n      options = {};\n    }\n\n    if (!options.userAgent) {\n      const defaultUserAgent = coreHttp.getDefaultUserAgentValue();\n      options.userAgent = `${packageName}/${packageVersion} ${defaultUserAgent}`;\n    }\n\n    super(undefined, options);\n\n    this.requestContentType = \"application/json; charset=utf-8\";\n\n    this.baseUri =\n      options.endpoint || \"https://digitaltwins-name.digitaltwins.azure.net\";\n\n    // Assigning values to Constant parameters\n    this.$host =\n      options.$host || \"https://digitaltwins-name.digitaltwins.azure.net\";\n    this.apiVersion = options.apiVersion || \"2022-05-31\";\n  }\n}\n", "/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as operations from \"./operations\";\nimport * as Models from \"./models\";\nimport * as Mappers from \"./models/mappers\";\nimport { AzureDigitalTwinsAPIContext } from \"./azureDigitalTwinsAPIContext\";\nimport { AzureDigitalTwinsAPIOptionalParams } from \"./models\";\n\nclass AzureDigitalTwinsAPI extends AzureDigitalTwinsAPIContext {\n  /**\n   * Initializes a new instance of the AzureDigitalTwinsAPI class.\n   * @param options The parameter options\n   */\n  constructor(options?: AzureDigitalTwinsAPIOptionalParams) {\n    super(options);\n    this.digitalTwinModels = new operations.DigitalTwinModels(this);\n    this.query = new operations.Query(this);\n    this.digitalTwins = new operations.DigitalTwins(this);\n    this.eventRoutes = new operations.EventRoutes(this);\n  }\n\n  digitalTwinModels: operations.DigitalTwinModels;\n  query: operations.Query;\n  digitalTwins: operations.DigitalTwins;\n  eventRoutes: operations.EventRoutes;\n}\n\n// Operation Specifications\n\nexport {\n  AzureDigitalTwinsAPI,\n  AzureDigitalTwinsAPIContext,\n  Models as AzureDigitalTwinsAPIModels,\n  Mappers as AzureDigitalTwinsAPIMappers\n};\nexport * from \"./operations\";\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nexport const SDK_VERSION: string = \"1.1.0\";\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { createTracingClient } from \"@azure/core-tracing\";\nimport { SDK_VERSION } from \"./constants\";\n\n/**\n * Creates a tracing client to manage tracing spans.\n * @internal\n */\nexport const tracingClient = createTracingClient({\n  namespace: \"Microsoft.DigitalTwins\",\n  packageName: \"@azure/digital-twins-core\",\n  packageVersion: SDK_VERSION,\n});\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { createClientLogger } from \"@azure/logger\";\n\n/**\n * The \\@azure/logger configuration for this package.\n */\nexport const logger = createClientLogger(\"azure-digitaltwins-core\");\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/// <reference lib=\"esnext.asynciterable\" />\n\nimport {\n  TokenCredential,\n  RestResponse,\n  OperationOptions,\n  InternalPipelineOptions,\n  bearerTokenAuthenticationPolicy,\n  createPipelineFromOptions,\n  generateUuid,\n  PipelineOptions,\n} from \"@azure/core-http\";\nimport { PageSettings, PagedAsyncIterableIterator } from \"@azure/core-paging\";\nimport { AzureDigitalTwinsAPI as GeneratedClient } from \"./generated/azureDigitalTwinsAPI\";\nimport {\n  DigitalTwinsGetByIdResponse,\n  DigitalTwinsAddOptionalParams,\n  DigitalTwinsAddResponse,\n  DigitalTwinsUpdateOptionalParams,\n  DigitalTwinsUpdateResponse,\n  DigitalTwinsDeleteOptionalParams,\n  DigitalTwinsGetComponentResponse,\n  DigitalTwinsUpdateComponentResponse,\n  DigitalTwinsUpdateComponentOptionalParams,\n  DigitalTwinsAddRelationshipResponse,\n  DigitalTwinsAddRelationshipOptionalParams,\n  DigitalTwinsUpdateRelationshipOptionalParams,\n  DigitalTwinsUpdateRelationshipResponse,\n  DigitalTwinsDeleteRelationshipOptionalParams,\n  DigitalTwinsSendTelemetryOptionalParams,\n  DigitalTwinsSendComponentTelemetryOptionalParams,\n  DigitalTwinsListRelationshipsResponse,\n  IncomingRelationship,\n  DigitalTwinsListIncomingRelationshipsResponse,\n  DigitalTwinsGetRelationshipByIdResponse,\n  DigitalTwinsModelData,\n  DigitalTwinModelsGetByIdResponse,\n  DigitalTwinModelsGetByIdOptionalParams,\n  DigitalTwinModelsAddResponse,\n  DigitalTwinModelsAddOptionalParams,\n  DigitalTwinModelsListResponse,\n  DigitalTwinModelsListOptionalParams,\n  EventRoutesGetByIdResponse,\n  EventRoute,\n  EventRoutesAddOptionalParams,\n  EventRoutesListNextResponse,\n  EventRoutesListOptionalParams,\n  QueryQueryTwinsOptionalParams,\n  QueryQueryTwinsResponse,\n  QuerySpecification,\n} from \"./generated/models\";\nimport { tracingClient } from \"./tracing\";\nimport { logger } from \"./logger\";\n\nexport const SDK_VERSION: string = \"1.1.0\";\n\nexport interface DigitalTwinsClientOptions extends PipelineOptions {\n  /**\n   * Api Version\n   */\n  apiVersion?: string;\n}\n\nconst DEFAULT_DIGITALTWINS_SCOPE = \"https://digitaltwins.azure.net/.default\";\n\n/**\n * Client for Azure IoT DigitalTwins API.\n */\nexport class DigitalTwinsClient {\n  /**\n   * A reference to the auto-generated AzureDigitalTwinsAPI\n   */\n  private readonly client: GeneratedClient;\n\n  /**\n   * Creates an instance of AzureDigitalTwinsAPI.\n   *\n   * Example usage:\n   * ```ts\n   * const { DigitalTwinsClient, ServiceClientCredentials } = require(\"@azure/digital-twins-core\");\n   *\n   * const client = new DigitalTwinsClient(\n   *   \"<endpoint>\",\n   *   new DefaultAzureCredential();\n   * );\n   * ```\n   * @param endpointUrl - The endpoint URL of the service.\n   * @param credential - Used to authenticate requests to the service.\n   * @param options - Used to configure the service client.\n   */\n  constructor(\n    endpointUrl: string,\n    credential: TokenCredential,\n    options: DigitalTwinsClientOptions = {}\n  ) {\n    const authPolicy = bearerTokenAuthenticationPolicy(credential, DEFAULT_DIGITALTWINS_SCOPE);\n    const libInfo = `azsdk-js-digital-twins-core/${SDK_VERSION}`;\n\n    const { apiVersion, ...pipelineOptions } = options;\n    if (!pipelineOptions.userAgentOptions) {\n      pipelineOptions.userAgentOptions = {};\n    }\n    if (pipelineOptions.userAgentOptions.userAgentPrefix) {\n      pipelineOptions.userAgentOptions.userAgentPrefix = `${pipelineOptions.userAgentOptions.userAgentPrefix} ${libInfo}`;\n    } else {\n      pipelineOptions.userAgentOptions.userAgentPrefix = libInfo;\n    }\n\n    const internalPipelineOptions: InternalPipelineOptions = {\n      ...pipelineOptions,\n      ...{\n        loggingOptions: {\n          logger: logger.info,\n          allowedHeaderNames: [\"x-ms-request-id\"],\n        },\n      },\n    };\n\n    const pipeline = createPipelineFromOptions(internalPipelineOptions, authPolicy);\n\n    this.client = new GeneratedClient({\n      endpoint: endpointUrl,\n      apiVersion,\n      ...pipeline,\n    });\n  }\n\n  /**\n   * Get a digital twin\n   *\n   * @param digitalTwinId - The Id of the digital twin.\n   * @param options - The operation options\n   * @returns The application/json digital twin and the http response.\n   */\n  public getDigitalTwin(\n    digitalTwinId: string,\n    options: OperationOptions = {}\n  ): Promise<DigitalTwinsGetByIdResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.getDigitalTwin\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.getById(digitalTwinId, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Create or update a digital twin\n   *\n   * @param digitalTwinId - The Id of the digital twin to create or update.\n   * @param digitalTwinJson - The application/json digital twin to create.\n   * @param options - Extended operation options including\n   *  ifNoneMatch: Only perform the operation if the entity does not already exist.\n   * @returns The created application/json digital twin and the http response.\n   */\n  public upsertDigitalTwin(\n    digitalTwinId: string,\n    digitalTwinJson: string,\n    options: DigitalTwinsAddOptionalParams = {}\n  ): Promise<DigitalTwinsAddResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.upsertDigitalTwin\",\n      options,\n      async (updatedOptions) => {\n        const payload = JSON.parse(digitalTwinJson);\n        return this.client.digitalTwins.add(digitalTwinId, payload, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Update a digital twin using a json patch.\n   *\n   * @param digitalTwinId - The Id of the digital twin.\n   * @param jsonPatch - An update specification described by JSON Patch. Updates to property values\n   * and $model elements may happen in the same request. Operations are limited to add, replace and\n   * remove.\n   * @param options - Extended operation options including\n   *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is provided.\n   * @returns The http response.\n   */\n  public updateDigitalTwin(\n    digitalTwinId: string,\n    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types -- changing the type any would be a breaking change\n    jsonPatch: any,\n    options: DigitalTwinsUpdateOptionalParams = {}\n  ): Promise<DigitalTwinsUpdateResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.updateDigitalTwin\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.update(digitalTwinId, jsonPatch, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Delete a digital twin\n   *\n   * @param digitalTwinId - The Id of the digital twin to delete.\n   * @param options - Extended operation options including\n   *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is provided.\n   * @returns The http response.\n   */\n  public deleteDigitalTwin(\n    digitalTwinId: string,\n    options: DigitalTwinsDeleteOptionalParams = {}\n  ): Promise<RestResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.deleteDigitalTwin\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.delete(digitalTwinId, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Get a component on a digital twin.\n   *\n   * @param digitalTwinId - The Id of the digital twin.\n   * @param componentName - The component being retrieved.\n   * @param options - The operation options\n   * @returns Json string representation of the component corresponding to the provided componentName and the HTTP response.\n   */\n  public getComponent(\n    digitalTwinId: string,\n    componentName: string,\n    options: OperationOptions = {}\n  ): Promise<DigitalTwinsGetComponentResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.getComponent\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.getComponent(digitalTwinId, componentName, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Update properties of a component on a digital twin using a JSON patch.\n   *\n   * @param digitalTwinId - The Id of the digital twin.\n   * @param componentName - The component being updated.\n   * @param jsonPatch - The application/json-patch+json operations to be performed on the specified digital twin's component.\n   * @param enableUpdate - If true then update of an existing digital twin is enabled.\n   * @param options - Extended operation options including\n   *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is provided.\n   * @returns The http response.\n   */\n  public updateComponent(\n    digitalTwinId: string,\n    componentName: string,\n    jsonPatch: any[],\n    options: DigitalTwinsUpdateComponentOptionalParams = {}\n  ): Promise<DigitalTwinsUpdateComponentResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.updateComponent\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.updateComponent(\n          digitalTwinId,\n          componentName,\n          jsonPatch,\n          updatedOptions\n        );\n      }\n    );\n  }\n\n  /**\n   * Get a relationship on a digital twin.\n   *\n   * @param digitalTwinId - The Id of the source digital twin.\n   * @param relationshipId - The Id of the relationship to retrieve.\n   * @param options - The operation options\n   * @returns The pageable list of application/json relationships belonging to the specified digital twin and the http response.\n   */\n  public getRelationship(\n    digitalTwinId: string,\n    relationshipId: string,\n    options: OperationOptions = {}\n  ): Promise<DigitalTwinsGetRelationshipByIdResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.getRelationship\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.getRelationshipById(\n          digitalTwinId,\n          relationshipId,\n          updatedOptions\n        );\n      }\n    );\n  }\n\n  /**\n   * Create or update a relationship on a digital twin.\n   *\n   * @param digitalTwinId - The Id of the source digital twin.\n   * @param relationshipId - The Id of the relationship to create.\n   * @param relationship - The application/json relationship to be created.\n   * @param options - Extended operation options including\n   *  ifNoneMatch: Only perform the operation if the entity does not already exist.\n   */\n  public upsertRelationship(\n    digitalTwinId: string,\n    relationshipId: string,\n    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types -- changing the type any would be a breaking change\n    relationship: any,\n    options: DigitalTwinsAddRelationshipOptionalParams = {}\n  ): Promise<DigitalTwinsAddRelationshipResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.upsertRelationship\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.addRelationship(\n          digitalTwinId,\n          relationshipId,\n          relationship,\n          updatedOptions\n        );\n      }\n    );\n  }\n\n  /**\n   * Updates the properties of a relationship on a digital twin using a JSON patch.\n   *\n   * @param digitalTwinId - The Id of the digital twin to delete.\n   * @param relationshipId - The Id of the relationship to be updated.\n   * @param jsonPatch - The application/json-patch+json operations to be performed on the specified digital twin's relationship.\n   * @param options - Extended operation options\n   *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is provided.\n   */\n  public updateRelationship(\n    digitalTwinId: string,\n    relationshipId: string,\n    jsonPatch: any[],\n    options: DigitalTwinsUpdateRelationshipOptionalParams = {}\n  ): Promise<DigitalTwinsUpdateRelationshipResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.updateRelationship\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.updateRelationship(\n          digitalTwinId,\n          relationshipId,\n          jsonPatch,\n          updatedOptions\n        );\n      }\n    );\n  }\n\n  /**\n   * Delete a relationship on a digital twin.\n   *\n   * @param digitalTwinId - The Id of the source digital twin.\n   * @param relationshipId - The Id of the relationship to delete.\n   * @param options - The operation options\n   *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is\n   * @returns The http response.\n   */\n  public deleteRelationship(\n    digitalTwinId: string,\n    relationshipId: string,\n    options: DigitalTwinsDeleteRelationshipOptionalParams = {}\n  ): Promise<RestResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.deleteRelationship\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.deleteRelationship(\n          digitalTwinId,\n          relationshipId,\n          updatedOptions\n        );\n      }\n    );\n  }\n\n  /**\n   * Deals with the pagination of {@link listRelationships}.\n   *\n   * @param digitalTwinId - The Id of the digital twin.\n   * @param options - Common options for the iterative endpoints.\n   * @param continuationState - An object that indicates the position of the paginated request.\n   *\n   */\n  private async *listRelationshipsPage(\n    digitalTwinId: string,\n    options: OperationOptions,\n    continuationState: PageSettings\n  ): AsyncIterableIterator<DigitalTwinsListRelationshipsResponse> {\n    if (continuationState.continuationToken == null) {\n      const optionsComplete: OperationOptions = {\n        ...options,\n      };\n      const listRelationshipResponse = await this.client.digitalTwins.listRelationships(\n        digitalTwinId,\n        optionsComplete\n      );\n      continuationState.continuationToken = listRelationshipResponse.nextLink;\n      yield listRelationshipResponse;\n    }\n    while (continuationState.continuationToken) {\n      const listRelationshipResponse = await this.client.digitalTwins.listRelationshipsNext(\n        \"\",\n        continuationState.continuationToken,\n        options\n      );\n\n      continuationState.continuationToken = listRelationshipResponse.nextLink;\n      yield listRelationshipResponse;\n    }\n  }\n\n  /**\n   * Deals with the iteration of all the available results of {@link listRelationships}.\n   * @param options - Common options for the iterative endpoints.\n   */\n  private async *listRelationshipsAll(\n    digitalTwinId: string,\n    options: OperationOptions\n  ): AsyncIterableIterator<any> {\n    for await (const page of this.listRelationshipsPage(digitalTwinId, options, {})) {\n      const value = page.value || [];\n      for (const item of value) {\n        yield item;\n      }\n    }\n  }\n\n  /**\n   * Retrieve relationships for a digital twin.\n   *\n   * @param digitalTwinId - The Id of the digital twin.\n   */\n  public listRelationships(\n    digitalTwinId: string,\n    options: OperationOptions & PageSettings = {}\n  ): PagedAsyncIterableIterator<any, DigitalTwinsListRelationshipsResponse> {\n    const iter = this.listRelationshipsAll(digitalTwinId, options);\n\n    return {\n      next() {\n        return iter.next();\n      },\n      [Symbol.asyncIterator]() {\n        return this;\n      },\n      byPage: (settings: PageSettings = {}) =>\n        this.listRelationshipsPage(digitalTwinId, options, settings),\n    };\n  }\n\n  /**\n   * Deals with the pagination of {@link listIncomingRelationships}.\n   *\n   * @param digitalTwinId - The Id of the digital twin.\n   * @param options - Common options for the iterative endpoints.\n   * @param continuationState - An object that indicates the position of the paginated request.\n   *\n   */\n  private async *listIncomingRelationshipsPage(\n    digitalTwinId: string,\n    options: OperationOptions,\n    continuationState: PageSettings\n  ): AsyncIterableIterator<DigitalTwinsListIncomingRelationshipsResponse> {\n    if (continuationState.continuationToken == null) {\n      const optionsComplete: OperationOptions = {\n        ...options,\n      };\n      const listIncomingRelationshipsResponse =\n        await this.client.digitalTwins.listIncomingRelationships(digitalTwinId, optionsComplete);\n      continuationState.continuationToken = listIncomingRelationshipsResponse.nextLink;\n      yield listIncomingRelationshipsResponse;\n    }\n    while (continuationState.continuationToken) {\n      const listIncomingRelationshipsResponse =\n        await this.client.digitalTwins.listIncomingRelationshipsNext(\n          \"\",\n          continuationState.continuationToken,\n          options\n        );\n\n      continuationState.continuationToken = listIncomingRelationshipsResponse.nextLink;\n      yield listIncomingRelationshipsResponse;\n    }\n  }\n\n  /**\n   * Deals with the iteration of all the available results of {@link listIncomingRelationships}.\n   * @param digitalTwinId - The Id of the digital twin.\n   * @param options - Common options for the iterative endpoints.\n   */\n  private async *listIncomingRelationshipsAll(\n    digitalTwinId: string,\n    options: OperationOptions\n  ): AsyncIterableIterator<IncomingRelationship> {\n    for await (const page of this.listIncomingRelationshipsPage(digitalTwinId, options, {})) {\n      const value = page.value || [];\n      yield* value;\n    }\n  }\n\n  /**\n   * Retrieve all incoming relationships for a digital twin.\n   *\n   * @param digitalTwinId - The Id of the digital twin.\n   */\n  public listIncomingRelationships(\n    digitalTwinId: string,\n    options: OperationOptions & PageSettings = {}\n  ): PagedAsyncIterableIterator<\n    IncomingRelationship,\n    DigitalTwinsListIncomingRelationshipsResponse\n  > {\n    const iter = this.listIncomingRelationshipsAll(digitalTwinId, options);\n\n    return {\n      next() {\n        return iter.next();\n      },\n      [Symbol.asyncIterator]() {\n        return this;\n      },\n      byPage: (settings: PageSettings = {}) =>\n        this.listIncomingRelationshipsPage(digitalTwinId, options, settings),\n    };\n  }\n\n  /**\n   * Publish telemetry from a digital twin, which is then consumed by one or many destination endpoints (subscribers) defined under.\n   *\n   * @param digitalTwinId - The Id of the digital twin to delete.\n   * @param payload - The application/json telemetry payload to be sent.\n   * @param messageId - The message Id.\n   * @param options - The operation options\n   * @returns The http response.\n   */\n  public publishTelemetry(\n    digitalTwinId: string,\n    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types -- changing the type any would be a breaking change\n    payload: any,\n    messageId: string,\n    options: OperationOptions = {}\n  ): Promise<RestResponse> {\n    const digitalTwinsSendTelemetryOptionalParams: DigitalTwinsSendTelemetryOptionalParams =\n      options;\n    digitalTwinsSendTelemetryOptionalParams.telemetrySourceTime = new Date().toISOString();\n    if (!messageId) {\n      messageId = generateUuid();\n    }\n\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.publishTelemetry\",\n      digitalTwinsSendTelemetryOptionalParams,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.sendTelemetry(\n          digitalTwinId,\n          messageId,\n          payload,\n          updatedOptions\n        );\n      }\n    );\n  }\n\n  /**\n   * Publish telemetry from a digital twin's component, which is then consumed by one or many destination endpoints (subscribers) defined under.\n   *\n   * @param digitalTwinId - The Id of the digital twin to delete.\n   * @param componentName - The name of the DTDL component.\n   * @param payload - The application/json telemetry payload to be sent.\n   * @param messageId - The message Id.\n   * @param options - The operation options\n   * @returns The http response.\n   */\n  public publishComponentTelemetry(\n    digitalTwinId: string,\n    componentName: string,\n    payload: string,\n    messageId: string,\n    options: OperationOptions = {}\n  ): Promise<RestResponse> {\n    const digitalTwinsSendComponentTelemetryOptionalParams: DigitalTwinsSendComponentTelemetryOptionalParams =\n      options;\n    digitalTwinsSendComponentTelemetryOptionalParams.telemetrySourceTime = new Date().toISOString();\n    if (!messageId) {\n      messageId = generateUuid();\n    }\n\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.publishComponentTelemetry\",\n      digitalTwinsSendComponentTelemetryOptionalParams,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.sendComponentTelemetry(\n          digitalTwinId,\n          componentName,\n          payload,\n          messageId,\n          updatedOptions\n        );\n      }\n    );\n  }\n\n  /**\n   * Get a model, including the model metadata and the model definition.\n   *\n   * @param modelId - The Id of the model.\n   * @param options - Extended operation options including\n   *  includeModelDefinition: When true the model definition will be returned as part of the result. Default value: false.\n   * @returns The application/json model and the http response.\n   */\n  public getModel(\n    modelId: string,\n    includeModelDefinition: boolean = false,\n    options: OperationOptions = {}\n  ): Promise<DigitalTwinModelsGetByIdResponse> {\n    const digitalTwinModelsGetByIdOptionalParams: DigitalTwinModelsGetByIdOptionalParams = options;\n    digitalTwinModelsGetByIdOptionalParams.includeModelDefinition = includeModelDefinition;\n\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.getModel\",\n      digitalTwinModelsGetByIdOptionalParams,\n      async (updatedOptions) => {\n        return this.client.digitalTwinModels.getById(modelId, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Deals with the pagination of {@link list}.\n   *\n   * @param options - Common options for the iterative endpoints.\n   * @param continuationState - An object that indicates the position of the paginated request.\n   *\n   */\n  private async *getModelsPage(\n    options: DigitalTwinModelsListOptionalParams,\n    continuationState: PageSettings\n  ): AsyncIterableIterator<DigitalTwinModelsListResponse> {\n    if (continuationState.continuationToken == null) {\n      const optionsComplete: DigitalTwinModelsListOptionalParams = options;\n      optionsComplete.maxItemsPerPage = continuationState.maxPageSize;\n\n      const listResponse = await this.client.digitalTwinModels.list(optionsComplete);\n      continuationState.continuationToken = listResponse.nextLink;\n      yield listResponse;\n    }\n    while (continuationState.continuationToken) {\n      const listResponse = await this.client.digitalTwinModels.listNext(\n        continuationState.continuationToken,\n        options\n      );\n\n      continuationState.continuationToken = listResponse.nextLink;\n      yield listResponse;\n    }\n  }\n\n  /**\n   * Deals with the iteration of all the available results of {@link list}.\n   * @param options - Common options for the iterative endpoints.\n   */\n  private async *getModelsAll(\n    options: DigitalTwinModelsListOptionalParams\n  ): AsyncIterableIterator<DigitalTwinsModelData> {\n    const f = {};\n\n    for await (const page of this.getModelsPage(options, f)) {\n      const value = page.value || [];\n      for (const item of value) {\n        yield item;\n      }\n    }\n  }\n\n  /**\n   * Get the list of models\n   *\n   * @param dependeciesFor - The model Ids to have dependencies retrieved. If omitted, all models are retrieved.\n   * @param includeModelDefinition - Whether to include the model definition in the result. If false, only the model metadata will be returned.\n   * @param resultsPerPage - The maximum number of items to retrieve per request. The server may choose to return less than the requested max.\n   * @returns A pageable set of application/json models and the http response.\n   */\n  public listModels(\n    dependeciesFor?: string[],\n    includeModelDefinition: boolean = false,\n    resultsPerPage?: number,\n    options: OperationOptions & PageSettings = {}\n  ): PagedAsyncIterableIterator<DigitalTwinsModelData, DigitalTwinModelsListResponse> {\n    let digitalTwinModelsListOptionalParams: DigitalTwinModelsListOptionalParams = options;\n    digitalTwinModelsListOptionalParams = {\n      maxItemsPerPage: resultsPerPage,\n      dependenciesFor: dependeciesFor,\n      includeModelDefinition: includeModelDefinition,\n    };\n\n    const iter = this.getModelsAll(digitalTwinModelsListOptionalParams);\n\n    return {\n      next() {\n        return iter.next();\n      },\n      [Symbol.asyncIterator]() {\n        return this;\n      },\n      byPage: (settings: PageSettings = {}) =>\n        this.getModelsPage(digitalTwinModelsListOptionalParams, settings),\n    };\n  }\n\n  /**\n   * Create one or many\n   *\n   * @param models - The set of models to create. Each string corresponds to exactly one model.\n   * @param options - The operation options\n   * @returns The created application/json models and the http response.\n   */\n  public createModels(\n    dtdlModels: any[],\n    options: OperationOptions = {}\n  ): Promise<DigitalTwinModelsAddResponse> {\n    const digitalTwinModelsAddOptionalParams: DigitalTwinModelsAddOptionalParams = options;\n    digitalTwinModelsAddOptionalParams.models = dtdlModels;\n\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.createModels\",\n      digitalTwinModelsAddOptionalParams,\n      async (updatedOptions) => {\n        return this.client.digitalTwinModels.add(updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Decommission a model using a json patch.\n   * When a model is decommissioned, new digital twins will no longer be able to be\n   * defined by this model. However, existing digital twins may continue to use this model.\n   * Once a model is decommissioned, it may not be recommissioned.\n   *\n   * @param modelId - The Id of the model to decommission.\n   * property can be replaced.\n   * @param options - The operation options\n   * @returns The http response.\n   *\n   */\n  public decomissionModel(modelId: string, options: OperationOptions = {}): Promise<RestResponse> {\n    const jsonPatch = [{ op: \"replace\", path: \"/decommissioned\", value: true }];\n\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.decomissionModel\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwinModels.update(modelId, jsonPatch, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Delete a model.\n   *\n   * @param modelId - The Id of the model to delete.\n   * @param options - The operation options\n   * @returns The http response.\n   */\n  public deleteModel(modelId: string, options: OperationOptions = {}): Promise<RestResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.deleteModel\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwinModels.delete(modelId, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Get an event route.\n   *\n   * @param modelId - The Id of the event route.\n   * @param options - The operation options\n   * @returns The application/json event route and the http response.\n   */\n  public getEventRoute(\n    eventRouteId: string,\n    options: OperationOptions = {}\n  ): Promise<EventRoutesGetByIdResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.getEventRoute\",\n      options,\n      async (updatedOptions) => {\n        return this.client.eventRoutes.getById(eventRouteId, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Deals with the pagination of {@link list}.\n   *\n   * @param options - Common options for the iterative endpoints.\n   * @param continuationState - An object that indicates the position of the paginated request.\n   *\n   */\n  private async *getEventRoutesPage(\n    options: EventRoutesListOptionalParams,\n    continuationState: PageSettings\n  ): AsyncIterableIterator<EventRoutesListNextResponse> {\n    if (continuationState.continuationToken == null) {\n      const optionsComplete: EventRoutesListOptionalParams = options;\n      optionsComplete.maxItemsPerPage = continuationState.maxPageSize;\n\n      const listResponse = await this.client.eventRoutes.list(optionsComplete);\n      continuationState.continuationToken = listResponse.nextLink;\n      yield listResponse;\n    }\n    while (continuationState.continuationToken) {\n      const listResponse = await this.client.eventRoutes.listNext(\n        continuationState.continuationToken,\n        options\n      );\n\n      continuationState.continuationToken = listResponse.nextLink;\n      yield listResponse;\n    }\n  }\n\n  /**\n   * Deals with the iteration of all the available results of {@link list}.\n   * @param options - Common options for the iterative endpoints.\n   */\n  private async *getEventRoutesAll(\n    options: EventRoutesListOptionalParams\n  ): AsyncIterableIterator<EventRoute> {\n    const f = {};\n    for await (const page of this.getEventRoutesPage(options, f)) {\n      const value = page.value || [];\n      for (const item of value) {\n        yield item;\n      }\n    }\n  }\n\n  /**\n   * List the event routes in a digital twins instance.\n   *\n   * @param resultsPerPage - The maximum number of items to retrieve per request. The server may choose to return less than\n   * the requested max.\n   * @returns The application/json event route and the http response.\n   */\n  public listEventRoutes(\n    resultsPerPage?: number,\n    options: OperationOptions & PageSettings = {}\n  ): PagedAsyncIterableIterator<EventRoute, EventRoutesListNextResponse> {\n    let eventRoutesListOptionalParams: EventRoutesListOptionalParams = options;\n    eventRoutesListOptionalParams = {\n      maxItemsPerPage: resultsPerPage,\n    };\n\n    const iter = this.getEventRoutesAll(eventRoutesListOptionalParams);\n\n    return {\n      next() {\n        return iter.next();\n      },\n      [Symbol.asyncIterator]() {\n        return this;\n      },\n      byPage: (settings: PageSettings = {}) =>\n        this.getEventRoutesPage(eventRoutesListOptionalParams, settings),\n    };\n  }\n\n  /**\n   * Create or update an event route.\n   *\n   * @param eventRouteId - The Id of the event route to create or update.\n   * @param endpointId - The id of the endpoint this event route is bound to.\n   * @param filter - An expression which describes the events which are routed to the endpoint.\n   * @param options - The operation options\n   * @returns The http response.\n   */\n  public upsertEventRoute(\n    eventRouteId: string,\n    endpointId: string,\n    filter: string,\n    options: OperationOptions = {}\n  ): Promise<RestResponse> {\n    const eventRoutesAddOptionalParams: EventRoutesAddOptionalParams = options;\n    const eventRoute: EventRoute = {\n      endpointName: endpointId,\n      filter: filter,\n    };\n    eventRoutesAddOptionalParams.eventRoute = eventRoute;\n\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.upsertEventRoute\",\n      eventRoutesAddOptionalParams,\n      async (updatedOptions) => {\n        return this.client.eventRoutes.add(eventRouteId, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Delete an event route.\n   *\n   * @param eventRouteId - The Id of the eventRoute to delete.\n   * @param options - The operation options\n   * @returns The http response.\n   */\n  public deleteEventRoute(\n    eventRouteId: string,\n    options: OperationOptions = {}\n  ): Promise<RestResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.deleteEventRoute\",\n      options,\n      async (updatedOptions) => {\n        return this.client.eventRoutes.delete(eventRouteId, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Deals with the pagination of {@link query}.\n   *\n   * @param query - The query string, in SQL-like syntax.\n   * @param options - Common options for the iterative endpoints.\n   * @param continuationState - An object that indicates the position of the paginated request.\n   *\n   */\n  private async *queryTwinsPage(\n    query: string,\n    options: QueryQueryTwinsOptionalParams,\n    continuationState: PageSettings\n  ): AsyncIterableIterator<QueryQueryTwinsResponse> {\n    if (continuationState.continuationToken == null) {\n      const querySpecification: QuerySpecification = {\n        query: query,\n        continuationToken: continuationState.continuationToken,\n      };\n      const queryResult = await this.client.query.queryTwins(querySpecification, options);\n      continuationState.continuationToken = queryResult.continuationToken;\n      yield queryResult;\n    }\n    while (continuationState.continuationToken) {\n      const querySpecification: QuerySpecification = {\n        query: query,\n        continuationToken: continuationState.continuationToken,\n      };\n      const queryResult = await this.client.query.queryTwins(querySpecification, options);\n\n      continuationState.continuationToken = queryResult.continuationToken;\n      yield queryResult;\n    }\n  }\n\n  /**\n   * Deals with the iteration of all the available results of {@link query}.\n   * @param query - The query string, in SQL-like syntax.\n   * @param options - Common options for the iterative endpoints.\n   */\n  private async *queryTwinsAll(\n    query: string,\n    options: QueryQueryTwinsOptionalParams\n  ): AsyncIterableIterator<any> {\n    const f = {};\n\n    for await (const page of this.queryTwinsPage(query, options, f)) {\n      if (page.value) {\n        for (const item of page.value) {\n          yield item;\n        }\n      }\n    }\n  }\n\n  /**\n   * Query for digital twins.\n   *\n   * @param query - The query string, in SQL-like syntax.\n   * @param resultsPerPage - The maximum number of items to retrieve per request. The server may choose to return less than the requested max.\n   * @returns The pageable list of query results.\n   */\n  public queryTwins(\n    query: string,\n    resultsPerPage?: number,\n    options: OperationOptions & PageSettings = {}\n  ): PagedAsyncIterableIterator<any, QueryQueryTwinsResponse> {\n    let queryQueryTwinsOptionalParams: QueryQueryTwinsOptionalParams = options;\n    queryQueryTwinsOptionalParams = {\n      maxItemsPerPage: resultsPerPage,\n    };\n\n    const iter = this.queryTwinsAll(query, queryQueryTwinsOptionalParams);\n\n    return {\n      next() {\n        return iter.next();\n      },\n      [Symbol.asyncIterator]() {\n        return this;\n      },\n      byPage: (settings: PageSettings = {}) =>\n        this.queryTwinsPage(query, queryQueryTwinsOptionalParams, settings),\n    };\n  }\n}\n"], "names": ["QueryCollectionFormat", "QuerySpecificationMapper", "EventRouteMapper", "coreHttp", "addOperationSpec", "listOperationSpec", "getByIdOperationSpec", "updateOperationSpec", "deleteOperationSpec", "listNextOperationSpec", "serializer", "Mappers.ErrorResponse", "Parameters.models", "Parameters.apiVersion", "Parameters.$host", "Parameters.contentType", "Parameters.traceparent", "Parameters.tracestate", "Mappers.PagedDigitalTwinsModelDataCollection", "Parameters.dependenciesFor", "Parameters.includeModelDefinition", "Parameters.maxItemsPerPage", "Mappers.DigitalTwinsModelData", "Parameters.id", "Parameters.updateModel", "Parameters.contentType1", "Parameters.nextLink", "Mappers.QueryResult", "Mappers.QueryQueryTwinsHeaders", "Parameters.querySpecification", "Mappers.DigitalTwinsGetByIdHeaders", "Mappers.DigitalTwinsAddHeaders", "Parameters.twin", "Parameters.ifNoneMatch", "Parameters.ifMatch", "Mappers.DigitalTwinsUpdateHeaders", "Parameters.patchDocument", "Mappers.DigitalTwinsGetRelationshipByIdHeaders", "Parameters.relationshipId", "Mappers.DigitalTwinsAddRelationshipHeaders", "Parameters.relationship", "Mappers.DigitalTwinsUpdateRelationshipHeaders", "Mappers.RelationshipCollection", "Parameters.relationshipName", "Mappers.IncomingRelationshipCollection", "Parameters.telemetry", "Parameters.messageId", "Parameters.telemetrySourceTime", "Parameters.componentPath", "Mappers.DigitalTwinsGetComponentHeaders", "Mappers.DigitalTwinsUpdateComponentHeaders", "Mappers.EventRouteCollection", "Mappers.EventRoute", "Parameters.eventRoute", "operations.DigitalTwinModels", "operations.Query", "operations.DigitalTwins", "operations.EventRoutes", "SDK_VERSION", "createTracingClient", "createClientLogger", "bearerTokenAuthenticationPolicy", "__rest", "createPipelineFromOptions", "GeneratedClient", "__await", "__asyncValues", "__asyncDelegator", "generateUuid"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;AAMG;AAII,MAAM,qBAAqB,GAA6B;AAC7D,IAAA,IAAI,EAAE;AACJ,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,SAAS,EAAE,uBAAuB;AAClC,QAAA,eAAe,EAAE;AACf,YAAA,WAAW,EAAE;AACX,gBAAA,cAAc,EAAE,aAAa;AAC7B,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;AACpC,iBAAA;AACF,aAAA;AACD,YAAA,WAAW,EAAE;AACX,gBAAA,cAAc,EAAE,aAAa;AAC7B,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;AACpC,iBAAA;AACF,aAAA;AACD,YAAA,EAAE,EAAE;AACF,gBAAA,cAAc,EAAE,IAAI;AACpB,gBAAA,QAAQ,EAAE,IAAI;AACd,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACD,YAAA,UAAU,EAAE;AACV,gBAAA,cAAc,EAAE,YAAY;AAC5B,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,UAAU;AACjB,iBAAA;AACF,aAAA;AACD,YAAA,cAAc,EAAE;AACd,gBAAA,cAAc,EAAE,gBAAgB;AAChC,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,SAAS;AAChB,iBAAA;AACF,aAAA;AACD,YAAA,KAAK,EAAE;AACL,gBAAA,cAAc,EAAE,OAAO;AACvB,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,KAAK;AACZ,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,aAAa,GAA6B;AACrD,IAAA,IAAI,EAAE;AACJ,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,SAAS,EAAE,eAAe;AAC1B,QAAA,eAAe,EAAE;AACf,YAAA,KAAK,EAAE;AACL,gBAAA,cAAc,EAAE,OAAO;AACvB,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,WAAW;AACjB,oBAAA,SAAS,EAAE,YAAY;AACxB,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,UAAU,GAA6B;AAClD,IAAA,IAAI,EAAE;AACJ,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,SAAS,EAAE,YAAY;AACvB,QAAA,eAAe,EAAE;AACf,YAAA,IAAI,EAAE;AACJ,gBAAA,cAAc,EAAE,MAAM;AACtB,gBAAA,QAAQ,EAAE,IAAI;AACd,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACD,YAAA,OAAO,EAAE;AACP,gBAAA,cAAc,EAAE,SAAS;AACzB,gBAAA,QAAQ,EAAE,IAAI;AACd,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACD,YAAA,OAAO,EAAE;AACP,gBAAA,cAAc,EAAE,SAAS;AACzB,gBAAA,QAAQ,EAAE,IAAI;AACd,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,UAAU;AAChB,oBAAA,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,EAAE;AAClE,iBAAA;AACF,aAAA;AACD,YAAA,UAAU,EAAE;AACV,gBAAA,cAAc,EAAE,YAAY;AAC5B,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,WAAW;AACjB,oBAAA,SAAS,EAAE,YAAY;AACxB,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,UAAU,GAA6B;AAClD,IAAA,IAAI,EAAE;AACJ,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,SAAS,EAAE,YAAY;AACvB,QAAA,eAAe,EAAE;AACf,YAAA,IAAI,EAAE;AACJ,gBAAA,cAAc,EAAE,MAAM;AACtB,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACD,YAAA,UAAU,EAAE;AACV,gBAAA,cAAc,EAAE,YAAY;AAC5B,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,WAAW;AACjB,oBAAA,SAAS,EAAE,YAAY;AACxB,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,oCAAoC,GAA6B;AAC5E,IAAA,IAAI,EAAE;AACJ,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,SAAS,EAAE,sCAAsC;AACjD,QAAA,eAAe,EAAE;AACf,YAAA,KAAK,EAAE;AACL,gBAAA,cAAc,EAAE,OAAO;AACvB,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,UAAU;AAChB,oBAAA,OAAO,EAAE;wBACP,IAAI,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,uBAAuB,EAAE;AAChE,qBAAA;AACF,iBAAA;AACF,aAAA;AACD,YAAA,QAAQ,EAAE;AACR,gBAAA,cAAc,EAAE,UAAU;AAC1B,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,kBAAkB,GAA6B;AAC1D,IAAA,IAAI,EAAE;AACJ,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,SAAS,EAAE,oBAAoB;AAC/B,QAAA,eAAe,EAAE;AACf,YAAA,KAAK,EAAE;AACL,gBAAA,cAAc,EAAE,OAAO;AACvB,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACD,YAAA,iBAAiB,EAAE;AACjB,gBAAA,cAAc,EAAE,mBAAmB;AACnC,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,WAAW,GAA6B;AACnD,IAAA,IAAI,EAAE;AACJ,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,SAAS,EAAE,aAAa;AACxB,QAAA,eAAe,EAAE;AACf,YAAA,KAAK,EAAE;AACL,gBAAA,cAAc,EAAE,OAAO;AACvB,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;AACnC,iBAAA;AACF,aAAA;AACD,YAAA,iBAAiB,EAAE;AACjB,gBAAA,cAAc,EAAE,mBAAmB;AACnC,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,sBAAsB,GAA6B;AAC9D,IAAA,IAAI,EAAE;AACJ,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,SAAS,EAAE,wBAAwB;AACnC,QAAA,eAAe,EAAE;AACf,YAAA,KAAK,EAAE;AACL,gBAAA,cAAc,EAAE,OAAO;AACvB,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;AACnC,iBAAA;AACF,aAAA;AACD,YAAA,QAAQ,EAAE;AACR,gBAAA,cAAc,EAAE,UAAU;AAC1B,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,8BAA8B,GAA6B;AACtE,IAAA,IAAI,EAAE;AACJ,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,SAAS,EAAE,gCAAgC;AAC3C,QAAA,eAAe,EAAE;AACf,YAAA,KAAK,EAAE;AACL,gBAAA,cAAc,EAAE,OAAO;AACvB,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,UAAU;AAChB,oBAAA,OAAO,EAAE;wBACP,IAAI,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,sBAAsB,EAAE;AAC/D,qBAAA;AACF,iBAAA;AACF,aAAA;AACD,YAAA,QAAQ,EAAE;AACR,gBAAA,cAAc,EAAE,UAAU;AAC1B,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,oBAAoB,GAA6B;AAC5D,IAAA,IAAI,EAAE;AACJ,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,SAAS,EAAE,sBAAsB;AACjC,QAAA,eAAe,EAAE;AACf,YAAA,cAAc,EAAE;AACd,gBAAA,cAAc,EAAE,iBAAiB;AACjC,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACD,YAAA,QAAQ,EAAE;AACR,gBAAA,cAAc,EAAE,WAAW;AAC3B,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACD,YAAA,gBAAgB,EAAE;AAChB,gBAAA,cAAc,EAAE,mBAAmB;AACnC,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACD,YAAA,gBAAgB,EAAE;AAChB,gBAAA,cAAc,EAAE,mBAAmB;AACnC,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,oBAAoB,GAA6B;AAC5D,IAAA,IAAI,EAAE;AACJ,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,SAAS,EAAE,sBAAsB;AACjC,QAAA,eAAe,EAAE;AACf,YAAA,KAAK,EAAE;AACL,gBAAA,cAAc,EAAE,OAAO;AACvB,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,UAAU;AAChB,oBAAA,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,EAAE;AAClE,iBAAA;AACF,aAAA;AACD,YAAA,QAAQ,EAAE;AACR,gBAAA,cAAc,EAAE,UAAU;AAC1B,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,UAAU,GAA6B;AAClD,IAAA,IAAI,EAAE;AACJ,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,SAAS,EAAE,YAAY;AACvB,QAAA,eAAe,EAAE;AACf,YAAA,EAAE,EAAE;AACF,gBAAA,cAAc,EAAE,IAAI;AACpB,gBAAA,QAAQ,EAAE,IAAI;AACd,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACD,YAAA,YAAY,EAAE;AACZ,gBAAA,cAAc,EAAE,cAAc;AAC9B,gBAAA,QAAQ,EAAE,IAAI;AACd,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACD,YAAA,MAAM,EAAE;AACN,gBAAA,cAAc,EAAE,QAAQ;AACxB,gBAAA,QAAQ,EAAE,IAAI;AACd,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,sBAAsB,GAA6B;AAC9D,IAAA,IAAI,EAAE;AACJ,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,SAAS,EAAE,wBAAwB;AACnC,QAAA,eAAe,EAAE;AACf,YAAA,WAAW,EAAE;AACX,gBAAA,cAAc,EAAE,cAAc;AAC9B,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,0BAA0B,GAA6B;AAClE,IAAA,IAAI,EAAE;AACJ,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,SAAS,EAAE,4BAA4B;AACvC,QAAA,eAAe,EAAE;AACf,YAAA,IAAI,EAAE;AACJ,gBAAA,cAAc,EAAE,MAAM;AACtB,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,sBAAsB,GAA6B;AAC9D,IAAA,IAAI,EAAE;AACJ,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,SAAS,EAAE,wBAAwB;AACnC,QAAA,eAAe,EAAE;AACf,YAAA,IAAI,EAAE;AACJ,gBAAA,cAAc,EAAE,MAAM;AACtB,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,yBAAyB,GAA6B;AACjE,IAAA,IAAI,EAAE;AACJ,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,SAAS,EAAE,2BAA2B;AACtC,QAAA,eAAe,EAAE;AACf,YAAA,IAAI,EAAE;AACJ,gBAAA,cAAc,EAAE,MAAM;AACtB,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,sCAAsC,GAA6B;AAC9E,IAAA,IAAI,EAAE;AACJ,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,SAAS,EAAE,wCAAwC;AACnD,QAAA,eAAe,EAAE;AACf,YAAA,IAAI,EAAE;AACJ,gBAAA,cAAc,EAAE,MAAM;AACtB,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,kCAAkC,GAA6B;AAC1E,IAAA,IAAI,EAAE;AACJ,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,SAAS,EAAE,oCAAoC;AAC/C,QAAA,eAAe,EAAE;AACf,YAAA,IAAI,EAAE;AACJ,gBAAA,cAAc,EAAE,MAAM;AACtB,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,qCAAqC,GAA6B;AAC7E,IAAA,IAAI,EAAE;AACJ,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,SAAS,EAAE,uCAAuC;AAClD,QAAA,eAAe,EAAE;AACf,YAAA,IAAI,EAAE;AACJ,gBAAA,cAAc,EAAE,MAAM;AACtB,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,+BAA+B,GAA6B;AACvE,IAAA,IAAI,EAAE;AACJ,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,SAAS,EAAE,iCAAiC;AAC5C,QAAA,eAAe,EAAE;AACf,YAAA,IAAI,EAAE;AACJ,gBAAA,cAAc,EAAE,MAAM;AACtB,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,kCAAkC,GAA6B;AAC1E,IAAA,IAAI,EAAE;AACJ,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,SAAS,EAAE,oCAAoC;AAC/C,QAAA,eAAe,EAAE;AACf,YAAA,IAAI,EAAE;AACJ,gBAAA,cAAc,EAAE,MAAM;AACtB,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjdD;;;;;;AAMG;AAaI,MAAM,WAAW,GAAuB;AAC7C,IAAA,aAAa,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;AACzC,IAAA,MAAM,EAAE;AACN,QAAA,YAAY,EAAE,kBAAkB;AAChC,QAAA,UAAU,EAAE,IAAI;AAChB,QAAA,cAAc,EAAE,cAAc;AAC9B,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,QAAQ;AACf,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,MAAM,GAAuB;AACxC,IAAA,aAAa,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC;AACpC,IAAA,MAAM,EAAE;AACN,QAAA,WAAW,EAAE;AACX,YAAA,QAAQ,EAAE,CAAC;AACX,YAAA,WAAW,EAAE,IAAI;AAClB,SAAA;AACD,QAAA,cAAc,EAAE,QAAQ;AACxB,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;AACnC,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,KAAK,GAA0B;AAC1C,IAAA,aAAa,EAAE,OAAO;AACtB,IAAA,MAAM,EAAE;AACN,QAAA,cAAc,EAAE,OAAO;AACvB,QAAA,QAAQ,EAAE,IAAI;AACd,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,QAAQ;AACf,SAAA;AACF,KAAA;AACD,IAAA,YAAY,EAAE,IAAI;CACnB,CAAC;AAEK,MAAM,WAAW,GAAuB;AAC7C,IAAA,aAAa,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;AACzC,IAAA,MAAM,EAAE;AACN,QAAA,cAAc,EAAE,aAAa;AAC7B,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,QAAQ;AACf,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,UAAU,GAAuB;AAC5C,IAAA,aAAa,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;AACxC,IAAA,MAAM,EAAE;AACN,QAAA,cAAc,EAAE,YAAY;AAC5B,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,QAAQ;AACf,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,UAAU,GAA4B;AACjD,IAAA,aAAa,EAAE,YAAY;AAC3B,IAAA,MAAM,EAAE;AACN,QAAA,YAAY,EAAE,YAAY;AAC1B,QAAA,UAAU,EAAE,IAAI;AAChB,QAAA,cAAc,EAAE,aAAa;AAC7B,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,QAAQ;AACf,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,eAAe,GAA4B;AACtD,IAAA,aAAa,EAAE,CAAC,SAAS,EAAE,iBAAiB,CAAC;AAC7C,IAAA,MAAM,EAAE;AACN,QAAA,cAAc,EAAE,iBAAiB;AACjC,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;AACtC,SAAA;AACF,KAAA;IACD,gBAAgB,EAAEA,8BAAqB,CAAC,GAAG;CAC5C,CAAC;AAEK,MAAM,sBAAsB,GAA4B;AAC7D,IAAA,aAAa,EAAE,CAAC,SAAS,EAAE,wBAAwB,CAAC;AACpD,IAAA,MAAM,EAAE;AACN,QAAA,cAAc,EAAE,wBAAwB;AACxC,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,SAAS;AAChB,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,eAAe,GAAuB;AACjD,IAAA,aAAa,EAAE,CAAC,SAAS,EAAE,iBAAiB,CAAC;AAC7C,IAAA,MAAM,EAAE;AACN,QAAA,cAAc,EAAE,oBAAoB;AACpC,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,QAAQ;AACf,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,EAAE,GAA0B;AACvC,IAAA,aAAa,EAAE,IAAI;AACnB,IAAA,MAAM,EAAE;AACN,QAAA,cAAc,EAAE,IAAI;AACpB,QAAA,QAAQ,EAAE,IAAI;AACd,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,QAAQ;AACf,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,YAAY,GAAuB;AAC9C,IAAA,aAAa,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;AACzC,IAAA,MAAM,EAAE;AACN,QAAA,YAAY,EAAE,6BAA6B;AAC3C,QAAA,UAAU,EAAE,IAAI;AAChB,QAAA,cAAc,EAAE,cAAc;AAC9B,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,QAAQ;AACf,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,WAAW,GAAuB;AAC7C,IAAA,aAAa,EAAE,aAAa;AAC5B,IAAA,MAAM,EAAE;AACN,QAAA,cAAc,EAAE,aAAa;AAC7B,QAAA,QAAQ,EAAE,IAAI;AACd,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;AACnC,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,QAAQ,GAA0B;AAC7C,IAAA,aAAa,EAAE,UAAU;AACzB,IAAA,MAAM,EAAE;AACN,QAAA,cAAc,EAAE,UAAU;AAC1B,QAAA,QAAQ,EAAE,IAAI;AACd,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,QAAQ;AACf,SAAA;AACF,KAAA;AACD,IAAA,YAAY,EAAE,IAAI;CACnB,CAAC;AAEK,MAAM,kBAAkB,GAAuB;AACpD,IAAA,aAAa,EAAE,oBAAoB;AACnC,IAAA,MAAM,EAAEC,kBAAwB;CACjC,CAAC;AAEK,MAAM,IAAI,GAAuB;AACtC,IAAA,aAAa,EAAE,MAAM;AACrB,IAAA,MAAM,EAAE;AACN,QAAA,cAAc,EAAE,MAAM;AACtB,QAAA,QAAQ,EAAE,IAAI;AACd,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,KAAK;AACZ,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,WAAW,GAAuB;AAC7C,IAAA,aAAa,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;AACzC,IAAA,MAAM,EAAE;AACN,QAAA,cAAc,EAAE,eAAe;AAC/B,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,QAAQ;AACf,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,OAAO,GAAuB;AACzC,IAAA,aAAa,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;AACrC,IAAA,MAAM,EAAE;AACN,QAAA,cAAc,EAAE,UAAU;AAC1B,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,QAAQ;AACf,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,aAAa,GAAuB;AAC/C,IAAA,aAAa,EAAE,eAAe;AAC9B,IAAA,MAAM,EAAE;AACN,QAAA,cAAc,EAAE,eAAe;AAC/B,QAAA,QAAQ,EAAE,IAAI;AACd,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;AACnC,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,cAAc,GAA0B;AACnD,IAAA,aAAa,EAAE,gBAAgB;AAC/B,IAAA,MAAM,EAAE;AACN,QAAA,cAAc,EAAE,gBAAgB;AAChC,QAAA,QAAQ,EAAE,IAAI;AACd,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,QAAQ;AACf,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,YAAY,GAAuB;AAC9C,IAAA,aAAa,EAAE,cAAc;AAC7B,IAAA,MAAM,EAAE;AACN,QAAA,cAAc,EAAE,cAAc;AAC9B,QAAA,QAAQ,EAAE,IAAI;AACd,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,KAAK;AACZ,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,gBAAgB,GAA4B;AACvD,IAAA,aAAa,EAAE,CAAC,SAAS,EAAE,kBAAkB,CAAC;AAC9C,IAAA,MAAM,EAAE;AACN,QAAA,cAAc,EAAE,kBAAkB;AAClC,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,QAAQ;AACf,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,SAAS,GAAuB;AAC3C,IAAA,aAAa,EAAE,WAAW;AAC1B,IAAA,MAAM,EAAE;AACN,QAAA,cAAc,EAAE,WAAW;AAC3B,QAAA,QAAQ,EAAE,IAAI;AACd,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,KAAK;AACZ,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,SAAS,GAAuB;AAC3C,IAAA,aAAa,EAAE,WAAW;AAC1B,IAAA,MAAM,EAAE;AACN,QAAA,cAAc,EAAE,YAAY;AAC5B,QAAA,QAAQ,EAAE,IAAI;AACd,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,QAAQ;AACf,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,mBAAmB,GAAuB;AACrD,IAAA,aAAa,EAAE,CAAC,SAAS,EAAE,qBAAqB,CAAC;AACjD,IAAA,MAAM,EAAE;AACN,QAAA,cAAc,EAAE,uBAAuB;AACvC,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,QAAQ;AACf,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,aAAa,GAA0B;AAClD,IAAA,aAAa,EAAE,eAAe;AAC9B,IAAA,MAAM,EAAE;AACN,QAAA,cAAc,EAAE,eAAe;AAC/B,QAAA,QAAQ,EAAE,IAAI;AACd,QAAA,IAAI,EAAE;AACJ,YAAA,IAAI,EAAE,QAAQ;AACf,SAAA;AACF,KAAA;CACF,CAAC;AAEK,MAAM,UAAU,GAAuB;AAC5C,IAAA,aAAa,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;AACxC,IAAA,MAAM,EAAEC,UAAgB;CACzB;;ACvSD;;;;;;AAMG;AAmBH;;AAEG;MACU,iBAAiB,CAAA;AAG5B;;;AAGG;AACH,IAAA,WAAA,CAAY,MAA4B,EAAA;AACtC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACtB;AAED;;;;;;;;;;;;AAYG;AACH,IAAA,GAAG,CACD,OAA4C,EAAA;QAE5C,MAAM,gBAAgB,GAAgCC,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAC7BC,kBAAgB,CACwB,CAAC;KAC5C;AAED;;;;;;;;;;AAUG;AACH,IAAA,IAAI,CACF,OAA6C,EAAA;QAE7C,MAAM,gBAAgB,GAAgCD,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAC7BE,mBAAiB,CACwB,CAAC;KAC7C;AAED;;;;;;;;;;;AAWG;IACH,OAAO,CACL,EAAU,EACV,OAAgD,EAAA;QAEhD,MAAM,gBAAgB,GAAgCF,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjCG,sBAAoB,CACwB,CAAC;KAChD;AAED;;;;;;;;;;;;;;;;AAgBG;AACH,IAAA,MAAM,CACJ,EAAU,EACV,WAAkB,EAClB,OAA+C,EAAA;QAE/C,MAAM,gBAAgB,GAAgCH,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,WAAW,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAC9CI,qBAAmB,CACc,CAAC;KACrC;AAED;;;;;;;;;;;;;AAaG;IACH,MAAM,CACJ,EAAU,EACV,OAA+C,EAAA;QAE/C,MAAM,gBAAgB,GAAgCJ,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjCK,qBAAmB,CACc,CAAC;KACrC;AAED;;;;AAIG;IACH,QAAQ,CACN,QAAgB,EAChB,OAAiD,EAAA;QAEjD,MAAM,gBAAgB,GAAgCL,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,QAAQ,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACvCM,uBAAqB,CACwB,CAAC;KACjD;AACF,CAAA;AACD;AAEA,MAAMC,YAAU,GAAG,IAAIP,mBAAQ,CAAC,UAAU,CAAC,OAAO,cAAc,KAAK,CAAC,CAAC;AAEvE,MAAMC,kBAAgB,GAA2B;AAC/C,IAAA,IAAI,EAAE,SAAS;AACf,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE;AACH,YAAA,UAAU,EAAE;AACV,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,UAAU;AAChB,oBAAA,OAAO,EAAE;wBACP,IAAI,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,uBAAuB,EAAE;AAChE,qBAAA;AACF,iBAAA;AACF,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAE;YACP,UAAU,EAAEO,aAAqB;AAClC,SAAA;AACF,KAAA;IACD,WAAW,EAAEC,MAAiB;AAC9B,IAAA,eAAe,EAAE,CAACC,UAAqB,CAAC;AACxC,IAAA,aAAa,EAAE,CAACC,KAAgB,CAAC;AACjC,IAAA,gBAAgB,EAAE;AAChB,QAAAC,WAAsB;AACtB,QAAAC,WAAsB;AACtB,QAAAC,UAAqB;AACtB,KAAA;AACD,IAAA,SAAS,EAAE,MAAM;gBACjBP,YAAU;CACX,CAAC;AACF,MAAML,mBAAiB,GAA2B;AAChD,IAAA,IAAI,EAAE,SAAS;AACf,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE;YACH,UAAU,EAAEa,oCAA4C;AACzD,SAAA;AACD,QAAA,OAAO,EAAE;YACP,UAAU,EAAEP,aAAqB;AAClC,SAAA;AACF,KAAA;AACD,IAAA,eAAe,EAAE;AACf,QAAAE,UAAqB;AACrB,QAAAM,eAA0B;AAC1B,QAAAC,sBAAiC;AAClC,KAAA;AACD,IAAA,aAAa,EAAE,CAACN,KAAgB,CAAC;AACjC,IAAA,gBAAgB,EAAE;AAChB,QAAAE,WAAsB;AACtB,QAAAC,UAAqB;AACrB,QAAAI,eAA0B;AAC3B,KAAA;gBACDX,YAAU;CACX,CAAC;AACF,MAAMJ,sBAAoB,GAA2B;AACnD,IAAA,IAAI,EAAE,cAAc;AACpB,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE;YACH,UAAU,EAAEgB,qBAA6B;AAC1C,SAAA;AACD,QAAA,OAAO,EAAE;YACP,UAAU,EAAEX,aAAqB;AAClC,SAAA;AACF,KAAA;IACD,eAAe,EAAE,CAACE,UAAqB,EAAEO,sBAAiC,CAAC;IAC3E,aAAa,EAAE,CAACN,KAAgB,EAAES,EAAa,CAAC;IAChD,gBAAgB,EAAE,CAACP,WAAsB,EAAEC,UAAqB,CAAC;gBACjEP,YAAU;CACX,CAAC;AACF,MAAMH,qBAAmB,GAA2B;AAClD,IAAA,IAAI,EAAE,cAAc;AACpB,IAAA,UAAU,EAAE,OAAO;AACnB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE,EAAE;AACP,QAAA,OAAO,EAAE;YACP,UAAU,EAAEI,aAAqB;AAClC,SAAA;AACF,KAAA;IACD,WAAW,EAAEa,WAAsB;AACnC,IAAA,eAAe,EAAE,CAACX,UAAqB,CAAC;IACxC,aAAa,EAAE,CAACC,KAAgB,EAAES,EAAa,CAAC;AAChD,IAAA,gBAAgB,EAAE;AAChB,QAAAP,WAAsB;AACtB,QAAAC,UAAqB;AACrB,QAAAQ,YAAuB;AACxB,KAAA;AACD,IAAA,SAAS,EAAE,MAAM;gBACjBf,YAAU;CACX,CAAC;AACF,MAAMF,qBAAmB,GAA2B;AAClD,IAAA,IAAI,EAAE,cAAc;AACpB,IAAA,UAAU,EAAE,QAAQ;AACpB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE,EAAE;AACP,QAAA,OAAO,EAAE;YACP,UAAU,EAAEG,aAAqB;AAClC,SAAA;AACF,KAAA;AACD,IAAA,eAAe,EAAE,CAACE,UAAqB,CAAC;IACxC,aAAa,EAAE,CAACC,KAAgB,EAAES,EAAa,CAAC;IAChD,gBAAgB,EAAE,CAACP,WAAsB,EAAEC,UAAqB,CAAC;gBACjEP,YAAU;CACX,CAAC;AACF,MAAMD,uBAAqB,GAA2B;AACpD,IAAA,IAAI,EAAE,YAAY;AAClB,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE;YACH,UAAU,EAAES,oCAA4C;AACzD,SAAA;AACD,QAAA,OAAO,EAAE;YACP,UAAU,EAAEP,aAAqB;AAClC,SAAA;AACF,KAAA;AACD,IAAA,eAAe,EAAE;AACf,QAAAE,UAAqB;AACrB,QAAAM,eAA0B;AAC1B,QAAAC,sBAAiC;AAClC,KAAA;IACD,aAAa,EAAE,CAACN,KAAgB,EAAEY,QAAmB,CAAC;AACtD,IAAA,gBAAgB,EAAE;AAChB,QAAAV,WAAsB;AACtB,QAAAC,UAAqB;AACrB,QAAAI,eAA0B;AAC3B,KAAA;gBACDX,YAAU;CACX;;AC9TD;;;;;;AAMG;AAYH;;AAEG;MACU,KAAK,CAAA;AAGhB;;;AAGG;AACH,IAAA,WAAA,CAAY,MAA4B,EAAA;AACtC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACtB;AAED;;;;;;;;;;;AAWG;IACH,UAAU,CACR,kBAAsC,EACtC,OAAuC,EAAA;QAEvC,MAAM,gBAAgB,GAAgCP,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,kBAAkB,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjD,uBAAuB,CACY,CAAC;KACvC;AACF,CAAA;AACD;AAEA,MAAMO,YAAU,GAAG,IAAIP,mBAAQ,CAAC,UAAU,CAAC,OAAO,cAAc,KAAK,CAAC,CAAC;AAEvE,MAAM,uBAAuB,GAA2B;AACtD,IAAA,IAAI,EAAE,QAAQ;AACd,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE;YACH,UAAU,EAAEwB,WAAmB;YAC/B,aAAa,EAAEC,sBAA8B;AAC9C,SAAA;AACD,QAAA,OAAO,EAAE;YACP,UAAU,EAAEjB,aAAqB;AAClC,SAAA;AACF,KAAA;IACD,WAAW,EAAEkB,kBAA6B;AAC1C,IAAA,eAAe,EAAE,CAAChB,UAAqB,CAAC;AACxC,IAAA,aAAa,EAAE,CAACC,KAAgB,CAAC;AACjC,IAAA,gBAAgB,EAAE;AAChB,QAAAC,WAAsB;AACtB,QAAAC,WAAsB;AACtB,QAAAC,UAAqB;AACrB,QAAAI,eAA0B;AAC3B,KAAA;AACD,IAAA,SAAS,EAAE,MAAM;gBACjBX,YAAU;CACX;;ACpFD;;;;;;AAMG;AAqCH;;AAEG;MACU,YAAY,CAAA;AAGvB;;;AAGG;AACH,IAAA,WAAA,CAAY,MAA4B,EAAA;AACtC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACtB;AAED;;;;;;;;;;AAUG;IACH,OAAO,CACL,EAAU,EACV,OAA2C,EAAA;QAE3C,MAAM,gBAAgB,GAAgCP,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjCG,sBAAoB,CACmB,CAAC;KAC3C;AAED;;;;;;;;;;;;;;AAcG;AACH,IAAA,GAAG,CACD,EAAU,EACV,IAAS,EACT,OAAuC,EAAA;QAEvC,MAAM,gBAAgB,GAAgCH,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACvCC,kBAAgB,CACmB,CAAC;KACvC;AAED;;;;;;;;;;;;;AAaG;IACH,MAAM,CACJ,EAAU,EACV,OAA0C,EAAA;QAE1C,MAAM,gBAAgB,GAAgCD,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjCK,qBAAmB,CACc,CAAC;KACrC;AAED;;;;;;;;;;;;;;;;AAgBG;AACH,IAAA,MAAM,CACJ,EAAU,EACV,aAAoB,EACpB,OAA0C,EAAA;QAE1C,MAAM,gBAAgB,GAAgCL,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,aAAa,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAChD,mBAAmB,CACmB,CAAC;KAC1C;AAED;;;;;;;;;;;;;AAaG;AACH,IAAA,mBAAmB,CACjB,EAAU,EACV,cAAsB,EACtB,OAAuD,EAAA;QAEvD,MAAM,gBAAgB,GAAgCA,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,cAAc,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjD,gCAAgC,CACmB,CAAC;KACvD;AAED;;;;;;;;;;;;;;;;;;;AAmBG;AACH,IAAA,eAAe,CACb,EAAU,EACV,cAAsB,EACtB,YAAiB,EACjB,OAAmD,EAAA;QAEnD,MAAM,gBAAgB,GAAgCA,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,cAAc,EAAE,YAAY,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAC/D,4BAA4B,CACmB,CAAC;KACnD;AAED;;;;;;;;;;;;;;;AAeG;AACH,IAAA,kBAAkB,CAChB,EAAU,EACV,cAAsB,EACtB,OAAsD,EAAA;QAEtD,MAAM,gBAAgB,GAAgCA,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,cAAc,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjD,+BAA+B,CACE,CAAC;KACrC;AAED;;;;;;;;;;;;;;;;;;;;;AAqBG;AACH,IAAA,kBAAkB,CAChB,EAAU,EACV,cAAsB,EACtB,aAAoB,EACpB,OAAsD,EAAA;QAEtD,MAAM,gBAAgB,GAAgCA,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,cAAc,EAAE,aAAa,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAChE,+BAA+B,CACmB,CAAC;KACtD;AAED;;;;;;;;;;AAUG;IACH,iBAAiB,CACf,EAAU,EACV,OAAqD,EAAA;QAErD,MAAM,gBAAgB,GAAgCA,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjC,8BAA8B,CACmB,CAAC;KACrD;AAED;;;;;;;;;;AAUG;IACH,yBAAyB,CACvB,EAAU,EACV,OAA6D,EAAA;QAE7D,MAAM,gBAAgB,GAAgCA,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjC,sCAAsC,CACmB,CAAC;KAC7D;AAED;;;;;;;;;;;;;;AAcG;AACH,IAAA,aAAa,CACX,EAAU,EACV,SAAiB,EACjB,SAAc,EACd,OAAiD,EAAA;QAEjD,MAAM,gBAAgB,GAAgCA,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACvD,0BAA0B,CACO,CAAC;KACrC;AAED;;;;;;;;;;;;;;;;AAgBG;IACH,sBAAsB,CACpB,EAAU,EACV,aAAqB,EACrB,SAAiB,EACjB,SAAc,EACd,OAA0D,EAAA;QAE1D,MAAM,gBAAgB,GAAgCA,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACtE,mCAAmC,CACF,CAAC;KACrC;AAED;;;;;;;;;;;;AAYG;AACH,IAAA,YAAY,CACV,EAAU,EACV,aAAqB,EACrB,OAAgD,EAAA;QAEhD,MAAM,gBAAgB,GAAgCA,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,aAAa,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAChD,yBAAyB,CACmB,CAAC;KAChD;AAED;;;;;;;;;;;;;;;;;AAiBG;AACH,IAAA,eAAe,CACb,EAAU,EACV,aAAqB,EACrB,aAAoB,EACpB,OAAmD,EAAA;QAEnD,MAAM,gBAAgB,GAAgCA,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,aAAa,EAAE,aAAa,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAC/D,4BAA4B,CACmB,CAAC;KACnD;AAED;;;;;AAKG;AACH,IAAA,qBAAqB,CACnB,EAAU,EACV,QAAgB,EAChB,OAAyD,EAAA;QAEzD,MAAM,gBAAgB,GAAgCA,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAC3C,kCAAkC,CACmB,CAAC;KACzD;AAED;;;;;;AAMG;AACH,IAAA,6BAA6B,CAC3B,EAAU,EACV,QAAgB,EAChB,OAAiE,EAAA;QAEjE,MAAM,gBAAgB,GAAgCA,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAC3C,0CAA0C,CACmB,CAAC;KACjE;AACF,CAAA;AACD;AAEA,MAAMO,YAAU,GAAG,IAAIP,mBAAQ,CAAC,UAAU,CAAC,OAAO,cAAc,KAAK,CAAC,CAAC;AAEvE,MAAMG,sBAAoB,GAA2B;AACnD,IAAA,IAAI,EAAE,oBAAoB;AAC1B,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE;YACH,UAAU,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;YACrC,aAAa,EAAEwB,0BAAkC;AAClD,SAAA;AACD,QAAA,OAAO,EAAE;YACP,UAAU,EAAEnB,aAAqB;AAClC,SAAA;AACF,KAAA;AACD,IAAA,eAAe,EAAE,CAACE,UAAqB,CAAC;IACxC,aAAa,EAAE,CAACC,KAAgB,EAAES,EAAa,CAAC;IAChD,gBAAgB,EAAE,CAACP,WAAsB,EAAEC,UAAqB,CAAC;gBACjEP,YAAU;CACX,CAAC;AACF,MAAMN,kBAAgB,GAA2B;AAC/C,IAAA,IAAI,EAAE,oBAAoB;AAC1B,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE;YACH,UAAU,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;YACrC,aAAa,EAAE2B,sBAA8B;AAC9C,SAAA;AACD,QAAA,GAAG,EAAE,EAAE;AACP,QAAA,OAAO,EAAE;YACP,UAAU,EAAEpB,aAAqB;AAClC,SAAA;AACF,KAAA;IACD,WAAW,EAAEqB,IAAe;AAC5B,IAAA,eAAe,EAAE,CAACnB,UAAqB,CAAC;IACxC,aAAa,EAAE,CAACC,KAAgB,EAAES,EAAa,CAAC;AAChD,IAAA,gBAAgB,EAAE;AAChB,QAAAR,WAAsB;AACtB,QAAAC,WAAsB;AACtB,QAAAC,UAAqB;AACrB,QAAAgB,WAAsB;AACvB,KAAA;AACD,IAAA,SAAS,EAAE,MAAM;gBACjBvB,YAAU;CACX,CAAC;AACF,MAAMF,qBAAmB,GAA2B;AAClD,IAAA,IAAI,EAAE,oBAAoB;AAC1B,IAAA,UAAU,EAAE,QAAQ;AACpB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE,EAAE;AACP,QAAA,OAAO,EAAE;YACP,UAAU,EAAEG,aAAqB;AAClC,SAAA;AACF,KAAA;AACD,IAAA,eAAe,EAAE,CAACE,UAAqB,CAAC;IACxC,aAAa,EAAE,CAACC,KAAgB,EAAES,EAAa,CAAC;AAChD,IAAA,gBAAgB,EAAE;AAChB,QAAAP,WAAsB;AACtB,QAAAC,UAAqB;AACrB,QAAAiB,OAAkB;AACnB,KAAA;gBACDxB,YAAU;CACX,CAAC;AACF,MAAM,mBAAmB,GAA2B;AAClD,IAAA,IAAI,EAAE,oBAAoB;AAC1B,IAAA,UAAU,EAAE,OAAO;AACnB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE,EAAE;AACP,QAAA,GAAG,EAAE;YACH,aAAa,EAAEyB,yBAAiC;AACjD,SAAA;AACD,QAAA,OAAO,EAAE;YACP,UAAU,EAAExB,aAAqB;AAClC,SAAA;AACF,KAAA;IACD,WAAW,EAAEyB,aAAwB;AACrC,IAAA,eAAe,EAAE,CAACvB,UAAqB,CAAC;IACxC,aAAa,EAAE,CAACC,KAAgB,EAAES,EAAa,CAAC;AAChD,IAAA,gBAAgB,EAAE;AAChB,QAAAP,WAAsB;AACtB,QAAAC,UAAqB;AACrB,QAAAQ,YAAuB;AACvB,QAAAS,OAAkB;AACnB,KAAA;AACD,IAAA,SAAS,EAAE,MAAM;gBACjBxB,YAAU;CACX,CAAC;AACF,MAAM,gCAAgC,GAA2B;AAC/D,IAAA,IAAI,EAAE,mDAAmD;AACzD,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE;YACH,UAAU,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;YACrC,aAAa,EAAE2B,sCAA8C;AAC9D,SAAA;AACD,QAAA,OAAO,EAAE;YACP,UAAU,EAAE1B,aAAqB;AAClC,SAAA;AACF,KAAA;AACD,IAAA,eAAe,EAAE,CAACE,UAAqB,CAAC;AACxC,IAAA,aAAa,EAAE,CAACC,KAAgB,EAAES,EAAa,EAAEe,cAAyB,CAAC;IAC3E,gBAAgB,EAAE,CAACtB,WAAsB,EAAEC,UAAqB,CAAC;gBACjEP,YAAU;CACX,CAAC;AACF,MAAM,4BAA4B,GAA2B;AAC3D,IAAA,IAAI,EAAE,mDAAmD;AACzD,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE;YACH,UAAU,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;YACrC,aAAa,EAAE6B,kCAA0C;AAC1D,SAAA;AACD,QAAA,OAAO,EAAE;YACP,UAAU,EAAE5B,aAAqB;AAClC,SAAA;AACF,KAAA;IACD,WAAW,EAAE6B,YAAuB;AACpC,IAAA,eAAe,EAAE,CAAC3B,UAAqB,CAAC;AACxC,IAAA,aAAa,EAAE,CAACC,KAAgB,EAAES,EAAa,EAAEe,cAAyB,CAAC;AAC3E,IAAA,gBAAgB,EAAE;AAChB,QAAAvB,WAAsB;AACtB,QAAAC,WAAsB;AACtB,QAAAC,UAAqB;AACrB,QAAAgB,WAAsB;AACvB,KAAA;AACD,IAAA,SAAS,EAAE,MAAM;gBACjBvB,YAAU;CACX,CAAC;AACF,MAAM,+BAA+B,GAA2B;AAC9D,IAAA,IAAI,EAAE,mDAAmD;AACzD,IAAA,UAAU,EAAE,QAAQ;AACpB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE,EAAE;AACP,QAAA,OAAO,EAAE;YACP,UAAU,EAAEC,aAAqB;AAClC,SAAA;AACF,KAAA;AACD,IAAA,eAAe,EAAE,CAACE,UAAqB,CAAC;AACxC,IAAA,aAAa,EAAE,CAACC,KAAgB,EAAES,EAAa,EAAEe,cAAyB,CAAC;AAC3E,IAAA,gBAAgB,EAAE;AAChB,QAAAtB,WAAsB;AACtB,QAAAC,UAAqB;AACrB,QAAAiB,OAAkB;AACnB,KAAA;gBACDxB,YAAU;CACX,CAAC;AACF,MAAM,+BAA+B,GAA2B;AAC9D,IAAA,IAAI,EAAE,mDAAmD;AACzD,IAAA,UAAU,EAAE,OAAO;AACnB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE;YACH,aAAa,EAAE+B,qCAA6C;AAC7D,SAAA;AACD,QAAA,OAAO,EAAE;YACP,UAAU,EAAE9B,aAAqB;AAClC,SAAA;AACF,KAAA;IACD,WAAW,EAAEyB,aAAwB;AACrC,IAAA,eAAe,EAAE,CAACvB,UAAqB,CAAC;AACxC,IAAA,aAAa,EAAE,CAACC,KAAgB,EAAES,EAAa,EAAEe,cAAyB,CAAC;AAC3E,IAAA,gBAAgB,EAAE;AAChB,QAAAtB,WAAsB;AACtB,QAAAC,UAAqB;AACrB,QAAAQ,YAAuB;AACvB,QAAAS,OAAkB;AACnB,KAAA;AACD,IAAA,SAAS,EAAE,MAAM;gBACjBxB,YAAU;CACX,CAAC;AACF,MAAM,8BAA8B,GAA2B;AAC7D,IAAA,IAAI,EAAE,kCAAkC;AACxC,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE;YACH,UAAU,EAAEgC,sBAA8B;AAC3C,SAAA;AACD,QAAA,OAAO,EAAE;YACP,UAAU,EAAE/B,aAAqB;AAClC,SAAA;AACF,KAAA;IACD,eAAe,EAAE,CAACE,UAAqB,EAAE8B,gBAA2B,CAAC;IACrE,aAAa,EAAE,CAAC7B,KAAgB,EAAES,EAAa,CAAC;IAChD,gBAAgB,EAAE,CAACP,WAAsB,EAAEC,UAAqB,CAAC;gBACjEP,YAAU;CACX,CAAC;AACF,MAAM,sCAAsC,GAA2B;AACrE,IAAA,IAAI,EAAE,0CAA0C;AAChD,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE;YACH,UAAU,EAAEkC,8BAAsC;AACnD,SAAA;AACD,QAAA,OAAO,EAAE;YACP,UAAU,EAAEjC,aAAqB;AAClC,SAAA;AACF,KAAA;AACD,IAAA,eAAe,EAAE,CAACE,UAAqB,CAAC;IACxC,aAAa,EAAE,CAACC,KAAgB,EAAES,EAAa,CAAC;IAChD,gBAAgB,EAAE,CAACP,WAAsB,EAAEC,UAAqB,CAAC;gBACjEP,YAAU;CACX,CAAC;AACF,MAAM,0BAA0B,GAA2B;AACzD,IAAA,IAAI,EAAE,8BAA8B;AACpC,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE,EAAE;AACP,QAAA,OAAO,EAAE;YACP,UAAU,EAAEC,aAAqB;AAClC,SAAA;AACF,KAAA;IACD,WAAW,EAAEkC,SAAoB;AACjC,IAAA,eAAe,EAAE,CAAChC,UAAqB,CAAC;IACxC,aAAa,EAAE,CAACC,KAAgB,EAAES,EAAa,CAAC;AAChD,IAAA,gBAAgB,EAAE;AAChB,QAAAR,WAAsB;AACtB,QAAAC,WAAsB;AACtB,QAAAC,UAAqB;AACrB,QAAA6B,SAAoB;AACpB,QAAAC,mBAA8B;AAC/B,KAAA;AACD,IAAA,SAAS,EAAE,MAAM;gBACjBrC,YAAU;CACX,CAAC;AACF,MAAM,mCAAmC,GAA2B;AAClE,IAAA,IAAI,EAAE,yDAAyD;AAC/D,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE,EAAE;AACP,QAAA,OAAO,EAAE;YACP,UAAU,EAAEC,aAAqB;AAClC,SAAA;AACF,KAAA;IACD,WAAW,EAAEkC,SAAoB;AACjC,IAAA,eAAe,EAAE,CAAChC,UAAqB,CAAC;AACxC,IAAA,aAAa,EAAE,CAACC,KAAgB,EAAES,EAAa,EAAEyB,aAAwB,CAAC;AAC1E,IAAA,gBAAgB,EAAE;AAChB,QAAAjC,WAAsB;AACtB,QAAAC,WAAsB;AACtB,QAAAC,UAAqB;AACrB,QAAA6B,SAAoB;AACpB,QAAAC,mBAA8B;AAC/B,KAAA;AACD,IAAA,SAAS,EAAE,MAAM;gBACjBrC,YAAU;CACX,CAAC;AACF,MAAM,yBAAyB,GAA2B;AACxD,IAAA,IAAI,EAAE,+CAA+C;AACrD,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE;YACH,UAAU,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;YACrC,aAAa,EAAEuC,+BAAuC;AACvD,SAAA;AACD,QAAA,OAAO,EAAE;YACP,UAAU,EAAEtC,aAAqB;AAClC,SAAA;AACF,KAAA;AACD,IAAA,eAAe,EAAE,CAACE,UAAqB,CAAC;AACxC,IAAA,aAAa,EAAE,CAACC,KAAgB,EAAES,EAAa,EAAEyB,aAAwB,CAAC;IAC1E,gBAAgB,EAAE,CAAChC,WAAsB,EAAEC,UAAqB,CAAC;gBACjEP,YAAU;CACX,CAAC;AACF,MAAM,4BAA4B,GAA2B;AAC3D,IAAA,IAAI,EAAE,+CAA+C;AACrD,IAAA,UAAU,EAAE,OAAO;AACnB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE,EAAE;AACP,QAAA,GAAG,EAAE;YACH,aAAa,EAAEwC,kCAA0C;AAC1D,SAAA;AACD,QAAA,OAAO,EAAE;YACP,UAAU,EAAEvC,aAAqB;AAClC,SAAA;AACF,KAAA;IACD,WAAW,EAAEyB,aAAwB;AACrC,IAAA,eAAe,EAAE,CAACvB,UAAqB,CAAC;AACxC,IAAA,aAAa,EAAE,CAACC,KAAgB,EAAES,EAAa,EAAEyB,aAAwB,CAAC;AAC1E,IAAA,gBAAgB,EAAE;AAChB,QAAAhC,WAAsB;AACtB,QAAAC,UAAqB;AACrB,QAAAQ,YAAuB;AACvB,QAAAS,OAAkB;AACnB,KAAA;AACD,IAAA,SAAS,EAAE,MAAM;gBACjBxB,YAAU;CACX,CAAC;AACF,MAAM,kCAAkC,GAA2B;AACjE,IAAA,IAAI,EAAE,YAAY;AAClB,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE;YACH,UAAU,EAAEgC,sBAA8B;AAC3C,SAAA;AACD,QAAA,OAAO,EAAE;YACP,UAAU,EAAE/B,aAAqB;AAClC,SAAA;AACF,KAAA;IACD,eAAe,EAAE,CAACE,UAAqB,EAAE8B,gBAA2B,CAAC;AACrE,IAAA,aAAa,EAAE,CAAC7B,KAAgB,EAAES,EAAa,EAAEG,QAAmB,CAAC;IACrE,gBAAgB,EAAE,CAACV,WAAsB,EAAEC,UAAqB,CAAC;gBACjEP,YAAU;CACX,CAAC;AACF,MAAM,0CAA0C,GAA2B;AACzE,IAAA,IAAI,EAAE,YAAY;AAClB,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE;YACH,UAAU,EAAEkC,8BAAsC;AACnD,SAAA;AACD,QAAA,OAAO,EAAE;YACP,UAAU,EAAEjC,aAAqB;AAClC,SAAA;AACF,KAAA;AACD,IAAA,eAAe,EAAE,CAACE,UAAqB,CAAC;AACxC,IAAA,aAAa,EAAE,CAACC,KAAgB,EAAES,EAAa,EAAEG,QAAmB,CAAC;IACrE,gBAAgB,EAAE,CAACV,WAAsB,EAAEC,UAAqB,CAAC;gBACjEP,YAAU;CACX;;AC5zBD;;;;;;AAMG;AAiBH;;AAEG;MACU,WAAW,CAAA;AAGtB;;;AAGG;AACH,IAAA,WAAA,CAAY,MAA4B,EAAA;AACtC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACtB;AAED;;;;;AAKG;AACH,IAAA,IAAI,CACF,OAAuC,EAAA;QAEvC,MAAM,gBAAgB,GAAgCP,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAC7B,iBAAiB,CACkB,CAAC;KACvC;AAED;;;;;;;;AAQG;IACH,OAAO,CACL,EAAU,EACV,OAA0C,EAAA;QAE1C,MAAM,gBAAgB,GAAgCA,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjC,oBAAoB,CACkB,CAAC;KAC1C;AAED;;;;;;;;;;;AAWG;IACH,GAAG,CACD,EAAU,EACV,OAAsC,EAAA;QAEtC,MAAM,gBAAgB,GAAgCA,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjC,gBAAgB,CACiB,CAAC;KACrC;AAED;;;;;;;;AAQG;IACH,MAAM,CACJ,EAAU,EACV,OAAyC,EAAA;QAEzC,MAAM,gBAAgB,GAAgCA,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjC,mBAAmB,CACc,CAAC;KACrC;AAED;;;;AAIG;IACH,QAAQ,CACN,QAAgB,EAChB,OAA2C,EAAA;QAE3C,MAAM,gBAAgB,GAAgCA,mBAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,QAAQ,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACvC,qBAAqB,CACkB,CAAC;KAC3C;AACF,CAAA;AACD;AAEA,MAAM,UAAU,GAAG,IAAIA,mBAAQ,CAAC,UAAU,CAAC,OAAO,cAAc,KAAK,CAAC,CAAC;AAEvE,MAAM,iBAAiB,GAA2B;AAChD,IAAA,IAAI,EAAE,cAAc;AACpB,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE;YACH,UAAU,EAAEgD,oBAA4B;AACzC,SAAA;AACD,QAAA,OAAO,EAAE;YACP,UAAU,EAAExC,aAAqB;AAClC,SAAA;AACF,KAAA;AACD,IAAA,eAAe,EAAE,CAACE,UAAqB,CAAC;AACxC,IAAA,aAAa,EAAE,CAACC,KAAgB,CAAC;AACjC,IAAA,gBAAgB,EAAE;AAChB,QAAAE,WAAsB;AACtB,QAAAC,UAAqB;AACrB,QAAAI,eAA0B;AAC3B,KAAA;IACD,UAAU;CACX,CAAC;AACF,MAAM,oBAAoB,GAA2B;AACnD,IAAA,IAAI,EAAE,mBAAmB;AACzB,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE;YACH,UAAU,EAAE+B,UAAkB;AAC/B,SAAA;AACD,QAAA,OAAO,EAAE;YACP,UAAU,EAAEzC,aAAqB;AAClC,SAAA;AACF,KAAA;AACD,IAAA,eAAe,EAAE,CAACE,UAAqB,CAAC;IACxC,aAAa,EAAE,CAACC,KAAgB,EAAES,EAAa,CAAC;IAChD,gBAAgB,EAAE,CAACP,WAAsB,EAAEC,UAAqB,CAAC;IACjE,UAAU;CACX,CAAC;AACF,MAAM,gBAAgB,GAA2B;AAC/C,IAAA,IAAI,EAAE,mBAAmB;AACzB,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE,EAAE;AACP,QAAA,OAAO,EAAE;YACP,UAAU,EAAEN,aAAqB;AAClC,SAAA;AACF,KAAA;IACD,WAAW,EAAE0C,UAAqB;AAClC,IAAA,eAAe,EAAE,CAACxC,UAAqB,CAAC;IACxC,aAAa,EAAE,CAACC,KAAgB,EAAES,EAAa,CAAC;AAChD,IAAA,gBAAgB,EAAE;AAChB,QAAAR,WAAsB;AACtB,QAAAC,WAAsB;AACtB,QAAAC,UAAqB;AACtB,KAAA;AACD,IAAA,SAAS,EAAE,MAAM;IACjB,UAAU;CACX,CAAC;AACF,MAAM,mBAAmB,GAA2B;AAClD,IAAA,IAAI,EAAE,mBAAmB;AACzB,IAAA,UAAU,EAAE,QAAQ;AACpB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE,EAAE;AACP,QAAA,OAAO,EAAE;YACP,UAAU,EAAEN,aAAqB;AAClC,SAAA;AACF,KAAA;AACD,IAAA,eAAe,EAAE,CAACE,UAAqB,CAAC;IACxC,aAAa,EAAE,CAACC,KAAgB,EAAES,EAAa,CAAC;IAChD,gBAAgB,EAAE,CAACP,WAAsB,EAAEC,UAAqB,CAAC;IACjE,UAAU;CACX,CAAC;AACF,MAAM,qBAAqB,GAA2B;AACpD,IAAA,IAAI,EAAE,YAAY;AAClB,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,SAAS,EAAE;AACT,QAAA,GAAG,EAAE;YACH,UAAU,EAAEkC,oBAA4B;AACzC,SAAA;AACD,QAAA,OAAO,EAAE;YACP,UAAU,EAAExC,aAAqB;AAClC,SAAA;AACF,KAAA;AACD,IAAA,eAAe,EAAE,CAACE,UAAqB,CAAC;IACxC,aAAa,EAAE,CAACC,KAAgB,EAAEY,QAAmB,CAAC;AACtD,IAAA,gBAAgB,EAAE;AAChB,QAAAV,WAAsB;AACtB,QAAAC,UAAqB;AACrB,QAAAI,eAA0B;AAC3B,KAAA;IACD,UAAU;CACX;;AC3OD;;;;;;AAMG;AAKH,MAAM,WAAW,GAAG,2BAA2B,CAAC;AAChD,MAAM,cAAc,GAAG,OAAO,CAAC;AAElB,MAAA,2BAA4B,SAAQlB,mBAAQ,CAAC,aAAa,CAAA;AAIrE;;;AAGG;AACH,IAAA,WAAA,CAAY,OAA4C,EAAA;;QAEtD,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,GAAG,EAAE,CAAC;AACd,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;AACtB,YAAA,MAAM,gBAAgB,GAAGA,mBAAQ,CAAC,wBAAwB,EAAE,CAAC;YAC7D,OAAO,CAAC,SAAS,GAAG,CAAG,EAAA,WAAW,IAAI,cAAc,CAAA,CAAA,EAAI,gBAAgB,CAAA,CAAE,CAAC;AAC5E,SAAA;AAED,QAAA,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AAE1B,QAAA,IAAI,CAAC,kBAAkB,GAAG,iCAAiC,CAAC;AAE5D,QAAA,IAAI,CAAC,OAAO;AACV,YAAA,OAAO,CAAC,QAAQ,IAAI,kDAAkD,CAAC;;AAGzE,QAAA,IAAI,CAAC,KAAK;AACR,YAAA,OAAO,CAAC,KAAK,IAAI,kDAAkD,CAAC;QACtE,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,YAAY,CAAC;KACtD;AACF;;AC7CD;;;;;;AAMG;AAQH,MAAM,oBAAqB,SAAQ,2BAA2B,CAAA;AAC5D;;;AAGG;AACH,IAAA,WAAA,CAAY,OAA4C,EAAA;QACtD,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,iBAAiB,GAAG,IAAImD,iBAA4B,CAAC,IAAI,CAAC,CAAC;QAChE,IAAI,CAAC,KAAK,GAAG,IAAIC,KAAgB,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,CAAC,YAAY,GAAG,IAAIC,YAAuB,CAAC,IAAI,CAAC,CAAC;QACtD,IAAI,CAAC,WAAW,GAAG,IAAIC,WAAsB,CAAC,IAAI,CAAC,CAAC;KACrD;AAMF;;AC/BD;AACA;AAEO,MAAMC,aAAW,GAAW,OAAO;;ACH1C;AAMA;;;AAGG;AACI,MAAM,aAAa,GAAGC,+BAAmB,CAAC;AAC/C,IAAA,SAAS,EAAE,wBAAwB;AACnC,IAAA,WAAW,EAAE,2BAA2B;AACxC,IAAA,cAAc,EAAED,aAAW;AAC5B,CAAA,CAAC;;ACdF;AAKA;;AAEG;AACI,MAAM,MAAM,GAAGE,2BAAkB,CAAC,yBAAyB,CAAC;;ACRnE;AAyDO,MAAM,WAAW,GAAW,OAAO,CAAC;AAS3C,MAAM,0BAA0B,GAAG,yCAAyC,CAAC;AAE7E;;AAEG;MACU,kBAAkB,CAAA;AAM7B;;;;;;;;;;;;;;;AAeG;AACH,IAAA,WAAA,CACE,WAAmB,EACnB,UAA2B,EAC3B,UAAqC,EAAE,EAAA;QAEvC,MAAM,UAAU,GAAGC,wCAA+B,CAAC,UAAU,EAAE,0BAA0B,CAAC,CAAC;AAC3F,QAAA,MAAM,OAAO,GAAG,CAA+B,4BAAA,EAAA,WAAW,EAAE,CAAC;QAE7D,MAAM,EAAE,UAAU,EAAA,GAAyB,OAAO,EAA3B,eAAe,GAAAC,YAAA,CAAK,OAAO,EAA5C,CAAkC,YAAA,CAAA,CAAU,CAAC;AACnD,QAAA,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE;AACrC,YAAA,eAAe,CAAC,gBAAgB,GAAG,EAAE,CAAC;AACvC,SAAA;AACD,QAAA,IAAI,eAAe,CAAC,gBAAgB,CAAC,eAAe,EAAE;AACpD,YAAA,eAAe,CAAC,gBAAgB,CAAC,eAAe,GAAG,CAAG,EAAA,eAAe,CAAC,gBAAgB,CAAC,eAAe,CAAI,CAAA,EAAA,OAAO,EAAE,CAAC;AACrH,SAAA;AAAM,aAAA;AACL,YAAA,eAAe,CAAC,gBAAgB,CAAC,eAAe,GAAG,OAAO,CAAC;AAC5D,SAAA;QAED,MAAM,uBAAuB,GACxB,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,eAAe,CACf,EAAA;AACD,YAAA,cAAc,EAAE;gBACd,MAAM,EAAE,MAAM,CAAC,IAAI;gBACnB,kBAAkB,EAAE,CAAC,iBAAiB,CAAC;AACxC,aAAA;AACF,SAAA,CACF,CAAC;QAEF,MAAM,QAAQ,GAAGC,kCAAyB,CAAC,uBAAuB,EAAE,UAAU,CAAC,CAAC;AAEhF,QAAA,IAAI,CAAC,MAAM,GAAG,IAAIC,oBAAe,CAC/B,MAAA,CAAA,MAAA,CAAA,EAAA,QAAQ,EAAE,WAAW,EACrB,UAAU,EACP,EAAA,QAAQ,EACX,CAAC;KACJ;AAED;;;;;;AAMG;AACI,IAAA,cAAc,CACnB,aAAqB,EACrB,OAAA,GAA4B,EAAE,EAAA;AAE9B,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,mCAAmC,EACnC,OAAO,EACP,OAAO,cAAc,KAAI;AACvB,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;AACzE,SAAC,CACF,CAAC;KACH;AAED;;;;;;;;AAQG;AACI,IAAA,iBAAiB,CACtB,aAAqB,EACrB,eAAuB,EACvB,UAAyC,EAAE,EAAA;AAE3C,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,sCAAsC,EACtC,OAAO,EACP,OAAO,cAAc,KAAI;YACvB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;AAC5C,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;AAC9E,SAAC,CACF,CAAC;KACH;AAED;;;;;;;;;;AAUG;AACI,IAAA,iBAAiB,CACtB,aAAqB;;IAErB,SAAc,EACd,UAA4C,EAAE,EAAA;AAE9C,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,sCAAsC,EACtC,OAAO,EACP,OAAO,cAAc,KAAI;AACvB,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;AACnF,SAAC,CACF,CAAC;KACH;AAED;;;;;;;AAOG;AACI,IAAA,iBAAiB,CACtB,aAAqB,EACrB,OAAA,GAA4C,EAAE,EAAA;AAE9C,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,sCAAsC,EACtC,OAAO,EACP,OAAO,cAAc,KAAI;AACvB,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;AACxE,SAAC,CACF,CAAC;KACH;AAED;;;;;;;AAOG;AACI,IAAA,YAAY,CACjB,aAAqB,EACrB,aAAqB,EACrB,UAA4B,EAAE,EAAA;AAE9B,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,iCAAiC,EACjC,OAAO,EACP,OAAO,cAAc,KAAI;AACvB,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,aAAa,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;AAC7F,SAAC,CACF,CAAC;KACH;AAED;;;;;;;;;;AAUG;IACI,eAAe,CACpB,aAAqB,EACrB,aAAqB,EACrB,SAAgB,EAChB,UAAqD,EAAE,EAAA;AAEvD,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,oCAAoC,EACpC,OAAO,EACP,OAAO,cAAc,KAAI;AACvB,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,eAAe,CAC7C,aAAa,EACb,aAAa,EACb,SAAS,EACT,cAAc,CACf,CAAC;AACJ,SAAC,CACF,CAAC;KACH;AAED;;;;;;;AAOG;AACI,IAAA,eAAe,CACpB,aAAqB,EACrB,cAAsB,EACtB,UAA4B,EAAE,EAAA;AAE9B,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,oCAAoC,EACpC,OAAO,EACP,OAAO,cAAc,KAAI;AACvB,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,mBAAmB,CACjD,aAAa,EACb,cAAc,EACd,cAAc,CACf,CAAC;AACJ,SAAC,CACF,CAAC;KACH;AAED;;;;;;;;AAQG;IACI,kBAAkB,CACvB,aAAqB,EACrB,cAAsB;;IAEtB,YAAiB,EACjB,UAAqD,EAAE,EAAA;AAEvD,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,uCAAuC,EACvC,OAAO,EACP,OAAO,cAAc,KAAI;AACvB,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,eAAe,CAC7C,aAAa,EACb,cAAc,EACd,YAAY,EACZ,cAAc,CACf,CAAC;AACJ,SAAC,CACF,CAAC;KACH;AAED;;;;;;;;AAQG;IACI,kBAAkB,CACvB,aAAqB,EACrB,cAAsB,EACtB,SAAgB,EAChB,UAAwD,EAAE,EAAA;AAE1D,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,uCAAuC,EACvC,OAAO,EACP,OAAO,cAAc,KAAI;AACvB,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,kBAAkB,CAChD,aAAa,EACb,cAAc,EACd,SAAS,EACT,cAAc,CACf,CAAC;AACJ,SAAC,CACF,CAAC;KACH;AAED;;;;;;;;AAQG;AACI,IAAA,kBAAkB,CACvB,aAAqB,EACrB,cAAsB,EACtB,UAAwD,EAAE,EAAA;AAE1D,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,uCAAuC,EACvC,OAAO,EACP,OAAO,cAAc,KAAI;AACvB,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,kBAAkB,CAChD,aAAa,EACb,cAAc,EACd,cAAc,CACf,CAAC;AACJ,SAAC,CACF,CAAC;KACH;AAED;;;;;;;AAOG;AACY,IAAA,qBAAqB,CAClC,aAAqB,EACrB,OAAyB,EACzB,iBAA+B,EAAA;;AAE/B,YAAA,IAAI,iBAAiB,CAAC,iBAAiB,IAAI,IAAI,EAAE;AAC/C,gBAAA,MAAM,eAAe,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAChB,OAAO,CACX,CAAC;AACF,gBAAA,MAAM,wBAAwB,GAAG,MAAAC,aAAA,CAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,iBAAiB,CAC/E,aAAa,EACb,eAAe,CAChB,CAAA,CAAC;AACF,gBAAA,iBAAiB,CAAC,iBAAiB,GAAG,wBAAwB,CAAC,QAAQ,CAAC;gBACxE,MAAM,MAAAA,aAAA,CAAA,wBAAwB,CAAA,CAAC;AAChC,aAAA;YACD,OAAO,iBAAiB,CAAC,iBAAiB,EAAE;gBAC1C,MAAM,wBAAwB,GAAG,MAAMA,aAAA,CAAA,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,qBAAqB,CACnF,EAAE,EACF,iBAAiB,CAAC,iBAAiB,EACnC,OAAO,CACR,CAAA,CAAC;AAEF,gBAAA,iBAAiB,CAAC,iBAAiB,GAAG,wBAAwB,CAAC,QAAQ,CAAC;gBACxE,MAAM,MAAAA,aAAA,CAAA,wBAAwB,CAAA,CAAC;AAChC,aAAA;SACF,CAAA,CAAA;AAAA,KAAA;AAED;;;AAGG;IACY,oBAAoB,CACjC,aAAqB,EACrB,OAAyB,EAAA;;;;AAEzB,gBAAA,KAAyB,IAAA,EAAA,GAAAC,mBAAA,CAAA,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE,CAAC,CAAA,EAAA,EAAA,EAAA,EAAA,GAAA,MAAAD,aAAA,CAAA,EAAA,CAAA,IAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,IAAA,GAAA;oBAApE,MAAM,IAAI,WAAA,CAAA;AACnB,oBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;AAC/B,oBAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;wBACxB,MAAM,MAAAA,aAAA,CAAA,IAAI,CAAA,CAAC;AACZ,qBAAA;AACF,iBAAA;;;;;;;;;SACF,CAAA,CAAA;AAAA,KAAA;AAED;;;;AAIG;AACI,IAAA,iBAAiB,CACtB,aAAqB,EACrB,OAAA,GAA2C,EAAE,EAAA;QAE7C,MAAM,IAAI,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QAE/D,OAAO;YACL,IAAI,GAAA;AACF,gBAAA,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;aACpB;YACD,CAAC,MAAM,CAAC,aAAa,CAAC,GAAA;AACpB,gBAAA,OAAO,IAAI,CAAC;aACb;AACD,YAAA,MAAM,EAAE,CAAC,QAAA,GAAyB,EAAE,KAClC,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,OAAO,EAAE,QAAQ,CAAC;SAC/D,CAAC;KACH;AAED;;;;;;;AAOG;AACY,IAAA,6BAA6B,CAC1C,aAAqB,EACrB,OAAyB,EACzB,iBAA+B,EAAA;;AAE/B,YAAA,IAAI,iBAAiB,CAAC,iBAAiB,IAAI,IAAI,EAAE;AAC/C,gBAAA,MAAM,eAAe,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAChB,OAAO,CACX,CAAC;AACF,gBAAA,MAAM,iCAAiC,GACrC,MAAAA,aAAA,CAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,yBAAyB,CAAC,aAAa,EAAE,eAAe,CAAC,CAAA,CAAC;AAC3F,gBAAA,iBAAiB,CAAC,iBAAiB,GAAG,iCAAiC,CAAC,QAAQ,CAAC;gBACjF,MAAM,MAAAA,aAAA,CAAA,iCAAiC,CAAA,CAAC;AACzC,aAAA;YACD,OAAO,iBAAiB,CAAC,iBAAiB,EAAE;gBAC1C,MAAM,iCAAiC,GACrC,MAAMA,aAAA,CAAA,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,6BAA6B,CAC1D,EAAE,EACF,iBAAiB,CAAC,iBAAiB,EACnC,OAAO,CACR,CAAA,CAAC;AAEJ,gBAAA,iBAAiB,CAAC,iBAAiB,GAAG,iCAAiC,CAAC,QAAQ,CAAC;gBACjF,MAAM,MAAAA,aAAA,CAAA,iCAAiC,CAAA,CAAC;AACzC,aAAA;SACF,CAAA,CAAA;AAAA,KAAA;AAED;;;;AAIG;IACY,4BAA4B,CACzC,aAAqB,EACrB,OAAyB,EAAA;;;;AAEzB,gBAAA,KAAyB,IAAA,EAAA,GAAAC,mBAAA,CAAA,IAAI,CAAC,6BAA6B,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE,CAAC,CAAA,EAAA,EAAA,EAAA,EAAA,GAAA,MAAAD,aAAA,CAAA,EAAA,CAAA,IAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,IAAA,GAAA;oBAA5E,MAAM,IAAI,WAAA,CAAA;AACnB,oBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;oBAC/B,MAAAA,aAAA,CAAA,OAAOE,sBAAA,CAAAD,oBAAA,KAAK,CAAA,CAAA,CAAA,CAAC;AACd,iBAAA;;;;;;;;;SACF,CAAA,CAAA;AAAA,KAAA;AAED;;;;AAIG;AACI,IAAA,yBAAyB,CAC9B,aAAqB,EACrB,OAAA,GAA2C,EAAE,EAAA;QAK7C,MAAM,IAAI,GAAG,IAAI,CAAC,4BAA4B,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QAEvE,OAAO;YACL,IAAI,GAAA;AACF,gBAAA,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;aACpB;YACD,CAAC,MAAM,CAAC,aAAa,CAAC,GAAA;AACpB,gBAAA,OAAO,IAAI,CAAC;aACb;AACD,YAAA,MAAM,EAAE,CAAC,QAAA,GAAyB,EAAE,KAClC,IAAI,CAAC,6BAA6B,CAAC,aAAa,EAAE,OAAO,EAAE,QAAQ,CAAC;SACvE,CAAC;KACH;AAED;;;;;;;;AAQG;AACI,IAAA,gBAAgB,CACrB,aAAqB;;AAErB,IAAA,OAAY,EACZ,SAAiB,EACjB,OAAA,GAA4B,EAAE,EAAA;QAE9B,MAAM,uCAAuC,GAC3C,OAAO,CAAC;QACV,uCAAuC,CAAC,mBAAmB,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QACvF,IAAI,CAAC,SAAS,EAAE;YACd,SAAS,GAAGE,qBAAY,EAAE,CAAC;AAC5B,SAAA;AAED,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,qCAAqC,EACrC,uCAAuC,EACvC,OAAO,cAAc,KAAI;AACvB,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,aAAa,CAC3C,aAAa,EACb,SAAS,EACT,OAAO,EACP,cAAc,CACf,CAAC;AACJ,SAAC,CACF,CAAC;KACH;AAED;;;;;;;;;AASG;IACI,yBAAyB,CAC9B,aAAqB,EACrB,aAAqB,EACrB,OAAe,EACf,SAAiB,EACjB,OAAA,GAA4B,EAAE,EAAA;QAE9B,MAAM,gDAAgD,GACpD,OAAO,CAAC;QACV,gDAAgD,CAAC,mBAAmB,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAChG,IAAI,CAAC,SAAS,EAAE;YACd,SAAS,GAAGA,qBAAY,EAAE,CAAC;AAC5B,SAAA;AAED,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,8CAA8C,EAC9C,gDAAgD,EAChD,OAAO,cAAc,KAAI;AACvB,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,sBAAsB,CACpD,aAAa,EACb,aAAa,EACb,OAAO,EACP,SAAS,EACT,cAAc,CACf,CAAC;AACJ,SAAC,CACF,CAAC;KACH;AAED;;;;;;;AAOG;AACI,IAAA,QAAQ,CACb,OAAe,EACf,yBAAkC,KAAK,EACvC,UAA4B,EAAE,EAAA;QAE9B,MAAM,sCAAsC,GAA2C,OAAO,CAAC;AAC/F,QAAA,sCAAsC,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;AAEvF,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,6BAA6B,EAC7B,sCAAsC,EACtC,OAAO,cAAc,KAAI;AACvB,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;AACxE,SAAC,CACF,CAAC;KACH;AAED;;;;;;AAMG;IACY,aAAa,CAC1B,OAA4C,EAC5C,iBAA+B,EAAA;;AAE/B,YAAA,IAAI,iBAAiB,CAAC,iBAAiB,IAAI,IAAI,EAAE;gBAC/C,MAAM,eAAe,GAAwC,OAAO,CAAC;AACrE,gBAAA,eAAe,CAAC,eAAe,GAAG,iBAAiB,CAAC,WAAW,CAAC;AAEhE,gBAAA,MAAM,YAAY,GAAG,MAAMH,aAAA,CAAA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA,CAAC;AAC/E,gBAAA,iBAAiB,CAAC,iBAAiB,GAAG,YAAY,CAAC,QAAQ,CAAC;gBAC5D,MAAM,MAAAA,aAAA,CAAA,YAAY,CAAA,CAAC;AACpB,aAAA;YACD,OAAO,iBAAiB,CAAC,iBAAiB,EAAE;AAC1C,gBAAA,MAAM,YAAY,GAAG,MAAAA,aAAA,CAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAC/D,iBAAiB,CAAC,iBAAiB,EACnC,OAAO,CACR,CAAA,CAAC;AAEF,gBAAA,iBAAiB,CAAC,iBAAiB,GAAG,YAAY,CAAC,QAAQ,CAAC;gBAC5D,MAAM,MAAAA,aAAA,CAAA,YAAY,CAAA,CAAC;AACpB,aAAA;SACF,CAAA,CAAA;AAAA,KAAA;AAED;;;AAGG;AACY,IAAA,YAAY,CACzB,OAA4C,EAAA;;;YAE5C,MAAM,CAAC,GAAG,EAAE,CAAC;;AAEb,gBAAA,KAAyB,IAAA,EAAA,GAAAC,mBAAA,CAAA,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA,EAAA,EAAA,EAAA,EAAA,GAAA,MAAAD,aAAA,CAAA,EAAA,CAAA,IAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,IAAA,GAAA;oBAA5C,MAAM,IAAI,WAAA,CAAA;AACnB,oBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;AAC/B,oBAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;wBACxB,MAAM,MAAAA,aAAA,CAAA,IAAI,CAAA,CAAC;AACZ,qBAAA;AACF,iBAAA;;;;;;;;;SACF,CAAA,CAAA;AAAA,KAAA;AAED;;;;;;;AAOG;IACI,UAAU,CACf,cAAyB,EACzB,sBAAA,GAAkC,KAAK,EACvC,cAAuB,EACvB,OAAA,GAA2C,EAAE,EAAA;QAE7C,IAAI,mCAAmC,GAAwC,OAAO,CAAC;AACvF,QAAA,mCAAmC,GAAG;AACpC,YAAA,eAAe,EAAE,cAAc;AAC/B,YAAA,eAAe,EAAE,cAAc;AAC/B,YAAA,sBAAsB,EAAE,sBAAsB;SAC/C,CAAC;QAEF,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,mCAAmC,CAAC,CAAC;QAEpE,OAAO;YACL,IAAI,GAAA;AACF,gBAAA,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;aACpB;YACD,CAAC,MAAM,CAAC,aAAa,CAAC,GAAA;AACpB,gBAAA,OAAO,IAAI,CAAC;aACb;AACD,YAAA,MAAM,EAAE,CAAC,QAAyB,GAAA,EAAE,KAClC,IAAI,CAAC,aAAa,CAAC,mCAAmC,EAAE,QAAQ,CAAC;SACpE,CAAC;KACH;AAED;;;;;;AAMG;AACI,IAAA,YAAY,CACjB,UAAiB,EACjB,OAAA,GAA4B,EAAE,EAAA;QAE9B,MAAM,kCAAkC,GAAuC,OAAO,CAAC;AACvF,QAAA,kCAAkC,CAAC,MAAM,GAAG,UAAU,CAAC;AAEvD,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,iCAAiC,EACjC,kCAAkC,EAClC,OAAO,cAAc,KAAI;YACvB,OAAO,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AAC3D,SAAC,CACF,CAAC;KACH;AAED;;;;;;;;;;;AAWG;AACI,IAAA,gBAAgB,CAAC,OAAe,EAAE,OAAA,GAA4B,EAAE,EAAA;AACrE,QAAA,MAAM,SAAS,GAAG,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;AAE5E,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,qCAAqC,EACrC,OAAO,EACP,OAAO,cAAc,KAAI;AACvB,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;AAClF,SAAC,CACF,CAAC;KACH;AAED;;;;;;AAMG;AACI,IAAA,WAAW,CAAC,OAAe,EAAE,OAAA,GAA4B,EAAE,EAAA;AAChE,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,gCAAgC,EAChC,OAAO,EACP,OAAO,cAAc,KAAI;AACvB,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;AACvE,SAAC,CACF,CAAC;KACH;AAED;;;;;;AAMG;AACI,IAAA,aAAa,CAClB,YAAoB,EACpB,OAAA,GAA4B,EAAE,EAAA;AAE9B,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,kCAAkC,EAClC,OAAO,EACP,OAAO,cAAc,KAAI;AACvB,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;AACvE,SAAC,CACF,CAAC;KACH;AAED;;;;;;AAMG;IACY,kBAAkB,CAC/B,OAAsC,EACtC,iBAA+B,EAAA;;AAE/B,YAAA,IAAI,iBAAiB,CAAC,iBAAiB,IAAI,IAAI,EAAE;gBAC/C,MAAM,eAAe,GAAkC,OAAO,CAAC;AAC/D,gBAAA,eAAe,CAAC,eAAe,GAAG,iBAAiB,CAAC,WAAW,CAAC;AAEhE,gBAAA,MAAM,YAAY,GAAG,MAAMA,aAAA,CAAA,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA,CAAC;AACzE,gBAAA,iBAAiB,CAAC,iBAAiB,GAAG,YAAY,CAAC,QAAQ,CAAC;gBAC5D,MAAM,MAAAA,aAAA,CAAA,YAAY,CAAA,CAAC;AACpB,aAAA;YACD,OAAO,iBAAiB,CAAC,iBAAiB,EAAE;AAC1C,gBAAA,MAAM,YAAY,GAAG,MAAAA,aAAA,CAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CACzD,iBAAiB,CAAC,iBAAiB,EACnC,OAAO,CACR,CAAA,CAAC;AAEF,gBAAA,iBAAiB,CAAC,iBAAiB,GAAG,YAAY,CAAC,QAAQ,CAAC;gBAC5D,MAAM,MAAAA,aAAA,CAAA,YAAY,CAAA,CAAC;AACpB,aAAA;SACF,CAAA,CAAA;AAAA,KAAA;AAED;;;AAGG;AACY,IAAA,iBAAiB,CAC9B,OAAsC,EAAA;;;YAEtC,MAAM,CAAC,GAAG,EAAE,CAAC;;AACb,gBAAA,KAAyB,IAAA,EAAA,GAAAC,mBAAA,CAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA,EAAA,EAAA,EAAA,EAAA,GAAA,MAAAD,aAAA,CAAA,EAAA,CAAA,IAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,IAAA,GAAA;oBAAjD,MAAM,IAAI,WAAA,CAAA;AACnB,oBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;AAC/B,oBAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;wBACxB,MAAM,MAAAA,aAAA,CAAA,IAAI,CAAA,CAAC;AACZ,qBAAA;AACF,iBAAA;;;;;;;;;SACF,CAAA,CAAA;AAAA,KAAA;AAED;;;;;;AAMG;AACI,IAAA,eAAe,CACpB,cAAuB,EACvB,OAAA,GAA2C,EAAE,EAAA;QAE7C,IAAI,6BAA6B,GAAkC,OAAO,CAAC;AAC3E,QAAA,6BAA6B,GAAG;AAC9B,YAAA,eAAe,EAAE,cAAc;SAChC,CAAC;QAEF,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,6BAA6B,CAAC,CAAC;QAEnE,OAAO;YACL,IAAI,GAAA;AACF,gBAAA,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;aACpB;YACD,CAAC,MAAM,CAAC,aAAa,CAAC,GAAA;AACpB,gBAAA,OAAO,IAAI,CAAC;aACb;AACD,YAAA,MAAM,EAAE,CAAC,QAAyB,GAAA,EAAE,KAClC,IAAI,CAAC,kBAAkB,CAAC,6BAA6B,EAAE,QAAQ,CAAC;SACnE,CAAC;KACH;AAED;;;;;;;;AAQG;IACI,gBAAgB,CACrB,YAAoB,EACpB,UAAkB,EAClB,MAAc,EACd,UAA4B,EAAE,EAAA;QAE9B,MAAM,4BAA4B,GAAiC,OAAO,CAAC;AAC3E,QAAA,MAAM,UAAU,GAAe;AAC7B,YAAA,YAAY,EAAE,UAAU;AACxB,YAAA,MAAM,EAAE,MAAM;SACf,CAAC;AACF,QAAA,4BAA4B,CAAC,UAAU,GAAG,UAAU,CAAC;AAErD,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,qCAAqC,EACrC,4BAA4B,EAC5B,OAAO,cAAc,KAAI;AACvB,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;AACnE,SAAC,CACF,CAAC;KACH;AAED;;;;;;AAMG;AACI,IAAA,gBAAgB,CACrB,YAAoB,EACpB,OAAA,GAA4B,EAAE,EAAA;AAE9B,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,qCAAqC,EACrC,OAAO,EACP,OAAO,cAAc,KAAI;AACvB,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;AACtE,SAAC,CACF,CAAC;KACH;AAED;;;;;;;AAOG;AACY,IAAA,cAAc,CAC3B,KAAa,EACb,OAAsC,EACtC,iBAA+B,EAAA;;AAE/B,YAAA,IAAI,iBAAiB,CAAC,iBAAiB,IAAI,IAAI,EAAE;AAC/C,gBAAA,MAAM,kBAAkB,GAAuB;AAC7C,oBAAA,KAAK,EAAE,KAAK;oBACZ,iBAAiB,EAAE,iBAAiB,CAAC,iBAAiB;iBACvD,CAAC;AACF,gBAAA,MAAM,WAAW,GAAG,MAAAA,aAAA,CAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAA,CAAC;AACpF,gBAAA,iBAAiB,CAAC,iBAAiB,GAAG,WAAW,CAAC,iBAAiB,CAAC;gBACpE,MAAM,MAAAA,aAAA,CAAA,WAAW,CAAA,CAAC;AACnB,aAAA;YACD,OAAO,iBAAiB,CAAC,iBAAiB,EAAE;AAC1C,gBAAA,MAAM,kBAAkB,GAAuB;AAC7C,oBAAA,KAAK,EAAE,KAAK;oBACZ,iBAAiB,EAAE,iBAAiB,CAAC,iBAAiB;iBACvD,CAAC;AACF,gBAAA,MAAM,WAAW,GAAG,MAAAA,aAAA,CAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAA,CAAC;AAEpF,gBAAA,iBAAiB,CAAC,iBAAiB,GAAG,WAAW,CAAC,iBAAiB,CAAC;gBACpE,MAAM,MAAAA,aAAA,CAAA,WAAW,CAAA,CAAC;AACnB,aAAA;SACF,CAAA,CAAA;AAAA,KAAA;AAED;;;;AAIG;IACY,aAAa,CAC1B,KAAa,EACb,OAAsC,EAAA;;;YAEtC,MAAM,CAAC,GAAG,EAAE,CAAC;;AAEb,gBAAA,KAAyB,IAAA,EAAA,GAAAC,mBAAA,CAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC,CAAA,EAAA,EAAA,EAAA,EAAA,GAAA,MAAAD,aAAA,CAAA,EAAA,CAAA,IAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,IAAA,GAAA;oBAApD,MAAM,IAAI,WAAA,CAAA;oBACnB,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,wBAAA,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;4BAC7B,MAAM,MAAAA,aAAA,CAAA,IAAI,CAAA,CAAC;AACZ,yBAAA;AACF,qBAAA;AACF,iBAAA;;;;;;;;;SACF,CAAA,CAAA;AAAA,KAAA;AAED;;;;;;AAMG;AACI,IAAA,UAAU,CACf,KAAa,EACb,cAAuB,EACvB,UAA2C,EAAE,EAAA;QAE7C,IAAI,6BAA6B,GAAkC,OAAO,CAAC;AAC3E,QAAA,6BAA6B,GAAG;AAC9B,YAAA,eAAe,EAAE,cAAc;SAChC,CAAC;QAEF,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,6BAA6B,CAAC,CAAC;QAEtE,OAAO;YACL,IAAI,GAAA;AACF,gBAAA,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;aACpB;YACD,CAAC,MAAM,CAAC,aAAa,CAAC,GAAA;AACpB,gBAAA,OAAO,IAAI,CAAC;aACb;AACD,YAAA,MAAM,EAAE,CAAC,QAAA,GAAyB,EAAE,KAClC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,6BAA6B,EAAE,QAAQ,CAAC;SACtE,CAAC;KACH;AACF;;;;"}