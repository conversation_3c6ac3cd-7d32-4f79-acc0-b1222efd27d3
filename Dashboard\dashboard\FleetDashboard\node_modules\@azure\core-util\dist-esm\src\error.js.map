{"version": 3, "file": "error.js", "sourceRoot": "", "sources": ["../../src/error.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,QAAQ,EAAE,MAAM,UAAU,CAAC;AAEpC;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,CAAU;IAChC,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE;QACf,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC;QAC3C,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,OAAO,KAAK,QAAQ,CAAC;QACjD,OAAO,OAAO,IAAI,UAAU,CAAC;KAC9B;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,eAAe,CAAC,CAAU;IACxC,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;QACd,OAAO,CAAC,CAAC,OAAO,CAAC;KAClB;SAAM;QACL,IAAI,WAAmB,CAAC;QACxB,IAAI;YACF,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,EAAE;gBAC9B,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;aACjC;iBAAM;gBACL,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;aACzB;SACF;QAAC,OAAO,GAAQ,EAAE;YACjB,WAAW,GAAG,6BAA6B,CAAC;SAC7C;QACD,OAAO,iBAAiB,WAAW,EAAE,CAAC;KACvC;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { isObject } from \"./object\";\n\n/**\n * Typeguard for an error object shape (has name and message)\n * @param e - Something caught by a catch clause.\n */\nexport function isError(e: unknown): e is Error {\n  if (isObject(e)) {\n    const hasName = typeof e.name === \"string\";\n    const hasMessage = typeof e.message === \"string\";\n    return hasName && hasMessage;\n  }\n  return false;\n}\n\n/**\n * Given what is thought to be an error object, return the message if possible.\n * If the message is missing, returns a stringified version of the input.\n * @param e - Something thrown from a try block\n * @returns The error message or a string of the input\n */\nexport function getErrorMessage(e: unknown): string {\n  if (isError(e)) {\n    return e.message;\n  } else {\n    let stringified: string;\n    try {\n      if (typeof e === \"object\" && e) {\n        stringified = JSON.stringify(e);\n      } else {\n        stringified = String(e);\n      }\n    } catch (err: any) {\n      stringified = \"[unable to stringify input]\";\n    }\n    return `Unknown error ${stringified}`;\n  }\n}\n"]}