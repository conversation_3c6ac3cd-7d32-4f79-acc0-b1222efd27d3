{"version": 3, "file": "httpClientCache.js", "sourceRoot": "", "sources": ["../../src/httpClientCache.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAc,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AAEhF,IAAI,gBAAwC,CAAC;AAE7C,MAAM,UAAU,0BAA0B;IACxC,IAAI,CAAC,gBAAgB,EAAE;QACrB,gBAAgB,GAAG,uBAAuB,EAAE,CAAC;KAC9C;IAED,OAAO,gBAAgB,CAAC;AAC1B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpClient, createDefaultHttpClient } from \"@azure/core-rest-pipeline\";\n\nlet cachedHttpClient: HttpClient | undefined;\n\nexport function getCachedDefaultHttpClient(): HttpClient {\n  if (!cachedHttpClient) {\n    cachedHttpClient = createDefaultHttpClient();\n  }\n\n  return cachedHttpClient;\n}\n"]}