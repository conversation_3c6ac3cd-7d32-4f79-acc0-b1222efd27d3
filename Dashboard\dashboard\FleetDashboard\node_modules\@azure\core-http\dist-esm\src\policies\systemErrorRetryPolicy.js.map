{"version": 3, "file": "systemErrorRetryPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/systemErrorRetryPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EACL,iBAAiB,GAIlB,MAAM,iBAAiB,CAAC;AACzB,OAAO,EACL,iCAAiC,EACjC,iCAAiC,EACjC,0BAA0B,EAC1B,6BAA6B,EAG7B,QAAQ,EACR,WAAW,EACX,eAAe,GAChB,MAAM,oCAAoC,CAAC;AAG5C,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEzC;;;;;;;GAOG;AACH,MAAM,UAAU,sBAAsB,CACpC,UAAmB,EACnB,aAAsB,EACtB,gBAAyB,EACzB,gBAAyB;IAEzB,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,EAAE,EAAE;YACnE,OAAO,IAAI,sBAAsB,CAC/B,UAAU,EACV,OAAO,EACP,UAAU,EACV,aAAa,EACb,gBAAgB,EAChB,gBAAgB,CACjB,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC;AAED;;;;;;GAMG;AACH,MAAM,OAAO,sBAAuB,SAAQ,iBAAiB;IAM3D,YACE,UAAyB,EACzB,OAA6B,EAC7B,UAAmB,EACnB,aAAsB,EACtB,gBAAyB,EACzB,gBAAyB;QAEzB,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,0BAA0B,CAAC;QACjF,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,6BAA6B,CAAC;QAC7F,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAC;YAChD,CAAC,CAAC,gBAAgB;YAClB,CAAC,CAAC,iCAAiC,CAAC;QACtC,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAC;YAChD,CAAC,CAAC,gBAAgB;YAClB,CAAC,CAAC,iCAAiC,CAAC;IACxC,CAAC;IAEM,WAAW,CAAC,OAAwB;QACzC,OAAO,IAAI,CAAC,WAAW;aACpB,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;aAC5B,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;IACnE,CAAC;CACF;AAED,KAAK,UAAU,KAAK,CAClB,MAA8B,EAC9B,OAAwB,EACxB,iBAAwC,EACxC,GAAgB,EAChB,SAAqB;IAErB,SAAS,GAAG,eAAe,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;IAEpD,SAAS,iBAAiB,CAAC,SAAiC,EAAE,KAAkB;QAC9E,IACE,KAAK;YACL,KAAK,CAAC,IAAI;YACV,CAAC,KAAK,CAAC,IAAI,KAAK,WAAW;gBACzB,KAAK,CAAC,IAAI,KAAK,iBAAiB;gBAChC,KAAK,CAAC,IAAI,KAAK,cAAc;gBAC7B,KAAK,CAAC,IAAI,KAAK,YAAY;gBAC3B,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC,EAC1B;YACA,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,WAAW,CAAC,MAAM,CAAC,UAAU,EAAE,iBAAiB,EAAE,SAAS,EAAE,iBAAiB,EAAE,GAAG,CAAC,EAAE;QACxF,mFAAmF;QACnF,IAAI;YACF,MAAM,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACrC,OAAO,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;SACxD;QAAC,OAAO,SAAc,EAAE;YACvB,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;SACxE;KACF;SAAM;QACL,IAAI,GAAG,EAAE;YACP,qFAAqF;YACrF,OAAO,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SACxC;QACD,OAAO,iBAAiB,CAAC;KAC1B;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n} from \"./requestPolicy\";\nimport {\n  DEFAULT_CLIENT_MAX_RETRY_INTERVAL,\n  DEFAULT_CLIENT_MIN_RETRY_INTERVAL,\n  DEFAULT_CLIENT_RETRY_COUNT,\n  DEFAULT_CLIENT_RETRY_INTERVAL,\n  RetryData,\n  RetryError,\n  isNumber,\n  shouldRetry,\n  updateRetryData,\n} from \"../util/exponentialBackoffStrategy\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\nimport { delay } from \"@azure/core-util\";\n\n/**\n * A policy that retries when there's a system error, identified by the codes \"ETIMEDOUT\", \"ESOCKETTIMEDOUT\", \"ECONNREFUSED\", \"ECONNRESET\" or \"ENOENT\".\n * @param retryCount - Maximum number of retries.\n * @param retryInterval - The client retry interval, in milliseconds.\n * @param minRetryInterval - The minimum retry interval, in milliseconds.\n * @param maxRetryInterval - The maximum retry interval, in milliseconds.\n * @returns An instance of the {@link SystemErrorRetryPolicy}\n */\nexport function systemErrorRetryPolicy(\n  retryCount?: number,\n  retryInterval?: number,\n  minRetryInterval?: number,\n  maxRetryInterval?: number\n): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new SystemErrorRetryPolicy(\n        nextPolicy,\n        options,\n        retryCount,\n        retryInterval,\n        minRetryInterval,\n        maxRetryInterval\n      );\n    },\n  };\n}\n\n/**\n * A policy that retries when there's a system error, identified by the codes \"ETIMEDOUT\", \"ESOCKETTIMEDOUT\", \"ECONNREFUSED\", \"ECONNRESET\" or \"ENOENT\".\n * @param retryCount - The client retry count.\n * @param retryInterval - The client retry interval, in milliseconds.\n * @param minRetryInterval - The minimum retry interval, in milliseconds.\n * @param maxRetryInterval - The maximum retry interval, in milliseconds.\n */\nexport class SystemErrorRetryPolicy extends BaseRequestPolicy {\n  retryCount: number;\n  retryInterval: number;\n  minRetryInterval: number;\n  maxRetryInterval: number;\n\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    retryCount?: number,\n    retryInterval?: number,\n    minRetryInterval?: number,\n    maxRetryInterval?: number\n  ) {\n    super(nextPolicy, options);\n    this.retryCount = isNumber(retryCount) ? retryCount : DEFAULT_CLIENT_RETRY_COUNT;\n    this.retryInterval = isNumber(retryInterval) ? retryInterval : DEFAULT_CLIENT_RETRY_INTERVAL;\n    this.minRetryInterval = isNumber(minRetryInterval)\n      ? minRetryInterval\n      : DEFAULT_CLIENT_MIN_RETRY_INTERVAL;\n    this.maxRetryInterval = isNumber(maxRetryInterval)\n      ? maxRetryInterval\n      : DEFAULT_CLIENT_MAX_RETRY_INTERVAL;\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    return this._nextPolicy\n      .sendRequest(request.clone())\n      .catch((error) => retry(this, request, error.response, error));\n  }\n}\n\nasync function retry(\n  policy: SystemErrorRetryPolicy,\n  request: WebResourceLike,\n  operationResponse: HttpOperationResponse,\n  err?: RetryError,\n  retryData?: RetryData\n): Promise<HttpOperationResponse> {\n  retryData = updateRetryData(policy, retryData, err);\n\n  function shouldPolicyRetry(_response?: HttpOperationResponse, error?: RetryError): boolean {\n    if (\n      error &&\n      error.code &&\n      (error.code === \"ETIMEDOUT\" ||\n        error.code === \"ESOCKETTIMEDOUT\" ||\n        error.code === \"ECONNREFUSED\" ||\n        error.code === \"ECONNRESET\" ||\n        error.code === \"ENOENT\")\n    ) {\n      return true;\n    }\n    return false;\n  }\n\n  if (shouldRetry(policy.retryCount, shouldPolicyRetry, retryData, operationResponse, err)) {\n    // If previous operation ended with an error and the policy allows a retry, do that\n    try {\n      await delay(retryData.retryInterval);\n      return policy._nextPolicy.sendRequest(request.clone());\n    } catch (nestedErr: any) {\n      return retry(policy, request, operationResponse, nestedErr, retryData);\n    }\n  } else {\n    if (err) {\n      // If the operation failed in the end, return all errors instead of just the last one\n      return Promise.reject(retryData.error);\n    }\n    return operationResponse;\n  }\n}\n"]}