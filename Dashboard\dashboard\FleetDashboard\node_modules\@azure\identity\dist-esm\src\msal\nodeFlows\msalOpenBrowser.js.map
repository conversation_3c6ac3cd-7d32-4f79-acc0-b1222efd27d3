{"version": 3, "file": "msalOpenBrowser.js", "sourceRoot": "", "sources": ["../../../../src/msal/nodeFlows/msalOpenBrowser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,KAAK,QAAQ,MAAM,kBAAkB,CAAC;AAC7C,OAAO,EAAE,QAAQ,EAAmB,MAAM,kBAAkB,CAAC;AAC7D,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AAGlF,OAAO,EAAE,0BAA0B,EAAE,MAAM,cAAc,CAAC;AAE1D,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EAAE,YAAY,EAAE,MAAM,UAAU,CAAC;AACxC,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,SAAS,MAAM,WAAW,CAAC;AAWlC;;;GAGG;AACH,MAAM,CAAC,MAAM,0BAA0B,GAAG;IACxC,IAAI;CACL,CAAC;AAEF;;;;GAIG;AACH,MAAM,OAAO,eAAgB,SAAQ,QAAQ;IAM3C,YAAY,OAA+B;QACzC,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;QAC5D,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QAEnC,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACtC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACpB,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;SAChB;QACD,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;IAC/B,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAC9B,OAA0C;QAE1C,OAAO,IAAI,CAAC,SAAU,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IACrD,CAAC;IAES,UAAU,CAClB,MAAgB,EAChB,OAAuC;QAEvC,OAAO,IAAI,OAAO,CAAc,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAClD,MAAM,eAAe,GAAa,EAAE,CAAC;YAErC,MAAM,eAAe,GAAG,CAAC,GAAyB,EAAE,GAAwB,EAAQ,EAAE;;gBACpF,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;oBACZ,MAAM,CACJ,IAAI,KAAK,CACP,0FAA0F,CAC3F,CACF,CAAC;oBACF,OAAO;iBACR;gBACD,IAAI,GAAQ,CAAC;gBACb,IAAI;oBACF,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;iBAC1C;gBAAC,OAAO,CAAM,EAAE;oBACf,MAAM,CACJ,IAAI,KAAK,CACP,0FAA0F,CAC3F,CACF,CAAC;oBACF,OAAO;iBACR;gBACD,MAAM,YAAY,GAAsC;oBACtD,IAAI,EAAE,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAE;oBACnC,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,MAAM,EAAE,MAAM;oBACd,SAAS,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS;oBAC7B,YAAY,EAAE,MAAA,IAAI,CAAC,SAAS,0CAAE,QAAQ;iBACvC,CAAC;gBAEF,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC;qBAClC,IAAI,CAAC,CAAC,YAAY,EAAE,EAAE;oBACrB,IAAI,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,OAAO,EAAE;wBACzB,IAAI,CAAC,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;qBAClE;oBACD,MAAM,cAAc,GAAG,mFAAmF,CAAC;oBAC3G,IAAI,YAAY,IAAI,YAAY,CAAC,SAAS,EAAE;wBAC1C,MAAM,kBAAkB,GAAG,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,SAAS,CAAC,OAAO,EAAE,CAAC;wBAC7D,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;wBACnB,GAAG,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;wBACxB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;wBAEjD,OAAO,CAAC;4BACN,kBAAkB;4BAClB,KAAK,EAAE,YAAY,CAAC,WAAW;yBAChC,CAAC,CAAC;qBACJ;yBAAM;wBACL,MAAM,YAAY,GAAG,WAAW,CAC9B,MAAM,EACN,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE,CACjF,CAAC;wBACF,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;wBACnB,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;wBACtB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBAExC,MAAM,CACJ,IAAI,KAAK,CACP,0FAA0F,CAC3F,CACF,CAAC;qBACH;oBACD,OAAO,EAAE,CAAC;oBACV,OAAO;gBACT,CAAC,CAAC;qBACD,KAAK,CAAC,GAAG,EAAE;oBACV,MAAM,YAAY,GAAG,WAAW,CAC9B,MAAM,EACN,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE,CACjF,CAAC;oBACF,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;oBACnB,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;oBACtB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAExC,MAAM,CACJ,IAAI,KAAK,CACP,0FAA0F,CAC3F,CACF,CAAC;oBACF,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC;YACP,CAAC,CAAC;YAEF,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;YAC/C,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;YAE9B,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE,CACvD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kDAAkD,IAAI,CAAC,IAAI,GAAG,CAAC,CACjF,CAAC;YAEF,SAAS,OAAO;gBACd,IAAI,MAAM,EAAE;oBACV,MAAM,CAAC,KAAK,EAAE,CAAC;iBAChB;gBAED,KAAK,MAAM,MAAM,IAAI,eAAe,EAAE;oBACpC,MAAM,CAAC,OAAO,EAAE,CAAC;iBAClB;gBAED,IAAI,MAAM,EAAE;oBACV,MAAM,CAAC,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,IAAI,EAAE,CAAC;iBACf;YACH,CAAC;YAED,GAAG,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAE/D,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;gBACtB,OAAO,EAAE,CAAC;gBACV,MAAM,IAAI,GAAI,GAAW,CAAC,IAAI,CAAC;gBAC/B,IAAI,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,YAAY,EAAE;oBAC9C,MAAM,CACJ,IAAI,0BAA0B,CAC5B;wBACE,uDAAuD,IAAI,CAAC,IAAI,GAAG;wBACnE,+DAA+D;wBAC/D,8EAA8E;qBAC/E,CAAC,IAAI,CAAC,GAAG,CAAC,CACZ,CACF,CAAC;iBACH;qBAAM;oBACL,MAAM,CACJ,IAAI,0BAA0B,CAC5B,kFAAkF,GAAG,CAAC,OAAO,EAAE,CAChG,CACF,CAAC;iBACH;YACH,CAAC,CAAC,CAAC;YAEH,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,GAAG,EAAE;gBACvB,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBAE1D,MAAM,WAAW,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,CAAC;gBACzC,IAAI,WAAW,EAAE;oBACf,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;wBACzC,OAAO,EAAE,CAAC;wBACV,MAAM,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;oBAC/B,CAAC,CAAC,CAAC;iBACJ;gBAED,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;oBACtB,OAAO,EAAE,CAAC;oBACV,MAAM,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAOO,KAAK,CAAC,eAAe,CAC3B,UAAoB,EACpB,OAAuC;QAEvC,qCAAqC;QACrC,MAAM,cAAc,GAAG,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAC;QACrD,6DAA6D;QAC7D,IAAI,CAAC,SAAS,GAAG,MAAM,cAAc,CAAC,iBAAiB,EAAE,CAAC;QAE1D,MAAM,qBAAqB,GAAqC;YAC9D,MAAM,EAAE,UAAU;YAClB,aAAa,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa;YACrC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,SAAS,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS;YAC7B,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS;YACvC,mBAAmB,EAAE,MAAM,EAAE,uBAAuB;SACrD,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAU,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;QAE7E,IAAI;YACF,4FAA4F;YAC5F,MAAM,0BAA0B,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;SACpF;QAAC,OAAO,CAAM,EAAE;YACf,MAAM,IAAI,0BAA0B,CAClC,yEAAyE,CAAC,CAAC,OAAO,EAAE,CACrF,CAAC;SACH;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as msalNode from \"@azure/msal-node\";\nimport { MsalNode, MsalNodeOptions } from \"./msalNodeCommon\";\nimport { credentialLogger, formatError, formatSuccess } from \"../../util/logging\";\nimport { AccessToken } from \"@azure/core-auth\";\nimport { CredentialFlowGetTokenOptions } from \"../credentials\";\nimport { CredentialUnavailableError } from \"../../errors\";\nimport { Socket } from \"net\";\nimport http from \"http\";\nimport { msalToPublic } from \"../utils\";\nimport open from \"open\";\nimport stoppable from \"stoppable\";\n\n/**\n * Options that can be passed to configure MSAL to handle authentication through opening a browser window.\n * @internal\n */\nexport interface MsalOpenBrowserOptions extends MsalNodeOptions {\n  redirectUri: string;\n  loginHint?: string;\n}\n\n/**\n * A call to open(), but mockable\n * @internal\n */\nexport const interactiveBrowserMockable = {\n  open,\n};\n\n/**\n * This MSAL client sets up a web server to listen for redirect callbacks, then calls to the MSAL's public application's `acquireTokenByDeviceCode` during `doGetToken`\n * to trigger the authentication flow, and then respond based on the values obtained from the redirect callback\n * @internal\n */\nexport class MsalOpenBrowser extends MsalNode {\n  private redirectUri: string;\n  private port: number;\n  private hostname: string;\n  private loginHint?: string;\n\n  constructor(options: MsalOpenBrowserOptions) {\n    super(options);\n    this.logger = credentialLogger(\"Node.js MSAL Open Browser\");\n    this.redirectUri = options.redirectUri;\n    this.loginHint = options.loginHint;\n\n    const url = new URL(this.redirectUri);\n    this.port = parseInt(url.port);\n    if (isNaN(this.port)) {\n      this.port = 80;\n    }\n    this.hostname = url.hostname;\n  }\n\n  private async acquireTokenByCode(\n    request: msalNode.AuthorizationCodeRequest\n  ): Promise<msalNode.AuthenticationResult | null> {\n    return this.publicApp!.acquireTokenByCode(request);\n  }\n\n  protected doGetToken(\n    scopes: string[],\n    options?: CredentialFlowGetTokenOptions\n  ): Promise<AccessToken> {\n    return new Promise<AccessToken>((resolve, reject) => {\n      const socketToDestroy: Socket[] = [];\n\n      const requestListener = (req: http.IncomingMessage, res: http.ServerResponse): void => {\n        if (!req.url) {\n          reject(\n            new Error(\n              `Interactive Browser Authentication Error \"Did not receive token with a valid expiration\"`\n            )\n          );\n          return;\n        }\n        let url: URL;\n        try {\n          url = new URL(req.url, this.redirectUri);\n        } catch (e: any) {\n          reject(\n            new Error(\n              `Interactive Browser Authentication Error \"Did not receive token with a valid expiration\"`\n            )\n          );\n          return;\n        }\n        const tokenRequest: msalNode.AuthorizationCodeRequest = {\n          code: url.searchParams.get(\"code\")!,\n          redirectUri: this.redirectUri,\n          scopes: scopes,\n          authority: options?.authority,\n          codeVerifier: this.pkceCodes?.verifier,\n        };\n\n        this.acquireTokenByCode(tokenRequest)\n          .then((authResponse) => {\n            if (authResponse?.account) {\n              this.account = msalToPublic(this.clientId, authResponse.account);\n            }\n            const successMessage = `Authentication Complete. You can close the browser and return to the application.`;\n            if (authResponse && authResponse.expiresOn) {\n              const expiresOnTimestamp = authResponse?.expiresOn.valueOf();\n              res.writeHead(200);\n              res.end(successMessage);\n              this.logger.getToken.info(formatSuccess(scopes));\n\n              resolve({\n                expiresOnTimestamp,\n                token: authResponse.accessToken,\n              });\n            } else {\n              const errorMessage = formatError(\n                scopes,\n                `${url.searchParams.get(\"error\")}. ${url.searchParams.get(\"error_description\")}`\n              );\n              res.writeHead(500);\n              res.end(errorMessage);\n              this.logger.getToken.info(errorMessage);\n\n              reject(\n                new Error(\n                  `Interactive Browser Authentication Error \"Did not receive token with a valid expiration\"`\n                )\n              );\n            }\n            cleanup();\n            return;\n          })\n          .catch(() => {\n            const errorMessage = formatError(\n              scopes,\n              `${url.searchParams.get(\"error\")}. ${url.searchParams.get(\"error_description\")}`\n            );\n            res.writeHead(500);\n            res.end(errorMessage);\n            this.logger.getToken.info(errorMessage);\n\n            reject(\n              new Error(\n                `Interactive Browser Authentication Error \"Did not receive token with a valid expiration\"`\n              )\n            );\n            cleanup();\n          });\n      };\n\n      const app = http.createServer(requestListener);\n      const server = stoppable(app);\n\n      const listen = app.listen(this.port, this.hostname, () =>\n        this.logger.info(`InteractiveBrowserCredential listening on port ${this.port}!`)\n      );\n\n      function cleanup(): void {\n        if (listen) {\n          listen.close();\n        }\n\n        for (const socket of socketToDestroy) {\n          socket.destroy();\n        }\n\n        if (server) {\n          server.close();\n          server.stop();\n        }\n      }\n\n      app.on(\"connection\", (socket) => socketToDestroy.push(socket));\n\n      app.on(\"error\", (err) => {\n        cleanup();\n        const code = (err as any).code;\n        if (code === \"EACCES\" || code === \"EADDRINUSE\") {\n          reject(\n            new CredentialUnavailableError(\n              [\n                `InteractiveBrowserCredential: Access denied to port ${this.port}.`,\n                `Try sending a redirect URI with a different port, as follows:`,\n                '`new InteractiveBrowserCredential({ redirectUri: \"http://localhost:1337\" })`',\n              ].join(\" \")\n            )\n          );\n        } else {\n          reject(\n            new CredentialUnavailableError(\n              `InteractiveBrowserCredential: Failed to start the necessary web server. Error: ${err.message}`\n            )\n          );\n        }\n      });\n\n      app.on(\"listening\", () => {\n        const openPromise = this.openAuthCodeUrl(scopes, options);\n\n        const abortSignal = options?.abortSignal;\n        if (abortSignal) {\n          abortSignal.addEventListener(\"abort\", () => {\n            cleanup();\n            reject(new Error(\"Aborted\"));\n          });\n        }\n\n        openPromise.catch((e) => {\n          cleanup();\n          reject(e);\n        });\n      });\n    });\n  }\n\n  private pkceCodes?: {\n    verifier: string;\n    challenge: string;\n  };\n\n  private async openAuthCodeUrl(\n    scopeArray: string[],\n    options?: CredentialFlowGetTokenOptions\n  ): Promise<void> {\n    // Initialize CryptoProvider instance\n    const cryptoProvider = new msalNode.CryptoProvider();\n    // Generate PKCE Codes before starting the authorization flow\n    this.pkceCodes = await cryptoProvider.generatePkceCodes();\n\n    const authCodeUrlParameters: msalNode.AuthorizationUrlRequest = {\n      scopes: scopeArray,\n      correlationId: options?.correlationId,\n      redirectUri: this.redirectUri,\n      authority: options?.authority,\n      claims: options?.claims,\n      loginHint: this.loginHint,\n      codeChallenge: this.pkceCodes.challenge,\n      codeChallengeMethod: \"S256\", // Use SHA256 Algorithm\n    };\n\n    const response = await this.publicApp!.getAuthCodeUrl(authCodeUrlParameters);\n\n    try {\n      // A new instance on macOS only which allows it to not hang, does not fix the issue on linux\n      await interactiveBrowserMockable.open(response, { wait: true, newInstance: true });\n    } catch (e: any) {\n      throw new CredentialUnavailableError(\n        `InteractiveBrowserCredential: Could not open a browser window. Error: ${e.message}`\n      );\n    }\n  }\n}\n"]}