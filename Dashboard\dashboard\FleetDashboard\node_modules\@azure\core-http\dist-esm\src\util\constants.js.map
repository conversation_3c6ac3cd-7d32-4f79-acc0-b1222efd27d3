{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../../../src/util/constants.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAClC;;GAEG;AACH,MAAM,CAAC,MAAM,SAAS,GAAG;IACvB;;OAEG;IACH,eAAe,EAAE,OAAO;IAExB;;OAEG;IACH,IAAI,EAAE,OAAO;IAEb;;OAEG;IACH,KAAK,EAAE,QAAQ;IAEf;;OAEG;IACH,UAAU,EAAE,YAAY;IAExB;;OAEG;IACH,WAAW,EAAE,aAAa;IAE1B;;OAEG;IACH,QAAQ,EAAE,UAAU;IAEpB;;OAEG;IACH,SAAS,EAAE,WAAW;IAEtB,aAAa,EAAE;QACb;;WAEG;QACH,SAAS,EAAE;YACT,GAAG,EAAE,KAAK;YACV,GAAG,EAAE,KAAK;YACV,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,OAAO;YACd,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,OAAO;SACf;QAED,WAAW,EAAE;YACX,eAAe,EAAE,GAAG;YACpB,kBAAkB,EAAE,GAAG;SACxB;KACF;IAED;;OAEG;IACH,eAAe,EAAE;QACf;;WAEG;QACH,aAAa,EAAE,eAAe;QAE9B,oBAAoB,EAAE,QAAQ;QAE9B;;;;WAIG;QACH,WAAW,EAAE,aAAa;QAE1B;;WAEG;QACH,UAAU,EAAE,YAAY;KACzB;CACF,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n/**\n * A set of constants used internally when processing requests.\n */\nexport const Constants = {\n  /**\n   * The core-http version\n   */\n  coreHttpVersion: \"2.3.2\",\n\n  /**\n   * Specifies HTTP.\n   */\n  HTTP: \"http:\",\n\n  /**\n   * Specifies HTTPS.\n   */\n  HTTPS: \"https:\",\n\n  /**\n   * Specifies HTTP Proxy.\n   */\n  HTTP_PROXY: \"HTTP_PROXY\",\n\n  /**\n   * Specifies HTTPS Proxy.\n   */\n  HTTPS_PROXY: \"HTTPS_PROXY\",\n\n  /**\n   * Specifies NO Proxy.\n   */\n  NO_PROXY: \"NO_PROXY\",\n\n  /**\n   * Specifies ALL Proxy.\n   */\n  ALL_PROXY: \"ALL_PROXY\",\n\n  HttpConstants: {\n    /**\n     * Http Verbs\n     */\n    HttpVerbs: {\n      PUT: \"PUT\",\n      GET: \"GET\",\n      DELETE: \"DELETE\",\n      POST: \"POST\",\n      MERGE: \"MERGE\",\n      HEAD: \"HEAD\",\n      PATCH: \"PATCH\",\n    },\n\n    StatusCodes: {\n      TooManyRequests: 429,\n      ServiceUnavailable: 503,\n    },\n  },\n\n  /**\n   * Defines constants for use with HTTP headers.\n   */\n  HeaderConstants: {\n    /**\n     * The Authorization header.\n     */\n    AUTHORIZATION: \"authorization\",\n\n    AUTHORIZATION_SCHEME: \"Bearer\",\n\n    /**\n     * The Retry-After response-header field can be used with a 503 (Service\n     * Unavailable) or 349 (Too Many Requests) responses to indicate how long\n     * the service is expected to be unavailable to the requesting client.\n     */\n    RETRY_AFTER: \"Retry-After\",\n\n    /**\n     * The UserAgent header.\n     */\n    USER_AGENT: \"User-Agent\",\n  },\n};\n"]}