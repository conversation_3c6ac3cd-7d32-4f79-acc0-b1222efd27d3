{"version": 3, "file": "isNode.js", "sourceRoot": "", "sources": ["../../src/isNode.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;AAElC;;GAEG;AACH,MAAM,CAAC,MAAM,MAAM,GACjB,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,MAAA,OAAO,CAAC,QAAQ,0CAAE,IAAI,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * A constant that indicates whether the environment the code is running is Node.JS.\n */\nexport const isNode =\n  typeof process !== \"undefined\" && <PERSON><PERSON><PERSON>(process.version) && <PERSON><PERSON><PERSON>(process.versions?.node);\n"]}