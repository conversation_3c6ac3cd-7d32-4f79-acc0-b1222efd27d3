{"version": 3, "file": "msalAuthorizationCode.js", "sourceRoot": "", "sources": ["../../../../src/msal/nodeFlows/msalAuthorizationCode.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,QAAQ,EAAmB,MAAM,kBAAkB,CAAC;AAG7D,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAYtD;;;;GAIG;AACH,MAAM,OAAO,qBAAsB,SAAQ,QAAQ;IAIjD,YAAY,OAAqC;QAC/C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC,iCAAiC,CAAC,CAAC;QAClE,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAC;QACnD,IAAI,OAAO,CAAC,YAAY,EAAE;YACxB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;SAC1D;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,OAAkD;QACrE,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,OAAO,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,SAAS,CAAE,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IAC3E,CAAC;IAES,KAAK,CAAC,UAAU,CACxB,MAAgB,EAChB,OAAuC;;QAEvC,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,CAAA,MAAA,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,SAAS,CAAC,0CAAE,kBAAkB,CAAC;gBAChF,MAAM;gBACN,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,IAAI,EAAE,IAAI,CAAC,iBAAiB;gBAC5B,aAAa,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa;gBACrC,SAAS,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS;gBAC7B,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM;aACxB,CAAC,CAAA,CAAC;YACH,yDAAyD;YACzD,8FAA8F;YAC9F,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,CAAC;SACtE;QAAC,OAAO,GAAQ,EAAE;YACjB,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;SAC9C;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { MsalN<PERSON>, MsalNodeOptions } from \"./msalNodeCommon\";\nimport { AccessToken } from \"@azure/core-auth\";\nimport { CredentialFlowGetTokenOptions } from \"../credentials\";\nimport { credentialLogger } from \"../../util/logging\";\n\n/**\n * Options that can be passed to configure MSAL to handle authentication through opening a browser window.\n * @internal\n */\nexport interface MsalAuthorizationCodeOptions extends MsalNodeOptions {\n  redirectUri: string;\n  authorizationCode: string;\n  clientSecret?: string;\n}\n\n/**\n * This MSAL client sets up a web server to listen for redirect callbacks, then calls to the MSAL's public application's `acquireTokenByDeviceCode` during `doGetToken`\n * to trigger the authentication flow, and then respond based on the values obtained from the redirect callback\n * @internal\n */\nexport class MsalAuthorizationCode extends MsalNode {\n  private redirectUri: string;\n  private authorizationCode: string;\n\n  constructor(options: MsalAuthorizationCodeOptions) {\n    super(options);\n    this.logger = credentialLogger(\"Node.js MSAL Authorization Code\");\n    this.redirectUri = options.redirectUri;\n    this.authorizationCode = options.authorizationCode;\n    if (options.clientSecret) {\n      this.msalConfig.auth.clientSecret = options.clientSecret;\n    }\n  }\n\n  async getAuthCodeUrl(options: { scopes: string[]; redirectUri: string }): Promise<string> {\n    await this.init();\n    return (this.confidentialApp || this.publicApp)!.getAuthCodeUrl(options);\n  }\n\n  protected async doGetToken(\n    scopes: string[],\n    options?: CredentialFlowGetTokenOptions\n  ): Promise<AccessToken> {\n    try {\n      const result = await (this.confidentialApp || this.publicApp)?.acquireTokenByCode({\n        scopes,\n        redirectUri: this.redirectUri,\n        code: this.authorizationCode,\n        correlationId: options?.correlationId,\n        authority: options?.authority,\n        claims: options?.claims,\n      });\n      // The Client Credential flow does not return an account,\n      // so each time getToken gets called, we will have to acquire a new token through the service.\n      return this.handleResult(scopes, this.clientId, result || undefined);\n    } catch (err: any) {\n      throw this.handleError(scopes, err, options);\n    }\n  }\n}\n"]}