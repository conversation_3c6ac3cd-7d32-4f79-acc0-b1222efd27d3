{"version": 3, "file": "digitalTwinsClient.js", "sourceRoot": "", "sources": ["../../src/digitalTwinsClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;AAElC,4CAA4C;AAE5C,OAAO,EAKL,+BAA+B,EAC/B,yBAAyB,EACzB,YAAY,GAEb,MAAM,kBAAkB,CAAC;AAE1B,OAAO,EAAE,oBAAoB,IAAI,eAAe,EAAE,MAAM,kCAAkC,CAAC;AAsC3F,OAAO,EAAE,aAAa,EAAE,MAAM,WAAW,CAAC;AAC1C,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAElC,MAAM,CAAC,MAAM,WAAW,GAAW,OAAO,CAAC;AAS3C,MAAM,0BAA0B,GAAG,yCAAyC,CAAC;AAE7E;;GAEG;AACH,MAAM,OAAO,kBAAkB;IAM7B;;;;;;;;;;;;;;;OAeG;IACH,YACE,WAAmB,EACnB,UAA2B,EAC3B,UAAqC,EAAE;QAEvC,MAAM,UAAU,GAAG,+BAA+B,CAAC,UAAU,EAAE,0BAA0B,CAAC,CAAC;QAC3F,MAAM,OAAO,GAAG,+BAA+B,WAAW,EAAE,CAAC;QAE7D,MAAM,EAAE,UAAU,KAAyB,OAAO,EAA3B,eAAe,UAAK,OAAO,EAA5C,cAAkC,CAAU,CAAC;QACnD,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE;YACrC,eAAe,CAAC,gBAAgB,GAAG,EAAE,CAAC;SACvC;QACD,IAAI,eAAe,CAAC,gBAAgB,CAAC,eAAe,EAAE;YACpD,eAAe,CAAC,gBAAgB,CAAC,eAAe,GAAG,GAAG,eAAe,CAAC,gBAAgB,CAAC,eAAe,IAAI,OAAO,EAAE,CAAC;SACrH;aAAM;YACL,eAAe,CAAC,gBAAgB,CAAC,eAAe,GAAG,OAAO,CAAC;SAC5D;QAED,MAAM,uBAAuB,mCACxB,eAAe,GACf;YACD,cAAc,EAAE;gBACd,MAAM,EAAE,MAAM,CAAC,IAAI;gBACnB,kBAAkB,EAAE,CAAC,iBAAiB,CAAC;aACxC;SACF,CACF,CAAC;QAEF,MAAM,QAAQ,GAAG,yBAAyB,CAAC,uBAAuB,EAAE,UAAU,CAAC,CAAC;QAEhF,IAAI,CAAC,MAAM,GAAG,IAAI,eAAe,iBAC/B,QAAQ,EAAE,WAAW,EACrB,UAAU,IACP,QAAQ,EACX,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACI,cAAc,CACnB,aAAqB,EACrB,UAA4B,EAAE;QAE9B,OAAO,aAAa,CAAC,QAAQ,CAC3B,mCAAmC,EACnC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;QACzE,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACI,iBAAiB,CACtB,aAAqB,EACrB,eAAuB,EACvB,UAAyC,EAAE;QAE3C,OAAO,aAAa,CAAC,QAAQ,CAC3B,sCAAsC,EACtC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;YAC5C,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;QAC9E,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;OAUG;IACI,iBAAiB,CACtB,aAAqB;IACrB,iIAAiI;IACjI,SAAc,EACd,UAA4C,EAAE;QAE9C,OAAO,aAAa,CAAC,QAAQ,CAC3B,sCAAsC,EACtC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;QACnF,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACI,iBAAiB,CACtB,aAAqB,EACrB,UAA4C,EAAE;QAE9C,OAAO,aAAa,CAAC,QAAQ,CAC3B,sCAAsC,EACtC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;QACxE,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACI,YAAY,CACjB,aAAqB,EACrB,aAAqB,EACrB,UAA4B,EAAE;QAE9B,OAAO,aAAa,CAAC,QAAQ,CAC3B,iCAAiC,EACjC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,aAAa,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;QAC7F,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;OAUG;IACI,eAAe,CACpB,aAAqB,EACrB,aAAqB,EACrB,SAAgB,EAChB,UAAqD,EAAE;QAEvD,OAAO,aAAa,CAAC,QAAQ,CAC3B,oCAAoC,EACpC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,eAAe,CAC7C,aAAa,EACb,aAAa,EACb,SAAS,EACT,cAAc,CACf,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACI,eAAe,CACpB,aAAqB,EACrB,cAAsB,EACtB,UAA4B,EAAE;QAE9B,OAAO,aAAa,CAAC,QAAQ,CAC3B,oCAAoC,EACpC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,mBAAmB,CACjD,aAAa,EACb,cAAc,EACd,cAAc,CACf,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACI,kBAAkB,CACvB,aAAqB,EACrB,cAAsB;IACtB,iIAAiI;IACjI,YAAiB,EACjB,UAAqD,EAAE;QAEvD,OAAO,aAAa,CAAC,QAAQ,CAC3B,uCAAuC,EACvC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,eAAe,CAC7C,aAAa,EACb,cAAc,EACd,YAAY,EACZ,cAAc,CACf,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACI,kBAAkB,CACvB,aAAqB,EACrB,cAAsB,EACtB,SAAgB,EAChB,UAAwD,EAAE;QAE1D,OAAO,aAAa,CAAC,QAAQ,CAC3B,uCAAuC,EACvC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,kBAAkB,CAChD,aAAa,EACb,cAAc,EACd,SAAS,EACT,cAAc,CACf,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACI,kBAAkB,CACvB,aAAqB,EACrB,cAAsB,EACtB,UAAwD,EAAE;QAE1D,OAAO,aAAa,CAAC,QAAQ,CAC3B,uCAAuC,EACvC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,kBAAkB,CAChD,aAAa,EACb,cAAc,EACd,cAAc,CACf,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACY,qBAAqB,CAClC,aAAqB,EACrB,OAAyB,EACzB,iBAA+B;;YAE/B,IAAI,iBAAiB,CAAC,iBAAiB,IAAI,IAAI,EAAE;gBAC/C,MAAM,eAAe,qBAChB,OAAO,CACX,CAAC;gBACF,MAAM,wBAAwB,GAAG,cAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,iBAAiB,CAC/E,aAAa,EACb,eAAe,CAChB,CAAA,CAAC;gBACF,iBAAiB,CAAC,iBAAiB,GAAG,wBAAwB,CAAC,QAAQ,CAAC;gBACxE,oBAAM,wBAAwB,CAAA,CAAC;aAChC;YACD,OAAO,iBAAiB,CAAC,iBAAiB,EAAE;gBAC1C,MAAM,wBAAwB,GAAG,cAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,qBAAqB,CACnF,EAAE,EACF,iBAAiB,CAAC,iBAAiB,EACnC,OAAO,CACR,CAAA,CAAC;gBAEF,iBAAiB,CAAC,iBAAiB,GAAG,wBAAwB,CAAC,QAAQ,CAAC;gBACxE,oBAAM,wBAAwB,CAAA,CAAC;aAChC;QACH,CAAC;KAAA;IAED;;;OAGG;IACY,oBAAoB,CACjC,aAAqB,EACrB,OAAyB;;;;gBAEzB,KAAyB,IAAA,KAAA,cAAA,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE,CAAC,CAAA,IAAA;oBAApE,MAAM,IAAI,WAAA,CAAA;oBACnB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;oBAC/B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;wBACxB,oBAAM,IAAI,CAAA,CAAC;qBACZ;iBACF;;;;;;;;;QACH,CAAC;KAAA;IAED;;;;OAIG;IACI,iBAAiB,CACtB,aAAqB,EACrB,UAA2C,EAAE;QAE7C,MAAM,IAAI,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QAE/D,OAAO;YACL,IAAI;gBACF,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;YACrB,CAAC;YACD,CAAC,MAAM,CAAC,aAAa,CAAC;gBACpB,OAAO,IAAI,CAAC;YACd,CAAC;YACD,MAAM,EAAE,CAAC,WAAyB,EAAE,EAAE,EAAE,CACtC,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,OAAO,EAAE,QAAQ,CAAC;SAC/D,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACY,6BAA6B,CAC1C,aAAqB,EACrB,OAAyB,EACzB,iBAA+B;;YAE/B,IAAI,iBAAiB,CAAC,iBAAiB,IAAI,IAAI,EAAE;gBAC/C,MAAM,eAAe,qBAChB,OAAO,CACX,CAAC;gBACF,MAAM,iCAAiC,GACrC,cAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,yBAAyB,CAAC,aAAa,EAAE,eAAe,CAAC,CAAA,CAAC;gBAC3F,iBAAiB,CAAC,iBAAiB,GAAG,iCAAiC,CAAC,QAAQ,CAAC;gBACjF,oBAAM,iCAAiC,CAAA,CAAC;aACzC;YACD,OAAO,iBAAiB,CAAC,iBAAiB,EAAE;gBAC1C,MAAM,iCAAiC,GACrC,cAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,6BAA6B,CAC1D,EAAE,EACF,iBAAiB,CAAC,iBAAiB,EACnC,OAAO,CACR,CAAA,CAAC;gBAEJ,iBAAiB,CAAC,iBAAiB,GAAG,iCAAiC,CAAC,QAAQ,CAAC;gBACjF,oBAAM,iCAAiC,CAAA,CAAC;aACzC;QACH,CAAC;KAAA;IAED;;;;OAIG;IACY,4BAA4B,CACzC,aAAqB,EACrB,OAAyB;;;;gBAEzB,KAAyB,IAAA,KAAA,cAAA,IAAI,CAAC,6BAA6B,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE,CAAC,CAAA,IAAA;oBAA5E,MAAM,IAAI,WAAA,CAAA;oBACnB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;oBAC/B,cAAA,KAAK,CAAC,CAAC,iBAAA,cAAA,KAAK,CAAA,CAAA,CAAA,CAAC;iBACd;;;;;;;;;QACH,CAAC;KAAA;IAED;;;;OAIG;IACI,yBAAyB,CAC9B,aAAqB,EACrB,UAA2C,EAAE;QAK7C,MAAM,IAAI,GAAG,IAAI,CAAC,4BAA4B,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QAEvE,OAAO;YACL,IAAI;gBACF,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;YACrB,CAAC;YACD,CAAC,MAAM,CAAC,aAAa,CAAC;gBACpB,OAAO,IAAI,CAAC;YACd,CAAC;YACD,MAAM,EAAE,CAAC,WAAyB,EAAE,EAAE,EAAE,CACtC,IAAI,CAAC,6BAA6B,CAAC,aAAa,EAAE,OAAO,EAAE,QAAQ,CAAC;SACvE,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACI,gBAAgB,CACrB,aAAqB;IACrB,iIAAiI;IACjI,OAAY,EACZ,SAAiB,EACjB,UAA4B,EAAE;QAE9B,MAAM,uCAAuC,GAC3C,OAAO,CAAC;QACV,uCAAuC,CAAC,mBAAmB,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QACvF,IAAI,CAAC,SAAS,EAAE;YACd,SAAS,GAAG,YAAY,EAAE,CAAC;SAC5B;QAED,OAAO,aAAa,CAAC,QAAQ,CAC3B,qCAAqC,EACrC,uCAAuC,EACvC,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,aAAa,CAC3C,aAAa,EACb,SAAS,EACT,OAAO,EACP,cAAc,CACf,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACI,yBAAyB,CAC9B,aAAqB,EACrB,aAAqB,EACrB,OAAe,EACf,SAAiB,EACjB,UAA4B,EAAE;QAE9B,MAAM,gDAAgD,GACpD,OAAO,CAAC;QACV,gDAAgD,CAAC,mBAAmB,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAChG,IAAI,CAAC,SAAS,EAAE;YACd,SAAS,GAAG,YAAY,EAAE,CAAC;SAC5B;QAED,OAAO,aAAa,CAAC,QAAQ,CAC3B,8CAA8C,EAC9C,gDAAgD,EAChD,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,sBAAsB,CACpD,aAAa,EACb,aAAa,EACb,OAAO,EACP,SAAS,EACT,cAAc,CACf,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACI,QAAQ,CACb,OAAe,EACf,yBAAkC,KAAK,EACvC,UAA4B,EAAE;QAE9B,MAAM,sCAAsC,GAA2C,OAAO,CAAC;QAC/F,sCAAsC,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;QAEvF,OAAO,aAAa,CAAC,QAAQ,CAC3B,6BAA6B,EAC7B,sCAAsC,EACtC,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;QACxE,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACY,aAAa,CAC1B,OAA4C,EAC5C,iBAA+B;;YAE/B,IAAI,iBAAiB,CAAC,iBAAiB,IAAI,IAAI,EAAE;gBAC/C,MAAM,eAAe,GAAwC,OAAO,CAAC;gBACrE,eAAe,CAAC,eAAe,GAAG,iBAAiB,CAAC,WAAW,CAAC;gBAEhE,MAAM,YAAY,GAAG,cAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA,CAAC;gBAC/E,iBAAiB,CAAC,iBAAiB,GAAG,YAAY,CAAC,QAAQ,CAAC;gBAC5D,oBAAM,YAAY,CAAA,CAAC;aACpB;YACD,OAAO,iBAAiB,CAAC,iBAAiB,EAAE;gBAC1C,MAAM,YAAY,GAAG,cAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAC/D,iBAAiB,CAAC,iBAAiB,EACnC,OAAO,CACR,CAAA,CAAC;gBAEF,iBAAiB,CAAC,iBAAiB,GAAG,YAAY,CAAC,QAAQ,CAAC;gBAC5D,oBAAM,YAAY,CAAA,CAAC;aACpB;QACH,CAAC;KAAA;IAED;;;OAGG;IACY,YAAY,CACzB,OAA4C;;;YAE5C,MAAM,CAAC,GAAG,EAAE,CAAC;;gBAEb,KAAyB,IAAA,KAAA,cAAA,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA,IAAA;oBAA5C,MAAM,IAAI,WAAA,CAAA;oBACnB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;oBAC/B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;wBACxB,oBAAM,IAAI,CAAA,CAAC;qBACZ;iBACF;;;;;;;;;QACH,CAAC;KAAA;IAED;;;;;;;OAOG;IACI,UAAU,CACf,cAAyB,EACzB,yBAAkC,KAAK,EACvC,cAAuB,EACvB,UAA2C,EAAE;QAE7C,IAAI,mCAAmC,GAAwC,OAAO,CAAC;QACvF,mCAAmC,GAAG;YACpC,eAAe,EAAE,cAAc;YAC/B,eAAe,EAAE,cAAc;YAC/B,sBAAsB,EAAE,sBAAsB;SAC/C,CAAC;QAEF,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,mCAAmC,CAAC,CAAC;QAEpE,OAAO;YACL,IAAI;gBACF,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;YACrB,CAAC;YACD,CAAC,MAAM,CAAC,aAAa,CAAC;gBACpB,OAAO,IAAI,CAAC;YACd,CAAC;YACD,MAAM,EAAE,CAAC,WAAyB,EAAE,EAAE,EAAE,CACtC,IAAI,CAAC,aAAa,CAAC,mCAAmC,EAAE,QAAQ,CAAC;SACpE,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACI,YAAY,CACjB,UAAiB,EACjB,UAA4B,EAAE;QAE9B,MAAM,kCAAkC,GAAuC,OAAO,CAAC;QACvF,kCAAkC,CAAC,MAAM,GAAG,UAAU,CAAC;QAEvD,OAAO,aAAa,CAAC,QAAQ,CAC3B,iCAAiC,EACjC,kCAAkC,EAClC,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC3D,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;OAWG;IACI,gBAAgB,CAAC,OAAe,EAAE,UAA4B,EAAE;QACrE,MAAM,SAAS,GAAG,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;QAE5E,OAAO,aAAa,CAAC,QAAQ,CAC3B,qCAAqC,EACrC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;QAClF,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACI,WAAW,CAAC,OAAe,EAAE,UAA4B,EAAE;QAChE,OAAO,aAAa,CAAC,QAAQ,CAC3B,gCAAgC,EAChC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;QACvE,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACI,aAAa,CAClB,YAAoB,EACpB,UAA4B,EAAE;QAE9B,OAAO,aAAa,CAAC,QAAQ,CAC3B,kCAAkC,EAClC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;QACvE,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACY,kBAAkB,CAC/B,OAAsC,EACtC,iBAA+B;;YAE/B,IAAI,iBAAiB,CAAC,iBAAiB,IAAI,IAAI,EAAE;gBAC/C,MAAM,eAAe,GAAkC,OAAO,CAAC;gBAC/D,eAAe,CAAC,eAAe,GAAG,iBAAiB,CAAC,WAAW,CAAC;gBAEhE,MAAM,YAAY,GAAG,cAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA,CAAC;gBACzE,iBAAiB,CAAC,iBAAiB,GAAG,YAAY,CAAC,QAAQ,CAAC;gBAC5D,oBAAM,YAAY,CAAA,CAAC;aACpB;YACD,OAAO,iBAAiB,CAAC,iBAAiB,EAAE;gBAC1C,MAAM,YAAY,GAAG,cAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CACzD,iBAAiB,CAAC,iBAAiB,EACnC,OAAO,CACR,CAAA,CAAC;gBAEF,iBAAiB,CAAC,iBAAiB,GAAG,YAAY,CAAC,QAAQ,CAAC;gBAC5D,oBAAM,YAAY,CAAA,CAAC;aACpB;QACH,CAAC;KAAA;IAED;;;OAGG;IACY,iBAAiB,CAC9B,OAAsC;;;YAEtC,MAAM,CAAC,GAAG,EAAE,CAAC;;gBACb,KAAyB,IAAA,KAAA,cAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA,IAAA;oBAAjD,MAAM,IAAI,WAAA,CAAA;oBACnB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;oBAC/B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;wBACxB,oBAAM,IAAI,CAAA,CAAC;qBACZ;iBACF;;;;;;;;;QACH,CAAC;KAAA;IAED;;;;;;OAMG;IACI,eAAe,CACpB,cAAuB,EACvB,UAA2C,EAAE;QAE7C,IAAI,6BAA6B,GAAkC,OAAO,CAAC;QAC3E,6BAA6B,GAAG;YAC9B,eAAe,EAAE,cAAc;SAChC,CAAC;QAEF,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,6BAA6B,CAAC,CAAC;QAEnE,OAAO;YACL,IAAI;gBACF,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;YACrB,CAAC;YACD,CAAC,MAAM,CAAC,aAAa,CAAC;gBACpB,OAAO,IAAI,CAAC;YACd,CAAC;YACD,MAAM,EAAE,CAAC,WAAyB,EAAE,EAAE,EAAE,CACtC,IAAI,CAAC,kBAAkB,CAAC,6BAA6B,EAAE,QAAQ,CAAC;SACnE,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACI,gBAAgB,CACrB,YAAoB,EACpB,UAAkB,EAClB,MAAc,EACd,UAA4B,EAAE;QAE9B,MAAM,4BAA4B,GAAiC,OAAO,CAAC;QAC3E,MAAM,UAAU,GAAe;YAC7B,YAAY,EAAE,UAAU;YACxB,MAAM,EAAE,MAAM;SACf,CAAC;QACF,4BAA4B,CAAC,UAAU,GAAG,UAAU,CAAC;QAErD,OAAO,aAAa,CAAC,QAAQ,CAC3B,qCAAqC,EACrC,4BAA4B,EAC5B,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;QACnE,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACI,gBAAgB,CACrB,YAAoB,EACpB,UAA4B,EAAE;QAE9B,OAAO,aAAa,CAAC,QAAQ,CAC3B,qCAAqC,EACrC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;QACtE,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACY,cAAc,CAC3B,KAAa,EACb,OAAsC,EACtC,iBAA+B;;YAE/B,IAAI,iBAAiB,CAAC,iBAAiB,IAAI,IAAI,EAAE;gBAC/C,MAAM,kBAAkB,GAAuB;oBAC7C,KAAK,EAAE,KAAK;oBACZ,iBAAiB,EAAE,iBAAiB,CAAC,iBAAiB;iBACvD,CAAC;gBACF,MAAM,WAAW,GAAG,cAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAA,CAAC;gBACpF,iBAAiB,CAAC,iBAAiB,GAAG,WAAW,CAAC,iBAAiB,CAAC;gBACpE,oBAAM,WAAW,CAAA,CAAC;aACnB;YACD,OAAO,iBAAiB,CAAC,iBAAiB,EAAE;gBAC1C,MAAM,kBAAkB,GAAuB;oBAC7C,KAAK,EAAE,KAAK;oBACZ,iBAAiB,EAAE,iBAAiB,CAAC,iBAAiB;iBACvD,CAAC;gBACF,MAAM,WAAW,GAAG,cAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAA,CAAC;gBAEpF,iBAAiB,CAAC,iBAAiB,GAAG,WAAW,CAAC,iBAAiB,CAAC;gBACpE,oBAAM,WAAW,CAAA,CAAC;aACnB;QACH,CAAC;KAAA;IAED;;;;OAIG;IACY,aAAa,CAC1B,KAAa,EACb,OAAsC;;;YAEtC,MAAM,CAAC,GAAG,EAAE,CAAC;;gBAEb,KAAyB,IAAA,KAAA,cAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC,CAAA,IAAA;oBAApD,MAAM,IAAI,WAAA,CAAA;oBACnB,IAAI,IAAI,CAAC,KAAK,EAAE;wBACd,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;4BAC7B,oBAAM,IAAI,CAAA,CAAC;yBACZ;qBACF;iBACF;;;;;;;;;QACH,CAAC;KAAA;IAED;;;;;;OAMG;IACI,UAAU,CACf,KAAa,EACb,cAAuB,EACvB,UAA2C,EAAE;QAE7C,IAAI,6BAA6B,GAAkC,OAAO,CAAC;QAC3E,6BAA6B,GAAG;YAC9B,eAAe,EAAE,cAAc;SAChC,CAAC;QAEF,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,6BAA6B,CAAC,CAAC;QAEtE,OAAO;YACL,IAAI;gBACF,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;YACrB,CAAC;YACD,CAAC,MAAM,CAAC,aAAa,CAAC;gBACpB,OAAO,IAAI,CAAC;YACd,CAAC;YACD,MAAM,EAAE,CAAC,WAAyB,EAAE,EAAE,EAAE,CACtC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,6BAA6B,EAAE,QAAQ,CAAC;SACtE,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/// <reference lib=\"esnext.asynciterable\" />\n\nimport {\n  TokenCredential,\n  RestResponse,\n  OperationOptions,\n  InternalPipelineOptions,\n  bearerTokenAuthenticationPolicy,\n  createPipelineFromOptions,\n  generateUuid,\n  PipelineOptions,\n} from \"@azure/core-http\";\nimport { PageSettings, PagedAsyncIterableIterator } from \"@azure/core-paging\";\nimport { AzureDigitalTwinsAPI as GeneratedClient } from \"./generated/azureDigitalTwinsAPI\";\nimport {\n  DigitalTwinsGetByIdResponse,\n  DigitalTwinsAddOptionalParams,\n  DigitalTwinsAddResponse,\n  DigitalTwinsUpdateOptionalParams,\n  DigitalTwinsUpdateResponse,\n  DigitalTwinsDeleteOptionalParams,\n  DigitalTwinsGetComponentResponse,\n  DigitalTwinsUpdateComponentResponse,\n  DigitalTwinsUpdateComponentOptionalParams,\n  DigitalTwinsAddRelationshipResponse,\n  DigitalTwinsAddRelationshipOptionalParams,\n  DigitalTwinsUpdateRelationshipOptionalParams,\n  DigitalTwinsUpdateRelationshipResponse,\n  DigitalTwinsDeleteRelationshipOptionalParams,\n  DigitalTwinsSendTelemetryOptionalParams,\n  DigitalTwinsSendComponentTelemetryOptionalParams,\n  DigitalTwinsListRelationshipsResponse,\n  IncomingRelationship,\n  DigitalTwinsListIncomingRelationshipsResponse,\n  DigitalTwinsGetRelationshipByIdResponse,\n  DigitalTwinsModelData,\n  DigitalTwinModelsGetByIdResponse,\n  DigitalTwinModelsGetByIdOptionalParams,\n  DigitalTwinModelsAddResponse,\n  DigitalTwinModelsAddOptionalParams,\n  DigitalTwinModelsListResponse,\n  DigitalTwinModelsListOptionalParams,\n  EventRoutesGetByIdResponse,\n  EventRoute,\n  EventRoutesAddOptionalParams,\n  EventRoutesListNextResponse,\n  EventRoutesListOptionalParams,\n  QueryQueryTwinsOptionalParams,\n  QueryQueryTwinsResponse,\n  QuerySpecification,\n} from \"./generated/models\";\nimport { tracingClient } from \"./tracing\";\nimport { logger } from \"./logger\";\n\nexport const SDK_VERSION: string = \"1.1.0\";\n\nexport interface DigitalTwinsClientOptions extends PipelineOptions {\n  /**\n   * Api Version\n   */\n  apiVersion?: string;\n}\n\nconst DEFAULT_DIGITALTWINS_SCOPE = \"https://digitaltwins.azure.net/.default\";\n\n/**\n * Client for Azure IoT DigitalTwins API.\n */\nexport class DigitalTwinsClient {\n  /**\n   * A reference to the auto-generated AzureDigitalTwinsAPI\n   */\n  private readonly client: GeneratedClient;\n\n  /**\n   * Creates an instance of AzureDigitalTwinsAPI.\n   *\n   * Example usage:\n   * ```ts\n   * const { DigitalTwinsClient, ServiceClientCredentials } = require(\"@azure/digital-twins-core\");\n   *\n   * const client = new DigitalTwinsClient(\n   *   \"<endpoint>\",\n   *   new DefaultAzureCredential();\n   * );\n   * ```\n   * @param endpointUrl - The endpoint URL of the service.\n   * @param credential - Used to authenticate requests to the service.\n   * @param options - Used to configure the service client.\n   */\n  constructor(\n    endpointUrl: string,\n    credential: TokenCredential,\n    options: DigitalTwinsClientOptions = {}\n  ) {\n    const authPolicy = bearerTokenAuthenticationPolicy(credential, DEFAULT_DIGITALTWINS_SCOPE);\n    const libInfo = `azsdk-js-digital-twins-core/${SDK_VERSION}`;\n\n    const { apiVersion, ...pipelineOptions } = options;\n    if (!pipelineOptions.userAgentOptions) {\n      pipelineOptions.userAgentOptions = {};\n    }\n    if (pipelineOptions.userAgentOptions.userAgentPrefix) {\n      pipelineOptions.userAgentOptions.userAgentPrefix = `${pipelineOptions.userAgentOptions.userAgentPrefix} ${libInfo}`;\n    } else {\n      pipelineOptions.userAgentOptions.userAgentPrefix = libInfo;\n    }\n\n    const internalPipelineOptions: InternalPipelineOptions = {\n      ...pipelineOptions,\n      ...{\n        loggingOptions: {\n          logger: logger.info,\n          allowedHeaderNames: [\"x-ms-request-id\"],\n        },\n      },\n    };\n\n    const pipeline = createPipelineFromOptions(internalPipelineOptions, authPolicy);\n\n    this.client = new GeneratedClient({\n      endpoint: endpointUrl,\n      apiVersion,\n      ...pipeline,\n    });\n  }\n\n  /**\n   * Get a digital twin\n   *\n   * @param digitalTwinId - The Id of the digital twin.\n   * @param options - The operation options\n   * @returns The application/json digital twin and the http response.\n   */\n  public getDigitalTwin(\n    digitalTwinId: string,\n    options: OperationOptions = {}\n  ): Promise<DigitalTwinsGetByIdResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.getDigitalTwin\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.getById(digitalTwinId, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Create or update a digital twin\n   *\n   * @param digitalTwinId - The Id of the digital twin to create or update.\n   * @param digitalTwinJson - The application/json digital twin to create.\n   * @param options - Extended operation options including\n   *  ifNoneMatch: Only perform the operation if the entity does not already exist.\n   * @returns The created application/json digital twin and the http response.\n   */\n  public upsertDigitalTwin(\n    digitalTwinId: string,\n    digitalTwinJson: string,\n    options: DigitalTwinsAddOptionalParams = {}\n  ): Promise<DigitalTwinsAddResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.upsertDigitalTwin\",\n      options,\n      async (updatedOptions) => {\n        const payload = JSON.parse(digitalTwinJson);\n        return this.client.digitalTwins.add(digitalTwinId, payload, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Update a digital twin using a json patch.\n   *\n   * @param digitalTwinId - The Id of the digital twin.\n   * @param jsonPatch - An update specification described by JSON Patch. Updates to property values\n   * and $model elements may happen in the same request. Operations are limited to add, replace and\n   * remove.\n   * @param options - Extended operation options including\n   *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is provided.\n   * @returns The http response.\n   */\n  public updateDigitalTwin(\n    digitalTwinId: string,\n    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types -- changing the type any would be a breaking change\n    jsonPatch: any,\n    options: DigitalTwinsUpdateOptionalParams = {}\n  ): Promise<DigitalTwinsUpdateResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.updateDigitalTwin\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.update(digitalTwinId, jsonPatch, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Delete a digital twin\n   *\n   * @param digitalTwinId - The Id of the digital twin to delete.\n   * @param options - Extended operation options including\n   *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is provided.\n   * @returns The http response.\n   */\n  public deleteDigitalTwin(\n    digitalTwinId: string,\n    options: DigitalTwinsDeleteOptionalParams = {}\n  ): Promise<RestResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.deleteDigitalTwin\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.delete(digitalTwinId, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Get a component on a digital twin.\n   *\n   * @param digitalTwinId - The Id of the digital twin.\n   * @param componentName - The component being retrieved.\n   * @param options - The operation options\n   * @returns Json string representation of the component corresponding to the provided componentName and the HTTP response.\n   */\n  public getComponent(\n    digitalTwinId: string,\n    componentName: string,\n    options: OperationOptions = {}\n  ): Promise<DigitalTwinsGetComponentResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.getComponent\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.getComponent(digitalTwinId, componentName, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Update properties of a component on a digital twin using a JSON patch.\n   *\n   * @param digitalTwinId - The Id of the digital twin.\n   * @param componentName - The component being updated.\n   * @param jsonPatch - The application/json-patch+json operations to be performed on the specified digital twin's component.\n   * @param enableUpdate - If true then update of an existing digital twin is enabled.\n   * @param options - Extended operation options including\n   *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is provided.\n   * @returns The http response.\n   */\n  public updateComponent(\n    digitalTwinId: string,\n    componentName: string,\n    jsonPatch: any[],\n    options: DigitalTwinsUpdateComponentOptionalParams = {}\n  ): Promise<DigitalTwinsUpdateComponentResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.updateComponent\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.updateComponent(\n          digitalTwinId,\n          componentName,\n          jsonPatch,\n          updatedOptions\n        );\n      }\n    );\n  }\n\n  /**\n   * Get a relationship on a digital twin.\n   *\n   * @param digitalTwinId - The Id of the source digital twin.\n   * @param relationshipId - The Id of the relationship to retrieve.\n   * @param options - The operation options\n   * @returns The pageable list of application/json relationships belonging to the specified digital twin and the http response.\n   */\n  public getRelationship(\n    digitalTwinId: string,\n    relationshipId: string,\n    options: OperationOptions = {}\n  ): Promise<DigitalTwinsGetRelationshipByIdResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.getRelationship\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.getRelationshipById(\n          digitalTwinId,\n          relationshipId,\n          updatedOptions\n        );\n      }\n    );\n  }\n\n  /**\n   * Create or update a relationship on a digital twin.\n   *\n   * @param digitalTwinId - The Id of the source digital twin.\n   * @param relationshipId - The Id of the relationship to create.\n   * @param relationship - The application/json relationship to be created.\n   * @param options - Extended operation options including\n   *  ifNoneMatch: Only perform the operation if the entity does not already exist.\n   */\n  public upsertRelationship(\n    digitalTwinId: string,\n    relationshipId: string,\n    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types -- changing the type any would be a breaking change\n    relationship: any,\n    options: DigitalTwinsAddRelationshipOptionalParams = {}\n  ): Promise<DigitalTwinsAddRelationshipResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.upsertRelationship\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.addRelationship(\n          digitalTwinId,\n          relationshipId,\n          relationship,\n          updatedOptions\n        );\n      }\n    );\n  }\n\n  /**\n   * Updates the properties of a relationship on a digital twin using a JSON patch.\n   *\n   * @param digitalTwinId - The Id of the digital twin to delete.\n   * @param relationshipId - The Id of the relationship to be updated.\n   * @param jsonPatch - The application/json-patch+json operations to be performed on the specified digital twin's relationship.\n   * @param options - Extended operation options\n   *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is provided.\n   */\n  public updateRelationship(\n    digitalTwinId: string,\n    relationshipId: string,\n    jsonPatch: any[],\n    options: DigitalTwinsUpdateRelationshipOptionalParams = {}\n  ): Promise<DigitalTwinsUpdateRelationshipResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.updateRelationship\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.updateRelationship(\n          digitalTwinId,\n          relationshipId,\n          jsonPatch,\n          updatedOptions\n        );\n      }\n    );\n  }\n\n  /**\n   * Delete a relationship on a digital twin.\n   *\n   * @param digitalTwinId - The Id of the source digital twin.\n   * @param relationshipId - The Id of the relationship to delete.\n   * @param options - The operation options\n   *   ifMatch: Only perform the operation if the entity's etag matches one of the etags provided or * is\n   * @returns The http response.\n   */\n  public deleteRelationship(\n    digitalTwinId: string,\n    relationshipId: string,\n    options: DigitalTwinsDeleteRelationshipOptionalParams = {}\n  ): Promise<RestResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.deleteRelationship\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.deleteRelationship(\n          digitalTwinId,\n          relationshipId,\n          updatedOptions\n        );\n      }\n    );\n  }\n\n  /**\n   * Deals with the pagination of {@link listRelationships}.\n   *\n   * @param digitalTwinId - The Id of the digital twin.\n   * @param options - Common options for the iterative endpoints.\n   * @param continuationState - An object that indicates the position of the paginated request.\n   *\n   */\n  private async *listRelationshipsPage(\n    digitalTwinId: string,\n    options: OperationOptions,\n    continuationState: PageSettings\n  ): AsyncIterableIterator<DigitalTwinsListRelationshipsResponse> {\n    if (continuationState.continuationToken == null) {\n      const optionsComplete: OperationOptions = {\n        ...options,\n      };\n      const listRelationshipResponse = await this.client.digitalTwins.listRelationships(\n        digitalTwinId,\n        optionsComplete\n      );\n      continuationState.continuationToken = listRelationshipResponse.nextLink;\n      yield listRelationshipResponse;\n    }\n    while (continuationState.continuationToken) {\n      const listRelationshipResponse = await this.client.digitalTwins.listRelationshipsNext(\n        \"\",\n        continuationState.continuationToken,\n        options\n      );\n\n      continuationState.continuationToken = listRelationshipResponse.nextLink;\n      yield listRelationshipResponse;\n    }\n  }\n\n  /**\n   * Deals with the iteration of all the available results of {@link listRelationships}.\n   * @param options - Common options for the iterative endpoints.\n   */\n  private async *listRelationshipsAll(\n    digitalTwinId: string,\n    options: OperationOptions\n  ): AsyncIterableIterator<any> {\n    for await (const page of this.listRelationshipsPage(digitalTwinId, options, {})) {\n      const value = page.value || [];\n      for (const item of value) {\n        yield item;\n      }\n    }\n  }\n\n  /**\n   * Retrieve relationships for a digital twin.\n   *\n   * @param digitalTwinId - The Id of the digital twin.\n   */\n  public listRelationships(\n    digitalTwinId: string,\n    options: OperationOptions & PageSettings = {}\n  ): PagedAsyncIterableIterator<any, DigitalTwinsListRelationshipsResponse> {\n    const iter = this.listRelationshipsAll(digitalTwinId, options);\n\n    return {\n      next() {\n        return iter.next();\n      },\n      [Symbol.asyncIterator]() {\n        return this;\n      },\n      byPage: (settings: PageSettings = {}) =>\n        this.listRelationshipsPage(digitalTwinId, options, settings),\n    };\n  }\n\n  /**\n   * Deals with the pagination of {@link listIncomingRelationships}.\n   *\n   * @param digitalTwinId - The Id of the digital twin.\n   * @param options - Common options for the iterative endpoints.\n   * @param continuationState - An object that indicates the position of the paginated request.\n   *\n   */\n  private async *listIncomingRelationshipsPage(\n    digitalTwinId: string,\n    options: OperationOptions,\n    continuationState: PageSettings\n  ): AsyncIterableIterator<DigitalTwinsListIncomingRelationshipsResponse> {\n    if (continuationState.continuationToken == null) {\n      const optionsComplete: OperationOptions = {\n        ...options,\n      };\n      const listIncomingRelationshipsResponse =\n        await this.client.digitalTwins.listIncomingRelationships(digitalTwinId, optionsComplete);\n      continuationState.continuationToken = listIncomingRelationshipsResponse.nextLink;\n      yield listIncomingRelationshipsResponse;\n    }\n    while (continuationState.continuationToken) {\n      const listIncomingRelationshipsResponse =\n        await this.client.digitalTwins.listIncomingRelationshipsNext(\n          \"\",\n          continuationState.continuationToken,\n          options\n        );\n\n      continuationState.continuationToken = listIncomingRelationshipsResponse.nextLink;\n      yield listIncomingRelationshipsResponse;\n    }\n  }\n\n  /**\n   * Deals with the iteration of all the available results of {@link listIncomingRelationships}.\n   * @param digitalTwinId - The Id of the digital twin.\n   * @param options - Common options for the iterative endpoints.\n   */\n  private async *listIncomingRelationshipsAll(\n    digitalTwinId: string,\n    options: OperationOptions\n  ): AsyncIterableIterator<IncomingRelationship> {\n    for await (const page of this.listIncomingRelationshipsPage(digitalTwinId, options, {})) {\n      const value = page.value || [];\n      yield* value;\n    }\n  }\n\n  /**\n   * Retrieve all incoming relationships for a digital twin.\n   *\n   * @param digitalTwinId - The Id of the digital twin.\n   */\n  public listIncomingRelationships(\n    digitalTwinId: string,\n    options: OperationOptions & PageSettings = {}\n  ): PagedAsyncIterableIterator<\n    IncomingRelationship,\n    DigitalTwinsListIncomingRelationshipsResponse\n  > {\n    const iter = this.listIncomingRelationshipsAll(digitalTwinId, options);\n\n    return {\n      next() {\n        return iter.next();\n      },\n      [Symbol.asyncIterator]() {\n        return this;\n      },\n      byPage: (settings: PageSettings = {}) =>\n        this.listIncomingRelationshipsPage(digitalTwinId, options, settings),\n    };\n  }\n\n  /**\n   * Publish telemetry from a digital twin, which is then consumed by one or many destination endpoints (subscribers) defined under.\n   *\n   * @param digitalTwinId - The Id of the digital twin to delete.\n   * @param payload - The application/json telemetry payload to be sent.\n   * @param messageId - The message Id.\n   * @param options - The operation options\n   * @returns The http response.\n   */\n  public publishTelemetry(\n    digitalTwinId: string,\n    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types -- changing the type any would be a breaking change\n    payload: any,\n    messageId: string,\n    options: OperationOptions = {}\n  ): Promise<RestResponse> {\n    const digitalTwinsSendTelemetryOptionalParams: DigitalTwinsSendTelemetryOptionalParams =\n      options;\n    digitalTwinsSendTelemetryOptionalParams.telemetrySourceTime = new Date().toISOString();\n    if (!messageId) {\n      messageId = generateUuid();\n    }\n\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.publishTelemetry\",\n      digitalTwinsSendTelemetryOptionalParams,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.sendTelemetry(\n          digitalTwinId,\n          messageId,\n          payload,\n          updatedOptions\n        );\n      }\n    );\n  }\n\n  /**\n   * Publish telemetry from a digital twin's component, which is then consumed by one or many destination endpoints (subscribers) defined under.\n   *\n   * @param digitalTwinId - The Id of the digital twin to delete.\n   * @param componentName - The name of the DTDL component.\n   * @param payload - The application/json telemetry payload to be sent.\n   * @param messageId - The message Id.\n   * @param options - The operation options\n   * @returns The http response.\n   */\n  public publishComponentTelemetry(\n    digitalTwinId: string,\n    componentName: string,\n    payload: string,\n    messageId: string,\n    options: OperationOptions = {}\n  ): Promise<RestResponse> {\n    const digitalTwinsSendComponentTelemetryOptionalParams: DigitalTwinsSendComponentTelemetryOptionalParams =\n      options;\n    digitalTwinsSendComponentTelemetryOptionalParams.telemetrySourceTime = new Date().toISOString();\n    if (!messageId) {\n      messageId = generateUuid();\n    }\n\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.publishComponentTelemetry\",\n      digitalTwinsSendComponentTelemetryOptionalParams,\n      async (updatedOptions) => {\n        return this.client.digitalTwins.sendComponentTelemetry(\n          digitalTwinId,\n          componentName,\n          payload,\n          messageId,\n          updatedOptions\n        );\n      }\n    );\n  }\n\n  /**\n   * Get a model, including the model metadata and the model definition.\n   *\n   * @param modelId - The Id of the model.\n   * @param options - Extended operation options including\n   *  includeModelDefinition: When true the model definition will be returned as part of the result. Default value: false.\n   * @returns The application/json model and the http response.\n   */\n  public getModel(\n    modelId: string,\n    includeModelDefinition: boolean = false,\n    options: OperationOptions = {}\n  ): Promise<DigitalTwinModelsGetByIdResponse> {\n    const digitalTwinModelsGetByIdOptionalParams: DigitalTwinModelsGetByIdOptionalParams = options;\n    digitalTwinModelsGetByIdOptionalParams.includeModelDefinition = includeModelDefinition;\n\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.getModel\",\n      digitalTwinModelsGetByIdOptionalParams,\n      async (updatedOptions) => {\n        return this.client.digitalTwinModels.getById(modelId, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Deals with the pagination of {@link list}.\n   *\n   * @param options - Common options for the iterative endpoints.\n   * @param continuationState - An object that indicates the position of the paginated request.\n   *\n   */\n  private async *getModelsPage(\n    options: DigitalTwinModelsListOptionalParams,\n    continuationState: PageSettings\n  ): AsyncIterableIterator<DigitalTwinModelsListResponse> {\n    if (continuationState.continuationToken == null) {\n      const optionsComplete: DigitalTwinModelsListOptionalParams = options;\n      optionsComplete.maxItemsPerPage = continuationState.maxPageSize;\n\n      const listResponse = await this.client.digitalTwinModels.list(optionsComplete);\n      continuationState.continuationToken = listResponse.nextLink;\n      yield listResponse;\n    }\n    while (continuationState.continuationToken) {\n      const listResponse = await this.client.digitalTwinModels.listNext(\n        continuationState.continuationToken,\n        options\n      );\n\n      continuationState.continuationToken = listResponse.nextLink;\n      yield listResponse;\n    }\n  }\n\n  /**\n   * Deals with the iteration of all the available results of {@link list}.\n   * @param options - Common options for the iterative endpoints.\n   */\n  private async *getModelsAll(\n    options: DigitalTwinModelsListOptionalParams\n  ): AsyncIterableIterator<DigitalTwinsModelData> {\n    const f = {};\n\n    for await (const page of this.getModelsPage(options, f)) {\n      const value = page.value || [];\n      for (const item of value) {\n        yield item;\n      }\n    }\n  }\n\n  /**\n   * Get the list of models\n   *\n   * @param dependeciesFor - The model Ids to have dependencies retrieved. If omitted, all models are retrieved.\n   * @param includeModelDefinition - Whether to include the model definition in the result. If false, only the model metadata will be returned.\n   * @param resultsPerPage - The maximum number of items to retrieve per request. The server may choose to return less than the requested max.\n   * @returns A pageable set of application/json models and the http response.\n   */\n  public listModels(\n    dependeciesFor?: string[],\n    includeModelDefinition: boolean = false,\n    resultsPerPage?: number,\n    options: OperationOptions & PageSettings = {}\n  ): PagedAsyncIterableIterator<DigitalTwinsModelData, DigitalTwinModelsListResponse> {\n    let digitalTwinModelsListOptionalParams: DigitalTwinModelsListOptionalParams = options;\n    digitalTwinModelsListOptionalParams = {\n      maxItemsPerPage: resultsPerPage,\n      dependenciesFor: dependeciesFor,\n      includeModelDefinition: includeModelDefinition,\n    };\n\n    const iter = this.getModelsAll(digitalTwinModelsListOptionalParams);\n\n    return {\n      next() {\n        return iter.next();\n      },\n      [Symbol.asyncIterator]() {\n        return this;\n      },\n      byPage: (settings: PageSettings = {}) =>\n        this.getModelsPage(digitalTwinModelsListOptionalParams, settings),\n    };\n  }\n\n  /**\n   * Create one or many\n   *\n   * @param models - The set of models to create. Each string corresponds to exactly one model.\n   * @param options - The operation options\n   * @returns The created application/json models and the http response.\n   */\n  public createModels(\n    dtdlModels: any[],\n    options: OperationOptions = {}\n  ): Promise<DigitalTwinModelsAddResponse> {\n    const digitalTwinModelsAddOptionalParams: DigitalTwinModelsAddOptionalParams = options;\n    digitalTwinModelsAddOptionalParams.models = dtdlModels;\n\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.createModels\",\n      digitalTwinModelsAddOptionalParams,\n      async (updatedOptions) => {\n        return this.client.digitalTwinModels.add(updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Decommission a model using a json patch.\n   * When a model is decommissioned, new digital twins will no longer be able to be\n   * defined by this model. However, existing digital twins may continue to use this model.\n   * Once a model is decommissioned, it may not be recommissioned.\n   *\n   * @param modelId - The Id of the model to decommission.\n   * property can be replaced.\n   * @param options - The operation options\n   * @returns The http response.\n   *\n   */\n  public decomissionModel(modelId: string, options: OperationOptions = {}): Promise<RestResponse> {\n    const jsonPatch = [{ op: \"replace\", path: \"/decommissioned\", value: true }];\n\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.decomissionModel\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwinModels.update(modelId, jsonPatch, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Delete a model.\n   *\n   * @param modelId - The Id of the model to delete.\n   * @param options - The operation options\n   * @returns The http response.\n   */\n  public deleteModel(modelId: string, options: OperationOptions = {}): Promise<RestResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.deleteModel\",\n      options,\n      async (updatedOptions) => {\n        return this.client.digitalTwinModels.delete(modelId, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Get an event route.\n   *\n   * @param modelId - The Id of the event route.\n   * @param options - The operation options\n   * @returns The application/json event route and the http response.\n   */\n  public getEventRoute(\n    eventRouteId: string,\n    options: OperationOptions = {}\n  ): Promise<EventRoutesGetByIdResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.getEventRoute\",\n      options,\n      async (updatedOptions) => {\n        return this.client.eventRoutes.getById(eventRouteId, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Deals with the pagination of {@link list}.\n   *\n   * @param options - Common options for the iterative endpoints.\n   * @param continuationState - An object that indicates the position of the paginated request.\n   *\n   */\n  private async *getEventRoutesPage(\n    options: EventRoutesListOptionalParams,\n    continuationState: PageSettings\n  ): AsyncIterableIterator<EventRoutesListNextResponse> {\n    if (continuationState.continuationToken == null) {\n      const optionsComplete: EventRoutesListOptionalParams = options;\n      optionsComplete.maxItemsPerPage = continuationState.maxPageSize;\n\n      const listResponse = await this.client.eventRoutes.list(optionsComplete);\n      continuationState.continuationToken = listResponse.nextLink;\n      yield listResponse;\n    }\n    while (continuationState.continuationToken) {\n      const listResponse = await this.client.eventRoutes.listNext(\n        continuationState.continuationToken,\n        options\n      );\n\n      continuationState.continuationToken = listResponse.nextLink;\n      yield listResponse;\n    }\n  }\n\n  /**\n   * Deals with the iteration of all the available results of {@link list}.\n   * @param options - Common options for the iterative endpoints.\n   */\n  private async *getEventRoutesAll(\n    options: EventRoutesListOptionalParams\n  ): AsyncIterableIterator<EventRoute> {\n    const f = {};\n    for await (const page of this.getEventRoutesPage(options, f)) {\n      const value = page.value || [];\n      for (const item of value) {\n        yield item;\n      }\n    }\n  }\n\n  /**\n   * List the event routes in a digital twins instance.\n   *\n   * @param resultsPerPage - The maximum number of items to retrieve per request. The server may choose to return less than\n   * the requested max.\n   * @returns The application/json event route and the http response.\n   */\n  public listEventRoutes(\n    resultsPerPage?: number,\n    options: OperationOptions & PageSettings = {}\n  ): PagedAsyncIterableIterator<EventRoute, EventRoutesListNextResponse> {\n    let eventRoutesListOptionalParams: EventRoutesListOptionalParams = options;\n    eventRoutesListOptionalParams = {\n      maxItemsPerPage: resultsPerPage,\n    };\n\n    const iter = this.getEventRoutesAll(eventRoutesListOptionalParams);\n\n    return {\n      next() {\n        return iter.next();\n      },\n      [Symbol.asyncIterator]() {\n        return this;\n      },\n      byPage: (settings: PageSettings = {}) =>\n        this.getEventRoutesPage(eventRoutesListOptionalParams, settings),\n    };\n  }\n\n  /**\n   * Create or update an event route.\n   *\n   * @param eventRouteId - The Id of the event route to create or update.\n   * @param endpointId - The id of the endpoint this event route is bound to.\n   * @param filter - An expression which describes the events which are routed to the endpoint.\n   * @param options - The operation options\n   * @returns The http response.\n   */\n  public upsertEventRoute(\n    eventRouteId: string,\n    endpointId: string,\n    filter: string,\n    options: OperationOptions = {}\n  ): Promise<RestResponse> {\n    const eventRoutesAddOptionalParams: EventRoutesAddOptionalParams = options;\n    const eventRoute: EventRoute = {\n      endpointName: endpointId,\n      filter: filter,\n    };\n    eventRoutesAddOptionalParams.eventRoute = eventRoute;\n\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.upsertEventRoute\",\n      eventRoutesAddOptionalParams,\n      async (updatedOptions) => {\n        return this.client.eventRoutes.add(eventRouteId, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Delete an event route.\n   *\n   * @param eventRouteId - The Id of the eventRoute to delete.\n   * @param options - The operation options\n   * @returns The http response.\n   */\n  public deleteEventRoute(\n    eventRouteId: string,\n    options: OperationOptions = {}\n  ): Promise<RestResponse> {\n    return tracingClient.withSpan(\n      \"DigitalTwinsClient.deleteEventRoute\",\n      options,\n      async (updatedOptions) => {\n        return this.client.eventRoutes.delete(eventRouteId, updatedOptions);\n      }\n    );\n  }\n\n  /**\n   * Deals with the pagination of {@link query}.\n   *\n   * @param query - The query string, in SQL-like syntax.\n   * @param options - Common options for the iterative endpoints.\n   * @param continuationState - An object that indicates the position of the paginated request.\n   *\n   */\n  private async *queryTwinsPage(\n    query: string,\n    options: QueryQueryTwinsOptionalParams,\n    continuationState: PageSettings\n  ): AsyncIterableIterator<QueryQueryTwinsResponse> {\n    if (continuationState.continuationToken == null) {\n      const querySpecification: QuerySpecification = {\n        query: query,\n        continuationToken: continuationState.continuationToken,\n      };\n      const queryResult = await this.client.query.queryTwins(querySpecification, options);\n      continuationState.continuationToken = queryResult.continuationToken;\n      yield queryResult;\n    }\n    while (continuationState.continuationToken) {\n      const querySpecification: QuerySpecification = {\n        query: query,\n        continuationToken: continuationState.continuationToken,\n      };\n      const queryResult = await this.client.query.queryTwins(querySpecification, options);\n\n      continuationState.continuationToken = queryResult.continuationToken;\n      yield queryResult;\n    }\n  }\n\n  /**\n   * Deals with the iteration of all the available results of {@link query}.\n   * @param query - The query string, in SQL-like syntax.\n   * @param options - Common options for the iterative endpoints.\n   */\n  private async *queryTwinsAll(\n    query: string,\n    options: QueryQueryTwinsOptionalParams\n  ): AsyncIterableIterator<any> {\n    const f = {};\n\n    for await (const page of this.queryTwinsPage(query, options, f)) {\n      if (page.value) {\n        for (const item of page.value) {\n          yield item;\n        }\n      }\n    }\n  }\n\n  /**\n   * Query for digital twins.\n   *\n   * @param query - The query string, in SQL-like syntax.\n   * @param resultsPerPage - The maximum number of items to retrieve per request. The server may choose to return less than the requested max.\n   * @returns The pageable list of query results.\n   */\n  public queryTwins(\n    query: string,\n    resultsPerPage?: number,\n    options: OperationOptions & PageSettings = {}\n  ): PagedAsyncIterableIterator<any, QueryQueryTwinsResponse> {\n    let queryQueryTwinsOptionalParams: QueryQueryTwinsOptionalParams = options;\n    queryQueryTwinsOptionalParams = {\n      maxItemsPerPage: resultsPerPage,\n    };\n\n    const iter = this.queryTwinsAll(query, queryQueryTwinsOptionalParams);\n\n    return {\n      next() {\n        return iter.next();\n      },\n      [Symbol.asyncIterator]() {\n        return this;\n      },\n      byPage: (settings: PageSettings = {}) =>\n        this.queryTwinsPage(query, queryQueryTwinsOptionalParams, settings),\n    };\n  }\n}\n"]}