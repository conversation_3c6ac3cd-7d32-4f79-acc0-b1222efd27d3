{"version": 3, "file": "mappers.js", "sourceRoot": "", "sources": ["../../../../src/generated/models/mappers.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAIH,MAAM,CAAC,MAAM,qBAAqB,GAA6B;IAC7D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,uBAAuB;QAClC,eAAe,EAAE;YACf,WAAW,EAAE;gBACX,cAAc,EAAE,aAAa;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;iBACpC;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,aAAa;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;iBACpC;aACF;YACD,EAAE,EAAE;gBACF,cAAc,EAAE,IAAI;gBACpB,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,YAAY;gBAC5B,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;iBACjB;aACF;YACD,cAAc,EAAE;gBACd,cAAc,EAAE,gBAAgB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,KAAK,EAAE;gBACL,cAAc,EAAE,OAAO;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,KAAK;iBACZ;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAA6B;IACrD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,eAAe;QAC1B,eAAe,EAAE;YACf,KAAK,EAAE;gBACL,cAAc,EAAE,OAAO;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,YAAY;iBACxB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAA6B;IAClD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,YAAY;QACvB,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,SAAS;gBACzB,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,SAAS;gBACzB,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,EAAE;iBAClE;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,YAAY;gBAC5B,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,YAAY;iBACxB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAA6B;IAClD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,YAAY;QACvB,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,YAAY;gBAC5B,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,YAAY;iBACxB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oCAAoC,GAA6B;IAC5E,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,sCAAsC;QACjD,eAAe,EAAE;YACf,KAAK,EAAE;gBACL,cAAc,EAAE,OAAO;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,uBAAuB,EAAE;qBAChE;iBACF;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,UAAU;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAA6B;IAC1D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,oBAAoB;QAC/B,eAAe,EAAE;YACf,KAAK,EAAE;gBACL,cAAc,EAAE,OAAO;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,mBAAmB;gBACnC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAA6B;IACnD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,aAAa;QACxB,eAAe,EAAE;YACf,KAAK,EAAE;gBACL,cAAc,EAAE,OAAO;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;iBACnC;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,mBAAmB;gBACnC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAA6B;IAC9D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,wBAAwB;QACnC,eAAe,EAAE;YACf,KAAK,EAAE;gBACL,cAAc,EAAE,OAAO;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;iBACnC;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,UAAU;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,8BAA8B,GAA6B;IACtE,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,gCAAgC;QAC3C,eAAe,EAAE;YACf,KAAK,EAAE;gBACL,cAAc,EAAE,OAAO;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,sBAAsB,EAAE;qBAC/D;iBACF;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,UAAU;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oBAAoB,GAA6B;IAC5D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,sBAAsB;QACjC,eAAe,EAAE;YACf,cAAc,EAAE;gBACd,cAAc,EAAE,iBAAiB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,WAAW;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,gBAAgB,EAAE;gBAChB,cAAc,EAAE,mBAAmB;gBACnC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,gBAAgB,EAAE;gBAChB,cAAc,EAAE,mBAAmB;gBACnC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oBAAoB,GAA6B;IAC5D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,sBAAsB;QACjC,eAAe,EAAE;YACf,KAAK,EAAE;gBACL,cAAc,EAAE,OAAO;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,EAAE;iBAClE;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,UAAU;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAA6B;IAClD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,YAAY;QACvB,eAAe,EAAE;YACf,EAAE,EAAE;gBACF,cAAc,EAAE,IAAI;gBACpB,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,cAAc;gBAC9B,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,MAAM,EAAE;gBACN,cAAc,EAAE,QAAQ;gBACxB,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAA6B;IAC9D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,wBAAwB;QACnC,eAAe,EAAE;YACf,WAAW,EAAE;gBACX,cAAc,EAAE,cAAc;gBAC9B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,0BAA0B,GAA6B;IAClE,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,4BAA4B;QACvC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAA6B;IAC9D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,wBAAwB;QACnC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,yBAAyB,GAA6B;IACjE,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,2BAA2B;QACtC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,sCAAsC,GAA6B;IAC9E,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,wCAAwC;QACnD,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kCAAkC,GAA6B;IAC1E,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,oCAAoC;QAC/C,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,qCAAqC,GAA6B;IAC7E,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,uCAAuC;QAClD,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,+BAA+B,GAA6B;IACvE,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,iCAAiC;QAC5C,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kCAAkC,GAA6B;IAC1E,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,oCAAoC;QAC/C,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreHttp from \"@azure/core-http\";\n\nexport const DigitalTwinsModelData: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"DigitalTwinsModelData\",\n    modelProperties: {\n      displayName: {\n        serializedName: \"displayName\",\n        type: {\n          name: \"Dictionary\",\n          value: { type: { name: \"String\" } }\n        }\n      },\n      description: {\n        serializedName: \"description\",\n        type: {\n          name: \"Dictionary\",\n          value: { type: { name: \"String\" } }\n        }\n      },\n      id: {\n        serializedName: \"id\",\n        required: true,\n        type: {\n          name: \"String\"\n        }\n      },\n      uploadTime: {\n        serializedName: \"uploadTime\",\n        type: {\n          name: \"DateTime\"\n        }\n      },\n      decommissioned: {\n        serializedName: \"decommissioned\",\n        type: {\n          name: \"Boolean\"\n        }\n      },\n      model: {\n        serializedName: \"model\",\n        type: {\n          name: \"any\"\n        }\n      }\n    }\n  }\n};\n\nexport const ErrorResponse: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"ErrorResponse\",\n    modelProperties: {\n      error: {\n        serializedName: \"error\",\n        type: {\n          name: \"Composite\",\n          className: \"ErrorModel\"\n        }\n      }\n    }\n  }\n};\n\nexport const ErrorModel: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"ErrorModel\",\n    modelProperties: {\n      code: {\n        serializedName: \"code\",\n        readOnly: true,\n        type: {\n          name: \"String\"\n        }\n      },\n      message: {\n        serializedName: \"message\",\n        readOnly: true,\n        type: {\n          name: \"String\"\n        }\n      },\n      details: {\n        serializedName: \"details\",\n        readOnly: true,\n        type: {\n          name: \"Sequence\",\n          element: { type: { name: \"Composite\", className: \"ErrorModel\" } }\n        }\n      },\n      innererror: {\n        serializedName: \"innererror\",\n        type: {\n          name: \"Composite\",\n          className: \"InnerError\"\n        }\n      }\n    }\n  }\n};\n\nexport const InnerError: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"InnerError\",\n    modelProperties: {\n      code: {\n        serializedName: \"code\",\n        type: {\n          name: \"String\"\n        }\n      },\n      innererror: {\n        serializedName: \"innererror\",\n        type: {\n          name: \"Composite\",\n          className: \"InnerError\"\n        }\n      }\n    }\n  }\n};\n\nexport const PagedDigitalTwinsModelDataCollection: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"PagedDigitalTwinsModelDataCollection\",\n    modelProperties: {\n      value: {\n        serializedName: \"value\",\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: { name: \"Composite\", className: \"DigitalTwinsModelData\" }\n          }\n        }\n      },\n      nextLink: {\n        serializedName: \"nextLink\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const QuerySpecification: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"QuerySpecification\",\n    modelProperties: {\n      query: {\n        serializedName: \"query\",\n        type: {\n          name: \"String\"\n        }\n      },\n      continuationToken: {\n        serializedName: \"continuationToken\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const QueryResult: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"QueryResult\",\n    modelProperties: {\n      value: {\n        serializedName: \"value\",\n        type: {\n          name: \"Sequence\",\n          element: { type: { name: \"any\" } }\n        }\n      },\n      continuationToken: {\n        serializedName: \"continuationToken\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const RelationshipCollection: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"RelationshipCollection\",\n    modelProperties: {\n      value: {\n        serializedName: \"value\",\n        type: {\n          name: \"Sequence\",\n          element: { type: { name: \"any\" } }\n        }\n      },\n      nextLink: {\n        serializedName: \"nextLink\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const IncomingRelationshipCollection: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"IncomingRelationshipCollection\",\n    modelProperties: {\n      value: {\n        serializedName: \"value\",\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: { name: \"Composite\", className: \"IncomingRelationship\" }\n          }\n        }\n      },\n      nextLink: {\n        serializedName: \"nextLink\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const IncomingRelationship: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"IncomingRelationship\",\n    modelProperties: {\n      relationshipId: {\n        serializedName: \"$relationshipId\",\n        type: {\n          name: \"String\"\n        }\n      },\n      sourceId: {\n        serializedName: \"$sourceId\",\n        type: {\n          name: \"String\"\n        }\n      },\n      relationshipName: {\n        serializedName: \"$relationshipName\",\n        type: {\n          name: \"String\"\n        }\n      },\n      relationshipLink: {\n        serializedName: \"$relationshipLink\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const EventRouteCollection: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"EventRouteCollection\",\n    modelProperties: {\n      value: {\n        serializedName: \"value\",\n        type: {\n          name: \"Sequence\",\n          element: { type: { name: \"Composite\", className: \"EventRoute\" } }\n        }\n      },\n      nextLink: {\n        serializedName: \"nextLink\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const EventRoute: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"EventRoute\",\n    modelProperties: {\n      id: {\n        serializedName: \"id\",\n        readOnly: true,\n        type: {\n          name: \"String\"\n        }\n      },\n      endpointName: {\n        serializedName: \"endpointName\",\n        required: true,\n        type: {\n          name: \"String\"\n        }\n      },\n      filter: {\n        serializedName: \"filter\",\n        required: true,\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const QueryQueryTwinsHeaders: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"QueryQueryTwinsHeaders\",\n    modelProperties: {\n      queryCharge: {\n        serializedName: \"query-charge\",\n        type: {\n          name: \"Number\"\n        }\n      }\n    }\n  }\n};\n\nexport const DigitalTwinsGetByIdHeaders: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"DigitalTwinsGetByIdHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const DigitalTwinsAddHeaders: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"DigitalTwinsAddHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const DigitalTwinsUpdateHeaders: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"DigitalTwinsUpdateHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const DigitalTwinsGetRelationshipByIdHeaders: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"DigitalTwinsGetRelationshipByIdHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const DigitalTwinsAddRelationshipHeaders: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"DigitalTwinsAddRelationshipHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const DigitalTwinsUpdateRelationshipHeaders: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"DigitalTwinsUpdateRelationshipHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const DigitalTwinsGetComponentHeaders: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"DigitalTwinsGetComponentHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const DigitalTwinsUpdateComponentHeaders: coreHttp.CompositeMapper = {\n  type: {\n    name: \"Composite\",\n    className: \"DigitalTwinsUpdateComponentHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n"]}