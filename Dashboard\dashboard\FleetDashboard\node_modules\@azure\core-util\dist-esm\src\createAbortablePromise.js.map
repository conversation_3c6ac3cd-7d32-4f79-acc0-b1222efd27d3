{"version": 3, "file": "createAbortablePromise.js", "sourceRoot": "", "sources": ["../../src/createAbortablePromise.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,UAAU,EAAmB,MAAM,yBAAyB,CAAC;AActE;;;;;GAKG;AACH,MAAM,UAAU,sBAAsB,CACpC,YAGS,EACT,OAAuC;IAEvC,MAAM,EAAE,kBAAkB,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,CAAC;IACzE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,SAAS,aAAa;YACpB,MAAM,CAAC,IAAI,UAAU,CAAC,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,4BAA4B,CAAC,CAAC,CAAC;QACxE,CAAC;QACD,SAAS,eAAe;YACtB,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACrD,CAAC;QACD,SAAS,OAAO;YACd,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,EAAI,CAAC;YACvB,eAAe,EAAE,CAAC;YAClB,aAAa,EAAE,CAAC;QAClB,CAAC;QACD,IAAI,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,OAAO,EAAE;YACxB,OAAO,aAAa,EAAE,CAAC;SACxB;QACD,IAAI;YACF,YAAY,CACV,CAAC,CAAC,EAAE,EAAE;gBACJ,eAAe,EAAE,CAAC;gBAClB,OAAO,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,EACD,CAAC,CAAC,EAAE,EAAE;gBACJ,eAAe,EAAE,CAAC;gBAClB,MAAM,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CACF,CAAC;SACH;QAAC,OAAO,GAAG,EAAE;YACZ,MAAM,CAAC,GAAG,CAAC,CAAC;SACb;QACD,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortError, AbortSignalLike } from \"@azure/abort-controller\";\n\n/**\n * Options for the createAbortablePromise function.\n */\nexport interface CreateAbortablePromiseOptions {\n  /** A function to be called if the promise was aborted */\n  cleanupBeforeAbort?: () => void;\n  /** An abort signal */\n  abortSignal?: AbortSignalLike;\n  /** An abort error message */\n  abortErrorMsg?: string;\n}\n\n/**\n * Creates an abortable promise.\n * @param buildPromise - A function that takes the resolve and reject functions as parameters.\n * @param options - The options for the abortable promise.\n * @returns A promise that can be aborted.\n */\nexport function createAbortablePromise<T>(\n  buildPromise: (\n    resolve: (value: T | PromiseLike<T>) => void,\n    reject: (reason?: any) => void\n  ) => void,\n  options?: CreateAbortablePromiseOptions\n): Promise<T> {\n  const { cleanupBeforeAbort, abortSignal, abortErrorMsg } = options ?? {};\n  return new Promise((resolve, reject) => {\n    function rejectOnAbort(): void {\n      reject(new AbortError(abortErrorMsg ?? \"The operation was aborted.\"));\n    }\n    function removeListeners(): void {\n      abortSignal?.removeEventListener(\"abort\", onAbort);\n    }\n    function onAbort(): void {\n      cleanupBeforeAbort?.();\n      removeListeners();\n      rejectOnAbort();\n    }\n    if (abortSignal?.aborted) {\n      return rejectOnAbort();\n    }\n    try {\n      buildPromise(\n        (x) => {\n          removeListeners();\n          resolve(x);\n        },\n        (x) => {\n          removeListeners();\n          reject(x);\n        }\n      );\n    } catch (err) {\n      reject(err);\n    }\n    abortSignal?.addEventListener(\"abort\", onAbort);\n  });\n}\n"]}