{"version": 3, "file": "base64.browser.js", "sourceRoot": "", "sources": ["../../src/base64.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAQlC;;;;GAIG;AACH,MAAM,UAAU,aAAa,CAAC,OAAe;IAC3C,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;QAC9B,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;KACpF;IAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;IAC7B,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACtC,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;KACjC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,cAAc,CAAC,MAAmB;IAChD,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;QAC9B,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;KACpF;IAED,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;IACrC,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;QACxB,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;KACrC;IACD,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC;AACtB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\ndeclare global {\n  // stub these out for the browser\n  function btoa(input: string): string;\n  function atob(input: string): string;\n}\n\n/**\n * Converts a base64 string into a byte array.\n * @param content - The base64 string to convert.\n * @internal\n */\nexport function base64ToBytes(content: string): Uint8Array {\n  if (typeof atob !== \"function\") {\n    throw new Error(`Your browser environment is missing the global \"atob\" function.`);\n  }\n\n  const binary = atob(content);\n  const bytes = new Uint8Array(binary.length);\n  for (let i = 0; i < binary.length; i++) {\n    bytes[i] = binary.charCodeAt(i);\n  }\n\n  return bytes;\n}\n\n/**\n * Converts an ArrayBuffer to base64 string.\n * @param buffer - Raw binary data.\n * @internal\n */\nexport function bufferToBase64(buffer: ArrayBuffer): string {\n  if (typeof btoa !== \"function\") {\n    throw new Error(`Your browser environment is missing the global \"btoa\" function.`);\n  }\n\n  const bytes = new Uint8Array(buffer);\n  let binary = \"\";\n  for (const byte of bytes) {\n    binary += String.fromCharCode(byte);\n  }\n  return btoa(binary);\n}\n"]}