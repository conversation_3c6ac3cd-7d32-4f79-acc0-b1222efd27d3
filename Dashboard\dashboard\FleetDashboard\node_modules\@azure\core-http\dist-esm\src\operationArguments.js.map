{"version": 3, "file": "operationArguments.js", "sourceRoot": "", "sources": ["../../src/operationArguments.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { RequestOptionsBase } from \"./webResource\";\n\n/**\n * A collection of properties that apply to a single invocation of an operation.\n */\nexport interface OperationArguments {\n  /**\n   * The parameters that were passed to the operation method.\n   */\n  [parameterName: string]: any;\n\n  /**\n   * The optional arugments that are provided to an operation.\n   */\n  options?: RequestOptionsBase;\n}\n"]}