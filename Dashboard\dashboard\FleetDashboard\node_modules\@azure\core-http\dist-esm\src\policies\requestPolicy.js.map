{"version": 3, "file": "requestPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/requestPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,oBAAoB,EAAE,MAAM,yBAAyB,CAAC;AAsB/D;;GAEG;AACH,MAAM,OAAgB,iBAAiB;IACrC;;OAEG;IACH;IACE;;OAEG;IACM,WAA0B;IACnC;;OAEG;IACM,QAAkC;QAJlC,gBAAW,GAAX,WAAW,CAAe;QAI1B,aAAQ,GAAR,QAAQ,CAA0B;IAC1C,CAAC;IAQJ;;;;OAIG;IACI,SAAS,CAAC,QAA8B;QAC7C,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IAC3C,CAAC;IAED;;;;;OAKG;IACI,GAAG,CAAC,QAA8B,EAAE,OAAe;QACxD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACvC,CAAC;CACF;AAsBD;;GAEG;AACH,MAAM,OAAO,oBAAoB;IAC/B,YAAoB,OAA4B;QAA5B,YAAO,GAAP,OAAO,CAAqB;IAAG,CAAC;IAEpD;;;;OAIG;IACI,SAAS,CAAC,QAA8B;QAC7C,OAAO,CACL,CAAC,CAAC,IAAI,CAAC,OAAO;YACd,QAAQ,KAAK,oBAAoB,CAAC,GAAG;YACrC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,CACzC,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACI,GAAG,CAAC,QAA8B,EAAE,OAAe;QACxD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;YAC5C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;SACrC;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { HttpPipelineLogLevel } from \"../httpPipelineLogLevel\";\nimport { HttpPipelineLogger } from \"../httpPipelineLogger\";\nimport { WebResourceLike } from \"../webResource\";\n\n/**\n * Creates a new RequestPolicy per-request that uses the provided nextPolicy.\n */\nexport type RequestPolicyFactory = {\n  create(nextPolicy: RequestPolicy, options: RequestPolicyOptionsLike): RequestPolicy;\n};\n\n/**\n * The underlying structure of a request policy.\n */\nexport interface RequestPolicy {\n  /**\n   * A method that retrieves an {@link HttpOperationResponse} given a {@link WebResourceLike} describing the request to be made.\n   * @param httpRequest - {@link WebResourceLike} describing the request to be made.\n   */\n  sendRequest(httpRequest: WebResourceLike): Promise<HttpOperationResponse>;\n}\n\n/**\n * The base class from which all request policies derive.\n */\nexport abstract class BaseRequestPolicy implements RequestPolicy {\n  /**\n   * The main method to implement that manipulates a request/response.\n   */\n  protected constructor(\n    /**\n     * The next policy in the pipeline. Each policy is responsible for executing the next one if the request is to continue through the pipeline.\n     */\n    readonly _nextPolicy: RequestPolicy,\n    /**\n     * The options that can be passed to a given request policy.\n     */\n    readonly _options: RequestPolicyOptionsLike\n  ) {}\n\n  /**\n   * Sends a network request based on the given web resource.\n   * @param webResource - A {@link WebResourceLike} that describes a HTTP request to be made.\n   */\n  public abstract sendRequest(webResource: WebResourceLike): Promise<HttpOperationResponse>;\n\n  /**\n   * Get whether or not a log with the provided log level should be logged.\n   * @param logLevel - The log level of the log that will be logged.\n   * @returns Whether or not a log with the provided log level should be logged.\n   */\n  public shouldLog(logLevel: HttpPipelineLogLevel): boolean {\n    return this._options.shouldLog(logLevel);\n  }\n\n  /**\n   * Attempt to log the provided message to the provided logger. If no logger was provided or if\n   * the log level does not meat the logger's threshold, then nothing will be logged.\n   * @param logLevel - The log level of this log.\n   * @param message - The message of this log.\n   */\n  public log(logLevel: HttpPipelineLogLevel, message: string): void {\n    this._options.log(logLevel, message);\n  }\n}\n\n/**\n * Optional properties that can be used when creating a RequestPolicy.\n */\nexport interface RequestPolicyOptionsLike {\n  /**\n   * Get whether or not a log with the provided log level should be logged.\n   * @param logLevel - The log level of the log that will be logged.\n   * @returns Whether or not a log with the provided log level should be logged.\n   */\n  shouldLog(logLevel: HttpPipelineLogLevel): boolean;\n\n  /**\n   * Attempt to log the provided message to the provided logger. If no logger was provided or if\n   * the log level does not meet the logger's threshold, then nothing will be logged.\n   * @param logLevel - The log level of this log.\n   * @param message - The message of this log.\n   */\n  log(logLevel: HttpPipelineLogLevel, message: string): void;\n}\n\n/**\n * Optional properties that can be used when creating a RequestPolicy.\n */\nexport class RequestPolicyOptions {\n  constructor(private _logger?: HttpPipelineLogger) {}\n\n  /**\n   * Get whether or not a log with the provided log level should be logged.\n   * @param logLevel - The log level of the log that will be logged.\n   * @returns Whether or not a log with the provided log level should be logged.\n   */\n  public shouldLog(logLevel: HttpPipelineLogLevel): boolean {\n    return (\n      !!this._logger &&\n      logLevel !== HttpPipelineLogLevel.OFF &&\n      logLevel <= this._logger.minimumLogLevel\n    );\n  }\n\n  /**\n   * Attempt to log the provided message to the provided logger. If no logger was provided or if\n   * the log level does not meet the logger's threshold, then nothing will be logged.\n   * @param logLevel - The log level of this log.\n   * @param message - The message of this log.\n   */\n  public log(logLevel: HttpPipelineLogLevel, message: string): void {\n    if (this._logger && this.shouldLog(logLevel)) {\n      this._logger.log(logLevel, message);\n    }\n  }\n}\n"]}