{"version": 3, "file": "traceParentHeader.js", "sourceRoot": "", "sources": ["../../../src/utils/traceParentHeader.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,MAAM,OAAO,GAAG,IAAI,CAAC;AAErB;;;;GAIG;AACH,MAAM,UAAU,uCAAuC,CACrD,iBAAyB;IAEzB,MAAM,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAE3C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO;KACR;IAED,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,CAAC,GAAG,KAAK,CAAC;IAEvD,IAAI,OAAO,KAAK,OAAO,EAAE;QACvB,OAAO;KACR;IAED,MAAM,UAAU,GAAG,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;IAE9C,MAAM,WAAW,GAAgB;QAC/B,MAAM;QACN,OAAO;QACP,UAAU;KACX,CAAC;IAEF,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,oBAAoB,CAAC,WAAwB;IAC3D,MAAM,aAAa,GAAa,EAAE,CAAC;IACnC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;QACxB,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAC/B;IACD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;QACvB,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC9B;IAED,IAAI,aAAa,CAAC,MAAM,EAAE;QACxB,OAAO;KACR;IAED,MAAM,KAAK,GAAG,WAAW,CAAC,UAAU,gBAAmB,CAAC;IACxD,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACpC,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC;IAErE,uEAAuE;IACvE,OAAO,GAAG,OAAO,IAAI,WAAW,CAAC,OAAO,IAAI,WAAW,CAAC,MAAM,IAAI,UAAU,EAAE,CAAC;AACjF,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { SpanContext, TraceFlags } from \"../interfaces\";\n\nconst VERSION = \"00\";\n\n/**\n * Generates a `SpanContext` given a `traceparent` header value.\n * @param traceParent - Serialized span context data as a `traceparent` header value.\n * @returns The `SpanContext` generated from the `traceparent` value.\n */\nexport function extractSpanContextFromTraceParentHeader(\n  traceParentHeader: string\n): SpanContext | undefined {\n  const parts = traceParentHeader.split(\"-\");\n\n  if (parts.length !== 4) {\n    return;\n  }\n\n  const [version, traceId, spanId, traceOptions] = parts;\n\n  if (version !== VERSION) {\n    return;\n  }\n\n  const traceFlags = parseInt(traceOptions, 16);\n\n  const spanContext: SpanContext = {\n    spanId,\n    traceId,\n    traceFlags\n  };\n\n  return spanContext;\n}\n\n/**\n * Generates a `traceparent` value given a span context.\n * @param spanContext - Contains context for a specific span.\n * @returns The `spanContext` represented as a `traceparent` value.\n */\nexport function getTraceParentHeader(spanContext: SpanContext): string | undefined {\n  const missingFields: string[] = [];\n  if (!spanContext.traceId) {\n    missingFields.push(\"traceId\");\n  }\n  if (!spanContext.spanId) {\n    missingFields.push(\"spanId\");\n  }\n\n  if (missingFields.length) {\n    return;\n  }\n\n  const flags = spanContext.traceFlags || TraceFlags.NONE;\n  const hexFlags = flags.toString(16);\n  const traceFlags = hexFlags.length === 1 ? `0${hexFlags}` : hexFlags;\n\n  // https://www.w3.org/TR/trace-context/#traceparent-header-field-values\n  return `${VERSION}-${spanContext.traceId}-${spanContext.spanId}-${traceFlags}`;\n}\n"]}