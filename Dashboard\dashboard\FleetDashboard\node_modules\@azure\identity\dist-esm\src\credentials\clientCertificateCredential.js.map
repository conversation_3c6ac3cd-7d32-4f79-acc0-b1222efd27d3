{"version": 3, "file": "clientCertificateCredential.js", "sourceRoot": "", "sources": ["../../../src/credentials/clientCertificateCredential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EACL,yBAAyB,EACzB,iCAAiC,GAClC,MAAM,uBAAuB,CAAC;AAE/B,OAAO,EAAE,qBAAqB,EAAE,MAAM,yCAAyC,CAAC;AAEhF,OAAO,EAAE,gBAAgB,EAAE,MAAM,iBAAiB,CAAC;AACnD,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,MAAM,cAAc,GAAG,6BAA6B,CAAC;AACrD,MAAM,MAAM,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;AAqChD;;;;;;;GAOG;AACH,MAAM,OAAO,2BAA2B;IAoDtC,YACE,QAAgB,EAChB,QAAgB,EAChB,8BAAoF,EACpF,UAA8C,EAAE;QAEhD,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,GAAG,cAAc,kDAAkD,CAAC,CAAC;SACtF;QAED,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,4BAA4B,GAAG,iCAAiC,CACnE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,0BAA0B,CACpC,CAAC;QAEF,MAAM,aAAa,qBACd,CAAC,OAAO,8BAA8B,KAAK,QAAQ;YACpD,CAAC,CAAC;gBACE,eAAe,EAAE,8BAA8B;aAChD;YACH,CAAC,CAAC,8BAA8B,CAAC,CACpC,CAAC;QACF,MAAM,WAAW,GAAwB,aAAiD;aACvF,WAAW,CAAC;QACf,MAAM,eAAe,GACnB,aACD,CAAC,eAAe,CAAC;QAClB,IAAI,CAAC,aAAa,IAAI,CAAC,CAAC,WAAW,IAAI,eAAe,CAAC,EAAE;YACvD,MAAM,IAAI,KAAK,CACb,GAAG,cAAc,4MAA4M,CAC9N,CAAC;SACH;QACD,IAAI,WAAW,IAAI,eAAe,EAAE;YAClC,MAAM,IAAI,KAAK,CACb,GAAG,cAAc,wOAAwO,CAC1P,CAAC;SACH;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,qBAAqB,iCACpC,OAAO,KACV,aAAa;YACb,MAAM;YACN,QAAQ;YACR,QAAQ,EACR,oBAAoB,EAAE,OAAO,CAAC,oBAAoB,EAClD,sBAAsB,EAAE,OAAO,IAC/B,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAyB,EAAE,UAA2B,EAAE;QACrE,OAAO,aAAa,CAAC,QAAQ,CAAC,GAAG,cAAc,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE;YACxF,UAAU,CAAC,QAAQ,GAAG,yBAAyB,CAC7C,IAAI,CAAC,QAAQ,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,CAClC,CAAC;YAEF,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport {\n  processMultiTenantRequest,\n  resolveAddionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { ClientCertificateCredentialOptions } from \"./clientCertificateCredentialOptions\";\nimport { MsalClientCertificate } from \"../msal/nodeFlows/msalClientCertificate\";\nimport { MsalFlow } from \"../msal/flows\";\nimport { credentialLogger } from \"../util/logging\";\nimport { tracingClient } from \"../util/tracing\";\n\nconst credentialName = \"ClientCertificateCredential\";\nconst logger = credentialLogger(credentialName);\n\n/**\n * Required configuration options for the {@link ClientCertificateCredential}, with the string contents of a PEM certificate\n */\nexport interface ClientCertificatePEMCertificate {\n  /**\n   * The PEM-encoded public/private key certificate on the filesystem.\n   */\n  certificate: string;\n\n  /**\n   * The password for the certificate file.\n   */\n  certificatePassword?: string;\n}\n/**\n * Required configuration options for the {@link ClientCertificateCredential}, with the path to a PEM certificate.\n */\nexport interface ClientCertificatePEMCertificatePath {\n  /**\n   * The path to the PEM-encoded public/private key certificate on the filesystem.\n   */\n  certificatePath: string;\n\n  /**\n   * The password for the certificate file.\n   */\n  certificatePassword?: string;\n}\n/**\n * Required configuration options for the {@link ClientCertificateCredential}, with either the string contents of a PEM certificate, or the path to a PEM certificate.\n */\nexport type ClientCertificateCredentialPEMConfiguration =\n  | ClientCertificatePEMCertificate\n  | ClientCertificatePEMCertificatePath;\n\n/**\n * Enables authentication to Azure Active Directory using a PEM-encoded\n * certificate that is assigned to an App Registration. More information\n * on how to configure certificate authentication can be found here:\n *\n * https://docs.microsoft.com/en-us/azure/active-directory/develop/active-directory-certificate-credentials#register-your-certificate-with-azure-ad\n *\n */\nexport class ClientCertificateCredential implements TokenCredential {\n  private tenantId: string;\n  private additionallyAllowedTenantIds: string[];\n  private msalFlow: MsalFlow;\n\n  /**\n   * Creates an instance of the ClientCertificateCredential with the details\n   * needed to authenticate against Azure Active Directory with a certificate.\n   *\n   * @param tenantId - The Azure Active Directory tenant (directory) ID.\n   * @param clientId - The client (application) ID of an App Registration in the tenant.\n   * @param certificatePath - The path to a PEM-encoded public/private key certificate on the filesystem.\n   * @param options - Options for configuring the client which makes the authentication request.\n   */\n  constructor(\n    tenantId: string,\n    clientId: string,\n    certificatePath: string,\n    options?: ClientCertificateCredentialOptions\n  );\n  /**\n   * Creates an instance of the ClientCertificateCredential with the details\n   * needed to authenticate against Azure Active Directory with a certificate.\n   *\n   * @param tenantId - The Azure Active Directory tenant (directory) ID.\n   * @param clientId - The client (application) ID of an App Registration in the tenant.\n   * @param configuration - Other parameters required, including the path of the certificate on the filesystem.\n   *                        If the type is ignored, we will throw the value of the path to a PEM certificate.\n   * @param options - Options for configuring the client which makes the authentication request.\n   */\n  constructor(\n    tenantId: string,\n    clientId: string,\n    configuration: ClientCertificatePEMCertificatePath,\n    options?: ClientCertificateCredentialOptions\n  );\n  /**\n   * Creates an instance of the ClientCertificateCredential with the details\n   * needed to authenticate against Azure Active Directory with a certificate.\n   *\n   * @param tenantId - The Azure Active Directory tenant (directory) ID.\n   * @param clientId - The client (application) ID of an App Registration in the tenant.\n   * @param configuration - Other parameters required, including the PEM-encoded certificate as a string.\n   *                        If the type is ignored, we will throw the value of the PEM-encoded certificate.\n   * @param options - Options for configuring the client which makes the authentication request.\n   */\n  constructor(\n    tenantId: string,\n    clientId: string,\n    configuration: ClientCertificatePEMCertificate,\n    options?: ClientCertificateCredentialOptions\n  );\n  constructor(\n    tenantId: string,\n    clientId: string,\n    certificatePathOrConfiguration: string | ClientCertificateCredentialPEMConfiguration,\n    options: ClientCertificateCredentialOptions = {}\n  ) {\n    if (!tenantId || !clientId) {\n      throw new Error(`${credentialName}: tenantId and clientId are required parameters.`);\n    }\n\n    this.tenantId = tenantId;\n    this.additionallyAllowedTenantIds = resolveAddionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants\n    );\n\n    const configuration: ClientCertificateCredentialPEMConfiguration = {\n      ...(typeof certificatePathOrConfiguration === \"string\"\n        ? {\n            certificatePath: certificatePathOrConfiguration,\n          }\n        : certificatePathOrConfiguration),\n    };\n    const certificate: string | undefined = (configuration as ClientCertificatePEMCertificate)\n      .certificate;\n    const certificatePath: string | undefined = (\n      configuration as ClientCertificatePEMCertificatePath\n    ).certificatePath;\n    if (!configuration || !(certificate || certificatePath)) {\n      throw new Error(\n        `${credentialName}: Provide either a PEM certificate in string form, or the path to that certificate in the filesystem. To troubleshoot, visit https://aka.ms/azsdk/js/identity/serviceprincipalauthentication/troubleshoot.`\n      );\n    }\n    if (certificate && certificatePath) {\n      throw new Error(\n        `${credentialName}: To avoid unexpected behaviors, providing both the contents of a PEM certificate and the path to a PEM certificate is forbidden. To troubleshoot, visit https://aka.ms/azsdk/js/identity/serviceprincipalauthentication/troubleshoot.`\n      );\n    }\n    this.msalFlow = new MsalClientCertificate({\n      ...options,\n      configuration,\n      logger,\n      clientId,\n      tenantId,\n      sendCertificateChain: options.sendCertificateChain,\n      tokenCredentialOptions: options,\n    });\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  async getToken(scopes: string | string[], options: GetTokenOptions = {}): Promise<AccessToken> {\n    return tracingClient.withSpan(`${credentialName}.getToken`, options, async (newOptions) => {\n      newOptions.tenantId = processMultiTenantRequest(\n        this.tenantId,\n        newOptions,\n        this.additionallyAllowedTenantIds\n      );\n\n      const arrayScopes = Array.isArray(scopes) ? scopes : [scopes];\n      return this.msalFlow.getToken(arrayScopes, newOptions);\n    });\n  }\n}\n"]}