{"version": 3, "file": "msalClientSecret.js", "sourceRoot": "", "sources": ["../../../../src/msal/nodeFlows/msalClientSecret.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,QAAQ,EAAmB,MAAM,kBAAkB,CAAC;AAe7D;;;GAGG;AACH,MAAM,OAAO,gBAAiB,SAAQ,QAAQ;IAC5C,YAAY,OAAgC;QAC1C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;IAC3D,CAAC;IAES,KAAK,CAAC,UAAU,CACxB,MAAgB,EAChB,UAAyC,EAAE;QAE3C,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAgB,CAAC,8BAA8B,CAAC;gBACxE,MAAM;gBACN,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,CAAC,CAAC;YACH,yDAAyD;YACzD,8FAA8F;YAC9F,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,CAAC;SACtE;QAAC,OAAO,GAAQ,EAAE;YACjB,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;SAC9C;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { Msal<PERSON><PERSON>, MsalNodeOptions } from \"./msalNodeCommon\";\nimport { AccessToken } from \"@azure/core-auth\";\nimport { CredentialFlowGetTokenOptions } from \"../credentials\";\n\n/**\n * Options that can be passed to configure MS<PERSON> to handle client secrets.\n * @internal\n */\nexport interface MsalClientSecretOptions extends MsalNodeOptions {\n  /**\n   * A client secret that was generated for the App Registration.\n   */\n  clientSecret: string;\n}\n\n/**\n * MSAL client secret client. Calls to MSAL's confidential application's `acquireTokenByClientCredential` during `doGetToken`.\n * @internal\n */\nexport class MsalClientSecret extends MsalNode {\n  constructor(options: MsalClientSecretOptions) {\n    super(options);\n    this.requiresConfidential = true;\n    this.msalConfig.auth.clientSecret = options.clientSecret;\n  }\n\n  protected async doGetToken(\n    scopes: string[],\n    options: CredentialFlowGetTokenOptions = {}\n  ): Promise<AccessToken> {\n    try {\n      const result = await this.confidentialApp!.acquireTokenByClientCredential({\n        scopes,\n        correlationId: options.correlationId,\n        azureRegion: this.azureRegion,\n        authority: options.authority,\n        claims: options.claims,\n      });\n      // The Client Credential flow does not return an account,\n      // so each time getToken gets called, we will have to acquire a new token through the service.\n      return this.handleResult(scopes, this.clientId, result || undefined);\n    } catch (err: any) {\n      throw this.handleError(scopes, err, options);\n    }\n  }\n}\n"]}