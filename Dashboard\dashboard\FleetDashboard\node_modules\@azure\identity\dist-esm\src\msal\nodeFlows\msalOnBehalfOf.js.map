{"version": 3, "file": "msalOnBehalfOf.js", "sourceRoot": "", "sources": ["../../../../src/msal/nodeFlows/msalOnBehalfOf.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,QAAQ,EAAmB,MAAM,kBAAkB,CAAC;AAG7D,OAAO,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AACjD,OAAO,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AA0B3D;;;GAGG;AACH,MAAM,OAAO,cAAe,SAAQ,QAAQ;IAM1C,YAAY,OAA8B;QACxC,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACzD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;QACrD,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAC/C,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,CAAC;QACzD,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;IAC3C,CAAC;IAED,iDAAiD;IACjD,KAAK,CAAC,IAAI,CAAC,OAAuC;QAChD,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI;gBACF,MAAM,KAAK,GAAG,MAAM,gBAAgB,CAClC,EAAE,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,EACzC,IAAI,CAAC,oBAAoB,CAC1B,CAAC;gBACF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,GAAG;oBACvC,UAAU,EAAE,KAAK,CAAC,UAAU;oBAC5B,UAAU,EAAE,KAAK,CAAC,mBAAmB;oBACrC,GAAG,EAAE,KAAK,CAAC,GAAG;iBACf,CAAC;aACH;YAAC,OAAO,KAAU,EAAE;gBACnB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;gBACzC,MAAM,KAAK,CAAC;aACb;SACF;aAAM;YACL,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;SACvD;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC;IAES,KAAK,CAAC,UAAU,CACxB,MAAgB,EAChB,UAAyC,EAAE;QAE3C,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAgB,CAAC,sBAAsB,CAAC;gBAChE,MAAM;gBACN,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,YAAY,EAAE,IAAI,CAAC,kBAAkB;aACtC,CAAC,CAAC;YACH,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,CAAC;SACtE;QAAC,OAAO,GAAQ,EAAE;YACjB,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;SAC9C;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { MsalNode, MsalNodeOptions } from \"./msalNodeCommon\";\nimport { AccessToken } from \"@azure/core-auth\";\nimport { CredentialFlowGetTokenOptions } from \"../credentials\";\nimport { formatError } from \"../../util/logging\";\nimport { parseCertificate } from \"./msalClientCertificate\";\n\n/**\n * Options that can be passed to configure MSAL to handle On-Behalf-Of authentication requests.\n * @internal\n */\nexport interface MsalOnBehalfOfOptions extends MsalNodeOptions {\n  /**\n   * A client secret that was generated for the App Registration.\n   */\n  clientSecret?: string;\n  /**\n   * Location of the PEM certificate.\n   */\n  certificatePath?: string;\n  /**\n   * Option to include x5c header for SubjectName and Issuer name authorization.\n   * Set this option to send base64 encoded public certificate in the client assertion header as an x5c claim\n   */\n  sendCertificateChain?: boolean;\n  /**\n   * The user assertion for the On-Behalf-Of flow.\n   */\n  userAssertionToken: string;\n}\n\n/**\n * MSAL on behalf of flow. Calls to MSAL's confidential application's `acquireTokenOnBehalfOf` during `doGetToken`.\n * @internal\n */\nexport class MsalOnBehalfOf extends MsalNode {\n  private userAssertionToken: string;\n  private certificatePath?: string;\n  private sendCertificateChain?: boolean;\n  private clientSecret?: string;\n\n  constructor(options: MsalOnBehalfOfOptions) {\n    super(options);\n    this.logger.info(\"Initialized MSAL's On-Behalf-Of flow\");\n    this.requiresConfidential = true;\n    this.userAssertionToken = options.userAssertionToken;\n    this.certificatePath = options.certificatePath;\n    this.sendCertificateChain = options.sendCertificateChain;\n    this.clientSecret = options.clientSecret;\n  }\n\n  // Changing the MSAL configuration asynchronously\n  async init(options?: CredentialFlowGetTokenOptions): Promise<void> {\n    if (this.certificatePath) {\n      try {\n        const parts = await parseCertificate(\n          { certificatePath: this.certificatePath },\n          this.sendCertificateChain\n        );\n        this.msalConfig.auth.clientCertificate = {\n          thumbprint: parts.thumbprint,\n          privateKey: parts.certificateContents,\n          x5c: parts.x5c,\n        };\n      } catch (error: any) {\n        this.logger.info(formatError(\"\", error));\n        throw error;\n      }\n    } else {\n      this.msalConfig.auth.clientSecret = this.clientSecret;\n    }\n    return super.init(options);\n  }\n\n  protected async doGetToken(\n    scopes: string[],\n    options: CredentialFlowGetTokenOptions = {}\n  ): Promise<AccessToken> {\n    try {\n      const result = await this.confidentialApp!.acquireTokenOnBehalfOf({\n        scopes,\n        correlationId: options.correlationId,\n        authority: options.authority,\n        claims: options.claims,\n        oboAssertion: this.userAssertionToken,\n      });\n      return this.handleResult(scopes, this.clientId, result || undefined);\n    } catch (err: any) {\n      throw this.handleError(scopes, err, options);\n    }\n  }\n}\n"]}