{"version": 3, "file": "credentials.js", "sourceRoot": "", "sources": ["../../../src/credentials/credentials.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * A function that receives a challenge and resolves a promise with a string token.\n * @deprecated The Authenticator type is not currently in use.\n */\nexport type Authenticator = (challenge: unknown) => Promise<string>;\n"]}