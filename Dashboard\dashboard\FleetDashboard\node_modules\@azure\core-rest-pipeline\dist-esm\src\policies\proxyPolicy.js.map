{"version": 3, "file": "proxyPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/proxyPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EAAE,eAAe,EAA0B,MAAM,mBAAmB,CAAC;AAC5E,OAAO,EAAE,cAAc,EAAyB,MAAM,kBAAkB,CAAC;AAGzE,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAEhC,MAAM,WAAW,GAAG,aAAa,CAAC;AAClC,MAAM,UAAU,GAAG,YAAY,CAAC;AAChC,MAAM,SAAS,GAAG,WAAW,CAAC;AAC9B,MAAM,QAAQ,GAAG,UAAU,CAAC;AAE5B;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,aAAa,CAAC;AAE7C;;;GAGG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAa,EAAE,CAAC;AAC9C,IAAI,iBAAiB,GAAY,KAAK,CAAC;AAEvC,yDAAyD;AACzD,MAAM,iBAAiB,GAAyB,IAAI,GAAG,EAAE,CAAC;AAE1D,SAAS,mBAAmB,CAAC,IAAY;IACvC,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QACrB,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KAC1B;SAAM,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE;QAC1C,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;KACxC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,yBAAyB;IAChC,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,SAAS,CAAC;KAClB;IAED,MAAM,UAAU,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC;IACpD,MAAM,QAAQ,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC;IAChD,MAAM,SAAS,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;IAElD,OAAO,UAAU,IAAI,QAAQ,IAAI,SAAS,CAAC;AAC7C,CAAC;AAED;;;;GAIG;AACH,SAAS,UAAU,CACjB,GAAW,EACX,WAAqB,EACrB,WAAkC;IAElC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;QAC5B,OAAO,KAAK,CAAC;KACd;IACD,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC;IACnC,IAAI,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,GAAG,CAAC,IAAI,CAAC,EAAE;QAC1B,OAAO,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KAC9B;IACD,IAAI,cAAc,GAAG,KAAK,CAAC;IAC3B,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE;QACjC,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACtB,mEAAmE;YACnE,mDAAmD;YACnD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;gBAC1B,cAAc,GAAG,IAAI,CAAC;aACvB;iBAAM;gBACL,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;oBACnE,cAAc,GAAG,IAAI,CAAC;iBACvB;aACF;SACF;aAAM;YACL,IAAI,IAAI,KAAK,OAAO,EAAE;gBACpB,cAAc,GAAG,IAAI,CAAC;aACvB;SACF;KACF;IACD,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,GAAG,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;IACvC,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,MAAM,UAAU,WAAW;IACzB,MAAM,OAAO,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IAC9C,iBAAiB,GAAG,IAAI,CAAC;IACzB,IAAI,OAAO,EAAE;QACX,OAAO,OAAO;aACX,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;aAC1B,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAClC;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,uBAAuB,CAAC,QAAiB;IACvD,IAAI,CAAC,QAAQ,EAAE;QACb,QAAQ,GAAG,yBAAyB,EAAE,CAAC;QACvC,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,SAAS,CAAC;SAClB;KACF;IAED,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;IACpC,MAAM,MAAM,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;IACnE,OAAO;QACL,IAAI,EAAE,MAAM,GAAG,SAAS,CAAC,QAAQ;QACjC,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC;QAC7C,QAAQ,EAAE,SAAS,CAAC,QAAQ;QAC5B,QAAQ,EAAE,SAAS,CAAC,QAAQ;KAC7B,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,oBAAoB,CAClC,aAA4B,EAC5B,EAAE,OAAO,EAAE,WAAW,EAAmB;IAEzC,IAAI,cAAmB,CAAC;IACxB,IAAI;QACF,cAAc,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;KAC9C;IAAC,OAAO,MAAM,EAAE;QACf,MAAM,IAAI,KAAK,CACb,+DAA+D,aAAa,CAAC,IAAI,IAAI,CACtF,CAAC;KACH;IAED,IAAI,WAAW,EAAE;QACf,MAAM,CAAC,OAAO,CACZ,uHAAuH,CACxH,CAAC;KACH;IAED,MAAM,iBAAiB,GAA2B;QAChD,QAAQ,EAAE,cAAc,CAAC,QAAQ;QACjC,IAAI,EAAE,aAAa,CAAC,IAAI;QACxB,QAAQ,EAAE,cAAc,CAAC,QAAQ;QACjC,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE;KAC1B,CAAC;IACF,IAAI,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,QAAQ,EAAE;QACpD,iBAAiB,CAAC,IAAI,GAAG,GAAG,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,QAAQ,EAAE,CAAC;KAChF;SAAM,IAAI,aAAa,CAAC,QAAQ,EAAE;QACjC,iBAAiB,CAAC,IAAI,GAAG,GAAG,aAAa,CAAC,QAAQ,EAAE,CAAC;KACtD;IACD,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAED,SAAS,sBAAsB,CAAC,OAAwB,EAAE,YAA0B;IAClF,2DAA2D;IAC3D,0CAA0C;IAC1C,IAAI,OAAO,CAAC,KAAK,EAAE;QACjB,OAAO;KACR;IAED,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAEjC,MAAM,UAAU,GAAG,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC;IAE7C,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;IAC5C,IAAI,aAAa,EAAE;QACjB,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE;gBAChC,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;gBACvE,YAAY,CAAC,cAAc,GAAG,IAAI,cAAc,CAAC,iBAAiB,CAAC,CAAC;aACrE;YACD,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,cAAc,CAAC;SAC7C;aAAM;YACL,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE;gBACjC,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;gBACvE,YAAY,CAAC,eAAe,GAAG,IAAI,eAAe,CAAC,iBAAiB,CAAC,CAAC;aACvE;YACD,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,eAAe,CAAC;SAC9C;KACF;AACH,CAAC;AAOD;;;;;;GAMG;AACH,MAAM,UAAU,WAAW,CACzB,aAAa,GAAG,uBAAuB,EAAE,EACzC,OAGC;IAED,IAAI,CAAC,iBAAiB,EAAE;QACtB,iBAAiB,CAAC,IAAI,CAAC,GAAG,WAAW,EAAE,CAAC,CAAC;KAC1C;IAED,MAAM,YAAY,GAAiB,EAAE,CAAC;IAEtC,OAAO;QACL,IAAI,EAAE,eAAe;QACrB,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;;YAC3D,IACE,CAAC,OAAO,CAAC,aAAa;gBACtB,CAAC,UAAU,CACT,OAAO,CAAC,GAAG,EACX,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,iBAAiB,mCAAI,iBAAiB,EAC/C,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,iBAAiB,EAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,CAC3D,EACD;gBACA,OAAO,CAAC,aAAa,GAAG,aAAa,CAAC;aACvC;YAED,IAAI,OAAO,CAAC,aAAa,EAAE;gBACzB,sBAAsB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;aAC/C;YACD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as http from \"http\";\nimport * as https from \"https\";\nimport { HttpsProxyAgent, HttpsProxyAgentOptions } from \"https-proxy-agent\";\nimport { HttpProxyAgent, HttpProxyAgentOptions } from \"http-proxy-agent\";\nimport { PipelineRequest, PipelineResponse, ProxySettings, SendRequest } from \"../interfaces\";\nimport { PipelinePolicy } from \"../pipeline\";\nimport { logger } from \"../log\";\n\nconst HTTPS_PROXY = \"HTTPS_PROXY\";\nconst HTTP_PROXY = \"HTTP_PROXY\";\nconst ALL_PROXY = \"ALL_PROXY\";\nconst NO_PROXY = \"NO_PROXY\";\n\n/**\n * The programmatic identifier of the proxyPolicy.\n */\nexport const proxyPolicyName = \"proxyPolicy\";\n\n/**\n * Stores the patterns specified in NO_PROXY environment variable.\n * @internal\n */\nexport const globalNoProxyList: string[] = [];\nlet noProxyListLoaded: boolean = false;\n\n/** A cache of whether a host should bypass the proxy. */\nconst globalBypassedMap: Map<string, boolean> = new Map();\n\nfunction getEnvironmentValue(name: string): string | undefined {\n  if (process.env[name]) {\n    return process.env[name];\n  } else if (process.env[name.toLowerCase()]) {\n    return process.env[name.toLowerCase()];\n  }\n  return undefined;\n}\n\nfunction loadEnvironmentProxyValue(): string | undefined {\n  if (!process) {\n    return undefined;\n  }\n\n  const httpsProxy = getEnvironmentValue(HTTPS_PROXY);\n  const allProxy = getEnvironmentValue(ALL_PROXY);\n  const httpProxy = getEnvironmentValue(HTTP_PROXY);\n\n  return httpsProxy || allProxy || httpProxy;\n}\n\n/**\n * Check whether the host of a given `uri` matches any pattern in the no proxy list.\n * If there's a match, any request sent to the same host shouldn't have the proxy settings set.\n * This implementation is a port of https://github.com/Azure/azure-sdk-for-net/blob/8cca811371159e527159c7eb65602477898683e2/sdk/core/Azure.Core/src/Pipeline/Internal/HttpEnvironmentProxy.cs#L210\n */\nfunction isBypassed(\n  uri: string,\n  noProxyList: string[],\n  bypassedMap?: Map<string, boolean>\n): boolean | undefined {\n  if (noProxyList.length === 0) {\n    return false;\n  }\n  const host = new URL(uri).hostname;\n  if (bypassedMap?.has(host)) {\n    return bypassedMap.get(host);\n  }\n  let isBypassedFlag = false;\n  for (const pattern of noProxyList) {\n    if (pattern[0] === \".\") {\n      // This should match either domain it self or any subdomain or host\n      // .foo.com will match foo.com it self or *.foo.com\n      if (host.endsWith(pattern)) {\n        isBypassedFlag = true;\n      } else {\n        if (host.length === pattern.length - 1 && host === pattern.slice(1)) {\n          isBypassedFlag = true;\n        }\n      }\n    } else {\n      if (host === pattern) {\n        isBypassedFlag = true;\n      }\n    }\n  }\n  bypassedMap?.set(host, isBypassedFlag);\n  return isBypassedFlag;\n}\n\nexport function loadNoProxy(): string[] {\n  const noProxy = getEnvironmentValue(NO_PROXY);\n  noProxyListLoaded = true;\n  if (noProxy) {\n    return noProxy\n      .split(\",\")\n      .map((item) => item.trim())\n      .filter((item) => item.length);\n  }\n\n  return [];\n}\n\n/**\n * This method converts a proxy url into `ProxySettings` for use with ProxyPolicy.\n * If no argument is given, it attempts to parse a proxy URL from the environment\n * variables `HTTPS_PROXY` or `HTTP_PROXY`.\n * @param proxyUrl - The url of the proxy to use. May contain authentication information.\n */\nexport function getDefaultProxySettings(proxyUrl?: string): ProxySettings | undefined {\n  if (!proxyUrl) {\n    proxyUrl = loadEnvironmentProxyValue();\n    if (!proxyUrl) {\n      return undefined;\n    }\n  }\n\n  const parsedUrl = new URL(proxyUrl);\n  const schema = parsedUrl.protocol ? parsedUrl.protocol + \"//\" : \"\";\n  return {\n    host: schema + parsedUrl.hostname,\n    port: Number.parseInt(parsedUrl.port || \"80\"),\n    username: parsedUrl.username,\n    password: parsedUrl.password,\n  };\n}\n\n/**\n * @internal\n */\nexport function getProxyAgentOptions(\n  proxySettings: ProxySettings,\n  { headers, tlsSettings }: PipelineRequest\n): HttpProxyAgentOptions {\n  let parsedProxyUrl: URL;\n  try {\n    parsedProxyUrl = new URL(proxySettings.host);\n  } catch (_error) {\n    throw new Error(\n      `Expecting a valid host string in proxy settings, but found \"${proxySettings.host}\".`\n    );\n  }\n\n  if (tlsSettings) {\n    logger.warning(\n      \"TLS settings are not supported in combination with custom Proxy, certificates provided to the client will be ignored.\"\n    );\n  }\n\n  const proxyAgentOptions: HttpsProxyAgentOptions = {\n    hostname: parsedProxyUrl.hostname,\n    port: proxySettings.port,\n    protocol: parsedProxyUrl.protocol,\n    headers: headers.toJSON(),\n  };\n  if (proxySettings.username && proxySettings.password) {\n    proxyAgentOptions.auth = `${proxySettings.username}:${proxySettings.password}`;\n  } else if (proxySettings.username) {\n    proxyAgentOptions.auth = `${proxySettings.username}`;\n  }\n  return proxyAgentOptions;\n}\n\nfunction setProxyAgentOnRequest(request: PipelineRequest, cachedAgents: CachedAgents): void {\n  // Custom Agent should take precedence so if one is present\n  // we should skip to avoid overwriting it.\n  if (request.agent) {\n    return;\n  }\n\n  const url = new URL(request.url);\n\n  const isInsecure = url.protocol !== \"https:\";\n\n  const proxySettings = request.proxySettings;\n  if (proxySettings) {\n    if (isInsecure) {\n      if (!cachedAgents.httpProxyAgent) {\n        const proxyAgentOptions = getProxyAgentOptions(proxySettings, request);\n        cachedAgents.httpProxyAgent = new HttpProxyAgent(proxyAgentOptions);\n      }\n      request.agent = cachedAgents.httpProxyAgent;\n    } else {\n      if (!cachedAgents.httpsProxyAgent) {\n        const proxyAgentOptions = getProxyAgentOptions(proxySettings, request);\n        cachedAgents.httpsProxyAgent = new HttpsProxyAgent(proxyAgentOptions);\n      }\n      request.agent = cachedAgents.httpsProxyAgent;\n    }\n  }\n}\n\ninterface CachedAgents {\n  httpsProxyAgent?: https.Agent;\n  httpProxyAgent?: http.Agent;\n}\n\n/**\n * A policy that allows one to apply proxy settings to all requests.\n * If not passed static settings, they will be retrieved from the HTTPS_PROXY\n * or HTTP_PROXY environment variables.\n * @param proxySettings - ProxySettings to use on each request.\n * @param options - additional settings, for example, custom NO_PROXY patterns\n */\nexport function proxyPolicy(\n  proxySettings = getDefaultProxySettings(),\n  options?: {\n    /** a list of patterns to override those loaded from NO_PROXY environment variable. */\n    customNoProxyList?: string[];\n  }\n): PipelinePolicy {\n  if (!noProxyListLoaded) {\n    globalNoProxyList.push(...loadNoProxy());\n  }\n\n  const cachedAgents: CachedAgents = {};\n\n  return {\n    name: proxyPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      if (\n        !request.proxySettings &&\n        !isBypassed(\n          request.url,\n          options?.customNoProxyList ?? globalNoProxyList,\n          options?.customNoProxyList ? undefined : globalBypassedMap\n        )\n      ) {\n        request.proxySettings = proxySettings;\n      }\n\n      if (request.proxySettings) {\n        setProxyAgentOnRequest(request, cachedAgents);\n      }\n      return next(request);\n    },\n  };\n}\n"]}