{"version": 3, "file": "restError.js", "sourceRoot": "", "sources": ["../../src/restError.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,OAAO,EAAE,MAAM,kBAAkB,CAAC;AAE3C,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,SAAS,EAAE,MAAM,kBAAkB,CAAC;AAE7C,MAAM,cAAc,GAAG,IAAI,SAAS,EAAE,CAAC;AAwBvC;;GAEG;AACH,MAAM,OAAO,SAAU,SAAQ,KAAK;IAkClC,YAAY,OAAe,EAAE,UAA4B,EAAE;QACzD,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;QACxB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAEjC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,CAAC,MAAM,CAAC;QACN,OAAO,cAAc,IAAI,CAAC,OAAO,OAAO,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;IAC1E,CAAC;;AAjDD;;;;GAIG;AACa,4BAAkB,GAAW,oBAAoB,CAAC;AAClE;;;GAGG;AACa,qBAAW,GAAW,aAAa,CAAC;AA0CtD;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,CAAU;IACpC,IAAI,CAAC,YAAY,SAAS,EAAE;QAC1B,OAAO,IAAI,CAAC;KACb;IACD,OAAO,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC;AAC9C,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { isError } from \"@azure/core-util\";\nimport { PipelineRequest, PipelineResponse } from \"./interfaces\";\nimport { custom } from \"./util/inspect\";\nimport { Sanitizer } from \"./util/sanitizer\";\n\nconst errorSanitizer = new Sanitizer();\n\n/**\n * The options supported by RestError.\n */\nexport interface RestErrorOptions {\n  /**\n   * The code of the error itself (use statics on RestError if possible.)\n   */\n  code?: string;\n  /**\n   * The HTTP status code of the request (if applicable.)\n   */\n  statusCode?: number;\n  /**\n   * The request that was made.\n   */\n  request?: PipelineRequest;\n  /**\n   * The response received (if any.)\n   */\n  response?: PipelineResponse;\n}\n\n/**\n * A custom error type for failed pipeline requests.\n */\nexport class RestError extends Error {\n  /**\n   * Something went wrong when making the request.\n   * This means the actual request failed for some reason,\n   * such as a DNS issue or the connection being lost.\n   */\n  static readonly REQUEST_SEND_ERROR: string = \"REQUEST_SEND_ERROR\";\n  /**\n   * This means that parsing the response from the server failed.\n   * It may have been malformed.\n   */\n  static readonly PARSE_ERROR: string = \"PARSE_ERROR\";\n\n  /**\n   * The code of the error itself (use statics on RestError if possible.)\n   */\n  public code?: string;\n  /**\n   * The HTTP status code of the request (if applicable.)\n   */\n  public statusCode?: number;\n  /**\n   * The request that was made.\n   */\n  public request?: PipelineRequest;\n  /**\n   * The response received (if any.)\n   */\n  public response?: PipelineResponse;\n  /**\n   * Bonus property set by the throw site.\n   */\n  public details?: unknown;\n\n  constructor(message: string, options: RestErrorOptions = {}) {\n    super(message);\n    this.name = \"RestError\";\n    this.code = options.code;\n    this.statusCode = options.statusCode;\n    this.request = options.request;\n    this.response = options.response;\n\n    Object.setPrototypeOf(this, RestError.prototype);\n  }\n\n  /**\n   * Logging method for util.inspect in Node\n   */\n  [custom](): string {\n    return `RestError: ${this.message} \\n ${errorSanitizer.sanitize(this)}`;\n  }\n}\n\n/**\n * Typeguard for RestError\n * @param e - Something caught by a catch clause.\n */\nexport function isRestError(e: unknown): e is RestError {\n  if (e instanceof RestError) {\n    return true;\n  }\n  return isError(e) && e.name === \"RestError\";\n}\n"]}