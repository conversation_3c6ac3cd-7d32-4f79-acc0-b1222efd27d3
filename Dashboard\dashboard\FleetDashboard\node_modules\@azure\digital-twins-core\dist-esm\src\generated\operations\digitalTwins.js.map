{"version": 3, "file": "digitalTwins.js", "sourceRoot": "", "sources": ["../../../../src/generated/operations/digitalTwins.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,OAAO,KAAK,QAAQ,MAAM,kBAAkB,CAAC;AAC7C,OAAO,KAAK,OAAO,MAAM,mBAAmB,CAAC;AAC7C,OAAO,KAAK,UAAU,MAAM,sBAAsB,CAAC;AAiCnD;;GAEG;AACH,MAAM,OAAO,YAAY;IAGvB;;;OAGG;IACH,YAAY,MAA4B;QACtC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;;;;;;;;;OAUG;IACH,OAAO,CACL,EAAU,EACV,OAA2C;QAE3C,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjC,oBAAoB,CACmB,CAAC;IAC5C,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,GAAG,CACD,EAAU,EACV,IAAS,EACT,OAAuC;QAEvC,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACvC,gBAAgB,CACmB,CAAC;IACxC,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,MAAM,CACJ,EAAU,EACV,OAA0C;QAE1C,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjC,mBAAmB,CACc,CAAC;IACtC,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,MAAM,CACJ,EAAU,EACV,aAAoB,EACpB,OAA0C;QAE1C,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,aAAa,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAChD,mBAAmB,CACmB,CAAC;IAC3C,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,mBAAmB,CACjB,EAAU,EACV,cAAsB,EACtB,OAAuD;QAEvD,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,cAAc,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjD,gCAAgC,CACmB,CAAC;IACxD,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACH,eAAe,CACb,EAAU,EACV,cAAsB,EACtB,YAAiB,EACjB,OAAmD;QAEnD,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,cAAc,EAAE,YAAY,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAC/D,4BAA4B,CACmB,CAAC;IACpD,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,kBAAkB,CAChB,EAAU,EACV,cAAsB,EACtB,OAAsD;QAEtD,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,cAAc,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjD,+BAA+B,CACE,CAAC;IACtC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,kBAAkB,CAChB,EAAU,EACV,cAAsB,EACtB,aAAoB,EACpB,OAAsD;QAEtD,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,cAAc,EAAE,aAAa,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAChE,+BAA+B,CACmB,CAAC;IACvD,CAAC;IAED;;;;;;;;;;OAUG;IACH,iBAAiB,CACf,EAAU,EACV,OAAqD;QAErD,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjC,8BAA8B,CACmB,CAAC;IACtD,CAAC;IAED;;;;;;;;;;OAUG;IACH,yBAAyB,CACvB,EAAU,EACV,OAA6D;QAE7D,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACjC,sCAAsC,CACmB,CAAC;IAC9D,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,aAAa,CACX,EAAU,EACV,SAAiB,EACjB,SAAc,EACd,OAAiD;QAEjD,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACvD,0BAA0B,CACO,CAAC;IACtC,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,sBAAsB,CACpB,EAAU,EACV,aAAqB,EACrB,SAAiB,EACjB,SAAc,EACd,OAA0D;QAE1D,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,gBAAgB,EAAE,EACtE,mCAAmC,CACF,CAAC;IACtC,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,YAAY,CACV,EAAU,EACV,aAAqB,EACrB,OAAgD;QAEhD,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,aAAa,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAChD,yBAAyB,CACmB,CAAC;IACjD,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,eAAe,CACb,EAAU,EACV,aAAqB,EACrB,aAAoB,EACpB,OAAmD;QAEnD,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,aAAa,EAAE,aAAa,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAC/D,4BAA4B,CACmB,CAAC;IACpD,CAAC;IAED;;;;;OAKG;IACH,qBAAqB,CACnB,EAAU,EACV,QAAgB,EAChB,OAAyD;QAEzD,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAC3C,kCAAkC,CACmB,CAAC;IAC1D,CAAC;IAED;;;;;;OAMG;IACH,6BAA6B,CAC3B,EAAU,EACV,QAAgB,EAChB,OAAiE;QAEjE,MAAM,gBAAgB,GAAgC,QAAQ,CAAC,oCAAoC,CACjG,OAAO,IAAI,EAAE,CACd,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAC3C,0CAA0C,CACmB,CAAC;IAClE,CAAC;CACF;AACD,2BAA2B;AAE3B,MAAM,UAAU,GAAG,IAAI,QAAQ,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;AAEvE,MAAM,oBAAoB,GAA2B;IACnD,IAAI,EAAE,oBAAoB;IAC1B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;YACrC,aAAa,EAAE,OAAO,CAAC,0BAA0B;SAClD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC;IAChD,gBAAgB,EAAE,CAAC,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,UAAU,CAAC;IACjE,UAAU;CACX,CAAC;AACF,MAAM,gBAAgB,GAA2B;IAC/C,IAAI,EAAE,oBAAoB;IAC1B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;YACrC,aAAa,EAAE,OAAO,CAAC,sBAAsB;SAC9C;QACD,GAAG,EAAE,EAAE;QACP,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,WAAW,EAAE,UAAU,CAAC,IAAI;IAC5B,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC;IAChD,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,WAAW;KACvB;IACD,SAAS,EAAE,MAAM;IACjB,UAAU;CACX,CAAC;AACF,MAAM,mBAAmB,GAA2B;IAClD,IAAI,EAAE,oBAAoB;IAC1B,UAAU,EAAE,QAAQ;IACpB,SAAS,EAAE;QACT,GAAG,EAAE,EAAE;QACP,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC;IAChD,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,OAAO;KACnB;IACD,UAAU;CACX,CAAC;AACF,MAAM,mBAAmB,GAA2B;IAClD,IAAI,EAAE,oBAAoB;IAC1B,UAAU,EAAE,OAAO;IACnB,SAAS,EAAE;QACT,GAAG,EAAE,EAAE;QACP,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,yBAAyB;SACjD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,WAAW,EAAE,UAAU,CAAC,aAAa;IACrC,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC;IAChD,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,YAAY;QACvB,UAAU,CAAC,OAAO;KACnB;IACD,SAAS,EAAE,MAAM;IACjB,UAAU;CACX,CAAC;AACF,MAAM,gCAAgC,GAA2B;IAC/D,IAAI,EAAE,mDAAmD;IACzD,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;YACrC,aAAa,EAAE,OAAO,CAAC,sCAAsC;SAC9D;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,cAAc,CAAC;IAC3E,gBAAgB,EAAE,CAAC,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,UAAU,CAAC;IACjE,UAAU;CACX,CAAC;AACF,MAAM,4BAA4B,GAA2B;IAC3D,IAAI,EAAE,mDAAmD;IACzD,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;YACrC,aAAa,EAAE,OAAO,CAAC,kCAAkC;SAC1D;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,WAAW,EAAE,UAAU,CAAC,YAAY;IACpC,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,cAAc,CAAC;IAC3E,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,WAAW;KACvB;IACD,SAAS,EAAE,MAAM;IACjB,UAAU;CACX,CAAC;AACF,MAAM,+BAA+B,GAA2B;IAC9D,IAAI,EAAE,mDAAmD;IACzD,UAAU,EAAE,QAAQ;IACpB,SAAS,EAAE;QACT,GAAG,EAAE,EAAE;QACP,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,cAAc,CAAC;IAC3E,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,OAAO;KACnB;IACD,UAAU;CACX,CAAC;AACF,MAAM,+BAA+B,GAA2B;IAC9D,IAAI,EAAE,mDAAmD;IACzD,UAAU,EAAE,OAAO;IACnB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,qCAAqC;SAC7D;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,WAAW,EAAE,UAAU,CAAC,aAAa;IACrC,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,cAAc,CAAC;IAC3E,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,YAAY;QACvB,UAAU,CAAC,OAAO;KACnB;IACD,SAAS,EAAE,MAAM;IACjB,UAAU;CACX,CAAC;AACF,MAAM,8BAA8B,GAA2B;IAC7D,IAAI,EAAE,kCAAkC;IACxC,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,sBAAsB;SAC3C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,gBAAgB,CAAC;IACrE,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC;IAChD,gBAAgB,EAAE,CAAC,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,UAAU,CAAC;IACjE,UAAU;CACX,CAAC;AACF,MAAM,sCAAsC,GAA2B;IACrE,IAAI,EAAE,0CAA0C;IAChD,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,8BAA8B;SACnD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC;IAChD,gBAAgB,EAAE,CAAC,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,UAAU,CAAC;IACjE,UAAU;CACX,CAAC;AACF,MAAM,0BAA0B,GAA2B;IACzD,IAAI,EAAE,8BAA8B;IACpC,UAAU,EAAE,MAAM;IAClB,SAAS,EAAE;QACT,GAAG,EAAE,EAAE;QACP,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,WAAW,EAAE,UAAU,CAAC,SAAS;IACjC,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC;IAChD,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,mBAAmB;KAC/B;IACD,SAAS,EAAE,MAAM;IACjB,UAAU;CACX,CAAC;AACF,MAAM,mCAAmC,GAA2B;IAClE,IAAI,EAAE,yDAAyD;IAC/D,UAAU,EAAE,MAAM;IAClB,SAAS,EAAE;QACT,GAAG,EAAE,EAAE;QACP,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,WAAW,EAAE,UAAU,CAAC,SAAS;IACjC,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,aAAa,CAAC;IAC1E,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,mBAAmB;KAC/B;IACD,SAAS,EAAE,MAAM;IACjB,UAAU;CACX,CAAC;AACF,MAAM,yBAAyB,GAA2B;IACxD,IAAI,EAAE,+CAA+C;IACrD,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;YACrC,aAAa,EAAE,OAAO,CAAC,+BAA+B;SACvD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,aAAa,CAAC;IAC1E,gBAAgB,EAAE,CAAC,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,UAAU,CAAC;IACjE,UAAU;CACX,CAAC;AACF,MAAM,4BAA4B,GAA2B;IAC3D,IAAI,EAAE,+CAA+C;IACrD,UAAU,EAAE,OAAO;IACnB,SAAS,EAAE;QACT,GAAG,EAAE,EAAE;QACP,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,kCAAkC;SAC1D;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,WAAW,EAAE,UAAU,CAAC,aAAa;IACrC,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,aAAa,CAAC;IAC1E,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,YAAY;QACvB,UAAU,CAAC,OAAO;KACnB;IACD,SAAS,EAAE,MAAM;IACjB,UAAU;CACX,CAAC;AACF,MAAM,kCAAkC,GAA2B;IACjE,IAAI,EAAE,YAAY;IAClB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,sBAAsB;SAC3C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,gBAAgB,CAAC;IACrE,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC;IACrE,gBAAgB,EAAE,CAAC,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,UAAU,CAAC;IACjE,UAAU;CACX,CAAC;AACF,MAAM,0CAA0C,GAA2B;IACzE,IAAI,EAAE,YAAY;IAClB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,8BAA8B;SACnD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,aAAa;SAClC;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACxC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC;IACrE,gBAAgB,EAAE,CAAC,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,UAAU,CAAC;IACjE,UAAU;CACX,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreHttp from \"@azure/core-http\";\nimport * as Mappers from \"../models/mappers\";\nimport * as Parameters from \"../models/parameters\";\nimport { AzureDigitalTwinsAPI } from \"../azureDigitalTwinsAPI\";\nimport {\n  DigitalTwinsGetByIdOptionalParams,\n  DigitalTwinsGetByIdResponse,\n  DigitalTwinsAddOptionalParams,\n  DigitalTwinsAddResponse,\n  DigitalTwinsDeleteOptionalParams,\n  DigitalTwinsUpdateOptionalParams,\n  DigitalTwinsUpdateResponse,\n  DigitalTwinsGetRelationshipByIdOptionalParams,\n  DigitalTwinsGetRelationshipByIdResponse,\n  DigitalTwinsAddRelationshipOptionalParams,\n  DigitalTwinsAddRelationshipResponse,\n  DigitalTwinsDeleteRelationshipOptionalParams,\n  DigitalTwinsUpdateRelationshipOptionalParams,\n  DigitalTwinsUpdateRelationshipResponse,\n  DigitalTwinsListRelationshipsOptionalParams,\n  DigitalTwinsListRelationshipsResponse,\n  DigitalTwinsListIncomingRelationshipsOptionalParams,\n  DigitalTwinsListIncomingRelationshipsResponse,\n  DigitalTwinsSendTelemetryOptionalParams,\n  DigitalTwinsSendComponentTelemetryOptionalParams,\n  DigitalTwinsGetComponentOptionalParams,\n  DigitalTwinsGetComponentResponse,\n  DigitalTwinsUpdateComponentOptionalParams,\n  DigitalTwinsUpdateComponentResponse,\n  DigitalTwinsListRelationshipsNextOptionalParams,\n  DigitalTwinsListRelationshipsNextResponse,\n  DigitalTwinsListIncomingRelationshipsNextOptionalParams,\n  DigitalTwinsListIncomingRelationshipsNextResponse\n} from \"../models\";\n\n/**\n * Class representing a DigitalTwins.\n */\nexport class DigitalTwins {\n  private readonly client: AzureDigitalTwinsAPI;\n\n  /**\n   * Initialize a new instance of the class DigitalTwins class.\n   * @param client Reference to the service client\n   */\n  constructor(client: AzureDigitalTwinsAPI) {\n    this.client = client;\n  }\n\n  /**\n   * Retrieves a digital twin.\n   * Status codes:\n   * * 200 OK\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id is invalid.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param options The options parameters.\n   */\n  getById(\n    id: string,\n    options?: DigitalTwinsGetByIdOptionalParams\n  ): Promise<DigitalTwinsGetByIdResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, options: operationOptions },\n      getByIdOperationSpec\n    ) as Promise<DigitalTwinsGetByIdResponse>;\n  }\n\n  /**\n   * Adds or replaces a digital twin.\n   * Status codes:\n   * * 200 OK\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id or payload is invalid.\n   *   * ModelDecommissioned - The model for the digital twin is decommissioned.\n   *   * TwinLimitReached - The maximum number of digital twins allowed has been reached.\n   *   * ValidationFailed - The digital twin payload is not valid.\n   * * 412 Precondition Failed\n   *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param twin The digital twin instance being added. If provided, the $dtId property is ignored.\n   * @param options The options parameters.\n   */\n  add(\n    id: string,\n    twin: any,\n    options?: DigitalTwinsAddOptionalParams\n  ): Promise<DigitalTwinsAddResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, twin, options: operationOptions },\n      addOperationSpec\n    ) as Promise<DigitalTwinsAddResponse>;\n  }\n\n  /**\n   * Deletes a digital twin. All relationships referencing the digital twin must already be deleted.\n   * Status codes:\n   * * 204 No Content\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id is invalid.\n   *   * RelationshipsNotDeleted - The digital twin contains relationships.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   * * 412 Precondition Failed\n   *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param options The options parameters.\n   */\n  delete(\n    id: string,\n    options?: DigitalTwinsDeleteOptionalParams\n  ): Promise<coreHttp.RestResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, options: operationOptions },\n      deleteOperationSpec\n    ) as Promise<coreHttp.RestResponse>;\n  }\n\n  /**\n   * Updates a digital twin.\n   * Status codes:\n   * * 204 No Content\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id or payload is invalid.\n   *   * JsonPatchInvalid - The JSON Patch provided is invalid.\n   *   * ValidationFailed - Applying the patch results in an invalid digital twin.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   * * 412 Precondition Failed\n   *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param patchDocument An update specification described by JSON Patch. Updates to property values and\n   *                      $model elements may happen in the same request. Operations are limited to add, replace and remove.\n   * @param options The options parameters.\n   */\n  update(\n    id: string,\n    patchDocument: any[],\n    options?: DigitalTwinsUpdateOptionalParams\n  ): Promise<DigitalTwinsUpdateResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, patchDocument, options: operationOptions },\n      updateOperationSpec\n    ) as Promise<DigitalTwinsUpdateResponse>;\n  }\n\n  /**\n   * Retrieves a relationship between two digital twins.\n   * Status codes:\n   * * 200 OK\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id or relationship id is invalid.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   *   * RelationshipNotFound - The relationship was not found.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param relationshipId The id of the relationship. The id is unique within the digital twin and case\n   *                       sensitive.\n   * @param options The options parameters.\n   */\n  getRelationshipById(\n    id: string,\n    relationshipId: string,\n    options?: DigitalTwinsGetRelationshipByIdOptionalParams\n  ): Promise<DigitalTwinsGetRelationshipByIdResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, relationshipId, options: operationOptions },\n      getRelationshipByIdOperationSpec\n    ) as Promise<DigitalTwinsGetRelationshipByIdResponse>;\n  }\n\n  /**\n   * Adds a relationship between two digital twins.\n   * Status codes:\n   * * 200 OK\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id, relationship id, or payload is invalid.\n   *   * InvalidRelationship - The relationship is invalid.\n   *   * OperationNotAllowed - The relationship cannot connect to the same digital twin.\n   *   * ValidationFailed - The relationship content is invalid.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   *   * TargetTwinNotFound - The digital twin target of the relationship was not found.\n   * * 412 Precondition Failed\n   *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param relationshipId The id of the relationship. The id is unique within the digital twin and case\n   *                       sensitive.\n   * @param relationship The data for the relationship.\n   * @param options The options parameters.\n   */\n  addRelationship(\n    id: string,\n    relationshipId: string,\n    relationship: any,\n    options?: DigitalTwinsAddRelationshipOptionalParams\n  ): Promise<DigitalTwinsAddRelationshipResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, relationshipId, relationship, options: operationOptions },\n      addRelationshipOperationSpec\n    ) as Promise<DigitalTwinsAddRelationshipResponse>;\n  }\n\n  /**\n   * Deletes a relationship between two digital twins.\n   * Status codes:\n   * * 204 No Content\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id or relationship id is invalid.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   *   * RelationshipNotFound - The relationship was not found.\n   * * 412 Precondition Failed\n   *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param relationshipId The id of the relationship. The id is unique within the digital twin and case\n   *                       sensitive.\n   * @param options The options parameters.\n   */\n  deleteRelationship(\n    id: string,\n    relationshipId: string,\n    options?: DigitalTwinsDeleteRelationshipOptionalParams\n  ): Promise<coreHttp.RestResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, relationshipId, options: operationOptions },\n      deleteRelationshipOperationSpec\n    ) as Promise<coreHttp.RestResponse>;\n  }\n\n  /**\n   * Updates the properties on a relationship between two digital twins.\n   * Status codes:\n   * * 204 No Content\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id or relationship id is invalid.\n   *   * InvalidRelationship - The relationship is invalid.\n   *   * JsonPatchInvalid - The JSON Patch provided is invalid.\n   *   * ValidationFailed - The relationship content is invalid.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   *   * RelationshipNotFound - The relationship was not found.\n   * * 409 Conflict\n   *   * RelationshipAlreadyExists - The relationship already exists.\n   * * 412 Precondition Failed\n   *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param relationshipId The id of the relationship. The id is unique within the digital twin and case\n   *                       sensitive.\n   * @param patchDocument JSON Patch description of the update to the relationship properties.\n   * @param options The options parameters.\n   */\n  updateRelationship(\n    id: string,\n    relationshipId: string,\n    patchDocument: any[],\n    options?: DigitalTwinsUpdateRelationshipOptionalParams\n  ): Promise<DigitalTwinsUpdateRelationshipResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, relationshipId, patchDocument, options: operationOptions },\n      updateRelationshipOperationSpec\n    ) as Promise<DigitalTwinsUpdateRelationshipResponse>;\n  }\n\n  /**\n   * Retrieves the relationships from a digital twin.\n   * Status codes:\n   * * 200 OK\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id is invalid.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param options The options parameters.\n   */\n  listRelationships(\n    id: string,\n    options?: DigitalTwinsListRelationshipsOptionalParams\n  ): Promise<DigitalTwinsListRelationshipsResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, options: operationOptions },\n      listRelationshipsOperationSpec\n    ) as Promise<DigitalTwinsListRelationshipsResponse>;\n  }\n\n  /**\n   * Retrieves all incoming relationship for a digital twin.\n   * Status codes:\n   * * 200 OK\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id is invalid.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param options The options parameters.\n   */\n  listIncomingRelationships(\n    id: string,\n    options?: DigitalTwinsListIncomingRelationshipsOptionalParams\n  ): Promise<DigitalTwinsListIncomingRelationshipsResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, options: operationOptions },\n      listIncomingRelationshipsOperationSpec\n    ) as Promise<DigitalTwinsListIncomingRelationshipsResponse>;\n  }\n\n  /**\n   * Sends telemetry on behalf of a digital twin.\n   * Status codes:\n   * * 204 No Content\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id or message id is invalid.\n   *   * ValidationFailed - The telemetry content is invalid.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param messageId A unique message identifier (in the scope of the digital twin id) that is commonly\n   *                  used for de-duplicating messages.\n   * @param telemetry The telemetry measurements to send from the digital twin.\n   * @param options The options parameters.\n   */\n  sendTelemetry(\n    id: string,\n    messageId: string,\n    telemetry: any,\n    options?: DigitalTwinsSendTelemetryOptionalParams\n  ): Promise<coreHttp.RestResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, messageId, telemetry, options: operationOptions },\n      sendTelemetryOperationSpec\n    ) as Promise<coreHttp.RestResponse>;\n  }\n\n  /**\n   * Sends telemetry on behalf of a component in a digital twin.\n   * Status codes:\n   * * 204 No Content\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id, message id, or component path is invalid.\n   *   * ValidationFailed - The telemetry content is invalid.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   *   * ComponentNotFound - The component path was not found.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param componentPath The name of the DTDL component.\n   * @param messageId A unique message identifier (in the scope of the digital twin id) that is commonly\n   *                  used for de-duplicating messages.\n   * @param telemetry The telemetry measurements to send from the digital twin's component.\n   * @param options The options parameters.\n   */\n  sendComponentTelemetry(\n    id: string,\n    componentPath: string,\n    messageId: string,\n    telemetry: any,\n    options?: DigitalTwinsSendComponentTelemetryOptionalParams\n  ): Promise<coreHttp.RestResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, componentPath, messageId, telemetry, options: operationOptions },\n      sendComponentTelemetryOperationSpec\n    ) as Promise<coreHttp.RestResponse>;\n  }\n\n  /**\n   * Retrieves a component from a digital twin.\n   * Status codes:\n   * * 200 OK\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id or component path is invalid.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   *   * ComponentNotFound - The component path was not found.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param componentPath The name of the DTDL component.\n   * @param options The options parameters.\n   */\n  getComponent(\n    id: string,\n    componentPath: string,\n    options?: DigitalTwinsGetComponentOptionalParams\n  ): Promise<DigitalTwinsGetComponentResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, componentPath, options: operationOptions },\n      getComponentOperationSpec\n    ) as Promise<DigitalTwinsGetComponentResponse>;\n  }\n\n  /**\n   * Updates a component on a digital twin.\n   * Status codes:\n   * * 204 No Content\n   * * 400 Bad Request\n   *   * InvalidArgument - The digital twin id, component path, or payload is invalid.\n   *   * JsonPatchInvalid - The JSON Patch provided is invalid.\n   *   * ValidationFailed - Applying the patch results in an invalid digital twin.\n   * * 404 Not Found\n   *   * DigitalTwinNotFound - The digital twin was not found.\n   * * 412 Precondition Failed\n   *   * PreconditionFailed - The precondition check (If-Match or If-None-Match) failed.\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param componentPath The name of the DTDL component.\n   * @param patchDocument An update specification described by JSON Patch. Updates to property values and\n   *                      $model elements may happen in the same request. Operations are limited to add, replace and remove.\n   * @param options The options parameters.\n   */\n  updateComponent(\n    id: string,\n    componentPath: string,\n    patchDocument: any[],\n    options?: DigitalTwinsUpdateComponentOptionalParams\n  ): Promise<DigitalTwinsUpdateComponentResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, componentPath, patchDocument, options: operationOptions },\n      updateComponentOperationSpec\n    ) as Promise<DigitalTwinsUpdateComponentResponse>;\n  }\n\n  /**\n   * ListRelationshipsNext\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param nextLink The nextLink from the previous successful call to the ListRelationships method.\n   * @param options The options parameters.\n   */\n  listRelationshipsNext(\n    id: string,\n    nextLink: string,\n    options?: DigitalTwinsListRelationshipsNextOptionalParams\n  ): Promise<DigitalTwinsListRelationshipsNextResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, nextLink, options: operationOptions },\n      listRelationshipsNextOperationSpec\n    ) as Promise<DigitalTwinsListRelationshipsNextResponse>;\n  }\n\n  /**\n   * ListIncomingRelationshipsNext\n   * @param id The id of the digital twin. The id is unique within the service and case sensitive.\n   * @param nextLink The nextLink from the previous successful call to the ListIncomingRelationships\n   *                 method.\n   * @param options The options parameters.\n   */\n  listIncomingRelationshipsNext(\n    id: string,\n    nextLink: string,\n    options?: DigitalTwinsListIncomingRelationshipsNextOptionalParams\n  ): Promise<DigitalTwinsListIncomingRelationshipsNextResponse> {\n    const operationOptions: coreHttp.RequestOptionsBase = coreHttp.operationOptionsToRequestOptionsBase(\n      options || {}\n    );\n    return this.client.sendOperationRequest(\n      { id, nextLink, options: operationOptions },\n      listIncomingRelationshipsNextOperationSpec\n    ) as Promise<DigitalTwinsListIncomingRelationshipsNextResponse>;\n  }\n}\n// Operation Specifications\n\nconst serializer = new coreHttp.Serializer(Mappers, /* isXml */ false);\n\nconst getByIdOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: { type: { name: \"any\" } },\n      headersMapper: Mappers.DigitalTwinsGetByIdHeaders\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\nconst addOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      bodyMapper: { type: { name: \"any\" } },\n      headersMapper: Mappers.DigitalTwinsAddHeaders\n    },\n    202: {},\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.twin,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.ifNoneMatch\n  ],\n  mediaType: \"json\",\n  serializer\n};\nconst deleteOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}\",\n  httpMethod: \"DELETE\",\n  responses: {\n    204: {},\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.ifMatch\n  ],\n  serializer\n};\nconst updateOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}\",\n  httpMethod: \"PATCH\",\n  responses: {\n    202: {},\n    204: {\n      headersMapper: Mappers.DigitalTwinsUpdateHeaders\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.patchDocument,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.contentType1,\n    Parameters.ifMatch\n  ],\n  mediaType: \"json\",\n  serializer\n};\nconst getRelationshipByIdOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}/relationships/{relationshipId}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: { type: { name: \"any\" } },\n      headersMapper: Mappers.DigitalTwinsGetRelationshipByIdHeaders\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id, Parameters.relationshipId],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\nconst addRelationshipOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}/relationships/{relationshipId}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      bodyMapper: { type: { name: \"any\" } },\n      headersMapper: Mappers.DigitalTwinsAddRelationshipHeaders\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.relationship,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id, Parameters.relationshipId],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.ifNoneMatch\n  ],\n  mediaType: \"json\",\n  serializer\n};\nconst deleteRelationshipOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}/relationships/{relationshipId}\",\n  httpMethod: \"DELETE\",\n  responses: {\n    204: {},\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id, Parameters.relationshipId],\n  headerParameters: [\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.ifMatch\n  ],\n  serializer\n};\nconst updateRelationshipOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}/relationships/{relationshipId}\",\n  httpMethod: \"PATCH\",\n  responses: {\n    204: {\n      headersMapper: Mappers.DigitalTwinsUpdateRelationshipHeaders\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.patchDocument,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id, Parameters.relationshipId],\n  headerParameters: [\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.contentType1,\n    Parameters.ifMatch\n  ],\n  mediaType: \"json\",\n  serializer\n};\nconst listRelationshipsOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}/relationships\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.RelationshipCollection\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion, Parameters.relationshipName],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\nconst listIncomingRelationshipsOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}/incomingrelationships\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.IncomingRelationshipCollection\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\nconst sendTelemetryOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}/telemetry\",\n  httpMethod: \"POST\",\n  responses: {\n    204: {},\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.telemetry,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.messageId,\n    Parameters.telemetrySourceTime\n  ],\n  mediaType: \"json\",\n  serializer\n};\nconst sendComponentTelemetryOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}/components/{componentPath}/telemetry\",\n  httpMethod: \"POST\",\n  responses: {\n    204: {},\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.telemetry,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id, Parameters.componentPath],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.messageId,\n    Parameters.telemetrySourceTime\n  ],\n  mediaType: \"json\",\n  serializer\n};\nconst getComponentOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}/components/{componentPath}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: { type: { name: \"any\" } },\n      headersMapper: Mappers.DigitalTwinsGetComponentHeaders\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id, Parameters.componentPath],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\nconst updateComponentOperationSpec: coreHttp.OperationSpec = {\n  path: \"/digitaltwins/{id}/components/{componentPath}\",\n  httpMethod: \"PATCH\",\n  responses: {\n    202: {},\n    204: {\n      headersMapper: Mappers.DigitalTwinsUpdateComponentHeaders\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  requestBody: Parameters.patchDocument,\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id, Parameters.componentPath],\n  headerParameters: [\n    Parameters.traceparent,\n    Parameters.tracestate,\n    Parameters.contentType1,\n    Parameters.ifMatch\n  ],\n  mediaType: \"json\",\n  serializer\n};\nconst listRelationshipsNextOperationSpec: coreHttp.OperationSpec = {\n  path: \"{nextLink}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.RelationshipCollection\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion, Parameters.relationshipName],\n  urlParameters: [Parameters.$host, Parameters.id, Parameters.nextLink],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\nconst listIncomingRelationshipsNextOperationSpec: coreHttp.OperationSpec = {\n  path: \"{nextLink}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.IncomingRelationshipCollection\n    },\n    default: {\n      bodyMapper: Mappers.ErrorResponse\n    }\n  },\n  queryParameters: [Parameters.apiVersion],\n  urlParameters: [Parameters.$host, Parameters.id, Parameters.nextLink],\n  headerParameters: [Parameters.traceparent, Parameters.tracestate],\n  serializer\n};\n"]}