{"version": 3, "file": "msalNodeCommon.js", "sourceRoot": "", "sources": ["../../../../src/msal/nodeFlows/msalNodeCommon.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,KAAK,QAAQ,MAAM,kBAAkB,CAAC;AAE7C,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EACL,iBAAiB,EACjB,qBAAqB,EACrB,YAAY,EACZ,mBAAmB,EACnB,YAAY,EACZ,YAAY,EACZ,eAAe,GAChB,MAAM,UAAU,CAAC;AAElB,OAAO,EACL,yBAAyB,EACzB,iCAAiC,EACjC,eAAe,GAChB,MAAM,0BAA0B,CAAC;AAGlC,OAAO,EAAE,2BAA2B,EAAE,MAAM,cAAc,CAAC;AAE3D,OAAO,EAAE,uBAAuB,EAAE,MAAM,iBAAiB,CAAC;AAC1D,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AAG7D,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAwB5D;;;GAGG;AACH,IAAI,mBAAmB,GAEP,SAAS,CAAC;AAE1B;;;GAGG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG;IACtC,cAAc,CAAC,cAA8D;QAC3E,mBAAmB,GAAG,cAAc,CAAC;IACvC,CAAC;CACF,CAAC;AAEF;;;;;;;;GAQG;AACH,MAAM,OAAgB,QAAS,SAAQ,iBAAiB;IAqBtD,YAAY,OAAwB;;QAClC,KAAK,CAAC,OAAO,CAAC,CAAC;QAbP,yBAAoB,GAAY,KAAK,CAAC;QAc9C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;QACpF,IAAI,CAAC,4BAA4B,GAAG,iCAAiC,CACnE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,sBAAsB,0CAAE,0BAA0B,CAC5D,CAAC;QACF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC9C,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,YAAY,EAAE;YACzB,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;SAC1C;QAED,qCAAqC;QACrC,IAAI,mBAAmB,KAAK,SAAS,KAAI,MAAA,OAAO,CAAC,4BAA4B,0CAAE,OAAO,CAAA,EAAE;YACtF,IAAI,CAAC,iBAAiB,GAAG,GAAG,EAAE,CAAC,mBAAoB,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;SAC3F;aAAM,IAAI,MAAA,OAAO,CAAC,4BAA4B,0CAAE,OAAO,EAAE;YACxD,MAAM,IAAI,KAAK,CACb;gBACE,qFAAqF;gBACrF,yHAAyH;gBACzH,mFAAmF;gBACnF,0FAA0F;aAC3F,CAAC,IAAI,CAAC,GAAG,CAAC,CACZ,CAAC;SACH;QAED,IAAI,CAAC,WAAW,GAAG,MAAA,OAAO,CAAC,iBAAiB,mCAAI,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC;QAC1F,IAAI,IAAI,CAAC,WAAW,KAAK,iBAAiB,CAAC,kBAAkB,EAAE;YAC7D,IAAI,CAAC,WAAW,GAAG,eAAe,CAAC;SACpC;IACH,CAAC;IAED;;OAEG;IACO,qBAAqB,CAAC,OAAwB;QACtD,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,uBAAuB,CAAC;QAC7D,MAAM,QAAQ,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;QAErF,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC;QAC/E,MAAM,SAAS,GAAG,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAE7D,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,iCACnC,OAAO,CAAC,sBAAsB,KACjC,aAAa,EAAE,SAAS,EACxB,cAAc,EAAE,OAAO,CAAC,cAAc,IACtC,CAAC;QAEH,IAAI,kBAAkB,GAAa,CAAC,KAAK,CAAC,CAAC;QAC3C,IAAI,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE;YAC1C,kBAAkB,GAAG,EAAE,CAAC;SACzB;QAED,OAAO;YACL,IAAI,EAAE;gBACJ,QAAQ;gBACR,SAAS;gBACT,gBAAgB,EAAE,mBAAmB,CAAC,QAAQ,EAAE,SAAS,CAAC;gBAC1D,kBAAkB;aACnB;YACD,sCAAsC;YACtC,MAAM,EAAE;gBACN,aAAa,EAAE,IAAI,CAAC,cAAc;gBAClC,aAAa,EAAE;oBACb,cAAc,EAAE,qBAAqB,CAAC,OAAO,CAAC,MAAM,CAAC;oBACrD,QAAQ,EAAE,eAAe,CAAC,WAAW,EAAE,CAAC;iBACzC;aACF;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI,CAAC,OAAuC;QAChD,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,EAAE;YACxB,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;gBACjD,6DAA6D;gBAC7D,mDAAmD;gBACnD,IAAI,CAAC,cAAe,CAAC,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAC5D,CAAC,CAAC,CAAC;SACJ;QAED,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,eAAe,EAAE;YAC1C,OAAO;SACR;QAED,IAAI,IAAI,CAAC,iBAAiB,KAAK,SAAS,EAAE;YACxC,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG;gBACtB,WAAW,EAAE,MAAM,IAAI,CAAC,iBAAiB,EAAE;aAC5C,CAAC;SACH;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,QAAQ,CAAC,uBAAuB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvE,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;SAClE;QACD,8EAA8E;QAC9E,IACE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY;YACjC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe;YACpC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,EACtC;YACA,IAAI,CAAC,eAAe,GAAG,IAAI,QAAQ,CAAC,6BAA6B,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SACpF;aAAM;YACL,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC7B,MAAM,IAAI,KAAK,CACb,gHAAgH,CACjH,CAAC;aACH;SACF;IACH,CAAC;IAED;;OAEG;IACO,gBAAgB,CACxB,OAAwD,EACxD,WAA6B,EAC7B,QAAqB;QAErB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,OAAO;iBACJ,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;gBAClB,OAAO,OAAO,CAAC,SAAU,CAAC,CAAC;YAC7B,CAAC,CAAC;iBACD,KAAK,CAAC,MAAM,CAAC,CAAC;YACjB,IAAI,WAAW,EAAE;gBACf,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;oBACzC,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,EAAI,CAAC;gBACf,CAAC,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB;;QACpB,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAO,IAAI,CAAC,OAAO,CAAC;SACrB;QACD,MAAM,KAAK,GAAG,MAAA,MAAA,IAAI,CAAC,eAAe,0CAAE,aAAa,EAAE,mCAAI,MAAA,IAAI,CAAC,SAAS,0CAAE,aAAa,EAAE,CAAC;QACvF,MAAM,gBAAgB,GAAG,MAAM,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,cAAc,EAAE,CAAA,CAAC;QAEvD,IAAI,CAAC,gBAAgB,EAAE;YACrB,OAAO;SACR;QAED,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;YACjC,IAAI,CAAC,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;SACjE;aAAM;YACL,IAAI,CAAC,MAAM;iBACR,IAAI,CAAC;;;;6KAI+J,CAAC,CAAC;YACzK,OAAO;SACR;QAED,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAClB,MAAgB,EAChB,OAAuC;;QAEvC,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,MAAM,IAAI,2BAA2B,CAAC;gBACpC,MAAM;gBACN,eAAe,EAAE,OAAO;gBACxB,OAAO,EACL,sFAAsF;aACzF,CAAC,CAAC;SACJ;QAED,MAAM,aAAa,GAA+B;YAChD,kFAAkF;YAClF,OAAO,EAAE,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC;YACnC,aAAa,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa;YACrC,MAAM;YACN,SAAS,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS;YAC7B,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM;SACxB,CAAC;QAEF,IAAI;YACF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACzD;;;;;eAKG;YACH,MAAM,CAAA,MAAA,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,eAAe,CAAC,0CAAE,aAAa,GAAG,cAAc,EAAE,CAAA,CAAC;YAEjF,MAAM,QAAQ,GACZ,MAAA,CAAC,MAAM,CAAA,MAAA,IAAI,CAAC,eAAe,0CAAE,kBAAkB,CAAC,aAAa,CAAC,CAAA,CAAC,mCAC/D,CAAC,MAAM,IAAI,CAAC,SAAU,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC,CAAC;YAC5D,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,QAAQ,IAAI,SAAS,CAAC,CAAC;SACxE;QAAC,OAAO,GAAQ,EAAE;YACjB,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;SAC9C;IACH,CAAC;IAOD;;;OAGG;IACI,KAAK,CAAC,QAAQ,CACnB,MAAgB,EAChB,UAAyC,EAAE;QAE3C,MAAM,QAAQ,GACZ,yBAAyB,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,4BAA4B,CAAC;YACpF,IAAI,CAAC,QAAQ,CAAC;QAEhB,OAAO,CAAC,SAAS,GAAG,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAE/D,OAAO,CAAC,aAAa,GAAG,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,KAAI,IAAI,CAAC,YAAY,EAAE,CAAC;QACtE,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEzB,IAAI;YACF,gDAAgD;YAChD,uGAAuG;YACvG,2GAA2G;YAC3G,MAAM,aAAa,GAAI,OAAe,CAAC,MAAM,CAAC;YAC9C,IAAI,aAAa,EAAE;gBACjB,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC;aACnC;YACD,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,aAAa,EAAE;gBACtC,OAAe,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;aAC7C;YACD,wEAAwE;YACxE,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;SACnD;QAAC,OAAO,GAAQ,EAAE;YACjB,IAAI,GAAG,CAAC,IAAI,KAAK,6BAA6B,EAAE;gBAC9C,MAAM,GAAG,CAAC;aACX;YACD,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,8BAA8B,EAAE;gBAC3C,MAAM,IAAI,2BAA2B,CAAC;oBACpC,MAAM;oBACN,eAAe,EAAE,OAAO;oBACxB,OAAO,EACL,uFAAuF;iBAC1F,CAAC,CAAC;aACJ;YACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mEAAmE,CAAC,CAAC;YACtF,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;SACzC;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as msal<PERSON><PERSON>mon from \"@azure/msal-common\";\nimport * as msalNode from \"@azure/msal-node\";\nimport { AccessToken, GetTokenOptions } from \"@azure/core-auth\";\nimport { getLogLevel } from \"@azure/logger\";\nimport {\n  MsalBaseUtilities,\n  defaultLoggerCallback,\n  getAuthority,\n  getKnownAuthorities,\n  msalToPublic,\n  publicToMsal,\n  getMSALLogLevel,\n} from \"../utils\";\nimport { MsalFlow, MsalFlowOptions } from \"../flows\";\nimport {\n  processMultiTenantRequest,\n  resolveAddionallyAllowedTenantIds,\n  resolveTenantId,\n} from \"../../util/tenantIdUtils\";\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { AuthenticationRecord } from \"../types\";\nimport { AuthenticationRequiredError } from \"../../errors\";\nimport { CredentialFlowGetTokenOptions } from \"../credentials\";\nimport { DeveloperSignOnClientId } from \"../../constants\";\nimport { IdentityClient } from \"../../client/identityClient\";\nimport { LogPolicyOptions } from \"@azure/core-rest-pipeline\";\nimport { MultiTenantTokenCredentialOptions } from \"../../credentials/multiTenantTokenCredentialOptions\";\nimport { RegionalAuthority } from \"../../regionalAuthority\";\nimport { TokenCachePersistenceOptions } from \"./tokenCachePersistenceOptions\";\n\n/**\n * Union of the constructor parameters that all MSAL flow types for Node.\n * @internal\n */\nexport interface MsalNodeOptions extends MsalFlowOptions {\n  tokenCachePersistenceOptions?: TokenCachePersistenceOptions;\n  tokenCredentialOptions: MultiTenantTokenCredentialOptions;\n  /**\n   * Specifies a regional authority. Please refer to the {@link RegionalAuthority} type for the accepted values.\n   * If {@link RegionalAuthority.AutoDiscoverRegion} is specified, we will try to discover the regional authority endpoint.\n   * If the property is not specified, uses a non-regional authority endpoint.\n   */\n  regionalAuthority?: string;\n  /**\n   * Allows logging account information once the authentication flow succeeds.\n   */\n  loggingOptions?: LogPolicyOptions & {\n    allowLoggingAccountIdentifiers?: boolean;\n  };\n}\n\n/**\n * The current persistence provider, undefined by default.\n * @internal\n */\nlet persistenceProvider:\n  | ((options?: TokenCachePersistenceOptions) => Promise<msalCommon.ICachePlugin>)\n  | undefined = undefined;\n\n/**\n * An object that allows setting the persistence provider.\n * @internal\n */\nexport const msalNodeFlowCacheControl = {\n  setPersistence(pluginProvider: Exclude<typeof persistenceProvider, undefined>): void {\n    persistenceProvider = pluginProvider;\n  },\n};\n\n/**\n * MSAL partial base client for Node.js.\n *\n * It completes the input configuration with some default values.\n * It also provides with utility protected methods that can be used from any of the clients,\n * which includes handlers for successful responses and errors.\n *\n * @internal\n */\nexport abstract class MsalNode extends MsalBaseUtilities implements MsalFlow {\n  protected publicApp: msalNode.PublicClientApplication | undefined;\n  protected confidentialApp: msalNode.ConfidentialClientApplication | undefined;\n  protected msalConfig: msalNode.Configuration;\n  protected clientId: string;\n  protected tenantId: string;\n  protected additionallyAllowedTenantIds: string[];\n  protected authorityHost?: string;\n  protected identityClient?: IdentityClient;\n  protected requiresConfidential: boolean = false;\n  protected azureRegion?: string;\n  protected createCachePlugin: (() => Promise<msalCommon.ICachePlugin>) | undefined;\n\n  /**\n   * MSAL currently caches the tokens depending on the claims used to retrieve them.\n   * In cases like CAE, in which we use claims to update the tokens, trying to retrieve the token without the claims will yield the original token.\n   * To ensure we always get the latest token, we have to keep track of the claims.\n   */\n  private cachedClaims: string | undefined;\n\n  protected getAssertion: (() => Promise<string>) | undefined;\n  constructor(options: MsalNodeOptions) {\n    super(options);\n    this.msalConfig = this.defaultNodeMsalConfig(options);\n    this.tenantId = resolveTenantId(options.logger, options.tenantId, options.clientId);\n    this.additionallyAllowedTenantIds = resolveAddionallyAllowedTenantIds(\n      options?.tokenCredentialOptions?.additionallyAllowedTenants\n    );\n    this.clientId = this.msalConfig.auth.clientId;\n    if (options?.getAssertion) {\n      this.getAssertion = options.getAssertion;\n    }\n\n    // If persistence has been configured\n    if (persistenceProvider !== undefined && options.tokenCachePersistenceOptions?.enabled) {\n      this.createCachePlugin = () => persistenceProvider!(options.tokenCachePersistenceOptions);\n    } else if (options.tokenCachePersistenceOptions?.enabled) {\n      throw new Error(\n        [\n          \"Persistent token caching was requested, but no persistence provider was configured.\",\n          \"You must install the identity-cache-persistence plugin package (`npm install --save @azure/identity-cache-persistence`)\",\n          \"and enable it by importing `useIdentityPlugin` from `@azure/identity` and calling\",\n          \"`useIdentityPlugin(cachePersistencePlugin)` before using `tokenCachePersistenceOptions`.\",\n        ].join(\" \")\n      );\n    }\n\n    this.azureRegion = options.regionalAuthority ?? process.env.AZURE_REGIONAL_AUTHORITY_NAME;\n    if (this.azureRegion === RegionalAuthority.AutoDiscoverRegion) {\n      this.azureRegion = \"AUTO_DISCOVER\";\n    }\n  }\n\n  /**\n   * Generates a MSAL configuration that generally works for Node.js\n   */\n  protected defaultNodeMsalConfig(options: MsalNodeOptions): msalNode.Configuration {\n    const clientId = options.clientId || DeveloperSignOnClientId;\n    const tenantId = resolveTenantId(options.logger, options.tenantId, options.clientId);\n\n    this.authorityHost = options.authorityHost || process.env.AZURE_AUTHORITY_HOST;\n    const authority = getAuthority(tenantId, this.authorityHost);\n\n    this.identityClient = new IdentityClient({\n      ...options.tokenCredentialOptions,\n      authorityHost: authority,\n      loggingOptions: options.loggingOptions,\n    });\n\n    let clientCapabilities: string[] = [\"cp1\"];\n    if (process.env.AZURE_IDENTITY_DISABLE_CP1) {\n      clientCapabilities = [];\n    }\n\n    return {\n      auth: {\n        clientId,\n        authority,\n        knownAuthorities: getKnownAuthorities(tenantId, authority),\n        clientCapabilities,\n      },\n      // Cache is defined in this.prepare();\n      system: {\n        networkClient: this.identityClient,\n        loggerOptions: {\n          loggerCallback: defaultLoggerCallback(options.logger),\n          logLevel: getMSALLogLevel(getLogLevel()),\n        },\n      },\n    };\n  }\n\n  /**\n   * Prepares the MSAL applications.\n   */\n  async init(options?: CredentialFlowGetTokenOptions): Promise<void> {\n    if (options?.abortSignal) {\n      options.abortSignal.addEventListener(\"abort\", () => {\n        // This will abort any pending request in the IdentityClient,\n        // based on the received or generated correlationId\n        this.identityClient!.abortRequests(options.correlationId);\n      });\n    }\n\n    if (this.publicApp || this.confidentialApp) {\n      return;\n    }\n\n    if (this.createCachePlugin !== undefined) {\n      this.msalConfig.cache = {\n        cachePlugin: await this.createCachePlugin(),\n      };\n    }\n\n    this.publicApp = new msalNode.PublicClientApplication(this.msalConfig);\n    if (this.getAssertion) {\n      this.msalConfig.auth.clientAssertion = await this.getAssertion();\n    }\n    // The confidential client requires either a secret, assertion or certificate.\n    if (\n      this.msalConfig.auth.clientSecret ||\n      this.msalConfig.auth.clientAssertion ||\n      this.msalConfig.auth.clientCertificate\n    ) {\n      this.confidentialApp = new msalNode.ConfidentialClientApplication(this.msalConfig);\n    } else {\n      if (this.requiresConfidential) {\n        throw new Error(\n          \"Unable to generate the MSAL confidential client. Missing either the client's secret, certificate or assertion.\"\n        );\n      }\n    }\n  }\n\n  /**\n   * Allows the cancellation of a MSAL request.\n   */\n  protected withCancellation(\n    promise: Promise<msalCommon.AuthenticationResult | null>,\n    abortSignal?: AbortSignalLike,\n    onCancel?: () => void\n  ): Promise<msalCommon.AuthenticationResult | null> {\n    return new Promise((resolve, reject) => {\n      promise\n        .then((msalToken) => {\n          return resolve(msalToken!);\n        })\n        .catch(reject);\n      if (abortSignal) {\n        abortSignal.addEventListener(\"abort\", () => {\n          onCancel?.();\n        });\n      }\n    });\n  }\n\n  /**\n   * Returns the existing account, attempts to load the account from MSAL.\n   */\n  async getActiveAccount(): Promise<AuthenticationRecord | undefined> {\n    if (this.account) {\n      return this.account;\n    }\n    const cache = this.confidentialApp?.getTokenCache() ?? this.publicApp?.getTokenCache();\n    const accountsByTenant = await cache?.getAllAccounts();\n\n    if (!accountsByTenant) {\n      return;\n    }\n\n    if (accountsByTenant.length === 1) {\n      this.account = msalToPublic(this.clientId, accountsByTenant[0]);\n    } else {\n      this.logger\n        .info(`More than one account was found authenticated for this Client ID and Tenant ID.\nHowever, no \"authenticationRecord\" has been provided for this credential,\ntherefore we're unable to pick between these accounts.\nA new login attempt will be requested, to ensure the correct account is picked.\nTo work with multiple accounts for the same Client ID and Tenant ID, please provide an \"authenticationRecord\" when initializing a credential to prevent this from happening.`);\n      return;\n    }\n\n    return this.account;\n  }\n\n  /**\n   * Attempts to retrieve a token from cache.\n   */\n  async getTokenSilent(\n    scopes: string[],\n    options?: CredentialFlowGetTokenOptions\n  ): Promise<AccessToken> {\n    await this.getActiveAccount();\n    if (!this.account) {\n      throw new AuthenticationRequiredError({\n        scopes,\n        getTokenOptions: options,\n        message:\n          \"Silent authentication failed. We couldn't retrieve an active account from the cache.\",\n      });\n    }\n\n    const silentRequest: msalNode.SilentFlowRequest = {\n      // To be able to re-use the account, the Token Cache must also have been provided.\n      account: publicToMsal(this.account),\n      correlationId: options?.correlationId,\n      scopes,\n      authority: options?.authority,\n      claims: options?.claims,\n    };\n\n    try {\n      this.logger.info(\"Attempting to acquire token silently\");\n      /**\n       * The following code to retrieve all accounts is done as a workaround in an attempt to force the\n       * refresh of the token cache with the token and the account passed in through the\n       * `authenticationRecord` parameter. See issue - https://github.com/Azure/azure-sdk-for-js/issues/24349#issuecomment-**********\n       * This workaround serves as a workoaround for silent authentication not happening when authenticationRecord is passed.\n       */\n      await (this.publicApp || this.confidentialApp)?.getTokenCache().getAllAccounts();\n\n      const response =\n        (await this.confidentialApp?.acquireTokenSilent(silentRequest)) ??\n        (await this.publicApp!.acquireTokenSilent(silentRequest));\n      return this.handleResult(scopes, this.clientId, response || undefined);\n    } catch (err: any) {\n      throw this.handleError(scopes, err, options);\n    }\n  }\n\n  /**\n   * Attempts to retrieve an authenticated token from MSAL.\n   */\n  protected abstract doGetToken(scopes: string[], options?: GetTokenOptions): Promise<AccessToken>;\n\n  /**\n   * Wrapper around each MSAL flow get token operation: doGetToken.\n   * If disableAutomaticAuthentication is sent through the constructor, it will prevent MSAL from requesting the user input.\n   */\n  public async getToken(\n    scopes: string[],\n    options: CredentialFlowGetTokenOptions = {}\n  ): Promise<AccessToken> {\n    const tenantId =\n      processMultiTenantRequest(this.tenantId, options, this.additionallyAllowedTenantIds) ||\n      this.tenantId;\n\n    options.authority = getAuthority(tenantId, this.authorityHost);\n\n    options.correlationId = options?.correlationId || this.generateUuid();\n    await this.init(options);\n\n    try {\n      // MSAL now caches tokens based on their claims,\n      // so now one has to keep track fo claims in order to retrieve the newer tokens from acquireTokenSilent\n      // This update happened on PR: https://github.com/AzureAD/microsoft-authentication-library-for-js/pull/4533\n      const optionsClaims = (options as any).claims;\n      if (optionsClaims) {\n        this.cachedClaims = optionsClaims;\n      }\n      if (this.cachedClaims && !optionsClaims) {\n        (options as any).claims = this.cachedClaims;\n      }\n      // We don't return the promise since we want to catch errors right here.\n      return await this.getTokenSilent(scopes, options);\n    } catch (err: any) {\n      if (err.name !== \"AuthenticationRequiredError\") {\n        throw err;\n      }\n      if (options?.disableAutomaticAuthentication) {\n        throw new AuthenticationRequiredError({\n          scopes,\n          getTokenOptions: options,\n          message:\n            \"Automatic authentication has been disabled. You may call the authentication() method.\",\n        });\n      }\n      this.logger.info(`Silent authentication failed, falling back to interactive method.`);\n      return this.doGetToken(scopes, options);\n    }\n  }\n}\n"]}