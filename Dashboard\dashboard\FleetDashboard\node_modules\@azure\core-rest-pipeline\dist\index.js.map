{"version": 3, "file": "index.js", "sources": ["../src/pipeline.ts", "../src/log.ts", "../src/util/sanitizer.ts", "../src/policies/logPolicy.ts", "../src/policies/redirectPolicy.ts", "../src/util/userAgentPlatform.ts", "../src/constants.ts", "../src/util/userAgent.ts", "../src/policies/userAgentPolicy.ts", "../src/policies/decompressResponsePolicy.ts", "../src/util/helpers.ts", "../src/retryStrategies/throttlingRetryStrategy.ts", "../src/retryStrategies/exponentialRetryStrategy.ts", "../src/policies/retryPolicy.ts", "../src/policies/defaultRetryPolicy.ts", "../src/policies/formDataPolicy.ts", "../src/policies/proxyPolicy.ts", "../src/policies/setClientRequestIdPolicy.ts", "../src/policies/tlsPolicy.ts", "../src/util/inspect.ts", "../src/restError.ts", "../src/policies/tracingPolicy.ts", "../src/createPipelineFromOptions.ts", "../src/httpHeaders.ts", "../src/nodeHttpClient.ts", "../src/defaultHttpClient.ts", "../src/pipelineRequest.ts", "../src/policies/exponentialRetryPolicy.ts", "../src/policies/systemErrorRetryPolicy.ts", "../src/policies/throttlingRetryPolicy.ts", "../src/util/tokenCycler.ts", "../src/policies/bearerTokenAuthenticationPolicy.ts", "../src/policies/ndJsonPolicy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpClient, PipelineRequest, PipelineResponse, SendRequest } from \"./interfaces\";\n\n/**\n * Policies are executed in phases.\n * The execution order is:\n * 1. Serialize Phase\n * 2. Policies not in a phase\n * 3. Deserialize Phase\n * 4. Retry Phase\n * 5. Sign Phase\n */\nexport type PipelinePhase = \"Deserialize\" | \"Serialize\" | \"Retry\" | \"Sign\";\n\nconst ValidPhaseNames = new Set<PipelinePhase>([\"Deserialize\", \"Serialize\", \"Retry\", \"Sign\"]);\n\n/**\n * Options when adding a policy to the pipeline.\n * Used to express dependencies on other policies.\n */\nexport interface AddPolicyOptions {\n  /**\n   * Policies that this policy must come before.\n   */\n  beforePolicies?: string[];\n  /**\n   * Policies that this policy must come after.\n   */\n  afterPolicies?: string[];\n  /**\n   * The phase that this policy must come after.\n   */\n  afterPhase?: PipelinePhase;\n  /**\n   * The phase this policy belongs to.\n   */\n  phase?: PipelinePhase;\n}\n\n/**\n * A pipeline policy manipulates a request as it travels through the pipeline.\n * It is conceptually a middleware that is allowed to modify the request before\n * it is made as well as the response when it is received.\n */\nexport interface PipelinePolicy {\n  /**\n   * The policy name. Must be a unique string in the pipeline.\n   */\n  name: string;\n  /**\n   * The main method to implement that manipulates a request/response.\n   * @param request - The request being performed.\n   * @param next - The next policy in the pipeline. Must be called to continue the pipeline.\n   */\n  sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse>;\n}\n\n/**\n * Represents a pipeline for making a HTTP request to a URL.\n * Pipelines can have multiple policies to manage manipulating each request\n * before and after it is made to the server.\n */\nexport interface Pipeline {\n  /**\n   * Add a new policy to the pipeline.\n   * @param policy - A policy that manipulates a request.\n   * @param options - A set of options for when the policy should run.\n   */\n  addPolicy(policy: PipelinePolicy, options?: AddPolicyOptions): void;\n  /**\n   * Remove a policy from the pipeline.\n   * @param options - Options that let you specify which policies to remove.\n   */\n  removePolicy(options: { name?: string; phase?: PipelinePhase }): PipelinePolicy[];\n  /**\n   * Uses the pipeline to make a HTTP request.\n   * @param httpClient - The HttpClient that actually performs the request.\n   * @param request - The request to be made.\n   */\n  sendRequest(httpClient: HttpClient, request: PipelineRequest): Promise<PipelineResponse>;\n  /**\n   * Returns the current set of policies in the pipeline in the order in which\n   * they will be applied to the request. Later in the list is closer to when\n   * the request is performed.\n   */\n  getOrderedPolicies(): PipelinePolicy[];\n  /**\n   * Duplicates this pipeline to allow for modifying an existing one without mutating it.\n   */\n  clone(): Pipeline;\n}\n\ninterface PipelineDescriptor {\n  policy: PipelinePolicy;\n  options: AddPolicyOptions;\n}\n\ninterface PolicyGraphNode {\n  policy: PipelinePolicy;\n  dependsOn: Set<PolicyGraphNode>;\n  dependants: Set<PolicyGraphNode>;\n  afterPhase?: Phase;\n}\n\ninterface Phase {\n  name: PipelinePhase | \"None\";\n  policies: Set<PolicyGraphNode>;\n  hasRun: boolean;\n  hasAfterPolicies: boolean;\n}\n\n/**\n * A private implementation of Pipeline.\n * Do not export this class from the package.\n * @internal\n */\nclass HttpPipeline implements Pipeline {\n  private _policies: PipelineDescriptor[] = [];\n  private _orderedPolicies?: PipelinePolicy[];\n\n  private constructor(policies?: PipelineDescriptor[]) {\n    this._policies = policies?.slice(0) ?? [];\n    this._orderedPolicies = undefined;\n  }\n\n  public addPolicy(policy: PipelinePolicy, options: AddPolicyOptions = {}): void {\n    if (options.phase && options.afterPhase) {\n      throw new Error(\"Policies inside a phase cannot specify afterPhase.\");\n    }\n    if (options.phase && !ValidPhaseNames.has(options.phase)) {\n      throw new Error(`Invalid phase name: ${options.phase}`);\n    }\n    if (options.afterPhase && !ValidPhaseNames.has(options.afterPhase)) {\n      throw new Error(`Invalid afterPhase name: ${options.afterPhase}`);\n    }\n    this._policies.push({\n      policy,\n      options,\n    });\n    this._orderedPolicies = undefined;\n  }\n\n  public removePolicy(options: { name?: string; phase?: string }): PipelinePolicy[] {\n    const removedPolicies: PipelinePolicy[] = [];\n\n    this._policies = this._policies.filter((policyDescriptor) => {\n      if (\n        (options.name && policyDescriptor.policy.name === options.name) ||\n        (options.phase && policyDescriptor.options.phase === options.phase)\n      ) {\n        removedPolicies.push(policyDescriptor.policy);\n        return false;\n      } else {\n        return true;\n      }\n    });\n    this._orderedPolicies = undefined;\n\n    return removedPolicies;\n  }\n\n  public sendRequest(httpClient: HttpClient, request: PipelineRequest): Promise<PipelineResponse> {\n    const policies = this.getOrderedPolicies();\n\n    const pipeline = policies.reduceRight<SendRequest>(\n      (next, policy) => {\n        return (req: PipelineRequest) => {\n          return policy.sendRequest(req, next);\n        };\n      },\n      (req: PipelineRequest) => httpClient.sendRequest(req)\n    );\n\n    return pipeline(request);\n  }\n\n  public getOrderedPolicies(): PipelinePolicy[] {\n    if (!this._orderedPolicies) {\n      this._orderedPolicies = this.orderPolicies();\n    }\n    return this._orderedPolicies;\n  }\n\n  public clone(): Pipeline {\n    return new HttpPipeline(this._policies);\n  }\n\n  public static create(): Pipeline {\n    return new HttpPipeline();\n  }\n\n  private orderPolicies(): PipelinePolicy[] {\n    /**\n     * The goal of this method is to reliably order pipeline policies\n     * based on their declared requirements when they were added.\n     *\n     * Order is first determined by phase:\n     *\n     * 1. Serialize Phase\n     * 2. Policies not in a phase\n     * 3. Deserialize Phase\n     * 4. Retry Phase\n     * 5. Sign Phase\n     *\n     * Within each phase, policies are executed in the order\n     * they were added unless they were specified to execute\n     * before/after other policies or after a particular phase.\n     *\n     * To determine the final order, we will walk the policy list\n     * in phase order multiple times until all dependencies are\n     * satisfied.\n     *\n     * `afterPolicies` are the set of policies that must be\n     * executed before a given policy. This requirement is\n     * considered satisfied when each of the listed policies\n     * have been scheduled.\n     *\n     * `beforePolicies` are the set of policies that must be\n     * executed after a given policy. Since this dependency\n     * can be expressed by converting it into a equivalent\n     * `afterPolicies` declarations, they are normalized\n     * into that form for simplicity.\n     *\n     * An `afterPhase` dependency is considered satisfied when all\n     * policies in that phase have scheduled.\n     *\n     */\n    const result: PipelinePolicy[] = [];\n\n    // Track all policies we know about.\n    const policyMap: Map<string, PolicyGraphNode> = new Map<string, PolicyGraphNode>();\n\n    function createPhase(name: PipelinePhase | \"None\"): Phase {\n      return {\n        name,\n        policies: new Set<PolicyGraphNode>(),\n        hasRun: false,\n        hasAfterPolicies: false,\n      };\n    }\n\n    // Track policies for each phase.\n    const serializePhase = createPhase(\"Serialize\");\n    const noPhase = createPhase(\"None\");\n    const deserializePhase = createPhase(\"Deserialize\");\n    const retryPhase = createPhase(\"Retry\");\n    const signPhase = createPhase(\"Sign\");\n\n    // a list of phases in order\n    const orderedPhases = [serializePhase, noPhase, deserializePhase, retryPhase, signPhase];\n\n    // Small helper function to map phase name to each Phase\n    function getPhase(phase: PipelinePhase | undefined): Phase {\n      if (phase === \"Retry\") {\n        return retryPhase;\n      } else if (phase === \"Serialize\") {\n        return serializePhase;\n      } else if (phase === \"Deserialize\") {\n        return deserializePhase;\n      } else if (phase === \"Sign\") {\n        return signPhase;\n      } else {\n        return noPhase;\n      }\n    }\n\n    // First walk each policy and create a node to track metadata.\n    for (const descriptor of this._policies) {\n      const policy = descriptor.policy;\n      const options = descriptor.options;\n      const policyName = policy.name;\n      if (policyMap.has(policyName)) {\n        throw new Error(\"Duplicate policy names not allowed in pipeline\");\n      }\n      const node: PolicyGraphNode = {\n        policy,\n        dependsOn: new Set<PolicyGraphNode>(),\n        dependants: new Set<PolicyGraphNode>(),\n      };\n      if (options.afterPhase) {\n        node.afterPhase = getPhase(options.afterPhase);\n        node.afterPhase.hasAfterPolicies = true;\n      }\n      policyMap.set(policyName, node);\n      const phase = getPhase(options.phase);\n      phase.policies.add(node);\n    }\n\n    // Now that each policy has a node, connect dependency references.\n    for (const descriptor of this._policies) {\n      const { policy, options } = descriptor;\n      const policyName = policy.name;\n      const node = policyMap.get(policyName);\n      if (!node) {\n        throw new Error(`Missing node for policy ${policyName}`);\n      }\n\n      if (options.afterPolicies) {\n        for (const afterPolicyName of options.afterPolicies) {\n          const afterNode = policyMap.get(afterPolicyName);\n          if (afterNode) {\n            // Linking in both directions helps later\n            // when we want to notify dependants.\n            node.dependsOn.add(afterNode);\n            afterNode.dependants.add(node);\n          }\n        }\n      }\n      if (options.beforePolicies) {\n        for (const beforePolicyName of options.beforePolicies) {\n          const beforeNode = policyMap.get(beforePolicyName);\n          if (beforeNode) {\n            // To execute before another node, make it\n            // depend on the current node.\n            beforeNode.dependsOn.add(node);\n            node.dependants.add(beforeNode);\n          }\n        }\n      }\n    }\n\n    function walkPhase(phase: Phase): void {\n      phase.hasRun = true;\n      // Sets iterate in insertion order\n      for (const node of phase.policies) {\n        if (node.afterPhase && (!node.afterPhase.hasRun || node.afterPhase.policies.size)) {\n          // If this node is waiting on a phase to complete,\n          // we need to skip it for now.\n          // Even if the phase is empty, we should wait for it\n          // to be walked to avoid re-ordering policies.\n          continue;\n        }\n        if (node.dependsOn.size === 0) {\n          // If there's nothing else we're waiting for, we can\n          // add this policy to the result list.\n          result.push(node.policy);\n          // Notify anything that depends on this policy that\n          // the policy has been scheduled.\n          for (const dependant of node.dependants) {\n            dependant.dependsOn.delete(node);\n          }\n          policyMap.delete(node.policy.name);\n          phase.policies.delete(node);\n        }\n      }\n    }\n\n    function walkPhases(): void {\n      for (const phase of orderedPhases) {\n        walkPhase(phase);\n        // if the phase isn't complete\n        if (phase.policies.size > 0 && phase !== noPhase) {\n          if (!noPhase.hasRun) {\n            // Try running noPhase to see if that unblocks this phase next tick.\n            // This can happen if a phase that happens before noPhase\n            // is waiting on a noPhase policy to complete.\n            walkPhase(noPhase);\n          }\n          // Don't proceed to the next phase until this phase finishes.\n          return;\n        }\n\n        if (phase.hasAfterPolicies) {\n          // Run any policies unblocked by this phase\n          walkPhase(noPhase);\n        }\n      }\n    }\n\n    // Iterate until we've put every node in the result list.\n    let iteration = 0;\n    while (policyMap.size > 0) {\n      iteration++;\n      const initialResultLength = result.length;\n      // Keep walking each phase in order until we can order every node.\n      walkPhases();\n      // The result list *should* get at least one larger each time\n      // after the first full pass.\n      // Otherwise, we're going to loop forever.\n      if (result.length <= initialResultLength && iteration > 1) {\n        throw new Error(\"Cannot satisfy policy dependencies due to requirements cycle.\");\n      }\n    }\n\n    return result;\n  }\n}\n\n/**\n * Creates a totally empty pipeline.\n * Useful for testing or creating a custom one.\n */\nexport function createEmptyPipeline(): Pipeline {\n  return HttpPipeline.create();\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { createClientLogger } from \"@azure/logger\";\nexport const logger = createClientLogger(\"core-rest-pipeline\");\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { UnknownObject, isObject } from \"@azure/core-util\";\n\n/**\n * @internal\n */\nexport interface SanitizerOptions {\n  /**\n   * Header names whose values will be logged when logging is enabled.\n   * Defaults include a list of well-known safe headers. Any headers\n   * specified in this field will be added to that list.  Any other values will\n   * be written to logs as \"REDACTED\".\n   */\n  additionalAllowedHeaderNames?: string[];\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   */\n  additionalAllowedQueryParameters?: string[];\n}\n\nconst RedactedString = \"REDACTED\";\n\n// Make sure this list is up-to-date with the one under core/logger/Readme#Keyconcepts\nconst defaultAllowedHeaderNames = [\n  \"x-ms-client-request-id\",\n  \"x-ms-return-client-request-id\",\n  \"x-ms-useragent\",\n  \"x-ms-correlation-request-id\",\n  \"x-ms-request-id\",\n  \"client-request-id\",\n  \"ms-cv\",\n  \"return-client-request-id\",\n  \"traceparent\",\n\n  \"Access-Control-Allow-Credentials\",\n  \"Access-Control-Allow-Headers\",\n  \"Access-Control-Allow-Methods\",\n  \"Access-Control-Allow-Origin\",\n  \"Access-Control-Expose-Headers\",\n  \"Access-Control-Max-Age\",\n  \"Access-Control-Request-Headers\",\n  \"Access-Control-Request-Method\",\n  \"Origin\",\n\n  \"Accept\",\n  \"Accept-Encoding\",\n  \"Cache-Control\",\n  \"Connection\",\n  \"Content-Length\",\n  \"Content-Type\",\n  \"Date\",\n  \"ETag\",\n  \"Expires\",\n  \"If-Match\",\n  \"If-Modified-Since\",\n  \"If-None-Match\",\n  \"If-Unmodified-Since\",\n  \"Last-Modified\",\n  \"Pragma\",\n  \"Request-Id\",\n  \"Retry-After\",\n  \"Server\",\n  \"Transfer-Encoding\",\n  \"User-Agent\",\n  \"WWW-Authenticate\",\n];\n\nconst defaultAllowedQueryParameters: string[] = [\"api-version\"];\n\n/**\n * @internal\n */\nexport class Sanitizer {\n  private allowedHeaderNames: Set<string>;\n  private allowedQueryParameters: Set<string>;\n\n  constructor({\n    additionalAllowedHeaderNames: allowedHeaderNames = [],\n    additionalAllowedQueryParameters: allowedQueryParameters = [],\n  }: SanitizerOptions = {}) {\n    allowedHeaderNames = defaultAllowedHeaderNames.concat(allowedHeaderNames);\n    allowedQueryParameters = defaultAllowedQueryParameters.concat(allowedQueryParameters);\n\n    this.allowedHeaderNames = new Set(allowedHeaderNames.map((n) => n.toLowerCase()));\n    this.allowedQueryParameters = new Set(allowedQueryParameters.map((p) => p.toLowerCase()));\n  }\n\n  public sanitize(obj: unknown): string {\n    const seen = new Set<unknown>();\n    return JSON.stringify(\n      obj,\n      (key: string, value: unknown) => {\n        // Ensure Errors include their interesting non-enumerable members\n        if (value instanceof Error) {\n          return {\n            ...value,\n            name: value.name,\n            message: value.message,\n          };\n        }\n\n        if (key === \"headers\") {\n          return this.sanitizeHeaders(value as UnknownObject);\n        } else if (key === \"url\") {\n          return this.sanitizeUrl(value as string);\n        } else if (key === \"query\") {\n          return this.sanitizeQuery(value as UnknownObject);\n        } else if (key === \"body\") {\n          // Don't log the request body\n          return undefined;\n        } else if (key === \"response\") {\n          // Don't log response again\n          return undefined;\n        } else if (key === \"operationSpec\") {\n          // When using sendOperationRequest, the request carries a massive\n          // field with the autorest spec. No need to log it.\n          return undefined;\n        } else if (Array.isArray(value) || isObject(value)) {\n          if (seen.has(value)) {\n            return \"[Circular]\";\n          }\n          seen.add(value);\n        }\n\n        return value;\n      },\n      2\n    );\n  }\n\n  private sanitizeHeaders(obj: UnknownObject): UnknownObject {\n    const sanitized: UnknownObject = {};\n    for (const key of Object.keys(obj)) {\n      if (this.allowedHeaderNames.has(key.toLowerCase())) {\n        sanitized[key] = obj[key];\n      } else {\n        sanitized[key] = RedactedString;\n      }\n    }\n    return sanitized;\n  }\n\n  private sanitizeQuery(value: UnknownObject): UnknownObject {\n    if (typeof value !== \"object\" || value === null) {\n      return value;\n    }\n\n    const sanitized: UnknownObject = {};\n\n    for (const k of Object.keys(value)) {\n      if (this.allowedQueryParameters.has(k.toLowerCase())) {\n        sanitized[k] = value[k];\n      } else {\n        sanitized[k] = RedactedString;\n      }\n    }\n\n    return sanitized;\n  }\n\n  private sanitizeUrl(value: string): string {\n    if (typeof value !== \"string\" || value === null) {\n      return value;\n    }\n\n    const url = new URL(value);\n\n    if (!url.search) {\n      return value;\n    }\n\n    for (const [key] of url.searchParams) {\n      if (!this.allowedQueryParameters.has(key.toLowerCase())) {\n        url.searchParams.set(key, RedactedString);\n      }\n    }\n\n    return url.toString();\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { Debugger } from \"@azure/logger\";\nimport { PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces\";\nimport { PipelinePolicy } from \"../pipeline\";\nimport { logger as coreLogger } from \"../log\";\nimport { Sanitizer } from \"../util/sanitizer\";\n\n/**\n * The programmatic identifier of the logPolicy.\n */\nexport const logPolicyName = \"logPolicy\";\n\n/**\n * Options to configure the logPolicy.\n */\nexport interface LogPolicyOptions {\n  /**\n   * Header names whose values will be logged when logging is enabled.\n   * Defaults include a list of well-known safe headers. Any headers\n   * specified in this field will be added to that list.  Any other values will\n   * be written to logs as \"REDACTED\".\n   */\n  additionalAllowedHeaderNames?: string[];\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   */\n  additionalAllowedQueryParameters?: string[];\n\n  /**\n   * The log function to use for writing pipeline logs.\n   * Defaults to core-http's built-in logger.\n   * Compatible with the `debug` library.\n   */\n  logger?: Debugger;\n}\n\n/**\n * A policy that logs all requests and responses.\n * @param options - Options to configure logPolicy.\n */\nexport function logPolicy(options: LogPolicyOptions = {}): PipelinePolicy {\n  const logger = options.logger ?? coreLogger.info;\n  const sanitizer = new Sanitizer({\n    additionalAllowedHeaderNames: options.additionalAllowedHeaderNames,\n    additionalAllowedQueryParameters: options.additionalAllowedQueryParameters,\n  });\n  return {\n    name: logPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      if (!logger.enabled) {\n        return next(request);\n      }\n\n      logger(`Request: ${sanitizer.sanitize(request)}`);\n\n      const response = await next(request);\n\n      logger(`Response status code: ${response.status}`);\n      logger(`Headers: ${sanitizer.sanitize(response.headers)}`);\n\n      return response;\n    },\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces\";\nimport { PipelinePolicy } from \"../pipeline\";\n\n/**\n * The programmatic identifier of the redirectPolicy.\n */\nexport const redirectPolicyName = \"redirectPolicy\";\n\n/**\n * Methods that are allowed to follow redirects 301 and 302\n */\nconst allowedRedirect = [\"GET\", \"HEAD\"];\n\n/**\n * Options for how redirect responses are handled.\n */\nexport interface RedirectPolicyOptions {\n  /**\n   * The maximum number of times the redirect URL will be tried before\n   * failing.  Defaults to 20.\n   */\n  maxRetries?: number;\n}\n\n/**\n * A policy to follow Location headers from the server in order\n * to support server-side redirection.\n * In the browser, this policy is not used.\n * @param options - Options to control policy behavior.\n */\nexport function redirectPolicy(options: RedirectPolicyOptions = {}): PipelinePolicy {\n  const { maxRetries = 20 } = options;\n  return {\n    name: redirectPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      const response = await next(request);\n      return handleRedirect(next, response, maxRetries);\n    },\n  };\n}\n\nasync function handleRedirect(\n  next: SendRequest,\n  response: PipelineResponse,\n  maxRetries: number,\n  currentRetries: number = 0\n): Promise<PipelineResponse> {\n  const { request, status, headers } = response;\n  const locationHeader = headers.get(\"location\");\n  if (\n    locationHeader &&\n    (status === 300 ||\n      (status === 301 && allowedRedirect.includes(request.method)) ||\n      (status === 302 && allowedRedirect.includes(request.method)) ||\n      (status === 303 && request.method === \"POST\") ||\n      status === 307) &&\n    currentRetries < maxRetries\n  ) {\n    const url = new URL(locationHeader, request.url);\n    request.url = url.toString();\n\n    // POST request with Status code 303 should be converted into a\n    // redirected GET request if the redirect url is present in the location header\n    if (status === 303) {\n      request.method = \"GET\";\n      request.headers.delete(\"Content-Length\");\n      delete request.body;\n    }\n\n    request.headers.delete(\"Authorization\");\n\n    const res = await next(request);\n    return handleRedirect(next, res, maxRetries, currentRetries + 1);\n  }\n\n  return response;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as os from \"os\";\n\n/**\n * @internal\n */\nexport function getHeaderName(): string {\n  return \"User-Agent\";\n}\n\n/**\n * @internal\n */\nexport function setPlatformSpecificData(map: Map<string, string>): void {\n  map.set(\"Node\", process.version);\n  map.set(\"OS\", `(${os.arch()}-${os.type()}-${os.release()})`);\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nexport const SDK_VERSION: string = \"1.10.3\";\n\nexport const DEFAULT_RETRY_POLICY_COUNT = 3;\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { getHeaderName, setPlatformSpecificData } from \"./userAgentPlatform\";\nimport { SDK_VERSION } from \"../constants\";\n\nfunction getUserAgentString(telemetryInfo: Map<string, string>): string {\n  const parts: string[] = [];\n  for (const [key, value] of telemetryInfo) {\n    const token = value ? `${key}/${value}` : key;\n    parts.push(token);\n  }\n  return parts.join(\" \");\n}\n\n/**\n * @internal\n */\nexport function getUserAgentHeaderName(): string {\n  return getHeaderName();\n}\n\n/**\n * @internal\n */\nexport function getUserAgentValue(prefix?: string): string {\n  const runtimeInfo = new Map<string, string>();\n  runtimeInfo.set(\"core-rest-pipeline\", SDK_VERSION);\n  setPlatformSpecificData(runtimeInfo);\n  const defaultAgent = getUserAgentString(runtimeInfo);\n  const userAgentValue = prefix ? `${prefix} ${defaultAgent}` : defaultAgent;\n  return userAgentValue;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces\";\nimport { PipelinePolicy } from \"../pipeline\";\nimport { getUserAgentHeaderName, getUserAgentValue } from \"../util/userAgent\";\n\nconst UserAgentHeaderName = getUserAgentHeaderName();\n\n/**\n * The programmatic identifier of the userAgentPolicy.\n */\nexport const userAgentPolicyName = \"userAgentPolicy\";\n\n/**\n * Options for adding user agent details to outgoing requests.\n */\nexport interface UserAgentPolicyOptions {\n  /**\n   * String prefix to add to the user agent for outgoing requests.\n   * Defaults to an empty string.\n   */\n  userAgentPrefix?: string;\n}\n\n/**\n * A policy that sets the User-Agent header (or equivalent) to reflect\n * the library version.\n * @param options - Options to customize the user agent value.\n */\nexport function userAgentPolicy(options: UserAgentPolicyOptions = {}): PipelinePolicy {\n  const userAgentValue = getUserAgentValue(options.userAgentPrefix);\n  return {\n    name: userA<PERSON>P<PERSON>yName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      if (!request.headers.has(UserAgentHeaderName)) {\n        request.headers.set(UserAgentHeaderName, userAgentValue);\n      }\n      return next(request);\n    },\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces\";\nimport { PipelinePolicy } from \"../pipeline\";\n\n/**\n * The programmatic identifier of the decompressResponsePolicy.\n */\nexport const decompressResponsePolicyName = \"decompressResponsePolicy\";\n\n/**\n * A policy to enable response decompression according to Accept-Encoding header\n * https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Encoding\n */\nexport function decompressResponsePolicy(): PipelinePolicy {\n  return {\n    name: decompressResponsePolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      // HEAD requests have no body\n      if (request.method !== \"HEAD\") {\n        request.headers.set(\"Accept-Encoding\", \"gzip,deflate\");\n      }\n      return next(request);\n    },\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortError, AbortSignalLike } from \"@azure/abort-controller\";\nimport { PipelineResponse } from \"../interfaces\";\n\nconst StandardAbortMessage = \"The operation was aborted.\";\n\n/**\n * A wrapper for setTimeout that resolves a promise after delayInMs milliseconds.\n * @param delayInMs - The number of milliseconds to be delayed.\n * @param value - The value to be resolved with after a timeout of t milliseconds.\n * @param options - The options for delay - currently abort options\n *                  - abortSignal - The abortSignal associated with containing operation.\n *                  - abortErrorMsg - The abort error message associated with containing operation.\n * @returns Resolved promise\n */\nexport function delay<T>(\n  delayInMs: number,\n  value?: T,\n  options?: {\n    abortSignal?: AbortSignalLike;\n    abortErrorMsg?: string;\n  }\n): Promise<T | void> {\n  return new Promise((resolve, reject) => {\n    let timer: ReturnType<typeof setTimeout> | undefined = undefined;\n    let onAborted: (() => void) | undefined = undefined;\n\n    const rejectOnAbort = (): void => {\n      return reject(\n        new AbortError(options?.abortErrorMsg ? options?.abortErrorMsg : StandardAbortMessage)\n      );\n    };\n\n    const removeListeners = (): void => {\n      if (options?.abortSignal && onAborted) {\n        options.abortSignal.removeEventListener(\"abort\", onAborted);\n      }\n    };\n\n    onAborted = (): void => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n      removeListeners();\n      return rejectOnAbort();\n    };\n\n    if (options?.abortSignal && options.abortSignal.aborted) {\n      return rejectOnAbort();\n    }\n\n    timer = setTimeout(() => {\n      removeListeners();\n      resolve(value);\n    }, delayInMs);\n\n    if (options?.abortSignal) {\n      options.abortSignal.addEventListener(\"abort\", onAborted);\n    }\n  });\n}\n\n/**\n * @internal\n * @returns the parsed value or undefined if the parsed value is invalid.\n */\nexport function parseHeaderValueAsNumber(\n  response: PipelineResponse,\n  headerName: string\n): number | undefined {\n  const value = response.headers.get(headerName);\n  if (!value) return;\n  const valueAsNum = Number(value);\n  if (Number.isNaN(valueAsNum)) return;\n  return valueAsNum;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PipelineResponse } from \"..\";\nimport { parseHeaderValueAsNumber } from \"../util/helpers\";\nimport { RetryStrategy } from \"./retryStrategy\";\n\n/**\n * The header that comes back from Azure services representing\n * the amount of time (minimum) to wait to retry (in seconds or timestamp after which we can retry).\n */\nconst RetryAfterHeader = \"Retry-After\";\n/**\n * The headers that come back from Azure services representing\n * the amount of time (minimum) to wait to retry.\n *\n * \"retry-after-ms\", \"x-ms-retry-after-ms\" : milliseconds\n * \"Retry-After\" : seconds or timestamp\n */\nconst AllRetryAfterHeaders: string[] = [\"retry-after-ms\", \"x-ms-retry-after-ms\", RetryAfterHeader];\n\n/**\n * A response is a throttling retry response if it has a throttling status code (429 or 503),\n * as long as one of the [ \"Retry-After\" or \"retry-after-ms\" or \"x-ms-retry-after-ms\" ] headers has a valid value.\n *\n * Returns the `retryAfterInMs` value if the response is a throttling retry response.\n * If not throttling retry response, returns `undefined`.\n *\n * @internal\n */\nfunction getRetryAfterInMs(response?: PipelineResponse): number | undefined {\n  if (!(response && [429, 503].includes(response.status))) return undefined;\n  try {\n    // Headers: \"retry-after-ms\", \"x-ms-retry-after-ms\", \"Retry-After\"\n    for (const header of AllRetryAfterHeaders) {\n      const retryAfterValue = parseHeaderValueAsNumber(response, header);\n      if (retryAfterValue === 0 || retryAfterValue) {\n        // \"Retry-After\" header ==> seconds\n        // \"retry-after-ms\", \"x-ms-retry-after-ms\" headers ==> milli-seconds\n        const multiplyingFactor = header === RetryAfterHeader ? 1000 : 1;\n        return retryAfterValue * multiplyingFactor; // in milli-seconds\n      }\n    }\n\n    // RetryAfterHeader (\"Retry-After\") has a special case where it might be formatted as a date instead of a number of seconds\n    const retryAfterHeader = response.headers.get(RetryAfterHeader);\n    if (!retryAfterHeader) return;\n\n    const date = Date.parse(retryAfterHeader);\n    const diff = date - Date.now();\n    // negative diff would mean a date in the past, so retry asap with 0 milliseconds\n    return Number.isFinite(diff) ? Math.max(0, diff) : undefined;\n  } catch (e: any) {\n    return undefined;\n  }\n}\n\n/**\n * A response is a retry response if it has a throttling status code (429 or 503),\n * as long as one of the [ \"Retry-After\" or \"retry-after-ms\" or \"x-ms-retry-after-ms\" ] headers has a valid value.\n */\nexport function isThrottlingRetryResponse(response?: PipelineResponse): boolean {\n  return Number.isFinite(getRetryAfterInMs(response));\n}\n\nexport function throttlingRetryStrategy(): RetryStrategy {\n  return {\n    name: \"throttlingRetryStrategy\",\n    retry({ response }) {\n      const retryAfterInMs = getRetryAfterInMs(response);\n      if (!Number.isFinite(retryAfterInMs)) {\n        return { skipStrategy: true };\n      }\n      return {\n        retryAfterInMs,\n      };\n    },\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PipelineResponse } from \"../interfaces\";\nimport { RestError } from \"../restError\";\nimport { getRandomIntegerInclusive } from \"@azure/core-util\";\nimport { RetryStrategy } from \"./retryStrategy\";\nimport { isThrottlingRetryResponse } from \"./throttlingRetryStrategy\";\n\n// intervals are in milliseconds\nconst DEFAULT_CLIENT_RETRY_INTERVAL = 1000;\nconst DEFAULT_CLIENT_MAX_RETRY_INTERVAL = 1000 * 64;\n\n/**\n * A retry strategy that retries with an exponentially increasing delay in these two cases:\n * - When there are errors in the underlying transport layer (e.g. DNS lookup failures).\n * - Or otherwise if the outgoing request fails (408, greater or equal than 500, except for 501 and 505).\n */\nexport function exponentialRetryStrategy(\n  options: {\n    /**\n     * The amount of delay in milliseconds between retry attempts. Defaults to 1000\n     * (1 second.) The delay increases exponentially with each retry up to a maximum\n     * specified by maxRetryDelayInMs.\n     */\n    retryDelayInMs?: number;\n\n    /**\n     * The maximum delay in milliseconds allowed before retrying an operation. Defaults\n     * to 64000 (64 seconds).\n     */\n    maxRetryDelayInMs?: number;\n\n    /**\n     * If true it won't retry if it received a system error.\n     */\n    ignoreSystemErrors?: boolean;\n\n    /**\n     * If true it won't retry if it received a non-fatal HTTP status code.\n     */\n    ignoreHttpStatusCodes?: boolean;\n  } = {}\n): RetryStrategy {\n  const retryInterval = options.retryDelayInMs ?? DEFAULT_CLIENT_RETRY_INTERVAL;\n  const maxRetryInterval = options.maxRetryDelayInMs ?? DEFAULT_CLIENT_MAX_RETRY_INTERVAL;\n\n  let retryAfterInMs = retryInterval;\n\n  return {\n    name: \"exponentialRetryStrategy\",\n    retry({ retryCount, response, responseError }) {\n      const matchedSystemError = isSystemError(responseError);\n      const ignoreSystemErrors = matchedSystemError && options.ignoreSystemErrors;\n\n      const isExponential = isExponentialRetryResponse(response);\n      const ignoreExponentialResponse = isExponential && options.ignoreHttpStatusCodes;\n      const unknownResponse = response && (isThrottlingRetryResponse(response) || !isExponential);\n\n      if (unknownResponse || ignoreExponentialResponse || ignoreSystemErrors) {\n        return { skipStrategy: true };\n      }\n\n      if (responseError && !matchedSystemError && !isExponential) {\n        return { errorToThrow: responseError };\n      }\n\n      // Exponentially increase the delay each time\n      const exponentialDelay = retryAfterInMs * Math.pow(2, retryCount);\n      // Don't let the delay exceed the maximum\n      const clampedExponentialDelay = Math.min(maxRetryInterval, exponentialDelay);\n      // Allow the final value to have some \"jitter\" (within 50% of the delay size) so\n      // that retries across multiple clients don't occur simultaneously.\n      retryAfterInMs =\n        clampedExponentialDelay / 2 + getRandomIntegerInclusive(0, clampedExponentialDelay / 2);\n      return { retryAfterInMs };\n    },\n  };\n}\n\n/**\n * A response is a retry response if it has status codes:\n * - 408, or\n * - Greater or equal than 500, except for 501 and 505.\n */\nexport function isExponentialRetryResponse(response?: PipelineResponse): boolean {\n  return Boolean(\n    response &&\n      response.status !== undefined &&\n      (response.status >= 500 || response.status === 408) &&\n      response.status !== 501 &&\n      response.status !== 505\n  );\n}\n\n/**\n * Determines whether an error from a pipeline response was triggered in the network layer.\n */\nexport function isSystemError(err?: RestError): boolean {\n  if (!err) {\n    return false;\n  }\n  return (\n    err.code === \"ETIMEDOUT\" ||\n    err.code === \"ESOCKETTIMEDOUT\" ||\n    err.code === \"ECONNREFUSED\" ||\n    err.code === \"ECONNRESET\" ||\n    err.code === \"ENOENT\"\n  );\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces\";\nimport { PipelinePolicy } from \"../pipeline\";\nimport { delay } from \"../util/helpers\";\nimport { createClientLogger } from \"@azure/logger\";\nimport { RetryStrategy } from \"../retryStrategies/retryStrategy\";\nimport { RestError } from \"../restError\";\nimport { AbortError } from \"@azure/abort-controller\";\nimport { AzureLogger } from \"@azure/logger\";\nimport { DEFAULT_RETRY_POLICY_COUNT } from \"../constants\";\n\nconst retryPolicyLogger = createClientLogger(\"core-rest-pipeline retryPolicy\");\n\n/**\n * The programmatic identifier of the retryPolicy.\n */\nconst retryPolicyName = \"retryPolicy\";\n\n/**\n * Options to the {@link retryPolicy}\n */\nexport interface RetryPolicyOptions {\n  /**\n   * Maximum number of retries. If not specified, it will limit to 3 retries.\n   */\n  maxRetries?: number;\n  /**\n   * Logger. If it's not provided, a default logger is used.\n   */\n  logger?: AzureLogger;\n}\n\n/**\n * retryPolicy is a generic policy to enable retrying requests when certain conditions are met\n */\nexport function retryPolicy(\n  strategies: RetryStrategy[],\n  options: RetryPolicyOptions = { maxRetries: DEFAULT_RETRY_POLICY_COUNT }\n): PipelinePolicy {\n  const logger = options.logger || retryPolicyLogger;\n  return {\n    name: retryPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      let response: PipelineResponse | undefined;\n      let responseError: RestError | undefined;\n      let retryCount = -1;\n\n      // eslint-disable-next-line no-constant-condition\n      retryRequest: while (true) {\n        retryCount += 1;\n        response = undefined;\n        responseError = undefined;\n\n        try {\n          logger.info(`Retry ${retryCount}: Attempting to send request`, request.requestId);\n          response = await next(request);\n          logger.info(`Retry ${retryCount}: Received a response from request`, request.requestId);\n        } catch (e: any) {\n          logger.error(`Retry ${retryCount}: Received an error from request`, request.requestId);\n\n          // RestErrors are valid targets for the retry strategies.\n          // If none of the retry strategies can work with them, they will be thrown later in this policy.\n          // If the received error is not a RestError, it is immediately thrown.\n          responseError = e as RestError;\n          if (!e || responseError.name !== \"RestError\") {\n            throw e;\n          }\n\n          response = responseError.response;\n        }\n\n        if (request.abortSignal?.aborted) {\n          logger.error(`Retry ${retryCount}: Request aborted.`);\n          const abortError = new AbortError();\n          throw abortError;\n        }\n\n        if (retryCount >= (options.maxRetries ?? DEFAULT_RETRY_POLICY_COUNT)) {\n          logger.info(\n            `Retry ${retryCount}: Maximum retries reached. Returning the last received response, or throwing the last received error.`\n          );\n          if (responseError) {\n            throw responseError;\n          } else if (response) {\n            return response;\n          } else {\n            throw new Error(\"Maximum retries reached with no response or error to throw\");\n          }\n        }\n\n        logger.info(`Retry ${retryCount}: Processing ${strategies.length} retry strategies.`);\n\n        strategiesLoop: for (const strategy of strategies) {\n          const strategyLogger = strategy.logger || retryPolicyLogger;\n          strategyLogger.info(`Retry ${retryCount}: Processing retry strategy ${strategy.name}.`);\n\n          const modifiers = strategy.retry({\n            retryCount,\n            response,\n            responseError,\n          });\n\n          if (modifiers.skipStrategy) {\n            strategyLogger.info(`Retry ${retryCount}: Skipped.`);\n            continue strategiesLoop;\n          }\n\n          const { errorToThrow, retryAfterInMs, redirectTo } = modifiers;\n\n          if (errorToThrow) {\n            strategyLogger.error(\n              `Retry ${retryCount}: Retry strategy ${strategy.name} throws error:`,\n              errorToThrow\n            );\n            throw errorToThrow;\n          }\n\n          if (retryAfterInMs || retryAfterInMs === 0) {\n            strategyLogger.info(\n              `Retry ${retryCount}: Retry strategy ${strategy.name} retries after ${retryAfterInMs}`\n            );\n            await delay(retryAfterInMs, undefined, { abortSignal: request.abortSignal });\n            continue retryRequest;\n          }\n\n          if (redirectTo) {\n            strategyLogger.info(\n              `Retry ${retryCount}: Retry strategy ${strategy.name} redirects to ${redirectTo}`\n            );\n            request.url = redirectTo;\n            continue retryRequest;\n          }\n        }\n\n        if (responseError) {\n          logger.info(\n            `None of the retry strategies could work with the received error. Throwing it.`\n          );\n          throw responseError;\n        }\n        if (response) {\n          logger.info(\n            `None of the retry strategies could work with the received response. Returning it.`\n          );\n          return response;\n        }\n\n        // If all the retries skip and there's no response,\n        // we're still in the retry loop, so a new request will be sent\n        // until `maxRetries` is reached.\n      }\n    },\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PipelineRetryOptions } from \"../interfaces\";\nimport { PipelinePolicy } from \"../pipeline\";\nimport { exponentialRetryStrategy } from \"../retryStrategies/exponentialRetryStrategy\";\nimport { throttlingRetryStrategy } from \"../retryStrategies/throttlingRetryStrategy\";\nimport { retryPolicy } from \"./retryPolicy\";\nimport { DEFAULT_RETRY_POLICY_COUNT } from \"../constants\";\n\n/**\n * Name of the {@link defaultRetryPolicy}\n */\nexport const defaultRetryPolicyName = \"defaultRetryPolicy\";\n\n/**\n * Options that control how to retry failed requests.\n */\nexport interface DefaultRetryPolicyOptions extends PipelineRetryOptions {}\n\n/**\n * A policy that retries according to three strategies:\n * - When the server sends a 429 response with a Retry-After header.\n * - When there are errors in the underlying transport layer (e.g. DNS lookup failures).\n * - Or otherwise if the outgoing request fails, it will retry with an exponentially increasing delay.\n */\nexport function defaultRetryPolicy(options: DefaultRetryPolicyOptions = {}): PipelinePolicy {\n  return {\n    name: defaultRetryPolicyName,\n    sendRequest: retryPolicy([throttlingRetryStrategy(), exponentialRetryStrategy(options)], {\n      maxRetries: options.maxRetries ?? DEFAULT_RETRY_POLICY_COUNT,\n    }).sendRequest,\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport FormData from \"form-data\";\nimport { FormDataMap, PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces\";\nimport { PipelinePolicy } from \"../pipeline\";\n\n/**\n * The programmatic identifier of the formDataPolicy.\n */\nexport const formDataPolicyName = \"formDataPolicy\";\n\n/**\n * A policy that encodes FormData on the request into the body.\n */\nexport function formDataPolicy(): PipelinePolicy {\n  return {\n    name: formDataPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      if (request.formData) {\n        const contentType = request.headers.get(\"Content-Type\");\n        if (contentType && contentType.indexOf(\"application/x-www-form-urlencoded\") !== -1) {\n          request.body = wwwFormUrlEncode(request.formData);\n          request.formData = undefined;\n        } else {\n          await prepareFormData(request.formData, request);\n        }\n      }\n      return next(request);\n    },\n  };\n}\n\nfunction wwwFormUrlEncode(formData: FormDataMap): string {\n  const urlSearchParams = new URLSearchParams();\n  for (const [key, value] of Object.entries(formData)) {\n    if (Array.isArray(value)) {\n      for (const subValue of value) {\n        urlSearchParams.append(key, subValue.toString());\n      }\n    } else {\n      urlSearchParams.append(key, value.toString());\n    }\n  }\n  return urlSearchParams.toString();\n}\n\nasync function prepareFormData(formData: FormDataMap, request: PipelineRequest): Promise<void> {\n  const requestForm = new FormData();\n  for (const formKey of Object.keys(formData)) {\n    const formValue = formData[formKey];\n    if (Array.isArray(formValue)) {\n      for (const subValue of formValue) {\n        requestForm.append(formKey, subValue);\n      }\n    } else {\n      requestForm.append(formKey, formValue);\n    }\n  }\n\n  request.body = requestForm;\n  request.formData = undefined;\n  const contentType = request.headers.get(\"Content-Type\");\n  if (contentType && contentType.indexOf(\"multipart/form-data\") !== -1) {\n    request.headers.set(\n      \"Content-Type\",\n      `multipart/form-data; boundary=${requestForm.getBoundary()}`\n    );\n  }\n  try {\n    const contentLength = await new Promise<number>((resolve, reject) => {\n      requestForm.getLength((err, length) => {\n        if (err) {\n          reject(err);\n        } else {\n          resolve(length);\n        }\n      });\n    });\n    request.headers.set(\"Content-Length\", contentLength);\n  } catch (e: any) {\n    // ignore setting the length if this fails\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as http from \"http\";\nimport * as https from \"https\";\nimport { HttpsProxyAgent, HttpsProxyAgentOptions } from \"https-proxy-agent\";\nimport { HttpProxyAgent, HttpProxyAgentOptions } from \"http-proxy-agent\";\nimport { PipelineRequest, PipelineResponse, ProxySettings, SendRequest } from \"../interfaces\";\nimport { PipelinePolicy } from \"../pipeline\";\nimport { logger } from \"../log\";\n\nconst HTTPS_PROXY = \"HTTPS_PROXY\";\nconst HTTP_PROXY = \"HTTP_PROXY\";\nconst ALL_PROXY = \"ALL_PROXY\";\nconst NO_PROXY = \"NO_PROXY\";\n\n/**\n * The programmatic identifier of the proxyPolicy.\n */\nexport const proxyPolicyName = \"proxyPolicy\";\n\n/**\n * Stores the patterns specified in NO_PROXY environment variable.\n * @internal\n */\nexport const globalNoProxyList: string[] = [];\nlet noProxyListLoaded: boolean = false;\n\n/** A cache of whether a host should bypass the proxy. */\nconst globalBypassedMap: Map<string, boolean> = new Map();\n\nfunction getEnvironmentValue(name: string): string | undefined {\n  if (process.env[name]) {\n    return process.env[name];\n  } else if (process.env[name.toLowerCase()]) {\n    return process.env[name.toLowerCase()];\n  }\n  return undefined;\n}\n\nfunction loadEnvironmentProxyValue(): string | undefined {\n  if (!process) {\n    return undefined;\n  }\n\n  const httpsProxy = getEnvironmentValue(HTTPS_PROXY);\n  const allProxy = getEnvironmentValue(ALL_PROXY);\n  const httpProxy = getEnvironmentValue(HTTP_PROXY);\n\n  return httpsProxy || allProxy || httpProxy;\n}\n\n/**\n * Check whether the host of a given `uri` matches any pattern in the no proxy list.\n * If there's a match, any request sent to the same host shouldn't have the proxy settings set.\n * This implementation is a port of https://github.com/Azure/azure-sdk-for-net/blob/8cca811371159e527159c7eb65602477898683e2/sdk/core/Azure.Core/src/Pipeline/Internal/HttpEnvironmentProxy.cs#L210\n */\nfunction isBypassed(\n  uri: string,\n  noProxyList: string[],\n  bypassedMap?: Map<string, boolean>\n): boolean | undefined {\n  if (noProxyList.length === 0) {\n    return false;\n  }\n  const host = new URL(uri).hostname;\n  if (bypassedMap?.has(host)) {\n    return bypassedMap.get(host);\n  }\n  let isBypassedFlag = false;\n  for (const pattern of noProxyList) {\n    if (pattern[0] === \".\") {\n      // This should match either domain it self or any subdomain or host\n      // .foo.com will match foo.com it self or *.foo.com\n      if (host.endsWith(pattern)) {\n        isBypassedFlag = true;\n      } else {\n        if (host.length === pattern.length - 1 && host === pattern.slice(1)) {\n          isBypassedFlag = true;\n        }\n      }\n    } else {\n      if (host === pattern) {\n        isBypassedFlag = true;\n      }\n    }\n  }\n  bypassedMap?.set(host, isBypassedFlag);\n  return isBypassedFlag;\n}\n\nexport function loadNoProxy(): string[] {\n  const noProxy = getEnvironmentValue(NO_PROXY);\n  noProxyListLoaded = true;\n  if (noProxy) {\n    return noProxy\n      .split(\",\")\n      .map((item) => item.trim())\n      .filter((item) => item.length);\n  }\n\n  return [];\n}\n\n/**\n * This method converts a proxy url into `ProxySettings` for use with ProxyPolicy.\n * If no argument is given, it attempts to parse a proxy URL from the environment\n * variables `HTTPS_PROXY` or `HTTP_PROXY`.\n * @param proxyUrl - The url of the proxy to use. May contain authentication information.\n */\nexport function getDefaultProxySettings(proxyUrl?: string): ProxySettings | undefined {\n  if (!proxyUrl) {\n    proxyUrl = loadEnvironmentProxyValue();\n    if (!proxyUrl) {\n      return undefined;\n    }\n  }\n\n  const parsedUrl = new URL(proxyUrl);\n  const schema = parsedUrl.protocol ? parsedUrl.protocol + \"//\" : \"\";\n  return {\n    host: schema + parsedUrl.hostname,\n    port: Number.parseInt(parsedUrl.port || \"80\"),\n    username: parsedUrl.username,\n    password: parsedUrl.password,\n  };\n}\n\n/**\n * @internal\n */\nexport function getProxyAgentOptions(\n  proxySettings: ProxySettings,\n  { headers, tlsSettings }: PipelineRequest\n): HttpProxyAgentOptions {\n  let parsedProxyUrl: URL;\n  try {\n    parsedProxyUrl = new URL(proxySettings.host);\n  } catch (_error) {\n    throw new Error(\n      `Expecting a valid host string in proxy settings, but found \"${proxySettings.host}\".`\n    );\n  }\n\n  if (tlsSettings) {\n    logger.warning(\n      \"TLS settings are not supported in combination with custom Proxy, certificates provided to the client will be ignored.\"\n    );\n  }\n\n  const proxyAgentOptions: HttpsProxyAgentOptions = {\n    hostname: parsedProxyUrl.hostname,\n    port: proxySettings.port,\n    protocol: parsedProxyUrl.protocol,\n    headers: headers.toJSON(),\n  };\n  if (proxySettings.username && proxySettings.password) {\n    proxyAgentOptions.auth = `${proxySettings.username}:${proxySettings.password}`;\n  } else if (proxySettings.username) {\n    proxyAgentOptions.auth = `${proxySettings.username}`;\n  }\n  return proxyAgentOptions;\n}\n\nfunction setProxyAgentOnRequest(request: PipelineRequest, cachedAgents: CachedAgents): void {\n  // Custom Agent should take precedence so if one is present\n  // we should skip to avoid overwriting it.\n  if (request.agent) {\n    return;\n  }\n\n  const url = new URL(request.url);\n\n  const isInsecure = url.protocol !== \"https:\";\n\n  const proxySettings = request.proxySettings;\n  if (proxySettings) {\n    if (isInsecure) {\n      if (!cachedAgents.httpProxyAgent) {\n        const proxyAgentOptions = getProxyAgentOptions(proxySettings, request);\n        cachedAgents.httpProxyAgent = new HttpProxyAgent(proxyAgentOptions);\n      }\n      request.agent = cachedAgents.httpProxyAgent;\n    } else {\n      if (!cachedAgents.httpsProxyAgent) {\n        const proxyAgentOptions = getProxyAgentOptions(proxySettings, request);\n        cachedAgents.httpsProxyAgent = new HttpsProxyAgent(proxyAgentOptions);\n      }\n      request.agent = cachedAgents.httpsProxyAgent;\n    }\n  }\n}\n\ninterface CachedAgents {\n  httpsProxyAgent?: https.Agent;\n  httpProxyAgent?: http.Agent;\n}\n\n/**\n * A policy that allows one to apply proxy settings to all requests.\n * If not passed static settings, they will be retrieved from the HTTPS_PROXY\n * or HTTP_PROXY environment variables.\n * @param proxySettings - ProxySettings to use on each request.\n * @param options - additional settings, for example, custom NO_PROXY patterns\n */\nexport function proxyPolicy(\n  proxySettings = getDefaultProxySettings(),\n  options?: {\n    /** a list of patterns to override those loaded from NO_PROXY environment variable. */\n    customNoProxyList?: string[];\n  }\n): PipelinePolicy {\n  if (!noProxyListLoaded) {\n    globalNoProxyList.push(...loadNoProxy());\n  }\n\n  const cachedAgents: CachedAgents = {};\n\n  return {\n    name: proxyPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      if (\n        !request.proxySettings &&\n        !isBypassed(\n          request.url,\n          options?.customNoProxyList ?? globalNoProxyList,\n          options?.customNoProxyList ? undefined : globalBypassedMap\n        )\n      ) {\n        request.proxySettings = proxySettings;\n      }\n\n      if (request.proxySettings) {\n        setProxyAgentOnRequest(request, cachedAgents);\n      }\n      return next(request);\n    },\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces\";\nimport { PipelinePolicy } from \"../pipeline\";\n\n/**\n * The programmatic identifier of the setClientRequestIdPolicy.\n */\nexport const setClientRequestIdPolicyName = \"setClientRequestIdPolicy\";\n\n/**\n * Each PipelineRequest gets a unique id upon creation.\n * This policy passes that unique id along via an HTTP header to enable better\n * telemetry and tracing.\n * @param requestIdHeaderName - The name of the header to pass the request ID to.\n */\nexport function setClientRequestIdPolicy(\n  requestIdHeaderName = \"x-ms-client-request-id\"\n): PipelinePolicy {\n  return {\n    name: setClientRequestIdPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      if (!request.headers.has(requestIdHeaderName)) {\n        request.headers.set(requestIdHeaderName, request.requestId);\n      }\n      return next(request);\n    },\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PipelinePolicy } from \"../pipeline\";\nimport { TlsSettings } from \"../interfaces\";\n\n/**\n * Name of the TLS Policy\n */\nexport const tlsPolicyName = \"tlsPolicy\";\n\n/**\n * Gets a pipeline policy that adds the client certificate to the HttpClient agent for authentication.\n */\nexport function tlsPolicy(tlsSettings?: TlsSettings): PipelinePolicy {\n  return {\n    name: tlsPolicyName,\n    sendRequest: async (req, next) => {\n      // Users may define a request tlsSettings, honor those over the client level one\n      if (!req.tlsSettings) {\n        req.tlsSettings = tlsSettings;\n      }\n      return next(req);\n    },\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { inspect } from \"util\";\n\nexport const custom = inspect.custom;\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { isError } from \"@azure/core-util\";\nimport { PipelineRequest, PipelineResponse } from \"./interfaces\";\nimport { custom } from \"./util/inspect\";\nimport { Sanitizer } from \"./util/sanitizer\";\n\nconst errorSanitizer = new Sanitizer();\n\n/**\n * The options supported by RestError.\n */\nexport interface RestErrorOptions {\n  /**\n   * The code of the error itself (use statics on RestError if possible.)\n   */\n  code?: string;\n  /**\n   * The HTTP status code of the request (if applicable.)\n   */\n  statusCode?: number;\n  /**\n   * The request that was made.\n   */\n  request?: PipelineRequest;\n  /**\n   * The response received (if any.)\n   */\n  response?: PipelineResponse;\n}\n\n/**\n * A custom error type for failed pipeline requests.\n */\nexport class RestError extends Error {\n  /**\n   * Something went wrong when making the request.\n   * This means the actual request failed for some reason,\n   * such as a DNS issue or the connection being lost.\n   */\n  static readonly REQUEST_SEND_ERROR: string = \"REQUEST_SEND_ERROR\";\n  /**\n   * This means that parsing the response from the server failed.\n   * It may have been malformed.\n   */\n  static readonly PARSE_ERROR: string = \"PARSE_ERROR\";\n\n  /**\n   * The code of the error itself (use statics on RestError if possible.)\n   */\n  public code?: string;\n  /**\n   * The HTTP status code of the request (if applicable.)\n   */\n  public statusCode?: number;\n  /**\n   * The request that was made.\n   */\n  public request?: PipelineRequest;\n  /**\n   * The response received (if any.)\n   */\n  public response?: PipelineResponse;\n  /**\n   * Bonus property set by the throw site.\n   */\n  public details?: unknown;\n\n  constructor(message: string, options: RestErrorOptions = {}) {\n    super(message);\n    this.name = \"RestError\";\n    this.code = options.code;\n    this.statusCode = options.statusCode;\n    this.request = options.request;\n    this.response = options.response;\n\n    Object.setPrototypeOf(this, RestError.prototype);\n  }\n\n  /**\n   * Logging method for util.inspect in Node\n   */\n  [custom](): string {\n    return `RestError: ${this.message} \\n ${errorSanitizer.sanitize(this)}`;\n  }\n}\n\n/**\n * Typeguard for RestError\n * @param e - Something caught by a catch clause.\n */\nexport function isRestError(e: unknown): e is RestError {\n  if (e instanceof RestError) {\n    return true;\n  }\n  return isError(e) && e.name === \"RestError\";\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  TracingClient,\n  TracingContext,\n  TracingSpan,\n  createTracingClient,\n} from \"@azure/core-tracing\";\nimport { SDK_VERSION } from \"../constants\";\nimport { PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces\";\nimport { PipelinePolicy } from \"../pipeline\";\nimport { getUserAgentValue } from \"../util/userAgent\";\nimport { logger } from \"../log\";\nimport { getErrorMessage, isError } from \"@azure/core-util\";\nimport { isRestError } from \"../restError\";\n\n/**\n * The programmatic identifier of the tracingPolicy.\n */\nexport const tracingPolicyName = \"tracingPolicy\";\n\n/**\n * Options to configure the tracing policy.\n */\nexport interface TracingPolicyOptions {\n  /**\n   * String prefix to add to the user agent logged as metadata\n   * on the generated Span.\n   * Defaults to an empty string.\n   */\n  userAgentPrefix?: string;\n}\n\n/**\n * A simple policy to create OpenTelemetry Spans for each request made by the pipeline\n * that has SpanOptions with a parent.\n * Requests made without a parent Span will not be recorded.\n * @param options - Options to configure the telemetry logged by the tracing policy.\n */\nexport function tracingPolicy(options: TracingPolicyOptions = {}): PipelinePolicy {\n  const userAgent = getUserAgentValue(options.userAgentPrefix);\n  const tracingClient = tryCreateTracingClient();\n\n  return {\n    name: tracingPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      if (!tracingClient || !request.tracingOptions?.tracingContext) {\n        return next(request);\n      }\n\n      const { span, tracingContext } = tryCreateSpan(tracingClient, request, userAgent) ?? {};\n\n      if (!span || !tracingContext) {\n        return next(request);\n      }\n\n      try {\n        const response = await tracingClient.withContext(tracingContext, next, request);\n        tryProcessResponse(span, response);\n        return response;\n      } catch (err: any) {\n        tryProcessError(span, err);\n        throw err;\n      }\n    },\n  };\n}\n\nfunction tryCreateTracingClient(): TracingClient | undefined {\n  try {\n    return createTracingClient({\n      namespace: \"\",\n      packageName: \"@azure/core-rest-pipeline\",\n      packageVersion: SDK_VERSION,\n    });\n  } catch (e: unknown) {\n    logger.warning(`Error when creating the TracingClient: ${getErrorMessage(e)}`);\n    return undefined;\n  }\n}\n\nfunction tryCreateSpan(\n  tracingClient: TracingClient,\n  request: PipelineRequest,\n  userAgent?: string\n): { span: TracingSpan; tracingContext: TracingContext } | undefined {\n  try {\n    // As per spec, we do not need to differentiate between HTTP and HTTPS in span name.\n    const { span, updatedOptions } = tracingClient.startSpan(\n      `HTTP ${request.method}`,\n      { tracingOptions: request.tracingOptions },\n      {\n        spanKind: \"client\",\n        spanAttributes: {\n          \"http.method\": request.method,\n          \"http.url\": request.url,\n          requestId: request.requestId,\n        },\n      }\n    );\n\n    // If the span is not recording, don't do any more work.\n    if (!span.isRecording()) {\n      span.end();\n      return undefined;\n    }\n\n    if (userAgent) {\n      span.setAttribute(\"http.user_agent\", userAgent);\n    }\n\n    // set headers\n    const headers = tracingClient.createRequestHeaders(\n      updatedOptions.tracingOptions.tracingContext\n    );\n    for (const [key, value] of Object.entries(headers)) {\n      request.headers.set(key, value);\n    }\n    return { span, tracingContext: updatedOptions.tracingOptions.tracingContext };\n  } catch (e: any) {\n    logger.warning(`Skipping creating a tracing span due to an error: ${getErrorMessage(e)}`);\n    return undefined;\n  }\n}\n\nfunction tryProcessError(span: TracingSpan, error: unknown): void {\n  try {\n    span.setStatus({\n      status: \"error\",\n      error: isError(error) ? error : undefined,\n    });\n    if (isRestError(error) && error.statusCode) {\n      span.setAttribute(\"http.status_code\", error.statusCode);\n    }\n    span.end();\n  } catch (e: any) {\n    logger.warning(`Skipping tracing span processing due to an error: ${getErrorMessage(e)}`);\n  }\n}\n\nfunction tryProcessResponse(span: TracingSpan, response: PipelineResponse): void {\n  try {\n    span.setAttribute(\"http.status_code\", response.status);\n    const serviceRequestId = response.headers.get(\"x-ms-request-id\");\n    if (serviceRequestId) {\n      span.setAttribute(\"serviceRequestId\", serviceRequestId);\n    }\n    span.setStatus({\n      status: \"success\",\n    });\n    span.end();\n  } catch (e: any) {\n    logger.warning(`Skipping tracing span processing due to an error: ${getErrorMessage(e)}`);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { LogPolicyOptions, logPolicy } from \"./policies/logPolicy\";\nimport { Pipeline, createEmptyPipeline } from \"./pipeline\";\nimport { PipelineRetryOptions, TlsSettings } from \"./interfaces\";\nimport { RedirectPolicyOptions, redirectPolicy } from \"./policies/redirectPolicy\";\nimport { UserAgentPolicyOptions, userAgentPolicy } from \"./policies/userAgentPolicy\";\n\nimport { ProxySettings } from \".\";\nimport { decompressResponsePolicy } from \"./policies/decompressResponsePolicy\";\nimport { defaultRetryPolicy } from \"./policies/defaultRetryPolicy\";\nimport { formDataPolicy } from \"./policies/formDataPolicy\";\nimport { isNode } from \"@azure/core-util\";\nimport { proxyPolicy } from \"./policies/proxyPolicy\";\nimport { setClientRequestIdPolicy } from \"./policies/setClientRequestIdPolicy\";\nimport { tlsPolicy } from \"./policies/tlsPolicy\";\nimport { tracingPolicy } from \"./policies/tracingPolicy\";\n\n/**\n * Defines options that are used to configure the HTTP pipeline for\n * an SDK client.\n */\nexport interface PipelineOptions {\n  /**\n   * Options that control how to retry failed requests.\n   */\n  retryOptions?: PipelineRetryOptions;\n\n  /**\n   * Options to configure a proxy for outgoing requests.\n   */\n  proxyOptions?: ProxySettings;\n\n  /** Options for configuring TLS authentication */\n  tlsOptions?: TlsSettings;\n\n  /**\n   * Options for how redirect responses are handled.\n   */\n  redirectOptions?: RedirectPolicyOptions;\n\n  /**\n   * Options for adding user agent details to outgoing requests.\n   */\n  userAgentOptions?: UserAgentPolicyOptions;\n}\n\n/**\n * Defines options that are used to configure internal options of\n * the HTTP pipeline for an SDK client.\n */\nexport interface InternalPipelineOptions extends PipelineOptions {\n  /**\n   * Options to configure request/response logging.\n   */\n  loggingOptions?: LogPolicyOptions;\n}\n\n/**\n * Create a new pipeline with a default set of customizable policies.\n * @param options - Options to configure a custom pipeline.\n */\nexport function createPipelineFromOptions(options: InternalPipelineOptions): Pipeline {\n  const pipeline = createEmptyPipeline();\n\n  if (isNode) {\n    if (options.tlsOptions) {\n      pipeline.addPolicy(tlsPolicy(options.tlsOptions));\n    }\n    pipeline.addPolicy(proxyPolicy(options.proxyOptions));\n    pipeline.addPolicy(decompressResponsePolicy());\n  }\n\n  pipeline.addPolicy(formDataPolicy());\n  pipeline.addPolicy(userAgentPolicy(options.userAgentOptions));\n  pipeline.addPolicy(setClientRequestIdPolicy());\n  pipeline.addPolicy(defaultRetryPolicy(options.retryOptions), { phase: \"Retry\" });\n  pipeline.addPolicy(tracingPolicy(options.userAgentOptions), { afterPhase: \"Retry\" });\n  if (isNode) {\n    // Both XHR and Fetch expect to handle redirects automatically,\n    // so only include this policy when we're in Node.\n    pipeline.addPolicy(redirectPolicy(options.redirectOptions), { afterPhase: \"Retry\" });\n  }\n  pipeline.addPolicy(logPolicy(options.loggingOptions), { afterPhase: \"Sign\" });\n\n  return pipeline;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpHeaders, RawHttpHeaders, RawHttpHeadersInput } from \"./interfaces\";\n\ninterface HeaderEntry {\n  name: string;\n  value: string;\n}\n\nfunction normalizeName(name: string): string {\n  return name.toLowerCase();\n}\n\nfunction* headerIterator(map: Map<string, HeaderEntry>): IterableIterator<[string, string]> {\n  for (const entry of map.values()) {\n    yield [entry.name, entry.value];\n  }\n}\n\nclass HttpHeadersImpl implements HttpHeaders {\n  private readonly _headersMap: Map<string, HeaderEntry>;\n\n  constructor(rawHeaders?: RawHttpHeaders | RawHttpHeadersInput) {\n    this._headersMap = new Map<string, HeaderEntry>();\n    if (rawHeaders) {\n      for (const headerName of Object.keys(rawHeaders)) {\n        this.set(headerName, rawHeaders[headerName]);\n      }\n    }\n  }\n\n  /**\n   * Set a header in this collection with the provided name and value. The name is\n   * case-insensitive.\n   * @param name - The name of the header to set. This value is case-insensitive.\n   * @param value - The value of the header to set.\n   */\n  public set(name: string, value: string | number | boolean): void {\n    this._headersMap.set(normalizeName(name), { name, value: String(value) });\n  }\n\n  /**\n   * Get the header value for the provided header name, or undefined if no header exists in this\n   * collection with the provided name.\n   * @param name - The name of the header. This value is case-insensitive.\n   */\n  public get(name: string): string | undefined {\n    return this._headersMap.get(normalizeName(name))?.value;\n  }\n\n  /**\n   * Get whether or not this header collection contains a header entry for the provided header name.\n   * @param name - The name of the header to set. This value is case-insensitive.\n   */\n  public has(name: string): boolean {\n    return this._headersMap.has(normalizeName(name));\n  }\n\n  /**\n   * Remove the header with the provided headerName.\n   * @param name - The name of the header to remove.\n   */\n  public delete(name: string): void {\n    this._headersMap.delete(normalizeName(name));\n  }\n\n  /**\n   * Get the JSON object representation of this HTTP header collection.\n   */\n  public toJSON(options: { preserveCase?: boolean } = {}): RawHttpHeaders {\n    const result: RawHttpHeaders = {};\n    if (options.preserveCase) {\n      for (const entry of this._headersMap.values()) {\n        result[entry.name] = entry.value;\n      }\n    } else {\n      for (const [normalizedName, entry] of this._headersMap) {\n        result[normalizedName] = entry.value;\n      }\n    }\n\n    return result;\n  }\n\n  /**\n   * Get the string representation of this HTTP header collection.\n   */\n  public toString(): string {\n    return JSON.stringify(this.toJSON({ preserveCase: true }));\n  }\n\n  /**\n   * Iterate over tuples of header [name, value] pairs.\n   */\n  [Symbol.iterator](): Iterator<[string, string]> {\n    return headerIterator(this._headersMap);\n  }\n}\n\n/**\n * Creates an object that satisfies the `HttpHeaders` interface.\n * @param rawHeaders - A simple object representing initial headers\n */\nexport function createHttpHeaders(rawHeaders?: RawHttpHeadersInput): HttpHeaders {\n  return new HttpHeadersImpl(rawHeaders);\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as http from \"http\";\nimport * as https from \"https\";\nimport * as zlib from \"zlib\";\nimport { Transform } from \"stream\";\nimport { AbortController, AbortError } from \"@azure/abort-controller\";\nimport {\n  HttpClient,\n  HttpHeaders,\n  PipelineRequest,\n  PipelineResponse,\n  RequestBodyType,\n  TlsSettings,\n  TransferProgressEvent,\n} from \"./interfaces\";\nimport { createHttpHeaders } from \"./httpHeaders\";\nimport { RestError } from \"./restError\";\nimport { IncomingMessage } from \"http\";\nimport { logger } from \"./log\";\n\nconst DEFAULT_TLS_SETTINGS = {};\n\nfunction isReadableStream(body: any): body is NodeJS.ReadableStream {\n  return body && typeof body.pipe === \"function\";\n}\n\nfunction isStreamComplete(stream: NodeJS.ReadableStream): Promise<void> {\n  return new Promise((resolve) => {\n    stream.on(\"close\", resolve);\n    stream.on(\"end\", resolve);\n    stream.on(\"error\", resolve);\n  });\n}\n\nfunction isArrayBuffer(body: any): body is ArrayBuffer | ArrayBufferView {\n  return body && typeof body.byteLength === \"number\";\n}\n\nclass ReportTransform extends Transform {\n  private loadedBytes = 0;\n  private progressCallback: (progress: TransferProgressEvent) => void;\n\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  _transform(chunk: string | Buffer, _encoding: string, callback: Function): void {\n    this.push(chunk);\n    this.loadedBytes += chunk.length;\n    try {\n      this.progressCallback({ loadedBytes: this.loadedBytes });\n      callback();\n    } catch (e: any) {\n      callback(e);\n    }\n  }\n\n  constructor(progressCallback: (progress: TransferProgressEvent) => void) {\n    super();\n    this.progressCallback = progressCallback;\n  }\n}\n\n/**\n * A HttpClient implementation that uses Node's \"https\" module to send HTTPS requests.\n * @internal\n */\nclass NodeHttpClient implements HttpClient {\n  private cachedHttpAgent?: http.Agent;\n  private cachedHttpsAgents: WeakMap<TlsSettings, https.Agent> = new WeakMap();\n\n  /**\n   * Makes a request over an underlying transport layer and returns the response.\n   * @param request - The request to be made.\n   */\n  public async sendRequest(request: PipelineRequest): Promise<PipelineResponse> {\n    const abortController = new AbortController();\n    let abortListener: ((event: any) => void) | undefined;\n    if (request.abortSignal) {\n      if (request.abortSignal.aborted) {\n        throw new AbortError(\"The operation was aborted.\");\n      }\n\n      abortListener = (event: Event) => {\n        if (event.type === \"abort\") {\n          abortController.abort();\n        }\n      };\n      request.abortSignal.addEventListener(\"abort\", abortListener);\n    }\n\n    if (request.timeout > 0) {\n      setTimeout(() => {\n        abortController.abort();\n      }, request.timeout);\n    }\n\n    const acceptEncoding = request.headers.get(\"Accept-Encoding\");\n    const shouldDecompress =\n      acceptEncoding?.includes(\"gzip\") || acceptEncoding?.includes(\"deflate\");\n\n    let body = typeof request.body === \"function\" ? request.body() : request.body;\n    if (body && !request.headers.has(\"Content-Length\")) {\n      const bodyLength = getBodyLength(body);\n      if (bodyLength !== null) {\n        request.headers.set(\"Content-Length\", bodyLength);\n      }\n    }\n\n    let responseStream: NodeJS.ReadableStream | undefined;\n    try {\n      if (body && request.onUploadProgress) {\n        const onUploadProgress = request.onUploadProgress;\n        const uploadReportStream = new ReportTransform(onUploadProgress);\n        uploadReportStream.on(\"error\", (e) => {\n          logger.error(\"Error in upload progress\", e);\n        });\n        if (isReadableStream(body)) {\n          body.pipe(uploadReportStream);\n        } else {\n          uploadReportStream.end(body);\n        }\n\n        body = uploadReportStream;\n      }\n\n      const res = await this.makeRequest(request, abortController, body);\n\n      const headers = getResponseHeaders(res);\n\n      const status = res.statusCode ?? 0;\n      const response: PipelineResponse = {\n        status,\n        headers,\n        request,\n      };\n\n      // Responses to HEAD must not have a body.\n      // If they do return a body, that body must be ignored.\n      if (request.method === \"HEAD\") {\n        // call resume() and not destroy() to avoid closing the socket\n        // and losing keep alive\n        res.resume();\n        return response;\n      }\n\n      responseStream = shouldDecompress ? getDecodedResponseStream(res, headers) : res;\n\n      const onDownloadProgress = request.onDownloadProgress;\n      if (onDownloadProgress) {\n        const downloadReportStream = new ReportTransform(onDownloadProgress);\n        downloadReportStream.on(\"error\", (e) => {\n          logger.error(\"Error in download progress\", e);\n        });\n        responseStream.pipe(downloadReportStream);\n        responseStream = downloadReportStream;\n      }\n\n      if (\n        // Value of POSITIVE_INFINITY in streamResponseStatusCodes is considered as any status code\n        request.streamResponseStatusCodes?.has(Number.POSITIVE_INFINITY) ||\n        request.streamResponseStatusCodes?.has(response.status)\n      ) {\n        response.readableStreamBody = responseStream;\n      } else {\n        response.bodyAsText = await streamToText(responseStream);\n      }\n\n      return response;\n    } finally {\n      // clean up event listener\n      if (request.abortSignal && abortListener) {\n        let uploadStreamDone = Promise.resolve();\n        if (isReadableStream(body)) {\n          uploadStreamDone = isStreamComplete(body as NodeJS.ReadableStream);\n        }\n        let downloadStreamDone = Promise.resolve();\n        if (isReadableStream(responseStream)) {\n          downloadStreamDone = isStreamComplete(responseStream);\n        }\n\n        Promise.all([uploadStreamDone, downloadStreamDone])\n          .then(() => {\n            // eslint-disable-next-line promise/always-return\n            if (abortListener) {\n              request.abortSignal?.removeEventListener(\"abort\", abortListener);\n            }\n          })\n          .catch((e) => {\n            logger.warning(\"Error when cleaning up abortListener on httpRequest\", e);\n          });\n      }\n    }\n  }\n\n  private makeRequest(\n    request: PipelineRequest,\n    abortController: AbortController,\n    body?: RequestBodyType\n  ): Promise<http.IncomingMessage> {\n    const url = new URL(request.url);\n\n    const isInsecure = url.protocol !== \"https:\";\n\n    if (isInsecure && !request.allowInsecureConnection) {\n      throw new Error(`Cannot connect to ${request.url} while allowInsecureConnection is false.`);\n    }\n\n    const agent = (request.agent as http.Agent) ?? this.getOrCreateAgent(request, isInsecure);\n    const options: http.RequestOptions = {\n      agent,\n      hostname: url.hostname,\n      path: `${url.pathname}${url.search}`,\n      port: url.port,\n      method: request.method,\n      headers: request.headers.toJSON({ preserveCase: true }),\n    };\n\n    return new Promise<http.IncomingMessage>((resolve, reject) => {\n      const req = isInsecure ? http.request(options, resolve) : https.request(options, resolve);\n\n      req.once(\"error\", (err: Error & { code?: string }) => {\n        reject(\n          new RestError(err.message, { code: err.code ?? RestError.REQUEST_SEND_ERROR, request })\n        );\n      });\n\n      abortController.signal.addEventListener(\"abort\", () => {\n        const abortError = new AbortError(\"The operation was aborted.\");\n        req.destroy(abortError);\n        reject(abortError);\n      });\n      if (body && isReadableStream(body)) {\n        body.pipe(req);\n      } else if (body) {\n        if (typeof body === \"string\" || Buffer.isBuffer(body)) {\n          req.end(body);\n        } else if (isArrayBuffer(body)) {\n          req.end(ArrayBuffer.isView(body) ? Buffer.from(body.buffer) : Buffer.from(body));\n        } else {\n          logger.error(\"Unrecognized body type\", body);\n          reject(new RestError(\"Unrecognized body type\"));\n        }\n      } else {\n        // streams don't like \"undefined\" being passed as data\n        req.end();\n      }\n    });\n  }\n\n  private getOrCreateAgent(request: PipelineRequest, isInsecure: boolean): http.Agent {\n    const disableKeepAlive = request.disableKeepAlive;\n\n    // Handle Insecure requests first\n    if (isInsecure) {\n      if (disableKeepAlive) {\n        // keepAlive:false is the default so we don't need a custom Agent\n        return http.globalAgent;\n      }\n\n      if (!this.cachedHttpAgent) {\n        // If there is no cached agent create a new one and cache it.\n        this.cachedHttpAgent = new http.Agent({ keepAlive: true });\n      }\n      return this.cachedHttpAgent;\n    } else {\n      if (disableKeepAlive && !request.tlsSettings) {\n        // When there are no tlsSettings and keepAlive is false\n        // we don't need a custom agent\n        return https.globalAgent;\n      }\n\n      // We use the tlsSettings to index cached clients\n      const tlsSettings = request.tlsSettings ?? DEFAULT_TLS_SETTINGS;\n\n      // Get the cached agent or create a new one with the\n      // provided values for keepAlive and tlsSettings\n      let agent = this.cachedHttpsAgents.get(tlsSettings);\n\n      if (agent && agent.options.keepAlive === !disableKeepAlive) {\n        return agent;\n      }\n\n      logger.info(\"No cached TLS Agent exist, creating a new Agent\");\n      agent = new https.Agent({\n        // keepAlive is true if disableKeepAlive is false.\n        keepAlive: !disableKeepAlive,\n        // Since we are spreading, if no tslSettings were provided, nothing is added to the agent options.\n        ...tlsSettings,\n      });\n\n      this.cachedHttpsAgents.set(tlsSettings, agent);\n      return agent;\n    }\n  }\n}\n\nfunction getResponseHeaders(res: IncomingMessage): HttpHeaders {\n  const headers = createHttpHeaders();\n  for (const header of Object.keys(res.headers)) {\n    const value = res.headers[header];\n    if (Array.isArray(value)) {\n      if (value.length > 0) {\n        headers.set(header, value[0]);\n      }\n    } else if (value) {\n      headers.set(header, value);\n    }\n  }\n  return headers;\n}\n\nfunction getDecodedResponseStream(\n  stream: IncomingMessage,\n  headers: HttpHeaders\n): NodeJS.ReadableStream {\n  const contentEncoding = headers.get(\"Content-Encoding\");\n  if (contentEncoding === \"gzip\") {\n    const unzip = zlib.createGunzip();\n    stream.pipe(unzip);\n    return unzip;\n  } else if (contentEncoding === \"deflate\") {\n    const inflate = zlib.createInflate();\n    stream.pipe(inflate);\n    return inflate;\n  }\n\n  return stream;\n}\n\nfunction streamToText(stream: NodeJS.ReadableStream): Promise<string> {\n  return new Promise<string>((resolve, reject) => {\n    const buffer: Buffer[] = [];\n\n    stream.on(\"data\", (chunk) => {\n      if (Buffer.isBuffer(chunk)) {\n        buffer.push(chunk);\n      } else {\n        buffer.push(Buffer.from(chunk));\n      }\n    });\n    stream.on(\"end\", () => {\n      resolve(Buffer.concat(buffer).toString(\"utf8\"));\n    });\n    stream.on(\"error\", (e) => {\n      if (e && e?.name === \"AbortError\") {\n        reject(e);\n      } else {\n        reject(\n          new RestError(`Error reading response as text: ${e.message}`, {\n            code: RestError.PARSE_ERROR,\n          })\n        );\n      }\n    });\n  });\n}\n\n/** @internal */\nexport function getBodyLength(body: RequestBodyType): number | null {\n  if (!body) {\n    return 0;\n  } else if (Buffer.isBuffer(body)) {\n    return body.length;\n  } else if (isReadableStream(body)) {\n    return null;\n  } else if (isArrayBuffer(body)) {\n    return body.byteLength;\n  } else if (typeof body === \"string\") {\n    return Buffer.from(body).length;\n  } else {\n    return null;\n  }\n}\n\n/**\n * Create a new HttpClient instance for the NodeJS environment.\n * @internal\n */\nexport function createNodeHttpClient(): HttpClient {\n  return new NodeHttpClient();\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpClient } from \"./interfaces\";\nimport { createNodeHttpClient } from \"./nodeHttpClient\";\n\n/**\n * Create the correct HttpClient for the current environment.\n */\nexport function createDefaultHttpClient(): HttpClient {\n  return createNodeHttpClient();\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  FormDataMap,\n  HttpHeaders,\n  HttpMethods,\n  PipelineRequest,\n  ProxySettings,\n  RequestBodyType,\n  TransferProgressEvent,\n} from \"./interfaces\";\nimport { createHttpHeaders } from \"./httpHeaders\";\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { randomUUID } from \"@azure/core-util\";\nimport { OperationTracingOptions } from \"@azure/core-tracing\";\n\n/**\n * Settings to initialize a request.\n * Almost equivalent to Partial<PipelineRequest>, but url is mandatory.\n */\nexport interface PipelineRequestOptions {\n  /**\n   * The URL to make the request to.\n   */\n  url: string;\n\n  /**\n   * The HTTP method to use when making the request.\n   */\n  method?: HttpMethods;\n\n  /**\n   * The HTTP headers to use when making the request.\n   */\n  headers?: HttpHeaders;\n\n  /**\n   * The number of milliseconds a request can take before automatically being terminated.\n   * If the request is terminated, an `AbortError` is thrown.\n   * Defaults to 0, which disables the timeout.\n   */\n  timeout?: number;\n\n  /**\n   * If credentials (cookies) should be sent along during an XHR.\n   * Defaults to false.\n   */\n  withCredentials?: boolean;\n\n  /**\n   * A unique identifier for the request. Used for logging and tracing.\n   */\n  requestId?: string;\n\n  /**\n   * The HTTP body content (if any)\n   */\n  body?: RequestBodyType;\n\n  /**\n   * To simulate a browser form post\n   */\n  formData?: FormDataMap;\n\n  /**\n   * A list of response status codes whose corresponding PipelineResponse body should be treated as a stream.\n   */\n  streamResponseStatusCodes?: Set<number>;\n\n  /**\n   * BROWSER ONLY\n   *\n   * A browser only option to enable use of the Streams API. If this option is set and streaming is used\n   * (see `streamResponseStatusCodes`), the response will have a property `browserStream` instead of\n   * `blobBody` which will be undefined.\n   *\n   * Default value is false\n   */\n  enableBrowserStreams?: boolean;\n\n  /**\n   * Proxy configuration.\n   */\n  proxySettings?: ProxySettings;\n\n  /**\n   * If the connection should not be reused.\n   */\n  disableKeepAlive?: boolean;\n\n  /**\n   * Used to abort the request later.\n   */\n  abortSignal?: AbortSignalLike;\n\n  /**\n   * Options used to create a span when tracing is enabled.\n   */\n  tracingOptions?: OperationTracingOptions;\n\n  /**\n   * Callback which fires upon upload progress.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /** Callback which fires upon download progress. */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n\n  /** Set to true if the request is sent over HTTP instead of HTTPS */\n  allowInsecureConnection?: boolean;\n}\n\nclass PipelineRequestImpl implements PipelineRequest {\n  public url: string;\n  public method: HttpMethods;\n  public headers: HttpHeaders;\n  public timeout: number;\n  public withCredentials: boolean;\n  public body?: RequestBodyType;\n  public formData?: FormDataMap;\n  public streamResponseStatusCodes?: Set<number>;\n  public enableBrowserStreams: boolean;\n\n  public proxySettings?: ProxySettings;\n  public disableKeepAlive: boolean;\n  public abortSignal?: AbortSignalLike;\n  public requestId: string;\n  public tracingOptions?: OperationTracingOptions;\n  public allowInsecureConnection?: boolean;\n  public onUploadProgress?: (progress: TransferProgressEvent) => void;\n  public onDownloadProgress?: (progress: TransferProgressEvent) => void;\n\n  constructor(options: PipelineRequestOptions) {\n    this.url = options.url;\n    this.body = options.body;\n    this.headers = options.headers ?? createHttpHeaders();\n    this.method = options.method ?? \"GET\";\n    this.timeout = options.timeout ?? 0;\n    this.formData = options.formData;\n    this.disableKeepAlive = options.disableKeepAlive ?? false;\n    this.proxySettings = options.proxySettings;\n    this.streamResponseStatusCodes = options.streamResponseStatusCodes;\n    this.withCredentials = options.withCredentials ?? false;\n    this.abortSignal = options.abortSignal;\n    this.tracingOptions = options.tracingOptions;\n    this.onUploadProgress = options.onUploadProgress;\n    this.onDownloadProgress = options.onDownloadProgress;\n    this.requestId = options.requestId || randomUUID();\n    this.allowInsecureConnection = options.allowInsecureConnection ?? false;\n    this.enableBrowserStreams = options.enableBrowserStreams ?? false;\n  }\n}\n\n/**\n * Creates a new pipeline request with the given options.\n * This method is to allow for the easy setting of default values and not required.\n * @param options - The options to create the request with.\n */\nexport function createPipelineRequest(options: PipelineRequestOptions): PipelineRequest {\n  return new PipelineRequestImpl(options);\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PipelinePolicy } from \"../pipeline\";\nimport { exponentialRetryStrategy } from \"../retryStrategies/exponentialRetryStrategy\";\nimport { retryPolicy } from \"./retryPolicy\";\nimport { DEFAULT_RETRY_POLICY_COUNT } from \"../constants\";\n\n/**\n * The programmatic identifier of the exponentialRetryPolicy.\n */\nexport const exponentialRetryPolicyName = \"exponentialRetryPolicy\";\n\n/**\n * Options that control how to retry failed requests.\n */\nexport interface ExponentialRetryPolicyOptions {\n  /**\n   * The maximum number of retry attempts. Defaults to 3.\n   */\n  maxRetries?: number;\n\n  /**\n   * The amount of delay in milliseconds between retry attempts. Defaults to 1000\n   * (1 second.) The delay increases exponentially with each retry up to a maximum\n   * specified by maxRetryDelayInMs.\n   */\n  retryDelayInMs?: number;\n\n  /**\n   * The maximum delay in milliseconds allowed before retrying an operation. Defaults\n   * to 64000 (64 seconds).\n   */\n  maxRetryDelayInMs?: number;\n}\n\n/**\n * A policy that attempts to retry requests while introducing an exponentially increasing delay.\n * @param options - Options that configure retry logic.\n */\nexport function exponentialRetryPolicy(\n  options: ExponentialRetryPolicyOptions = {}\n): PipelinePolicy {\n  return retryPolicy(\n    [\n      exponentialRetryStrategy({\n        ...options,\n        ignoreSystemErrors: true,\n      }),\n    ],\n    {\n      maxRetries: options.maxRetries ?? DEFAULT_RETRY_POLICY_COUNT,\n    }\n  );\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PipelinePolicy } from \"../pipeline\";\nimport { exponentialRetryStrategy } from \"../retryStrategies/exponentialRetryStrategy\";\nimport { retryPolicy } from \"./retryPolicy\";\nimport { DEFAULT_RETRY_POLICY_COUNT } from \"../constants\";\n\n/**\n * Name of the {@link systemErrorRetryPolicy}\n */\nexport const systemErrorRetryPolicyName = \"systemErrorRetryPolicy\";\n\n/**\n * Options that control how to retry failed requests.\n */\nexport interface SystemErrorRetryPolicyOptions {\n  /**\n   * The maximum number of retry attempts. Defaults to 3.\n   */\n  maxRetries?: number;\n\n  /**\n   * The amount of delay in milliseconds between retry attempts. Defaults to 1000\n   * (1 second.) The delay increases exponentially with each retry up to a maximum\n   * specified by maxRetryDelayInMs.\n   */\n  retryDelayInMs?: number;\n\n  /**\n   * The maximum delay in milliseconds allowed before retrying an operation. Defaults\n   * to 64000 (64 seconds).\n   */\n  maxRetryDelayInMs?: number;\n}\n\n/**\n * A retry policy that specifically seeks to handle errors in the\n * underlying transport layer (e.g. DNS lookup failures) rather than\n * retryable error codes from the server itself.\n * @param options - Options that customize the policy.\n */\nexport function systemErrorRetryPolicy(\n  options: SystemErrorRetryPolicyOptions = {}\n): PipelinePolicy {\n  return {\n    name: systemErrorRetryPolicyName,\n    sendRequest: retryPolicy(\n      [\n        exponentialRetryStrategy({\n          ...options,\n          ignoreHttpStatusCodes: true,\n        }),\n      ],\n      {\n        maxRetries: options.maxRetries ?? DEFAULT_RETRY_POLICY_COUNT,\n      }\n    ).sendRequest,\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PipelinePolicy } from \"../pipeline\";\nimport { throttlingRetryStrategy } from \"../retryStrategies/throttlingRetryStrategy\";\nimport { retryPolicy } from \"./retryPolicy\";\nimport { DEFAULT_RETRY_POLICY_COUNT } from \"../constants\";\n\n/**\n * Name of the {@link throttlingRetryPolicy}\n */\nexport const throttlingRetryPolicyName = \"throttlingRetryPolicy\";\n\n/**\n * Options that control how to retry failed requests.\n */\nexport interface ThrottlingRetryPolicyOptions {\n  /**\n   * The maximum number of retry attempts. Defaults to 3.\n   */\n  maxRetries?: number;\n}\n\n/**\n * A policy that retries when the server sends a 429 response with a Retry-After header.\n *\n * To learn more, please refer to\n * https://docs.microsoft.com/en-us/azure/azure-resource-manager/resource-manager-request-limits,\n * https://docs.microsoft.com/en-us/azure/azure-subscription-service-limits and\n * https://docs.microsoft.com/en-us/azure/virtual-machines/troubleshooting/troubleshooting-throttling-errors\n *\n * @param options - Options that configure retry logic.\n */\nexport function throttlingRetryPolicy(options: ThrottlingRetryPolicyOptions = {}): PipelinePolicy {\n  return {\n    name: throttlingRetryPolicyName,\n    sendRequest: retryPolicy([throttlingRetryStrategy()], {\n      maxRetries: options.maxRetries ?? DEFAULT_RETRY_POLICY_COUNT,\n    }).sendRequest,\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport { delay } from \"./helpers\";\n\n/**\n * A function that gets a promise of an access token and allows providing\n * options.\n *\n * @param options - the options to pass to the underlying token provider\n */\nexport type AccessTokenGetter = (\n  scopes: string | string[],\n  options: GetTokenOptions\n) => Promise<AccessToken>;\n\nexport interface TokenCyclerOptions {\n  /**\n   * The window of time before token expiration during which the token will be\n   * considered unusable due to risk of the token expiring before sending the\n   * request.\n   *\n   * This will only become meaningful if the refresh fails for over\n   * (refreshWindow - forcedRefreshWindow) milliseconds.\n   */\n  forcedRefreshWindowInMs: number;\n  /**\n   * Interval in milliseconds to retry failed token refreshes.\n   */\n  retryIntervalInMs: number;\n  /**\n   * The window of time before token expiration during which\n   * we will attempt to refresh the token.\n   */\n  refreshWindowInMs: number;\n}\n\n// Default options for the cycler if none are provided\nexport const DEFAULT_CYCLER_OPTIONS: TokenCyclerOptions = {\n  forcedRefreshWindowInMs: 1000, // Force waiting for a refresh 1s before the token expires\n  retryIntervalInMs: 3000, // Allow refresh attempts every 3s\n  refreshWindowInMs: 1000 * 60 * 2, // Start refreshing 2m before expiry\n};\n\n/**\n * Converts an an unreliable access token getter (which may resolve with null)\n * into an AccessTokenGetter by retrying the unreliable getter in a regular\n * interval.\n *\n * @param getAccessToken - A function that produces a promise of an access token that may fail by returning null.\n * @param retryIntervalInMs - The time (in milliseconds) to wait between retry attempts.\n * @param refreshTimeout - The timestamp after which the refresh attempt will fail, throwing an exception.\n * @returns - A promise that, if it resolves, will resolve with an access token.\n */\nasync function beginRefresh(\n  getAccessToken: () => Promise<AccessToken | null>,\n  retryIntervalInMs: number,\n  refreshTimeout: number\n): Promise<AccessToken> {\n  // This wrapper handles exceptions gracefully as long as we haven't exceeded\n  // the timeout.\n  async function tryGetAccessToken(): Promise<AccessToken | null> {\n    if (Date.now() < refreshTimeout) {\n      try {\n        return await getAccessToken();\n      } catch {\n        return null;\n      }\n    } else {\n      const finalToken = await getAccessToken();\n\n      // Timeout is up, so throw if it's still null\n      if (finalToken === null) {\n        throw new Error(\"Failed to refresh access token.\");\n      }\n\n      return finalToken;\n    }\n  }\n\n  let token: AccessToken | null = await tryGetAccessToken();\n\n  while (token === null) {\n    await delay(retryIntervalInMs);\n\n    token = await tryGetAccessToken();\n  }\n\n  return token;\n}\n\n/**\n * Creates a token cycler from a credential, scopes, and optional settings.\n *\n * A token cycler represents a way to reliably retrieve a valid access token\n * from a TokenCredential. It will handle initializing the token, refreshing it\n * when it nears expiration, and synchronizes refresh attempts to avoid\n * concurrency hazards.\n *\n * @param credential - the underlying TokenCredential that provides the access\n * token\n * @param tokenCyclerOptions - optionally override default settings for the cycler\n *\n * @returns - a function that reliably produces a valid access token\n */\nexport function createTokenCycler(\n  credential: TokenCredential,\n  tokenCyclerOptions?: Partial<TokenCyclerOptions>\n): AccessTokenGetter {\n  let refreshWorker: Promise<AccessToken> | null = null;\n  let token: AccessToken | null = null;\n  let tenantId: string | undefined;\n\n  const options = {\n    ...DEFAULT_CYCLER_OPTIONS,\n    ...tokenCyclerOptions,\n  };\n\n  /**\n   * This little holder defines several predicates that we use to construct\n   * the rules of refreshing the token.\n   */\n  const cycler = {\n    /**\n     * Produces true if a refresh job is currently in progress.\n     */\n    get isRefreshing(): boolean {\n      return refreshWorker !== null;\n    },\n    /**\n     * Produces true if the cycler SHOULD refresh (we are within the refresh\n     * window and not already refreshing)\n     */\n    get shouldRefresh(): boolean {\n      return (\n        !cycler.isRefreshing &&\n        (token?.expiresOnTimestamp ?? 0) - options.refreshWindowInMs < Date.now()\n      );\n    },\n    /**\n     * Produces true if the cycler MUST refresh (null or nearly-expired\n     * token).\n     */\n    get mustRefresh(): boolean {\n      return (\n        token === null || token.expiresOnTimestamp - options.forcedRefreshWindowInMs < Date.now()\n      );\n    },\n  };\n\n  /**\n   * Starts a refresh job or returns the existing job if one is already\n   * running.\n   */\n  function refresh(\n    scopes: string | string[],\n    getTokenOptions: GetTokenOptions\n  ): Promise<AccessToken> {\n    if (!cycler.isRefreshing) {\n      // We bind `scopes` here to avoid passing it around a lot\n      const tryGetAccessToken = (): Promise<AccessToken | null> =>\n        credential.getToken(scopes, getTokenOptions);\n\n      // Take advantage of promise chaining to insert an assignment to `token`\n      // before the refresh can be considered done.\n      refreshWorker = beginRefresh(\n        tryGetAccessToken,\n        options.retryIntervalInMs,\n        // If we don't have a token, then we should timeout immediately\n        token?.expiresOnTimestamp ?? Date.now()\n      )\n        .then((_token) => {\n          refreshWorker = null;\n          token = _token;\n          tenantId = getTokenOptions.tenantId;\n          return token;\n        })\n        .catch((reason) => {\n          // We also should reset the refresher if we enter a failed state.  All\n          // existing awaiters will throw, but subsequent requests will start a\n          // new retry chain.\n          refreshWorker = null;\n          token = null;\n          tenantId = undefined;\n          throw reason;\n        });\n    }\n\n    return refreshWorker as Promise<AccessToken>;\n  }\n\n  return async (scopes: string | string[], tokenOptions: GetTokenOptions): Promise<AccessToken> => {\n    //\n    // Simple rules:\n    // - If we MUST refresh, then return the refresh task, blocking\n    //   the pipeline until a token is available.\n    // - If we SHOULD refresh, then run refresh but don't return it\n    //   (we can still use the cached token).\n    // - Return the token, since it's fine if we didn't return in\n    //   step 1.\n    //\n\n    // If the tenantId passed in token options is different to the one we have\n    // Or if we are in claim challenge and the token was rejected and a new access token need to be issued, we need to\n    // refresh the token with the new tenantId or token.\n    const mustRefresh =\n      tenantId !== tokenOptions.tenantId || Boolean(tokenOptions.claims) || cycler.mustRefresh;\n\n    if (mustRefresh) return refresh(scopes, tokenOptions);\n\n    if (cycler.shouldRefresh) {\n      refresh(scopes, tokenOptions);\n    }\n\n    return token as AccessToken;\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport { AzureLogger } from \"@azure/logger\";\nimport { PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces\";\nimport { PipelinePolicy } from \"../pipeline\";\nimport { createTokenCycler } from \"../util/tokenCycler\";\nimport { logger as coreLogger } from \"../log\";\n\n/**\n * The programmatic identifier of the bearerTokenAuthenticationPolicy.\n */\nexport const bearerTokenAuthenticationPolicyName = \"bearerTokenAuthenticationPolicy\";\n\n/**\n * Options sent to the authorizeRequest callback\n */\nexport interface AuthorizeRequestOptions {\n  /**\n   * The scopes for which the bearer token applies.\n   */\n  scopes: string[];\n  /**\n   * Function that retrieves either a cached access token or a new access token.\n   */\n  getAccessToken: (scopes: string[], options: GetTokenOptions) => Promise<AccessToken | null>;\n  /**\n   * Request that the policy is trying to fulfill.\n   */\n  request: PipelineRequest;\n  /**\n   * A logger, if one was sent through the HTTP pipeline.\n   */\n  logger?: AzureLogger;\n}\n\n/**\n * Options sent to the authorizeRequestOnChallenge callback\n */\nexport interface AuthorizeRequestOnChallengeOptions {\n  /**\n   * The scopes for which the bearer token applies.\n   */\n  scopes: string[];\n  /**\n   * Function that retrieves either a cached access token or a new access token.\n   */\n  getAccessToken: (scopes: string[], options: GetTokenOptions) => Promise<AccessToken | null>;\n  /**\n   * Request that the policy is trying to fulfill.\n   */\n  request: PipelineRequest;\n  /**\n   * Response containing the challenge.\n   */\n  response: PipelineResponse;\n  /**\n   * A logger, if one was sent through the HTTP pipeline.\n   */\n  logger?: AzureLogger;\n}\n\n/**\n * Options to override the processing of [Continuous Access Evaluation](https://docs.microsoft.com/azure/active-directory/conditional-access/concept-continuous-access-evaluation) challenges.\n */\nexport interface ChallengeCallbacks {\n  /**\n   * Allows for the authorization of the main request of this policy before it's sent.\n   */\n  authorizeRequest?(options: AuthorizeRequestOptions): Promise<void>;\n  /**\n   * Allows to handle authentication challenges and to re-authorize the request.\n   * The response containing the challenge is `options.response`.\n   * If this method returns true, the underlying request will be sent once again.\n   * The request may be modified before being sent.\n   */\n  authorizeRequestOnChallenge?(options: AuthorizeRequestOnChallengeOptions): Promise<boolean>;\n}\n\n/**\n * Options to configure the bearerTokenAuthenticationPolicy\n */\nexport interface BearerTokenAuthenticationPolicyOptions {\n  /**\n   * The TokenCredential implementation that can supply the bearer token.\n   */\n  credential?: TokenCredential;\n  /**\n   * The scopes for which the bearer token applies.\n   */\n  scopes: string | string[];\n  /**\n   * Allows for the processing of [Continuous Access Evaluation](https://docs.microsoft.com/azure/active-directory/conditional-access/concept-continuous-access-evaluation) challenges.\n   * If provided, it must contain at least the `authorizeRequestOnChallenge` method.\n   * If provided, after a request is sent, if it has a challenge, it can be processed to re-send the original request with the relevant challenge information.\n   */\n  challengeCallbacks?: ChallengeCallbacks;\n  /**\n   * A logger can be sent for debugging purposes.\n   */\n  logger?: AzureLogger;\n}\n\n/**\n * Default authorize request handler\n */\nasync function defaultAuthorizeRequest(options: AuthorizeRequestOptions): Promise<void> {\n  const { scopes, getAccessToken, request } = options;\n  const getTokenOptions: GetTokenOptions = {\n    abortSignal: request.abortSignal,\n    tracingOptions: request.tracingOptions,\n  };\n  const accessToken = await getAccessToken(scopes, getTokenOptions);\n\n  if (accessToken) {\n    options.request.headers.set(\"Authorization\", `Bearer ${accessToken.token}`);\n  }\n}\n\n/**\n * We will retrieve the challenge only if the response status code was 401,\n * and if the response contained the header \"WWW-Authenticate\" with a non-empty value.\n */\nfunction getChallenge(response: PipelineResponse): string | undefined {\n  const challenge = response.headers.get(\"WWW-Authenticate\");\n  if (response.status === 401 && challenge) {\n    return challenge;\n  }\n  return;\n}\n\n/**\n * A policy that can request a token from a TokenCredential implementation and\n * then apply it to the Authorization header of a request as a Bearer token.\n */\nexport function bearerTokenAuthenticationPolicy(\n  options: BearerTokenAuthenticationPolicyOptions\n): PipelinePolicy {\n  const { credential, scopes, challengeCallbacks } = options;\n  const logger = options.logger || coreLogger;\n  const callbacks = {\n    authorizeRequest: challengeCallbacks?.authorizeRequest ?? defaultAuthorizeRequest,\n    authorizeRequestOnChallenge: challengeCallbacks?.authorizeRequestOnChallenge,\n    // keep all other properties\n    ...challengeCallbacks,\n  };\n\n  // This function encapsulates the entire process of reliably retrieving the token\n  // The options are left out of the public API until there's demand to configure this.\n  // Remember to extend `BearerTokenAuthenticationPolicyOptions` with `TokenCyclerOptions`\n  // in order to pass through the `options` object.\n  const getAccessToken = credential\n    ? createTokenCycler(credential /* , options */)\n    : () => Promise.resolve(null);\n\n  return {\n    name: bearerTokenAuthenticationPolicyName,\n    /**\n     * If there's no challenge parameter:\n     * - It will try to retrieve the token using the cache, or the credential's getToken.\n     * - Then it will try the next policy with or without the retrieved token.\n     *\n     * It uses the challenge parameters to:\n     * - Skip a first attempt to get the token from the credential if there's no cached token,\n     *   since it expects the token to be retrievable only after the challenge.\n     * - Prepare the outgoing request if the `prepareRequest` method has been provided.\n     * - Send an initial request to receive the challenge if it fails.\n     * - Process a challenge if the response contains it.\n     * - Retrieve a token with the challenge information, then re-send the request.\n     */\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      if (!request.url.toLowerCase().startsWith(\"https://\")) {\n        throw new Error(\n          \"Bearer token authentication is not permitted for non-TLS protected (non-https) URLs.\"\n        );\n      }\n\n      await callbacks.authorizeRequest({\n        scopes: Array.isArray(scopes) ? scopes : [scopes],\n        request,\n        getAccessToken,\n        logger,\n      });\n\n      let response: PipelineResponse;\n      let error: Error | undefined;\n      try {\n        response = await next(request);\n      } catch (err: any) {\n        error = err;\n        response = err.response;\n      }\n\n      if (\n        callbacks.authorizeRequestOnChallenge &&\n        response?.status === 401 &&\n        getChallenge(response)\n      ) {\n        // processes challenge\n        const shouldSendRequest = await callbacks.authorizeRequestOnChallenge({\n          scopes: Array.isArray(scopes) ? scopes : [scopes],\n          request,\n          response,\n          getAccessToken,\n          logger,\n        });\n\n        if (shouldSendRequest) {\n          return next(request);\n        }\n      }\n\n      if (error) {\n        throw error;\n      } else {\n        return response;\n      }\n    },\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces\";\nimport { PipelinePolicy } from \"../pipeline\";\n\n/**\n * The programmatic identifier of the ndJsonPolicy.\n */\nexport const ndJsonPolicyName = \"ndJsonPolicy\";\n\n/**\n * ndJsonPolicy is a policy used to control keep alive settings for every request.\n */\nexport function ndJsonPolicy(): PipelinePolicy {\n  return {\n    name: ndJsonPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      // There currently isn't a good way to bypass the serializer\n      if (typeof request.body === \"string\" && request.body.startsWith(\"[\")) {\n        const body = JSON.parse(request.body);\n        if (Array.isArray(body)) {\n          request.body = body.map((item) => JSON.stringify(item) + \"\\n\").join(\"\");\n        }\n      }\n      return next(request);\n    },\n  };\n}\n"], "names": ["createClientLogger", "isObject", "logger", "core<PERSON>ogger", "os", "AbortError", "getRandomIntegerInclusive", "FormData", "HttpProxyAgent", "HttpsProxyAgent", "inspect", "isError", "createTracingClient", "getErrorMessage", "isNode", "Transform", "abortController", "AbortController", "http", "https", "zlib", "randomUUID"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AAeA,MAAM,eAAe,GAAG,IAAI,GAAG,CAAgB,CAAC,aAAa,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;AAiG9F;;;;AAIG;AACH,MAAM,YAAY,CAAA;AAIhB,IAAA,WAAA,CAAoB,QAA+B,EAAA;;QAH3C,IAAS,CAAA,SAAA,GAAyB,EAAE,CAAC;AAI3C,QAAA,IAAI,CAAC,SAAS,GAAG,CAAA,EAAA,GAAA,QAAQ,aAAR,QAAQ,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAR,QAAQ,CAAE,KAAK,CAAC,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,EAAE,CAAC;AAC1C,QAAA,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;KACnC;AAEM,IAAA,SAAS,CAAC,MAAsB,EAAE,OAAA,GAA4B,EAAE,EAAA;AACrE,QAAA,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,UAAU,EAAE;AACvC,YAAA,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,CAAC,KAAK,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACxD,MAAM,IAAI,KAAK,CAAC,CAAA,oBAAA,EAAuB,OAAO,CAAC,KAAK,CAAE,CAAA,CAAC,CAAC;AACzD,SAAA;AACD,QAAA,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YAClE,MAAM,IAAI,KAAK,CAAC,CAAA,yBAAA,EAA4B,OAAO,CAAC,UAAU,CAAE,CAAA,CAAC,CAAC;AACnE,SAAA;AACD,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAClB,MAAM;YACN,OAAO;AACR,SAAA,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;KACnC;AAEM,IAAA,YAAY,CAAC,OAA0C,EAAA;QAC5D,MAAM,eAAe,GAAqB,EAAE,CAAC;AAE7C,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,gBAAgB,KAAI;AAC1D,YAAA,IACE,CAAC,OAAO,CAAC,IAAI,IAAI,gBAAgB,CAAC,MAAM,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI;AAC9D,iBAAC,OAAO,CAAC,KAAK,IAAI,gBAAgB,CAAC,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,CAAC,EACnE;AACA,gBAAA,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;AAC9C,gBAAA,OAAO,KAAK,CAAC;AACd,aAAA;AAAM,iBAAA;AACL,gBAAA,OAAO,IAAI,CAAC;AACb,aAAA;AACH,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;AAElC,QAAA,OAAO,eAAe,CAAC;KACxB;IAEM,WAAW,CAAC,UAAsB,EAAE,OAAwB,EAAA;AACjE,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE3C,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,CACnC,CAAC,IAAI,EAAE,MAAM,KAAI;YACf,OAAO,CAAC,GAAoB,KAAI;gBAC9B,OAAO,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACvC,aAAC,CAAC;AACJ,SAAC,EACD,CAAC,GAAoB,KAAK,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CACtD,CAAC;AAEF,QAAA,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAC;KAC1B;IAEM,kBAAkB,GAAA;AACvB,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AAC1B,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;AAC9C,SAAA;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAC9B;IAEM,KAAK,GAAA;AACV,QAAA,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACzC;AAEM,IAAA,OAAO,MAAM,GAAA;QAClB,OAAO,IAAI,YAAY,EAAE,CAAC;KAC3B;IAEO,aAAa,GAAA;AACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCG;QACH,MAAM,MAAM,GAAqB,EAAE,CAAC;;AAGpC,QAAA,MAAM,SAAS,GAAiC,IAAI,GAAG,EAA2B,CAAC;QAEnF,SAAS,WAAW,CAAC,IAA4B,EAAA;YAC/C,OAAO;gBACL,IAAI;gBACJ,QAAQ,EAAE,IAAI,GAAG,EAAmB;AACpC,gBAAA,MAAM,EAAE,KAAK;AACb,gBAAA,gBAAgB,EAAE,KAAK;aACxB,CAAC;SACH;;AAGD,QAAA,MAAM,cAAc,GAAG,WAAW,CAAC,WAAW,CAAC,CAAC;AAChD,QAAA,MAAM,OAAO,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;AACpC,QAAA,MAAM,gBAAgB,GAAG,WAAW,CAAC,aAAa,CAAC,CAAC;AACpD,QAAA,MAAM,UAAU,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;AACxC,QAAA,MAAM,SAAS,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;;AAGtC,QAAA,MAAM,aAAa,GAAG,CAAC,cAAc,EAAE,OAAO,EAAE,gBAAgB,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;;QAGzF,SAAS,QAAQ,CAAC,KAAgC,EAAA;YAChD,IAAI,KAAK,KAAK,OAAO,EAAE;AACrB,gBAAA,OAAO,UAAU,CAAC;AACnB,aAAA;iBAAM,IAAI,KAAK,KAAK,WAAW,EAAE;AAChC,gBAAA,OAAO,cAAc,CAAC;AACvB,aAAA;iBAAM,IAAI,KAAK,KAAK,aAAa,EAAE;AAClC,gBAAA,OAAO,gBAAgB,CAAC;AACzB,aAAA;iBAAM,IAAI,KAAK,KAAK,MAAM,EAAE;AAC3B,gBAAA,OAAO,SAAS,CAAC;AAClB,aAAA;AAAM,iBAAA;AACL,gBAAA,OAAO,OAAO,CAAC;AAChB,aAAA;SACF;;AAGD,QAAA,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,SAAS,EAAE;AACvC,YAAA,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;AACjC,YAAA,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;AACnC,YAAA,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC;AAC/B,YAAA,IAAI,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;AAC7B,gBAAA,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;AACnE,aAAA;AACD,YAAA,MAAM,IAAI,GAAoB;gBAC5B,MAAM;gBACN,SAAS,EAAE,IAAI,GAAG,EAAmB;gBACrC,UAAU,EAAE,IAAI,GAAG,EAAmB;aACvC,CAAC;YACF,IAAI,OAAO,CAAC,UAAU,EAAE;gBACtB,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAC/C,gBAAA,IAAI,CAAC,UAAU,CAAC,gBAAgB,GAAG,IAAI,CAAC;AACzC,aAAA;AACD,YAAA,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YAChC,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACtC,YAAA,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC1B,SAAA;;AAGD,QAAA,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,SAAS,EAAE;AACvC,YAAA,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,UAAU,CAAC;AACvC,YAAA,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC;YAC/B,MAAM,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACvC,IAAI,CAAC,IAAI,EAAE;AACT,gBAAA,MAAM,IAAI,KAAK,CAAC,2BAA2B,UAAU,CAAA,CAAE,CAAC,CAAC;AAC1D,aAAA;YAED,IAAI,OAAO,CAAC,aAAa,EAAE;AACzB,gBAAA,KAAK,MAAM,eAAe,IAAI,OAAO,CAAC,aAAa,EAAE;oBACnD,MAAM,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;AACjD,oBAAA,IAAI,SAAS,EAAE;;;AAGb,wBAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC9B,wBAAA,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAChC,qBAAA;AACF,iBAAA;AACF,aAAA;YACD,IAAI,OAAO,CAAC,cAAc,EAAE;AAC1B,gBAAA,KAAK,MAAM,gBAAgB,IAAI,OAAO,CAAC,cAAc,EAAE;oBACrD,MAAM,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;AACnD,oBAAA,IAAI,UAAU,EAAE;;;AAGd,wBAAA,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC/B,wBAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACjC,qBAAA;AACF,iBAAA;AACF,aAAA;AACF,SAAA;QAED,SAAS,SAAS,CAAC,KAAY,EAAA;AAC7B,YAAA,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;;AAEpB,YAAA,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;gBACjC,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;;;;;oBAKjF,SAAS;AACV,iBAAA;AACD,gBAAA,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,EAAE;;;AAG7B,oBAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;;AAGzB,oBAAA,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE;AACvC,wBAAA,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAClC,qBAAA;oBACD,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACnC,oBAAA,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7B,iBAAA;AACF,aAAA;SACF;AAED,QAAA,SAAS,UAAU,GAAA;AACjB,YAAA,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE;gBACjC,SAAS,CAAC,KAAK,CAAC,CAAC;;gBAEjB,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,KAAK,OAAO,EAAE;AAChD,oBAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;;;;wBAInB,SAAS,CAAC,OAAO,CAAC,CAAC;AACpB,qBAAA;;oBAED,OAAO;AACR,iBAAA;gBAED,IAAI,KAAK,CAAC,gBAAgB,EAAE;;oBAE1B,SAAS,CAAC,OAAO,CAAC,CAAC;AACpB,iBAAA;AACF,aAAA;SACF;;QAGD,IAAI,SAAS,GAAG,CAAC,CAAC;AAClB,QAAA,OAAO,SAAS,CAAC,IAAI,GAAG,CAAC,EAAE;AACzB,YAAA,SAAS,EAAE,CAAC;AACZ,YAAA,MAAM,mBAAmB,GAAG,MAAM,CAAC,MAAM,CAAC;;AAE1C,YAAA,UAAU,EAAE,CAAC;;;;YAIb,IAAI,MAAM,CAAC,MAAM,IAAI,mBAAmB,IAAI,SAAS,GAAG,CAAC,EAAE;AACzD,gBAAA,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;AAClF,aAAA;AACF,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACf;AACF,CAAA;AAED;;;AAGG;SACa,mBAAmB,GAAA;AACjC,IAAA,OAAO,YAAY,CAAC,MAAM,EAAE,CAAC;AAC/B;;AC5YA;AAIO,MAAM,MAAM,GAAGA,2BAAkB,CAAC,oBAAoB,CAAC;;ACJ9D;AAwBA,MAAM,cAAc,GAAG,UAAU,CAAC;AAElC;AACA,MAAM,yBAAyB,GAAG;IAChC,wBAAwB;IACxB,+BAA+B;IAC/B,gBAAgB;IAChB,6BAA6B;IAC7B,iBAAiB;IACjB,mBAAmB;IACnB,OAAO;IACP,0BAA0B;IAC1B,aAAa;IAEb,kCAAkC;IAClC,8BAA8B;IAC9B,8BAA8B;IAC9B,6BAA6B;IAC7B,+BAA+B;IAC/B,wBAAwB;IACxB,gCAAgC;IAChC,+BAA+B;IAC/B,QAAQ;IAER,QAAQ;IACR,iBAAiB;IACjB,eAAe;IACf,YAAY;IACZ,gBAAgB;IAChB,cAAc;IACd,MAAM;IACN,MAAM;IACN,SAAS;IACT,UAAU;IACV,mBAAmB;IACnB,eAAe;IACf,qBAAqB;IACrB,eAAe;IACf,QAAQ;IACR,YAAY;IACZ,aAAa;IACb,QAAQ;IACR,mBAAmB;IACnB,YAAY;IACZ,kBAAkB;CACnB,CAAC;AAEF,MAAM,6BAA6B,GAAa,CAAC,aAAa,CAAC,CAAC;AAEhE;;AAEG;MACU,SAAS,CAAA;AAIpB,IAAA,WAAA,CAAY,EACV,4BAA4B,EAAE,kBAAkB,GAAG,EAAE,EACrD,gCAAgC,EAAE,sBAAsB,GAAG,EAAE,MACzC,EAAE,EAAA;AACtB,QAAA,kBAAkB,GAAG,yBAAyB,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;AAC1E,QAAA,sBAAsB,GAAG,6BAA6B,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;QAEtF,IAAI,CAAC,kBAAkB,GAAG,IAAI,GAAG,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAClF,IAAI,CAAC,sBAAsB,GAAG,IAAI,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;KAC3F;AAEM,IAAA,QAAQ,CAAC,GAAY,EAAA;AAC1B,QAAA,MAAM,IAAI,GAAG,IAAI,GAAG,EAAW,CAAC;QAChC,OAAO,IAAI,CAAC,SAAS,CACnB,GAAG,EACH,CAAC,GAAW,EAAE,KAAc,KAAI;;YAE9B,IAAI,KAAK,YAAY,KAAK,EAAE;AAC1B,gBAAA,OAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACK,KAAK,CAAA,EAAA,EACR,IAAI,EAAE,KAAK,CAAC,IAAI,EAChB,OAAO,EAAE,KAAK,CAAC,OAAO,EACtB,CAAA,CAAA;AACH,aAAA;YAED,IAAI,GAAG,KAAK,SAAS,EAAE;AACrB,gBAAA,OAAO,IAAI,CAAC,eAAe,CAAC,KAAsB,CAAC,CAAC;AACrD,aAAA;iBAAM,IAAI,GAAG,KAAK,KAAK,EAAE;AACxB,gBAAA,OAAO,IAAI,CAAC,WAAW,CAAC,KAAe,CAAC,CAAC;AAC1C,aAAA;iBAAM,IAAI,GAAG,KAAK,OAAO,EAAE;AAC1B,gBAAA,OAAO,IAAI,CAAC,aAAa,CAAC,KAAsB,CAAC,CAAC;AACnD,aAAA;iBAAM,IAAI,GAAG,KAAK,MAAM,EAAE;;AAEzB,gBAAA,OAAO,SAAS,CAAC;AAClB,aAAA;iBAAM,IAAI,GAAG,KAAK,UAAU,EAAE;;AAE7B,gBAAA,OAAO,SAAS,CAAC;AAClB,aAAA;iBAAM,IAAI,GAAG,KAAK,eAAe,EAAE;;;AAGlC,gBAAA,OAAO,SAAS,CAAC;AAClB,aAAA;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAIC,iBAAQ,CAAC,KAAK,CAAC,EAAE;AAClD,gBAAA,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AACnB,oBAAA,OAAO,YAAY,CAAC;AACrB,iBAAA;AACD,gBAAA,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACjB,aAAA;AAED,YAAA,OAAO,KAAK,CAAC;SACd,EACD,CAAC,CACF,CAAC;KACH;AAEO,IAAA,eAAe,CAAC,GAAkB,EAAA;QACxC,MAAM,SAAS,GAAkB,EAAE,CAAC;QACpC,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YAClC,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,EAAE;gBAClD,SAAS,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AAC3B,aAAA;AAAM,iBAAA;AACL,gBAAA,SAAS,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC;AACjC,aAAA;AACF,SAAA;AACD,QAAA,OAAO,SAAS,CAAC;KAClB;AAEO,IAAA,aAAa,CAAC,KAAoB,EAAA;QACxC,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE;AAC/C,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;QAED,MAAM,SAAS,GAAkB,EAAE,CAAC;QAEpC,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAClC,IAAI,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,EAAE;gBACpD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACzB,aAAA;AAAM,iBAAA;AACL,gBAAA,SAAS,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC;AAC/B,aAAA;AACF,SAAA;AAED,QAAA,OAAO,SAAS,CAAC;KAClB;AAEO,IAAA,WAAW,CAAC,KAAa,EAAA;QAC/B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE;AAC/C,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AAED,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;AAE3B,QAAA,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;AACf,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;QAED,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,YAAY,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,EAAE;gBACvD,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;AAC3C,aAAA;AACF,SAAA;AAED,QAAA,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;KACvB;AACF;;ACvLD;AASA;;AAEG;AACI,MAAM,aAAa,GAAG,YAAY;AA4BzC;;;AAGG;AACa,SAAA,SAAS,CAAC,OAAA,GAA4B,EAAE,EAAA;;IACtD,MAAMC,QAAM,GAAG,CAAA,EAAA,GAAA,OAAO,CAAC,MAAM,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAAC,MAAU,CAAC,IAAI,CAAC;AACjD,IAAA,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC;QAC9B,4BAA4B,EAAE,OAAO,CAAC,4BAA4B;QAClE,gCAAgC,EAAE,OAAO,CAAC,gCAAgC;AAC3E,KAAA,CAAC,CAAC;IACH,OAAO;AACL,QAAA,IAAI,EAAE,aAAa;AACnB,QAAA,MAAM,WAAW,CAAC,OAAwB,EAAE,IAAiB,EAAA;AAC3D,YAAA,IAAI,CAACD,QAAM,CAAC,OAAO,EAAE;AACnB,gBAAA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;AACtB,aAAA;YAEDA,QAAM,CAAC,CAAY,SAAA,EAAA,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAE,CAAA,CAAC,CAAC;AAElD,YAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;AAErC,YAAAA,QAAM,CAAC,CAAyB,sBAAA,EAAA,QAAQ,CAAC,MAAM,CAAA,CAAE,CAAC,CAAC;AACnD,YAAAA,QAAM,CAAC,CAAA,SAAA,EAAY,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAE,CAAA,CAAC,CAAC;AAE3D,YAAA,OAAO,QAAQ,CAAC;SACjB;KACF,CAAC;AACJ;;ACnEA;AACA;AAKA;;AAEG;AACI,MAAM,kBAAkB,GAAG,iBAAiB;AAEnD;;AAEG;AACH,MAAM,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAaxC;;;;;AAKG;AACa,SAAA,cAAc,CAAC,OAAA,GAAiC,EAAE,EAAA;AAChE,IAAA,MAAM,EAAE,UAAU,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;IACpC,OAAO;AACL,QAAA,IAAI,EAAE,kBAAkB;AACxB,QAAA,MAAM,WAAW,CAAC,OAAwB,EAAE,IAAiB,EAAA;AAC3D,YAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;YACrC,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;SACnD;KACF,CAAC;AACJ,CAAC;AAED,eAAe,cAAc,CAC3B,IAAiB,EACjB,QAA0B,EAC1B,UAAkB,EAClB,cAAA,GAAyB,CAAC,EAAA;IAE1B,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,QAAQ,CAAC;IAC9C,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AAC/C,IAAA,IACE,cAAc;SACb,MAAM,KAAK,GAAG;AACb,aAAC,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC5D,aAAC,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAC3D,MAAM,KAAK,GAAG,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,CAAC;YAC7C,MAAM,KAAK,GAAG,CAAC;QACjB,cAAc,GAAG,UAAU,EAC3B;QACA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;AACjD,QAAA,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;;;QAI7B,IAAI,MAAM,KAAK,GAAG,EAAE;AAClB,YAAA,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC;AACvB,YAAA,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;YACzC,OAAO,OAAO,CAAC,IAAI,CAAC;AACrB,SAAA;AAED,QAAA,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;AAExC,QAAA,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;AAChC,QAAA,OAAO,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC;AAClE,KAAA;AAED,IAAA,OAAO,QAAQ,CAAC;AAClB;;AC/EA;AAKA;;AAEG;SACa,aAAa,GAAA;AAC3B,IAAA,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;;AAEG;AACG,SAAU,uBAAuB,CAAC,GAAwB,EAAA;IAC9D,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IACjC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAI,CAAA,EAAAE,aAAE,CAAC,IAAI,EAAE,CAAA,CAAA,EAAIA,aAAE,CAAC,IAAI,EAAE,CAAI,CAAA,EAAAA,aAAE,CAAC,OAAO,EAAE,CAAG,CAAA,CAAA,CAAC,CAAC;AAC/D;;AClBA;AACA;AAEO,MAAM,WAAW,GAAW,QAAQ,CAAC;AAErC,MAAM,0BAA0B,GAAG,CAAC;;ACL3C;AAMA,SAAS,kBAAkB,CAAC,aAAkC,EAAA;IAC5D,MAAM,KAAK,GAAa,EAAE,CAAC;IAC3B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,aAAa,EAAE;AACxC,QAAA,MAAM,KAAK,GAAG,KAAK,GAAG,CAAA,EAAG,GAAG,CAAA,CAAA,EAAI,KAAK,CAAE,CAAA,GAAG,GAAG,CAAC;AAC9C,QAAA,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACnB,KAAA;AACD,IAAA,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACzB,CAAC;AAED;;AAEG;SACa,sBAAsB,GAAA;IACpC,OAAO,aAAa,EAAE,CAAC;AACzB,CAAC;AAED;;AAEG;AACG,SAAU,iBAAiB,CAAC,MAAe,EAAA;AAC/C,IAAA,MAAM,WAAW,GAAG,IAAI,GAAG,EAAkB,CAAC;AAC9C,IAAA,WAAW,CAAC,GAAG,CAAC,oBAAoB,EAAE,WAAW,CAAC,CAAC;IACnD,uBAAuB,CAAC,WAAW,CAAC,CAAC;AACrC,IAAA,MAAM,YAAY,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAC;AACrD,IAAA,MAAM,cAAc,GAAG,MAAM,GAAG,CAAA,EAAG,MAAM,CAAA,CAAA,EAAI,YAAY,CAAE,CAAA,GAAG,YAAY,CAAC;AAC3E,IAAA,OAAO,cAAc,CAAC;AACxB;;AChCA;AAOA,MAAM,mBAAmB,GAAG,sBAAsB,EAAE,CAAC;AAErD;;AAEG;AACI,MAAM,mBAAmB,GAAG,kBAAkB;AAarD;;;;AAIG;AACa,SAAA,eAAe,CAAC,OAAA,GAAkC,EAAE,EAAA;IAClE,MAAM,cAAc,GAAG,iBAAiB,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IAClE,OAAO;AACL,QAAA,IAAI,EAAE,mBAAmB;AACzB,QAAA,MAAM,WAAW,CAAC,OAAwB,EAAE,IAAiB,EAAA;YAC3D,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE;gBAC7C,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,cAAc,CAAC,CAAC;AAC1D,aAAA;AACD,YAAA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;SACtB;KACF,CAAC;AACJ;;ACzCA;AACA;AAKA;;AAEG;AACI,MAAM,4BAA4B,GAAG,2BAA2B;AAEvE;;;AAGG;SACa,wBAAwB,GAAA;IACtC,OAAO;AACL,QAAA,IAAI,EAAE,4BAA4B;AAClC,QAAA,MAAM,WAAW,CAAC,OAAwB,EAAE,IAAiB,EAAA;;AAE3D,YAAA,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE;gBAC7B,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;AACxD,aAAA;AACD,YAAA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;SACtB;KACF,CAAC;AACJ;;AC1BA;AAMA,MAAM,oBAAoB,GAAG,4BAA4B,CAAC;AAE1D;;;;;;;;AAQG;SACa,KAAK,CACnB,SAAiB,EACjB,KAAS,EACT,OAGC,EAAA;IAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;QACrC,IAAI,KAAK,GAA8C,SAAS,CAAC;QACjE,IAAI,SAAS,GAA6B,SAAS,CAAC;QAEpD,MAAM,aAAa,GAAG,MAAW;AAC/B,YAAA,OAAO,MAAM,CACX,IAAIC,0BAAU,CAAC,CAAA,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,aAAa,IAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,aAAa,GAAG,oBAAoB,CAAC,CACvF,CAAC;AACJ,SAAC,CAAC;QAEF,MAAM,eAAe,GAAG,MAAW;YACjC,IAAI,CAAA,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,WAAW,KAAI,SAAS,EAAE;gBACrC,OAAO,CAAC,WAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AAC7D,aAAA;AACH,SAAC,CAAC;QAEF,SAAS,GAAG,MAAW;AACrB,YAAA,IAAI,KAAK,EAAE;gBACT,YAAY,CAAC,KAAK,CAAC,CAAC;AACrB,aAAA;AACD,YAAA,eAAe,EAAE,CAAC;YAClB,OAAO,aAAa,EAAE,CAAC;AACzB,SAAC,CAAC;AAEF,QAAA,IAAI,CAAA,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,WAAW,KAAI,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE;YACvD,OAAO,aAAa,EAAE,CAAC;AACxB,SAAA;AAED,QAAA,KAAK,GAAG,UAAU,CAAC,MAAK;AACtB,YAAA,eAAe,EAAE,CAAC;YAClB,OAAO,CAAC,KAAK,CAAC,CAAC;SAChB,EAAE,SAAS,CAAC,CAAC;AAEd,QAAA,IAAI,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,WAAW,EAAE;YACxB,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AAC1D,SAAA;AACH,KAAC,CAAC,CAAC;AACL,CAAC;AAED;;;AAGG;AACa,SAAA,wBAAwB,CACtC,QAA0B,EAC1B,UAAkB,EAAA;IAElB,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AAC/C,IAAA,IAAI,CAAC,KAAK;QAAE,OAAO;AACnB,IAAA,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AACjC,IAAA,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC;QAAE,OAAO;AACrC,IAAA,OAAO,UAAU,CAAC;AACpB;;AC7EA;AAOA;;;AAGG;AACH,MAAM,gBAAgB,GAAG,aAAa,CAAC;AACvC;;;;;;AAMG;AACH,MAAM,oBAAoB,GAAa,CAAC,gBAAgB,EAAE,qBAAqB,EAAE,gBAAgB,CAAC,CAAC;AAEnG;;;;;;;;AAQG;AACH,SAAS,iBAAiB,CAAC,QAA2B,EAAA;AACpD,IAAA,IAAI,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAAE,QAAA,OAAO,SAAS,CAAC;IAC1E,IAAI;;AAEF,QAAA,KAAK,MAAM,MAAM,IAAI,oBAAoB,EAAE;YACzC,MAAM,eAAe,GAAG,wBAAwB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AACnE,YAAA,IAAI,eAAe,KAAK,CAAC,IAAI,eAAe,EAAE;;;AAG5C,gBAAA,MAAM,iBAAiB,GAAG,MAAM,KAAK,gBAAgB,GAAG,IAAI,GAAG,CAAC,CAAC;AACjE,gBAAA,OAAO,eAAe,GAAG,iBAAiB,CAAC;AAC5C,aAAA;AACF,SAAA;;QAGD,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;AAChE,QAAA,IAAI,CAAC,gBAAgB;YAAE,OAAO;QAE9B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QAC1C,MAAM,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;;QAE/B,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,SAAS,CAAC;AAC9D,KAAA;AAAC,IAAA,OAAO,CAAM,EAAE;AACf,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;AACH,CAAC;AAED;;;AAGG;AACG,SAAU,yBAAyB,CAAC,QAA2B,EAAA;IACnE,OAAO,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC;AACtD,CAAC;SAEe,uBAAuB,GAAA;IACrC,OAAO;AACL,QAAA,IAAI,EAAE,yBAAyB;QAC/B,KAAK,CAAC,EAAE,QAAQ,EAAE,EAAA;AAChB,YAAA,MAAM,cAAc,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC;AACnD,YAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE;AACpC,gBAAA,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;AAC/B,aAAA;YACD,OAAO;gBACL,cAAc;aACf,CAAC;SACH;KACF,CAAC;AACJ;;AC9EA;AASA;AACA,MAAM,6BAA6B,GAAG,IAAI,CAAC;AAC3C,MAAM,iCAAiC,GAAG,IAAI,GAAG,EAAE,CAAC;AAEpD;;;;AAIG;AACa,SAAA,wBAAwB,CACtC,OAAA,GAuBI,EAAE,EAAA;;IAEN,MAAM,aAAa,GAAG,CAAA,EAAA,GAAA,OAAO,CAAC,cAAc,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,6BAA6B,CAAC;IAC9E,MAAM,gBAAgB,GAAG,CAAA,EAAA,GAAA,OAAO,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,iCAAiC,CAAC;IAExF,IAAI,cAAc,GAAG,aAAa,CAAC;IAEnC,OAAO;AACL,QAAA,IAAI,EAAE,0BAA0B;AAChC,QAAA,KAAK,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,aAAa,EAAE,EAAA;AAC3C,YAAA,MAAM,kBAAkB,GAAG,aAAa,CAAC,aAAa,CAAC,CAAC;AACxD,YAAA,MAAM,kBAAkB,GAAG,kBAAkB,IAAI,OAAO,CAAC,kBAAkB,CAAC;AAE5E,YAAA,MAAM,aAAa,GAAG,0BAA0B,CAAC,QAAQ,CAAC,CAAC;AAC3D,YAAA,MAAM,yBAAyB,GAAG,aAAa,IAAI,OAAO,CAAC,qBAAqB,CAAC;AACjF,YAAA,MAAM,eAAe,GAAG,QAAQ,KAAK,yBAAyB,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAE5F,YAAA,IAAI,eAAe,IAAI,yBAAyB,IAAI,kBAAkB,EAAE;AACtE,gBAAA,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;AAC/B,aAAA;AAED,YAAA,IAAI,aAAa,IAAI,CAAC,kBAAkB,IAAI,CAAC,aAAa,EAAE;AAC1D,gBAAA,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,CAAC;AACxC,aAAA;;AAGD,YAAA,MAAM,gBAAgB,GAAG,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;;YAElE,MAAM,uBAAuB,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;;;YAG7E,cAAc;gBACZ,uBAAuB,GAAG,CAAC,GAAGC,kCAAyB,CAAC,CAAC,EAAE,uBAAuB,GAAG,CAAC,CAAC,CAAC;YAC1F,OAAO,EAAE,cAAc,EAAE,CAAC;SAC3B;KACF,CAAC;AACJ,CAAC;AAED;;;;AAIG;AACG,SAAU,0BAA0B,CAAC,QAA2B,EAAA;IACpE,OAAO,OAAO,CACZ,QAAQ;QACN,QAAQ,CAAC,MAAM,KAAK,SAAS;SAC5B,QAAQ,CAAC,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAC;QACnD,QAAQ,CAAC,MAAM,KAAK,GAAG;AACvB,QAAA,QAAQ,CAAC,MAAM,KAAK,GAAG,CAC1B,CAAC;AACJ,CAAC;AAED;;AAEG;AACG,SAAU,aAAa,CAAC,GAAe,EAAA;IAC3C,IAAI,CAAC,GAAG,EAAE;AACR,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AACD,IAAA,QACE,GAAG,CAAC,IAAI,KAAK,WAAW;QACxB,GAAG,CAAC,IAAI,KAAK,iBAAiB;QAC9B,GAAG,CAAC,IAAI,KAAK,cAAc;QAC3B,GAAG,CAAC,IAAI,KAAK,YAAY;AACzB,QAAA,GAAG,CAAC,IAAI,KAAK,QAAQ,EACrB;AACJ;;AC7GA;AAaA,MAAM,iBAAiB,GAAGN,2BAAkB,CAAC,gCAAgC,CAAC,CAAC;AAE/E;;AAEG;AACH,MAAM,eAAe,GAAG,aAAa,CAAC;AAgBtC;;AAEG;AACG,SAAU,WAAW,CACzB,UAA2B,EAC3B,UAA8B,EAAE,UAAU,EAAE,0BAA0B,EAAE,EAAA;AAExE,IAAA,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,iBAAiB,CAAC;IACnD,OAAO;AACL,QAAA,IAAI,EAAE,eAAe;AACrB,QAAA,MAAM,WAAW,CAAC,OAAwB,EAAE,IAAiB,EAAA;;AAC3D,YAAA,IAAI,QAAsC,CAAC;AAC3C,YAAA,IAAI,aAAoC,CAAC;AACzC,YAAA,IAAI,UAAU,GAAG,CAAC,CAAC,CAAC;;YAGpB,YAAY,EAAE,OAAO,IAAI,EAAE;gBACzB,UAAU,IAAI,CAAC,CAAC;gBAChB,QAAQ,GAAG,SAAS,CAAC;gBACrB,aAAa,GAAG,SAAS,CAAC;gBAE1B,IAAI;oBACF,MAAM,CAAC,IAAI,CAAC,CAAS,MAAA,EAAA,UAAU,CAA8B,4BAAA,CAAA,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AAClF,oBAAA,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC/B,MAAM,CAAC,IAAI,CAAC,CAAS,MAAA,EAAA,UAAU,CAAoC,kCAAA,CAAA,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AACzF,iBAAA;AAAC,gBAAA,OAAO,CAAM,EAAE;oBACf,MAAM,CAAC,KAAK,CAAC,CAAS,MAAA,EAAA,UAAU,CAAkC,gCAAA,CAAA,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;;;;oBAKvF,aAAa,GAAG,CAAc,CAAC;oBAC/B,IAAI,CAAC,CAAC,IAAI,aAAa,CAAC,IAAI,KAAK,WAAW,EAAE;AAC5C,wBAAA,MAAM,CAAC,CAAC;AACT,qBAAA;AAED,oBAAA,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;AACnC,iBAAA;AAED,gBAAA,IAAI,MAAA,OAAO,CAAC,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,EAAE;AAChC,oBAAA,MAAM,CAAC,KAAK,CAAC,SAAS,UAAU,CAAA,kBAAA,CAAoB,CAAC,CAAC;AACtD,oBAAA,MAAM,UAAU,GAAG,IAAIK,0BAAU,EAAE,CAAC;AACpC,oBAAA,MAAM,UAAU,CAAC;AAClB,iBAAA;gBAED,IAAI,UAAU,KAAK,CAAA,EAAA,GAAA,OAAO,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,0BAA0B,CAAC,EAAE;AACpE,oBAAA,MAAM,CAAC,IAAI,CACT,SAAS,UAAU,CAAA,qGAAA,CAAuG,CAC3H,CAAC;AACF,oBAAA,IAAI,aAAa,EAAE;AACjB,wBAAA,MAAM,aAAa,CAAC;AACrB,qBAAA;AAAM,yBAAA,IAAI,QAAQ,EAAE;AACnB,wBAAA,OAAO,QAAQ,CAAC;AACjB,qBAAA;AAAM,yBAAA;AACL,wBAAA,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;AAC/E,qBAAA;AACF,iBAAA;gBAED,MAAM,CAAC,IAAI,CAAC,CAAS,MAAA,EAAA,UAAU,CAAgB,aAAA,EAAA,UAAU,CAAC,MAAM,CAAoB,kBAAA,CAAA,CAAC,CAAC;AAEtF,gBAAA,cAAc,EAAE,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE;AACjD,oBAAA,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,IAAI,iBAAiB,CAAC;oBAC5D,cAAc,CAAC,IAAI,CAAC,CAAS,MAAA,EAAA,UAAU,CAA+B,4BAAA,EAAA,QAAQ,CAAC,IAAI,CAAG,CAAA,CAAA,CAAC,CAAC;AAExF,oBAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC;wBAC/B,UAAU;wBACV,QAAQ;wBACR,aAAa;AACd,qBAAA,CAAC,CAAC;oBAEH,IAAI,SAAS,CAAC,YAAY,EAAE;AAC1B,wBAAA,cAAc,CAAC,IAAI,CAAC,SAAS,UAAU,CAAA,UAAA,CAAY,CAAC,CAAC;AACrD,wBAAA,SAAS,cAAc,CAAC;AACzB,qBAAA;oBAED,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,SAAS,CAAC;AAE/D,oBAAA,IAAI,YAAY,EAAE;AAChB,wBAAA,cAAc,CAAC,KAAK,CAClB,CAAA,MAAA,EAAS,UAAU,CAAA,iBAAA,EAAoB,QAAQ,CAAC,IAAI,CAAA,cAAA,CAAgB,EACpE,YAAY,CACb,CAAC;AACF,wBAAA,MAAM,YAAY,CAAC;AACpB,qBAAA;AAED,oBAAA,IAAI,cAAc,IAAI,cAAc,KAAK,CAAC,EAAE;AAC1C,wBAAA,cAAc,CAAC,IAAI,CACjB,CAAA,MAAA,EAAS,UAAU,CAAA,iBAAA,EAAoB,QAAQ,CAAC,IAAI,CAAA,eAAA,EAAkB,cAAc,CAAA,CAAE,CACvF,CAAC;AACF,wBAAA,MAAM,KAAK,CAAC,cAAc,EAAE,SAAS,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;AAC7E,wBAAA,SAAS,YAAY,CAAC;AACvB,qBAAA;AAED,oBAAA,IAAI,UAAU,EAAE;AACd,wBAAA,cAAc,CAAC,IAAI,CACjB,CAAA,MAAA,EAAS,UAAU,CAAA,iBAAA,EAAoB,QAAQ,CAAC,IAAI,CAAA,cAAA,EAAiB,UAAU,CAAA,CAAE,CAClF,CAAC;AACF,wBAAA,OAAO,CAAC,GAAG,GAAG,UAAU,CAAC;AACzB,wBAAA,SAAS,YAAY,CAAC;AACvB,qBAAA;AACF,iBAAA;AAED,gBAAA,IAAI,aAAa,EAAE;AACjB,oBAAA,MAAM,CAAC,IAAI,CACT,CAAA,6EAAA,CAA+E,CAChF,CAAC;AACF,oBAAA,MAAM,aAAa,CAAC;AACrB,iBAAA;AACD,gBAAA,IAAI,QAAQ,EAAE;AACZ,oBAAA,MAAM,CAAC,IAAI,CACT,CAAA,iFAAA,CAAmF,CACpF,CAAC;AACF,oBAAA,OAAO,QAAQ,CAAC;AACjB,iBAAA;;;;AAKF,aAAA;SACF;KACF,CAAC;AACJ;;AC3JA;AAUA;;AAEG;AACI,MAAM,sBAAsB,GAAG,oBAAoB,CAAC;AAO3D;;;;;AAKG;AACa,SAAA,kBAAkB,CAAC,OAAA,GAAqC,EAAE,EAAA;;IACxE,OAAO;AACL,QAAA,IAAI,EAAE,sBAAsB;AAC5B,QAAA,WAAW,EAAE,WAAW,CAAC,CAAC,uBAAuB,EAAE,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC,EAAE;AACvF,YAAA,UAAU,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,UAAU,mCAAI,0BAA0B;AAC7D,SAAA,CAAC,CAAC,WAAW;KACf,CAAC;AACJ;;ACjCA;AAOA;;AAEG;AACI,MAAM,kBAAkB,GAAG,iBAAiB;AAEnD;;AAEG;SACa,cAAc,GAAA;IAC5B,OAAO;AACL,QAAA,IAAI,EAAE,kBAAkB;AACxB,QAAA,MAAM,WAAW,CAAC,OAAwB,EAAE,IAAiB,EAAA;YAC3D,IAAI,OAAO,CAAC,QAAQ,EAAE;gBACpB,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;gBACxD,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,CAAC,mCAAmC,CAAC,KAAK,CAAC,CAAC,EAAE;oBAClF,OAAO,CAAC,IAAI,GAAG,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAClD,oBAAA,OAAO,CAAC,QAAQ,GAAG,SAAS,CAAC;AAC9B,iBAAA;AAAM,qBAAA;oBACL,MAAM,eAAe,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAClD,iBAAA;AACF,aAAA;AACD,YAAA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;SACtB;KACF,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CAAC,QAAqB,EAAA;AAC7C,IAAA,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;AAC9C,IAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AACnD,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACxB,YAAA,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE;gBAC5B,eAAe,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;AAClD,aAAA;AACF,SAAA;AAAM,aAAA;YACL,eAAe,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC/C,SAAA;AACF,KAAA;AACD,IAAA,OAAO,eAAe,CAAC,QAAQ,EAAE,CAAC;AACpC,CAAC;AAED,eAAe,eAAe,CAAC,QAAqB,EAAE,OAAwB,EAAA;AAC5E,IAAA,MAAM,WAAW,GAAG,IAAIE,4BAAQ,EAAE,CAAC;IACnC,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AAC3C,QAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;AACpC,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;AAC5B,YAAA,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;AAChC,gBAAA,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AACvC,aAAA;AACF,SAAA;AAAM,aAAA;AACL,YAAA,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AACxC,SAAA;AACF,KAAA;AAED,IAAA,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC;AAC3B,IAAA,OAAO,CAAC,QAAQ,GAAG,SAAS,CAAC;IAC7B,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IACxD,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,EAAE;AACpE,QAAA,OAAO,CAAC,OAAO,CAAC,GAAG,CACjB,cAAc,EACd,CAAiC,8BAAA,EAAA,WAAW,CAAC,WAAW,EAAE,CAAA,CAAE,CAC7D,CAAC;AACH,KAAA;IACD,IAAI;QACF,MAAM,aAAa,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,KAAI;YAClE,WAAW,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,MAAM,KAAI;AACpC,gBAAA,IAAI,GAAG,EAAE;oBACP,MAAM,CAAC,GAAG,CAAC,CAAC;AACb,iBAAA;AAAM,qBAAA;oBACL,OAAO,CAAC,MAAM,CAAC,CAAC;AACjB,iBAAA;AACH,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;QACH,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;AACtD,KAAA;AAAC,IAAA,OAAO,CAAM,EAAE;;AAEhB,KAAA;AACH;;ACnFA;AAWA,MAAM,WAAW,GAAG,aAAa,CAAC;AAClC,MAAM,UAAU,GAAG,YAAY,CAAC;AAChC,MAAM,SAAS,GAAG,WAAW,CAAC;AAC9B,MAAM,QAAQ,GAAG,UAAU,CAAC;AAE5B;;AAEG;AACI,MAAM,eAAe,GAAG,cAAc;AAE7C;;;AAGG;AACI,MAAM,iBAAiB,GAAa,EAAE,CAAC;AAC9C,IAAI,iBAAiB,GAAY,KAAK,CAAC;AAEvC;AACA,MAAM,iBAAiB,GAAyB,IAAI,GAAG,EAAE,CAAC;AAE1D,SAAS,mBAAmB,CAAC,IAAY,EAAA;AACvC,IAAA,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACrB,QAAA,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC1B,KAAA;SAAM,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE;QAC1C,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AACxC,KAAA;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,yBAAyB,GAAA;IAChC,IAAI,CAAC,OAAO,EAAE;AACZ,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;AAED,IAAA,MAAM,UAAU,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC;AACpD,IAAA,MAAM,QAAQ,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC;AAChD,IAAA,MAAM,SAAS,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;AAElD,IAAA,OAAO,UAAU,IAAI,QAAQ,IAAI,SAAS,CAAC;AAC7C,CAAC;AAED;;;;AAIG;AACH,SAAS,UAAU,CACjB,GAAW,EACX,WAAqB,EACrB,WAAkC,EAAA;AAElC,IAAA,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;AAC5B,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;IACD,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC;IACnC,IAAI,WAAW,KAAX,IAAA,IAAA,WAAW,KAAX,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,WAAW,CAAE,GAAG,CAAC,IAAI,CAAC,EAAE;AAC1B,QAAA,OAAO,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC9B,KAAA;IACD,IAAI,cAAc,GAAG,KAAK,CAAC;AAC3B,IAAA,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE;AACjC,QAAA,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;;;AAGtB,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;gBAC1B,cAAc,GAAG,IAAI,CAAC;AACvB,aAAA;AAAM,iBAAA;AACL,gBAAA,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;oBACnE,cAAc,GAAG,IAAI,CAAC;AACvB,iBAAA;AACF,aAAA;AACF,SAAA;AAAM,aAAA;YACL,IAAI,IAAI,KAAK,OAAO,EAAE;gBACpB,cAAc,GAAG,IAAI,CAAC;AACvB,aAAA;AACF,SAAA;AACF,KAAA;IACD,WAAW,KAAA,IAAA,IAAX,WAAW,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAX,WAAW,CAAE,GAAG,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;AACvC,IAAA,OAAO,cAAc,CAAC;AACxB,CAAC;SAEe,WAAW,GAAA;AACzB,IAAA,MAAM,OAAO,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IAC9C,iBAAiB,GAAG,IAAI,CAAC;AACzB,IAAA,IAAI,OAAO,EAAE;AACX,QAAA,OAAO,OAAO;aACX,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;aAC1B,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;AAClC,KAAA;AAED,IAAA,OAAO,EAAE,CAAC;AACZ,CAAC;AAED;;;;;AAKG;AACG,SAAU,uBAAuB,CAAC,QAAiB,EAAA;IACvD,IAAI,CAAC,QAAQ,EAAE;QACb,QAAQ,GAAG,yBAAyB,EAAE,CAAC;QACvC,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,OAAO,SAAS,CAAC;AAClB,SAAA;AACF,KAAA;AAED,IAAA,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;AACpC,IAAA,MAAM,MAAM,GAAG,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;IACnE,OAAO;AACL,QAAA,IAAI,EAAE,MAAM,GAAG,SAAS,CAAC,QAAQ;QACjC,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC;QAC7C,QAAQ,EAAE,SAAS,CAAC,QAAQ;QAC5B,QAAQ,EAAE,SAAS,CAAC,QAAQ;KAC7B,CAAC;AACJ,CAAC;AAED;;AAEG;AACG,SAAU,oBAAoB,CAClC,aAA4B,EAC5B,EAAE,OAAO,EAAE,WAAW,EAAmB,EAAA;AAEzC,IAAA,IAAI,cAAmB,CAAC;IACxB,IAAI;QACF,cAAc,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AAC9C,KAAA;AAAC,IAAA,OAAO,MAAM,EAAE;QACf,MAAM,IAAI,KAAK,CACb,CAAA,4DAAA,EAA+D,aAAa,CAAC,IAAI,CAAI,EAAA,CAAA,CACtF,CAAC;AACH,KAAA;AAED,IAAA,IAAI,WAAW,EAAE;AACf,QAAA,MAAM,CAAC,OAAO,CACZ,uHAAuH,CACxH,CAAC;AACH,KAAA;AAED,IAAA,MAAM,iBAAiB,GAA2B;QAChD,QAAQ,EAAE,cAAc,CAAC,QAAQ;QACjC,IAAI,EAAE,aAAa,CAAC,IAAI;QACxB,QAAQ,EAAE,cAAc,CAAC,QAAQ;AACjC,QAAA,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE;KAC1B,CAAC;AACF,IAAA,IAAI,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,QAAQ,EAAE;AACpD,QAAA,iBAAiB,CAAC,IAAI,GAAG,CAAA,EAAG,aAAa,CAAC,QAAQ,CAAA,CAAA,EAAI,aAAa,CAAC,QAAQ,CAAA,CAAE,CAAC;AAChF,KAAA;SAAM,IAAI,aAAa,CAAC,QAAQ,EAAE;QACjC,iBAAiB,CAAC,IAAI,GAAG,CAAA,EAAG,aAAa,CAAC,QAAQ,EAAE,CAAC;AACtD,KAAA;AACD,IAAA,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAED,SAAS,sBAAsB,CAAC,OAAwB,EAAE,YAA0B,EAAA;;;IAGlF,IAAI,OAAO,CAAC,KAAK,EAAE;QACjB,OAAO;AACR,KAAA;IAED,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAEjC,IAAA,MAAM,UAAU,GAAG,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC;AAE7C,IAAA,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;AAC5C,IAAA,IAAI,aAAa,EAAE;AACjB,QAAA,IAAI,UAAU,EAAE;AACd,YAAA,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE;gBAChC,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;gBACvE,YAAY,CAAC,cAAc,GAAG,IAAIC,6BAAc,CAAC,iBAAiB,CAAC,CAAC;AACrE,aAAA;AACD,YAAA,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,cAAc,CAAC;AAC7C,SAAA;AAAM,aAAA;AACL,YAAA,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE;gBACjC,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;gBACvE,YAAY,CAAC,eAAe,GAAG,IAAIC,+BAAe,CAAC,iBAAiB,CAAC,CAAC;AACvE,aAAA;AACD,YAAA,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,eAAe,CAAC;AAC9C,SAAA;AACF,KAAA;AACH,CAAC;AAOD;;;;;;AAMG;AACG,SAAU,WAAW,CACzB,aAAa,GAAG,uBAAuB,EAAE,EACzC,OAGC,EAAA;IAED,IAAI,CAAC,iBAAiB,EAAE;AACtB,QAAA,iBAAiB,CAAC,IAAI,CAAC,GAAG,WAAW,EAAE,CAAC,CAAC;AAC1C,KAAA;IAED,MAAM,YAAY,GAAiB,EAAE,CAAC;IAEtC,OAAO;AACL,QAAA,IAAI,EAAE,eAAe;AACrB,QAAA,MAAM,WAAW,CAAC,OAAwB,EAAE,IAAiB,EAAA;;YAC3D,IACE,CAAC,OAAO,CAAC,aAAa;AACtB,gBAAA,CAAC,UAAU,CACT,OAAO,CAAC,GAAG,EACX,CAAA,EAAA,GAAA,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,iBAAiB,mCAAI,iBAAiB,EAC/C,CAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,iBAAiB,IAAG,SAAS,GAAG,iBAAiB,CAC3D,EACD;AACA,gBAAA,OAAO,CAAC,aAAa,GAAG,aAAa,CAAC;AACvC,aAAA;YAED,IAAI,OAAO,CAAC,aAAa,EAAE;AACzB,gBAAA,sBAAsB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;AAC/C,aAAA;AACD,YAAA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;SACtB;KACF,CAAC;AACJ;;AC9OA;AACA;AAKA;;AAEG;AACI,MAAM,4BAA4B,GAAG,2BAA2B;AAEvE;;;;;AAKG;AACa,SAAA,wBAAwB,CACtC,mBAAmB,GAAG,wBAAwB,EAAA;IAE9C,OAAO;AACL,QAAA,IAAI,EAAE,4BAA4B;AAClC,QAAA,MAAM,WAAW,CAAC,OAAwB,EAAE,IAAiB,EAAA;YAC3D,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE;gBAC7C,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AAC7D,aAAA;AACD,YAAA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;SACtB;KACF,CAAC;AACJ;;AC7BA;AACA;AAKA;;AAEG;AACI,MAAM,aAAa,GAAG,YAAY;AAEzC;;AAEG;AACG,SAAU,SAAS,CAAC,WAAyB,EAAA;IACjD,OAAO;AACL,QAAA,IAAI,EAAE,aAAa;AACnB,QAAA,WAAW,EAAE,OAAO,GAAG,EAAE,IAAI,KAAI;;AAE/B,YAAA,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE;AACpB,gBAAA,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC;AAC/B,aAAA;AACD,YAAA,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;SAClB;KACF,CAAC;AACJ;;ACzBA;AAKO,MAAM,MAAM,GAAGC,YAAO,CAAC,MAAM;;ACLpC;AAQA,MAAM,cAAc,GAAG,IAAI,SAAS,EAAE,CAAC;AAwBvC;;AAEG;AACG,MAAO,SAAU,SAAQ,KAAK,CAAA;IAkClC,WAAY,CAAA,OAAe,EAAE,OAAA,GAA4B,EAAE,EAAA;QACzD,KAAK,CAAC,OAAO,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;AACxB,QAAA,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AACzB,QAAA,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;AACrC,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;AAC/B,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAEjC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;KAClD;AAED;;AAEG;AACH,IAAA,CAAC,MAAM,CAAC,GAAA;AACN,QAAA,OAAO,CAAc,WAAA,EAAA,IAAI,CAAC,OAAO,CAAO,IAAA,EAAA,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,CAAE,CAAC;KACzE;;AAjDD;;;;AAIG;AACa,SAAkB,CAAA,kBAAA,GAAW,oBAAoB,CAAC;AAClE;;;AAGG;AACa,SAAW,CAAA,WAAA,GAAW,aAAa,CAAC;AA0CtD;;;AAGG;AACG,SAAU,WAAW,CAAC,CAAU,EAAA;IACpC,IAAI,CAAC,YAAY,SAAS,EAAE;AAC1B,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;IACD,OAAOC,gBAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC;AAC9C;;ACjGA;AAiBA;;AAEG;AACI,MAAM,iBAAiB,GAAG,gBAAgB;AAcjD;;;;;AAKG;AACa,SAAA,aAAa,CAAC,OAAA,GAAgC,EAAE,EAAA;IAC9D,MAAM,SAAS,GAAG,iBAAiB,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;AAC7D,IAAA,MAAM,aAAa,GAAG,sBAAsB,EAAE,CAAC;IAE/C,OAAO;AACL,QAAA,IAAI,EAAE,iBAAiB;AACvB,QAAA,MAAM,WAAW,CAAC,OAAwB,EAAE,IAAiB,EAAA;;AAC3D,YAAA,IAAI,CAAC,aAAa,IAAI,EAAC,CAAA,EAAA,GAAA,OAAO,CAAC,cAAc,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,cAAc,CAAA,EAAE;AAC7D,gBAAA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;AACtB,aAAA;AAED,YAAA,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,CAAA,EAAA,GAAA,aAAa,CAAC,aAAa,EAAE,OAAO,EAAE,SAAS,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,EAAE,CAAC;AAExF,YAAA,IAAI,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE;AAC5B,gBAAA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;AACtB,aAAA;YAED,IAAI;AACF,gBAAA,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,WAAW,CAAC,cAAc,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAChF,gBAAA,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AACnC,gBAAA,OAAO,QAAQ,CAAC;AACjB,aAAA;AAAC,YAAA,OAAO,GAAQ,EAAE;AACjB,gBAAA,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAC3B,gBAAA,MAAM,GAAG,CAAC;AACX,aAAA;SACF;KACF,CAAC;AACJ,CAAC;AAED,SAAS,sBAAsB,GAAA;IAC7B,IAAI;AACF,QAAA,OAAOC,+BAAmB,CAAC;AACzB,YAAA,SAAS,EAAE,EAAE;AACb,YAAA,WAAW,EAAE,2BAA2B;AACxC,YAAA,cAAc,EAAE,WAAW;AAC5B,SAAA,CAAC,CAAC;AACJ,KAAA;AAAC,IAAA,OAAO,CAAU,EAAE;QACnB,MAAM,CAAC,OAAO,CAAC,CAA0C,uCAAA,EAAAC,wBAAe,CAAC,CAAC,CAAC,CAAE,CAAA,CAAC,CAAC;AAC/E,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;AACH,CAAC;AAED,SAAS,aAAa,CACpB,aAA4B,EAC5B,OAAwB,EACxB,SAAkB,EAAA;IAElB,IAAI;;QAEF,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,aAAa,CAAC,SAAS,CACtD,CAAA,KAAA,EAAQ,OAAO,CAAC,MAAM,CAAE,CAAA,EACxB,EAAE,cAAc,EAAE,OAAO,CAAC,cAAc,EAAE,EAC1C;AACE,YAAA,QAAQ,EAAE,QAAQ;AAClB,YAAA,cAAc,EAAE;gBACd,aAAa,EAAE,OAAO,CAAC,MAAM;gBAC7B,UAAU,EAAE,OAAO,CAAC,GAAG;gBACvB,SAAS,EAAE,OAAO,CAAC,SAAS;AAC7B,aAAA;AACF,SAAA,CACF,CAAC;;AAGF,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YACvB,IAAI,CAAC,GAAG,EAAE,CAAC;AACX,YAAA,OAAO,SAAS,CAAC;AAClB,SAAA;AAED,QAAA,IAAI,SAAS,EAAE;AACb,YAAA,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC;AACjD,SAAA;;AAGD,QAAA,MAAM,OAAO,GAAG,aAAa,CAAC,oBAAoB,CAChD,cAAc,CAAC,cAAc,CAAC,cAAc,CAC7C,CAAC;AACF,QAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAClD,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACjC,SAAA;QACD,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE,cAAc,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;AAC/E,KAAA;AAAC,IAAA,OAAO,CAAM,EAAE;QACf,MAAM,CAAC,OAAO,CAAC,CAAqD,kDAAA,EAAAA,wBAAe,CAAC,CAAC,CAAC,CAAE,CAAA,CAAC,CAAC;AAC1F,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;AACH,CAAC;AAED,SAAS,eAAe,CAAC,IAAiB,EAAE,KAAc,EAAA;IACxD,IAAI;QACF,IAAI,CAAC,SAAS,CAAC;AACb,YAAA,MAAM,EAAE,OAAO;AACf,YAAA,KAAK,EAAEF,gBAAO,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,SAAS;AAC1C,SAAA,CAAC,CAAC;QACH,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,UAAU,EAAE;YAC1C,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;AACzD,SAAA;QACD,IAAI,CAAC,GAAG,EAAE,CAAC;AACZ,KAAA;AAAC,IAAA,OAAO,CAAM,EAAE;QACf,MAAM,CAAC,OAAO,CAAC,CAAqD,kDAAA,EAAAE,wBAAe,CAAC,CAAC,CAAC,CAAE,CAAA,CAAC,CAAC;AAC3F,KAAA;AACH,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAiB,EAAE,QAA0B,EAAA;IACvE,IAAI;QACF,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;QACvD,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;AACjE,QAAA,IAAI,gBAAgB,EAAE;AACpB,YAAA,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;AACzD,SAAA;QACD,IAAI,CAAC,SAAS,CAAC;AACb,YAAA,MAAM,EAAE,SAAS;AAClB,SAAA,CAAC,CAAC;QACH,IAAI,CAAC,GAAG,EAAE,CAAC;AACZ,KAAA;AAAC,IAAA,OAAO,CAAM,EAAE;QACf,MAAM,CAAC,OAAO,CAAC,CAAqD,kDAAA,EAAAA,wBAAe,CAAC,CAAC,CAAC,CAAE,CAAA,CAAC,CAAC;AAC3F,KAAA;AACH;;AC3JA;AA2DA;;;AAGG;AACG,SAAU,yBAAyB,CAAC,OAAgC,EAAA;AACxE,IAAA,MAAM,QAAQ,GAAG,mBAAmB,EAAE,CAAC;AAEvC,IAAA,IAAIC,eAAM,EAAE;QACV,IAAI,OAAO,CAAC,UAAU,EAAE;YACtB,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;AACnD,SAAA;QACD,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;AACtD,QAAA,QAAQ,CAAC,SAAS,CAAC,wBAAwB,EAAE,CAAC,CAAC;AAChD,KAAA;AAED,IAAA,QAAQ,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC;IACrC,QAAQ,CAAC,SAAS,CAAC,eAAe,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC9D,IAAA,QAAQ,CAAC,SAAS,CAAC,wBAAwB,EAAE,CAAC,CAAC;AAC/C,IAAA,QAAQ,CAAC,SAAS,CAAC,kBAAkB,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;AACjF,IAAA,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC;AACrF,IAAA,IAAIA,eAAM,EAAE;;;AAGV,QAAA,QAAQ,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC;AACtF,KAAA;AACD,IAAA,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC;AAE9E,IAAA,OAAO,QAAQ,CAAC;AAClB;;ACvFA;AACA;AASA,SAAS,aAAa,CAAC,IAAY,EAAA;AACjC,IAAA,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;AAC5B,CAAC;AAED,UAAU,cAAc,CAAC,GAA6B,EAAA;AACpD,IAAA,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE,EAAE;QAChC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AACjC,KAAA;AACH,CAAC;AAED,MAAM,eAAe,CAAA;AAGnB,IAAA,WAAA,CAAY,UAAiD,EAAA;AAC3D,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAuB,CAAC;AAClD,QAAA,IAAI,UAAU,EAAE;YACd,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;gBAChD,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;AAC9C,aAAA;AACF,SAAA;KACF;AAED;;;;;AAKG;IACI,GAAG,CAAC,IAAY,EAAE,KAAgC,EAAA;QACvD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;KAC3E;AAED;;;;AAIG;AACI,IAAA,GAAG,CAAC,IAAY,EAAA;;AACrB,QAAA,OAAO,CAAA,EAAA,GAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,CAAC;KACzD;AAED;;;AAGG;AACI,IAAA,GAAG,CAAC,IAAY,EAAA;QACrB,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;KAClD;AAED;;;AAGG;AACI,IAAA,MAAM,CAAC,IAAY,EAAA;QACxB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;KAC9C;AAED;;AAEG;IACI,MAAM,CAAC,UAAsC,EAAE,EAAA;QACpD,MAAM,MAAM,GAAmB,EAAE,CAAC;QAClC,IAAI,OAAO,CAAC,YAAY,EAAE;YACxB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE;gBAC7C,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;AAClC,aAAA;AACF,SAAA;AAAM,aAAA;YACL,KAAK,MAAM,CAAC,cAAc,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE;AACtD,gBAAA,MAAM,CAAC,cAAc,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;AACtC,aAAA;AACF,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACf;AAED;;AAEG;IACI,QAAQ,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;KAC5D;AAED;;AAEG;IACH,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAA;AACf,QAAA,OAAO,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KACzC;AACF,CAAA;AAED;;;AAGG;AACG,SAAU,iBAAiB,CAAC,UAAgC,EAAA;AAChE,IAAA,OAAO,IAAI,eAAe,CAAC,UAAU,CAAC,CAAC;AACzC;;AC1GA;AAsBA,MAAM,oBAAoB,GAAG,EAAE,CAAC;AAEhC,SAAS,gBAAgB,CAAC,IAAS,EAAA;IACjC,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC;AACjD,CAAC;AAED,SAAS,gBAAgB,CAAC,MAA6B,EAAA;AACrD,IAAA,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,KAAI;AAC7B,QAAA,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC5B,QAAA,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAC1B,QAAA,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC9B,KAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,aAAa,CAAC,IAAS,EAAA;IAC9B,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,CAAC;AACrD,CAAC;AAED,MAAM,eAAgB,SAAQC,gBAAS,CAAA;AAgBrC,IAAA,WAAA,CAAY,gBAA2D,EAAA;AACrE,QAAA,KAAK,EAAE,CAAC;QAhBF,IAAW,CAAA,WAAA,GAAG,CAAC,CAAC;AAiBtB,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;KAC1C;;AAdD,IAAA,UAAU,CAAC,KAAsB,EAAE,SAAiB,EAAE,QAAkB,EAAA;AACtE,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACjB,QAAA,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,MAAM,CAAC;QACjC,IAAI;YACF,IAAI,CAAC,gBAAgB,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AACzD,YAAA,QAAQ,EAAE,CAAC;AACZ,SAAA;AAAC,QAAA,OAAO,CAAM,EAAE;YACf,QAAQ,CAAC,CAAC,CAAC,CAAC;AACb,SAAA;KACF;AAMF,CAAA;AAED;;;AAGG;AACH,MAAM,cAAc,CAAA;AAApB,IAAA,WAAA,GAAA;AAEU,QAAA,IAAA,CAAA,iBAAiB,GAAsC,IAAI,OAAO,EAAE,CAAC;KAkO9E;AAhOC;;;AAGG;IACI,MAAM,WAAW,CAAC,OAAwB,EAAA;;AAC/C,QAAA,MAAMC,iBAAe,GAAG,IAAIC,+BAAe,EAAE,CAAC;AAC9C,QAAA,IAAI,aAAiD,CAAC;QACtD,IAAI,OAAO,CAAC,WAAW,EAAE;AACvB,YAAA,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE;AAC/B,gBAAA,MAAM,IAAIZ,0BAAU,CAAC,4BAA4B,CAAC,CAAC;AACpD,aAAA;AAED,YAAA,aAAa,GAAG,CAAC,KAAY,KAAI;AAC/B,gBAAA,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;oBAC1BW,iBAAe,CAAC,KAAK,EAAE,CAAC;AACzB,iBAAA;AACH,aAAC,CAAC;YACF,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;AAC9D,SAAA;AAED,QAAA,IAAI,OAAO,CAAC,OAAO,GAAG,CAAC,EAAE;YACvB,UAAU,CAAC,MAAK;gBACdA,iBAAe,CAAC,KAAK,EAAE,CAAC;AAC1B,aAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;AACrB,SAAA;QAED,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAC9D,MAAM,gBAAgB,GACpB,CAAA,cAAc,KAAA,IAAA,IAAd,cAAc,KAAd,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,cAAc,CAAE,QAAQ,CAAC,MAAM,CAAC,MAAI,cAAc,KAAd,IAAA,IAAA,cAAc,KAAd,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,cAAc,CAAE,QAAQ,CAAC,SAAS,CAAC,CAAA,CAAC;QAE1E,IAAI,IAAI,GAAG,OAAO,OAAO,CAAC,IAAI,KAAK,UAAU,GAAG,OAAO,CAAC,IAAI,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;QAC9E,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE;AAClD,YAAA,MAAM,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,UAAU,KAAK,IAAI,EAAE;gBACvB,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC;AACnD,aAAA;AACF,SAAA;AAED,QAAA,IAAI,cAAiD,CAAC;QACtD,IAAI;AACF,YAAA,IAAI,IAAI,IAAI,OAAO,CAAC,gBAAgB,EAAE;AACpC,gBAAA,MAAM,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;AAClD,gBAAA,MAAM,kBAAkB,GAAG,IAAI,eAAe,CAAC,gBAAgB,CAAC,CAAC;gBACjE,kBAAkB,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,KAAI;AACnC,oBAAA,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,CAAC,CAAC,CAAC;AAC9C,iBAAC,CAAC,CAAC;AACH,gBAAA,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAC1B,oBAAA,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AAC/B,iBAAA;AAAM,qBAAA;AACL,oBAAA,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC9B,iBAAA;gBAED,IAAI,GAAG,kBAAkB,CAAC;AAC3B,aAAA;AAED,YAAA,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAEA,iBAAe,EAAE,IAAI,CAAC,CAAC;AAEnE,YAAA,MAAM,OAAO,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;YAExC,MAAM,MAAM,GAAG,CAAA,EAAA,GAAA,GAAG,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,CAAC,CAAC;AACnC,YAAA,MAAM,QAAQ,GAAqB;gBACjC,MAAM;gBACN,OAAO;gBACP,OAAO;aACR,CAAC;;;AAIF,YAAA,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE;;;gBAG7B,GAAG,CAAC,MAAM,EAAE,CAAC;AACb,gBAAA,OAAO,QAAQ,CAAC;AACjB,aAAA;AAED,YAAA,cAAc,GAAG,gBAAgB,GAAG,wBAAwB,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;AAEjF,YAAA,MAAM,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;AACtD,YAAA,IAAI,kBAAkB,EAAE;AACtB,gBAAA,MAAM,oBAAoB,GAAG,IAAI,eAAe,CAAC,kBAAkB,CAAC,CAAC;gBACrE,oBAAoB,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,KAAI;AACrC,oBAAA,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,CAAC,CAAC,CAAC;AAChD,iBAAC,CAAC,CAAC;AACH,gBAAA,cAAc,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAC1C,cAAc,GAAG,oBAAoB,CAAC;AACvC,aAAA;AAED,YAAA;;YAEE,CAAA,CAAA,EAAA,GAAA,OAAO,CAAC,yBAAyB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,GAAG,CAAC,MAAM,CAAC,iBAAiB,CAAC;iBAChE,CAAA,EAAA,GAAA,OAAO,CAAC,yBAAyB,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,EACvD;AACA,gBAAA,QAAQ,CAAC,kBAAkB,GAAG,cAAc,CAAC;AAC9C,aAAA;AAAM,iBAAA;gBACL,QAAQ,CAAC,UAAU,GAAG,MAAM,YAAY,CAAC,cAAc,CAAC,CAAC;AAC1D,aAAA;AAED,YAAA,OAAO,QAAQ,CAAC;AACjB,SAAA;AAAS,gBAAA;;AAER,YAAA,IAAI,OAAO,CAAC,WAAW,IAAI,aAAa,EAAE;AACxC,gBAAA,IAAI,gBAAgB,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;AACzC,gBAAA,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAC1B,oBAAA,gBAAgB,GAAG,gBAAgB,CAAC,IAA6B,CAAC,CAAC;AACpE,iBAAA;AACD,gBAAA,IAAI,kBAAkB,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;AAC3C,gBAAA,IAAI,gBAAgB,CAAC,cAAc,CAAC,EAAE;AACpC,oBAAA,kBAAkB,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;AACvD,iBAAA;gBAED,OAAO,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC;qBAChD,IAAI,CAAC,MAAK;;;AAET,oBAAA,IAAI,aAAa,EAAE;wBACjB,CAAA,EAAA,GAAA,OAAO,CAAC,WAAW,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,mBAAmB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;AAClE,qBAAA;AACH,iBAAC,CAAC;AACD,qBAAA,KAAK,CAAC,CAAC,CAAC,KAAI;AACX,oBAAA,MAAM,CAAC,OAAO,CAAC,qDAAqD,EAAE,CAAC,CAAC,CAAC;AAC3E,iBAAC,CAAC,CAAC;AACN,aAAA;AACF,SAAA;KACF;AAEO,IAAA,WAAW,CACjB,OAAwB,EACxBA,iBAAgC,EAChC,IAAsB,EAAA;;QAEtB,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAEjC,QAAA,MAAM,UAAU,GAAG,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC;AAE7C,QAAA,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE;YAClD,MAAM,IAAI,KAAK,CAAC,CAAA,kBAAA,EAAqB,OAAO,CAAC,GAAG,CAA0C,wCAAA,CAAA,CAAC,CAAC;AAC7F,SAAA;AAED,QAAA,MAAM,KAAK,GAAG,CAAC,EAAA,GAAA,OAAO,CAAC,KAAoB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AAC1F,QAAA,MAAM,OAAO,GAAwB;YACnC,KAAK;YACL,QAAQ,EAAE,GAAG,CAAC,QAAQ;YACtB,IAAI,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAG,EAAA,GAAG,CAAC,MAAM,CAAE,CAAA;YACpC,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,OAAO,CAAC,MAAM;AACtB,YAAA,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;SACxD,CAAC;QAEF,OAAO,IAAI,OAAO,CAAuB,CAAC,OAAO,EAAE,MAAM,KAAI;YAC3D,MAAM,GAAG,GAAG,UAAU,GAAGE,eAAI,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,GAAGC,gBAAK,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAE1F,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,GAA8B,KAAI;;gBACnD,MAAM,CACJ,IAAI,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAA,EAAA,GAAA,GAAG,CAAC,IAAI,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,SAAS,CAAC,kBAAkB,EAAE,OAAO,EAAE,CAAC,CACxF,CAAC;AACJ,aAAC,CAAC,CAAC;YAEHH,iBAAe,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAK;AACpD,gBAAA,MAAM,UAAU,GAAG,IAAIX,0BAAU,CAAC,4BAA4B,CAAC,CAAC;AAChE,gBAAA,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBACxB,MAAM,CAAC,UAAU,CAAC,CAAC;AACrB,aAAC,CAAC,CAAC;AACH,YAAA,IAAI,IAAI,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAClC,gBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAChB,aAAA;AAAM,iBAAA,IAAI,IAAI,EAAE;gBACf,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACrD,oBAAA,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACf,iBAAA;AAAM,qBAAA,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE;AAC9B,oBAAA,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAClF,iBAAA;AAAM,qBAAA;AACL,oBAAA,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC;AAC7C,oBAAA,MAAM,CAAC,IAAI,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC;AACjD,iBAAA;AACF,aAAA;AAAM,iBAAA;;gBAEL,GAAG,CAAC,GAAG,EAAE,CAAC;AACX,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;IAEO,gBAAgB,CAAC,OAAwB,EAAE,UAAmB,EAAA;;AACpE,QAAA,MAAM,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;;AAGlD,QAAA,IAAI,UAAU,EAAE;AACd,YAAA,IAAI,gBAAgB,EAAE;;gBAEpB,OAAOa,eAAI,CAAC,WAAW,CAAC;AACzB,aAAA;AAED,YAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;;AAEzB,gBAAA,IAAI,CAAC,eAAe,GAAG,IAAIA,eAAI,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AAC5D,aAAA;YACD,OAAO,IAAI,CAAC,eAAe,CAAC;AAC7B,SAAA;AAAM,aAAA;AACL,YAAA,IAAI,gBAAgB,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;;;gBAG5C,OAAOC,gBAAK,CAAC,WAAW,CAAC;AAC1B,aAAA;;YAGD,MAAM,WAAW,GAAG,CAAA,EAAA,GAAA,OAAO,CAAC,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,oBAAoB,CAAC;;;YAIhE,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAEpD,IAAI,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,KAAK,CAAC,gBAAgB,EAAE;AAC1D,gBAAA,OAAO,KAAK,CAAC;AACd,aAAA;AAED,YAAA,MAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;AAC/D,YAAA,KAAK,GAAG,IAAIA,gBAAK,CAAC,KAAK,CAAA,MAAA,CAAA,MAAA,CAAA;;AAErB,gBAAA,SAAS,EAAE,CAAC,gBAAgB,EAEzB,EAAA,WAAW,EACd,CAAC;YAEH,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;AAC/C,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;KACF;AACF,CAAA;AAED,SAAS,kBAAkB,CAAC,GAAoB,EAAA;AAC9C,IAAA,MAAM,OAAO,GAAG,iBAAiB,EAAE,CAAC;IACpC,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;QAC7C,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAClC,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACxB,YAAA,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBACpB,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B,aAAA;AACF,SAAA;AAAM,aAAA,IAAI,KAAK,EAAE;AAChB,YAAA,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC5B,SAAA;AACF,KAAA;AACD,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,wBAAwB,CAC/B,MAAuB,EACvB,OAAoB,EAAA;IAEpB,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IACxD,IAAI,eAAe,KAAK,MAAM,EAAE;AAC9B,QAAA,MAAM,KAAK,GAAGC,eAAI,CAAC,YAAY,EAAE,CAAC;AAClC,QAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACnB,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;SAAM,IAAI,eAAe,KAAK,SAAS,EAAE;AACxC,QAAA,MAAM,OAAO,GAAGA,eAAI,CAAC,aAAa,EAAE,CAAC;AACrC,QAAA,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACrB,QAAA,OAAO,OAAO,CAAC;AAChB,KAAA;AAED,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,YAAY,CAAC,MAA6B,EAAA;IACjD,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,KAAI;QAC7C,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,KAAI;AAC1B,YAAA,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AAC1B,gBAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACpB,aAAA;AAAM,iBAAA;gBACL,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACjC,aAAA;AACH,SAAC,CAAC,CAAC;AACH,QAAA,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,MAAK;AACpB,YAAA,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AAClD,SAAC,CAAC,CAAC;QACH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,KAAI;AACvB,YAAA,IAAI,CAAC,IAAI,CAAA,CAAC,KAAD,IAAA,IAAA,CAAC,KAAD,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAC,CAAE,IAAI,MAAK,YAAY,EAAE;gBACjC,MAAM,CAAC,CAAC,CAAC,CAAC;AACX,aAAA;AAAM,iBAAA;gBACL,MAAM,CACJ,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAC,OAAO,CAAA,CAAE,EAAE;oBAC5D,IAAI,EAAE,SAAS,CAAC,WAAW;AAC5B,iBAAA,CAAC,CACH,CAAC;AACH,aAAA;AACH,SAAC,CAAC,CAAC;AACL,KAAC,CAAC,CAAC;AACL,CAAC;AAED;AACM,SAAU,aAAa,CAAC,IAAqB,EAAA;IACjD,IAAI,CAAC,IAAI,EAAE;AACT,QAAA,OAAO,CAAC,CAAC;AACV,KAAA;AAAM,SAAA,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAChC,OAAO,IAAI,CAAC,MAAM,CAAC;AACpB,KAAA;AAAM,SAAA,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;AACjC,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AAAM,SAAA,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE;QAC9B,OAAO,IAAI,CAAC,UAAU,CAAC;AACxB,KAAA;AAAM,SAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QACnC,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;AACjC,KAAA;AAAM,SAAA;AACL,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AACH,CAAC;AAED;;;AAGG;SACa,oBAAoB,GAAA;IAClC,OAAO,IAAI,cAAc,EAAE,CAAC;AAC9B;;AC5XA;AAMA;;AAEG;SACa,uBAAuB,GAAA;IACrC,OAAO,oBAAoB,EAAE,CAAC;AAChC;;ACXA;AAiHA,MAAM,mBAAmB,CAAA;AAoBvB,IAAA,WAAA,CAAY,OAA+B,EAAA;;AACzC,QAAA,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;AACvB,QAAA,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,CAAA,EAAA,GAAA,OAAO,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,iBAAiB,EAAE,CAAC;QACtD,IAAI,CAAC,MAAM,GAAG,CAAA,EAAA,GAAA,OAAO,CAAC,MAAM,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,KAAK,CAAC;QACtC,IAAI,CAAC,OAAO,GAAG,CAAA,EAAA,GAAA,OAAO,CAAC,OAAO,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,CAAC,CAAC;AACpC,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,gBAAgB,GAAG,CAAA,EAAA,GAAA,OAAO,CAAC,gBAAgB,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,KAAK,CAAC;AAC1D,QAAA,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;AAC3C,QAAA,IAAI,CAAC,yBAAyB,GAAG,OAAO,CAAC,yBAAyB,CAAC;QACnE,IAAI,CAAC,eAAe,GAAG,CAAA,EAAA,GAAA,OAAO,CAAC,eAAe,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,KAAK,CAAC;AACxD,QAAA,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;AACvC,QAAA,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;AAC7C,QAAA,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;AACjD,QAAA,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;QACrD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAIC,mBAAU,EAAE,CAAC;QACnD,IAAI,CAAC,uBAAuB,GAAG,CAAA,EAAA,GAAA,OAAO,CAAC,uBAAuB,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,KAAK,CAAC;QACxE,IAAI,CAAC,oBAAoB,GAAG,CAAA,EAAA,GAAA,OAAO,CAAC,oBAAoB,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,KAAK,CAAC;KACnE;AACF,CAAA;AAED;;;;AAIG;AACG,SAAU,qBAAqB,CAAC,OAA+B,EAAA;AACnE,IAAA,OAAO,IAAI,mBAAmB,CAAC,OAAO,CAAC,CAAC;AAC1C;;ACjKA;AAQA;;AAEG;AACI,MAAM,0BAA0B,GAAG,yBAAyB;AAyBnE;;;AAGG;AACa,SAAA,sBAAsB,CACpC,OAAA,GAAyC,EAAE,EAAA;;AAE3C,IAAA,OAAO,WAAW,CAChB;AACE,QAAA,wBAAwB,iCACnB,OAAO,CAAA,EAAA,EACV,kBAAkB,EAAE,IAAI,EACxB,CAAA,CAAA;KACH,EACD;AACE,QAAA,UAAU,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,UAAU,mCAAI,0BAA0B;AAC7D,KAAA,CACF,CAAC;AACJ;;ACtDA;AAQA;;AAEG;AACI,MAAM,0BAA0B,GAAG,yBAAyB;AAyBnE;;;;;AAKG;AACa,SAAA,sBAAsB,CACpC,OAAA,GAAyC,EAAE,EAAA;;IAE3C,OAAO;AACL,QAAA,IAAI,EAAE,0BAA0B;QAChC,WAAW,EAAE,WAAW,CACtB;AACE,YAAA,wBAAwB,iCACnB,OAAO,CAAA,EAAA,EACV,qBAAqB,EAAE,IAAI,EAC3B,CAAA,CAAA;SACH,EACD;AACE,YAAA,UAAU,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,UAAU,mCAAI,0BAA0B;AAC7D,SAAA,CACF,CAAC,WAAW;KACd,CAAC;AACJ;;AC3DA;AAQA;;AAEG;AACI,MAAM,yBAAyB,GAAG,wBAAwB;AAYjE;;;;;;;;;AASG;AACa,SAAA,qBAAqB,CAAC,OAAA,GAAwC,EAAE,EAAA;;IAC9E,OAAO;AACL,QAAA,IAAI,EAAE,yBAAyB;AAC/B,QAAA,WAAW,EAAE,WAAW,CAAC,CAAC,uBAAuB,EAAE,CAAC,EAAE;AACpD,YAAA,UAAU,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,UAAU,mCAAI,0BAA0B;AAC7D,SAAA,CAAC,CAAC,WAAW;KACf,CAAC;AACJ;;ACxCA;AAsCA;AACO,MAAM,sBAAsB,GAAuB;AACxD,IAAA,uBAAuB,EAAE,IAAI;AAC7B,IAAA,iBAAiB,EAAE,IAAI;AACvB,IAAA,iBAAiB,EAAE,IAAI,GAAG,EAAE,GAAG,CAAC;CACjC,CAAC;AAEF;;;;;;;;;AASG;AACH,eAAe,YAAY,CACzB,cAAiD,EACjD,iBAAyB,EACzB,cAAsB,EAAA;;;AAItB,IAAA,eAAe,iBAAiB,GAAA;AAC9B,QAAA,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,EAAE;YAC/B,IAAI;gBACF,OAAO,MAAM,cAAc,EAAE,CAAC;AAC/B,aAAA;YAAC,OAAM,EAAA,EAAA;AACN,gBAAA,OAAO,IAAI,CAAC;AACb,aAAA;AACF,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,UAAU,GAAG,MAAM,cAAc,EAAE,CAAC;;YAG1C,IAAI,UAAU,KAAK,IAAI,EAAE;AACvB,gBAAA,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;AACpD,aAAA;AAED,YAAA,OAAO,UAAU,CAAC;AACnB,SAAA;KACF;AAED,IAAA,IAAI,KAAK,GAAuB,MAAM,iBAAiB,EAAE,CAAC;IAE1D,OAAO,KAAK,KAAK,IAAI,EAAE;AACrB,QAAA,MAAM,KAAK,CAAC,iBAAiB,CAAC,CAAC;AAE/B,QAAA,KAAK,GAAG,MAAM,iBAAiB,EAAE,CAAC;AACnC,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;;;;;;;;AAaG;AACa,SAAA,iBAAiB,CAC/B,UAA2B,EAC3B,kBAAgD,EAAA;IAEhD,IAAI,aAAa,GAAgC,IAAI,CAAC;IACtD,IAAI,KAAK,GAAuB,IAAI,CAAC;AACrC,IAAA,IAAI,QAA4B,CAAC;AAEjC,IAAA,MAAM,OAAO,GACR,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,sBAAsB,CACtB,EAAA,kBAAkB,CACtB,CAAC;AAEF;;;AAGG;AACH,IAAA,MAAM,MAAM,GAAG;AACb;;AAEG;AACH,QAAA,IAAI,YAAY,GAAA;YACd,OAAO,aAAa,KAAK,IAAI,CAAC;SAC/B;AACD;;;AAGG;AACH,QAAA,IAAI,aAAa,GAAA;;AACf,YAAA,QACE,CAAC,MAAM,CAAC,YAAY;gBACpB,CAAC,CAAA,EAAA,GAAA,KAAK,KAAL,IAAA,IAAA,KAAK,uBAAL,KAAK,CAAE,kBAAkB,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,CAAC,IAAI,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,EAAE,EACzE;SACH;AACD;;;AAGG;AACH,QAAA,IAAI,WAAW,GAAA;AACb,YAAA,QACE,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,kBAAkB,GAAG,OAAO,CAAC,uBAAuB,GAAG,IAAI,CAAC,GAAG,EAAE,EACzF;SACH;KACF,CAAC;AAEF;;;AAGG;AACH,IAAA,SAAS,OAAO,CACd,MAAyB,EACzB,eAAgC,EAAA;;AAEhC,QAAA,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;;AAExB,YAAA,MAAM,iBAAiB,GAAG,MACxB,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;;;AAI/C,YAAA,aAAa,GAAG,YAAY,CAC1B,iBAAiB,EACjB,OAAO,CAAC,iBAAiB;;AAEzB,YAAA,CAAA,EAAA,GAAA,KAAK,KAAA,IAAA,IAAL,KAAK,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAL,KAAK,CAAE,kBAAkB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,IAAI,CAAC,GAAG,EAAE,CACxC;AACE,iBAAA,IAAI,CAAC,CAAC,MAAM,KAAI;gBACf,aAAa,GAAG,IAAI,CAAC;gBACrB,KAAK,GAAG,MAAM,CAAC;AACf,gBAAA,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC;AACpC,gBAAA,OAAO,KAAK,CAAC;AACf,aAAC,CAAC;AACD,iBAAA,KAAK,CAAC,CAAC,MAAM,KAAI;;;;gBAIhB,aAAa,GAAG,IAAI,CAAC;gBACrB,KAAK,GAAG,IAAI,CAAC;gBACb,QAAQ,GAAG,SAAS,CAAC;AACrB,gBAAA,MAAM,MAAM,CAAC;AACf,aAAC,CAAC,CAAC;AACN,SAAA;AAED,QAAA,OAAO,aAAqC,CAAC;KAC9C;AAED,IAAA,OAAO,OAAO,MAAyB,EAAE,YAA6B,KAA0B;;;;;;;;;;;;;AAc9F,QAAA,MAAM,WAAW,GACf,QAAQ,KAAK,YAAY,CAAC,QAAQ,IAAI,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,WAAW,CAAC;AAE3F,QAAA,IAAI,WAAW;AAAE,YAAA,OAAO,OAAO,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAEtD,IAAI,MAAM,CAAC,aAAa,EAAE;AACxB,YAAA,OAAO,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;AAC/B,SAAA;AAED,QAAA,OAAO,KAAoB,CAAC;AAC9B,KAAC,CAAC;AACJ;;ACzNA;AAUA;;AAEG;AACI,MAAM,mCAAmC,GAAG,kCAAkC;AA2FrF;;AAEG;AACH,eAAe,uBAAuB,CAAC,OAAgC,EAAA;IACrE,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;AACpD,IAAA,MAAM,eAAe,GAAoB;QACvC,WAAW,EAAE,OAAO,CAAC,WAAW;QAChC,cAAc,EAAE,OAAO,CAAC,cAAc;KACvC,CAAC;IACF,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;AAElE,IAAA,IAAI,WAAW,EAAE;AACf,QAAA,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,WAAW,CAAC,KAAK,CAAA,CAAE,CAAC,CAAC;AAC7E,KAAA;AACH,CAAC;AAED;;;AAGG;AACH,SAAS,YAAY,CAAC,QAA0B,EAAA;IAC9C,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AAC3D,IAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,IAAI,SAAS,EAAE;AACxC,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;IACD,OAAO;AACT,CAAC;AAED;;;AAGG;AACG,SAAU,+BAA+B,CAC7C,OAA+C,EAAA;;IAE/C,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,kBAAkB,EAAE,GAAG,OAAO,CAAC;AAC3D,IAAA,MAAMnB,QAAM,GAAG,OAAO,CAAC,MAAM,IAAIC,MAAU,CAAC;IAC5C,MAAM,SAAS,GACb,MAAA,CAAA,MAAA,CAAA,EAAA,gBAAgB,EAAE,CAAA,EAAA,GAAA,kBAAkB,KAAA,IAAA,IAAlB,kBAAkB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAlB,kBAAkB,CAAE,gBAAgB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,uBAAuB,EACjF,2BAA2B,EAAE,kBAAkB,KAAA,IAAA,IAAlB,kBAAkB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAlB,kBAAkB,CAAE,2BAA2B,EAAA,EAEzE,kBAAkB,CACtB,CAAC;;;;;IAMF,MAAM,cAAc,GAAG,UAAU;AAC/B,UAAE,iBAAiB,CAAC,UAAU,iBAAiB;UAC7C,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAEhC,OAAO;AACL,QAAA,IAAI,EAAE,mCAAmC;AACzC;;;;;;;;;;;;AAYG;AACH,QAAA,MAAM,WAAW,CAAC,OAAwB,EAAE,IAAiB,EAAA;AAC3D,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;AACrD,gBAAA,MAAM,IAAI,KAAK,CACb,sFAAsF,CACvF,CAAC;AACH,aAAA;YAED,MAAM,SAAS,CAAC,gBAAgB,CAAC;AAC/B,gBAAA,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC;gBACjD,OAAO;gBACP,cAAc;wBACdD,QAAM;AACP,aAAA,CAAC,CAAC;AAEH,YAAA,IAAI,QAA0B,CAAC;AAC/B,YAAA,IAAI,KAAwB,CAAC;YAC7B,IAAI;AACF,gBAAA,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;AAChC,aAAA;AAAC,YAAA,OAAO,GAAQ,EAAE;gBACjB,KAAK,GAAG,GAAG,CAAC;AACZ,gBAAA,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;AACzB,aAAA;YAED,IACE,SAAS,CAAC,2BAA2B;gBACrC,CAAA,QAAQ,aAAR,QAAQ,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAR,QAAQ,CAAE,MAAM,MAAK,GAAG;gBACxB,YAAY,CAAC,QAAQ,CAAC,EACtB;;AAEA,gBAAA,MAAM,iBAAiB,GAAG,MAAM,SAAS,CAAC,2BAA2B,CAAC;AACpE,oBAAA,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC;oBACjD,OAAO;oBACP,QAAQ;oBACR,cAAc;4BACdA,QAAM;AACP,iBAAA,CAAC,CAAC;AAEH,gBAAA,IAAI,iBAAiB,EAAE;AACrB,oBAAA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;AACtB,iBAAA;AACF,aAAA;AAED,YAAA,IAAI,KAAK,EAAE;AACT,gBAAA,MAAM,KAAK,CAAC;AACb,aAAA;AAAM,iBAAA;AACL,gBAAA,OAAO,QAAQ,CAAC;AACjB,aAAA;SACF;KACF,CAAC;AACJ;;AC5NA;AACA;AAKA;;AAEG;AACI,MAAM,gBAAgB,GAAG,eAAe;AAE/C;;AAEG;SACa,YAAY,GAAA;IAC1B,OAAO;AACL,QAAA,IAAI,EAAE,gBAAgB;AACtB,QAAA,MAAM,WAAW,CAAC,OAAwB,EAAE,IAAiB,EAAA;;AAE3D,YAAA,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;gBACpE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACtC,gBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACvB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACzE,iBAAA;AACF,aAAA;AACD,YAAA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;SACtB;KACF,CAAC;AACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}