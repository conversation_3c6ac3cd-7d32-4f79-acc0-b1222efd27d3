{"version": 3, "file": "azureApplicationCredential.browser.js", "sourceRoot": "", "sources": ["../../../src/credentials/azureApplicationCredential.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAEhE,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAC;AAGlE,MAAM,wBAAwB,GAAG,IAAI,KAAK,CACxC,kGAAkG,CACnG,CAAC;AACF,MAAM,MAAM,GAAG,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;AAEzD;;;;;GAKG;AACH,MAAM,OAAO,0BAA2B,SAAQ,sBAAsB;IACpE;;;;;;;;;OASG;IACH,YAAY,uBAAgD;QAC1D,KAAK,EAAE,CAAC;QACR,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,wBAAwB,CAAC,CAAC,CAAC;QACvD,MAAM,wBAAwB,CAAC;IACjC,CAAC;IAEM,QAAQ;QACb,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,wBAAwB,CAAC,CAAC,CAAC;QAChE,MAAM,wBAAwB,CAAC;IACjC,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { credentialLogger, formatError } from \"../util/logging\";\nimport { AccessToken } from \"@azure/core-auth\";\nimport { ChainedTokenCredential } from \"./chainedTokenCredential\";\nimport { TokenCredentialOptions } from \"../tokenCredentialOptions\";\n\nconst BrowserNotSupportedError = new Error(\n  \"ApplicationCredential is not supported in the browser. Use InteractiveBrowserCredential instead.\"\n);\nconst logger = credentialLogger(\"ApplicationCredential\");\n\n/**\n * Provides a default {@link ChainedTokenCredential} configuration for\n * applications that will be deployed to Azure.\n *\n * Only available in Node.js\n */\nexport class AzureApplicationCredential extends ChainedTokenCredential {\n  /**\n   * Creates an instance of the AzureApplicationCredential class.\n   *\n   * The AzureApplicationCredential provides a default {@link ChainedTokenCredential} configuration for\n   * applications that will be deployed to Azure.\n   *\n   * Only available in Node.js\n   *\n   * @param options - Options for configuring the client which makes the authentication request.\n   */\n  constructor(_tokenCredentialOptions?: TokenCredentialOptions) {\n    super();\n    logger.info(formatError(\"\", BrowserNotSupportedError));\n    throw BrowserNotSupportedError;\n  }\n\n  public getToken(): Promise<AccessToken> {\n    logger.getToken.info(formatError(\"\", BrowserNotSupportedError));\n    throw BrowserNotSupportedError;\n  }\n}\n"]}