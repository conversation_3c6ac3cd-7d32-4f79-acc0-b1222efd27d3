{"version": 3, "file": "url.js", "sourceRoot": "", "sources": ["../../src/url.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAI1C;;GAEG;AACH,MAAM,OAAO,QAAQ;IAArB;QACmB,cAAS,GAAwD,EAAE,CAAC;IAiIvF,CAAC;IA/HC;;OAEG;IACI,GAAG;QACR,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,IAAI;QACT,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACrC,CAAC;IAED;;;;OAIG;IACI,GAAG,CAAC,aAAqB,EAAE,cAAuB;QACvD,MAAM,kBAAkB,GAAG,cAE1B,CAAC;QACF,IAAI,aAAa,EAAE;YACjB,IAAI,kBAAkB,KAAK,SAAS,IAAI,kBAAkB,KAAK,IAAI,EAAE;gBACnE,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC;oBAChD,CAAC,CAAC,kBAAkB;oBACpB,CAAC,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;gBAClC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC;aAC1C;iBAAM;gBACL,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;aACtC;SACF;IACH,CAAC;IAED;;;OAGG;IACI,GAAG,CAAC,aAAqB;QAC9B,OAAO,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACnE,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,SAAS,EAAE;YAC1C,IAAI,MAAM,EAAE;gBACV,MAAM,IAAI,GAAG,CAAC;aACf;YACD,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACrD,IAAI,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;gBACjC,MAAM,gBAAgB,GAAG,EAAE,CAAC;gBAC5B,KAAK,MAAM,qBAAqB,IAAI,cAAc,EAAE;oBAClD,gBAAgB,CAAC,IAAI,CAAC,GAAG,aAAa,IAAI,qBAAqB,EAAE,CAAC,CAAC;iBACpE;gBACD,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACtC;iBAAM;gBACL,MAAM,IAAI,GAAG,aAAa,IAAI,cAAc,EAAE,CAAC;aAChD;SACF;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,IAAY;QAC9B,MAAM,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;QAE9B,IAAI,IAAI,EAAE;YACR,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;gBACxB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;aAC1B;YAED,IAAI,YAAY,GAAuB,eAAe,CAAC;YAEvD,IAAI,aAAa,GAAG,EAAE,CAAC;YACvB,IAAI,cAAc,GAAG,EAAE,CAAC;YACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACpC,MAAM,gBAAgB,GAAW,IAAI,CAAC,CAAC,CAAC,CAAC;gBACzC,QAAQ,YAAY,EAAE;oBACpB,KAAK,eAAe;wBAClB,QAAQ,gBAAgB,EAAE;4BACxB,KAAK,GAAG;gCACN,YAAY,GAAG,gBAAgB,CAAC;gCAChC,MAAM;4BAER,KAAK,GAAG;gCACN,aAAa,GAAG,EAAE,CAAC;gCACnB,cAAc,GAAG,EAAE,CAAC;gCACpB,MAAM;4BAER;gCACE,aAAa,IAAI,gBAAgB,CAAC;gCAClC,MAAM;yBACT;wBACD,MAAM;oBAER,KAAK,gBAAgB;wBACnB,QAAQ,gBAAgB,EAAE;4BACxB,KAAK,GAAG;gCACN,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;gCAC1C,aAAa,GAAG,EAAE,CAAC;gCACnB,cAAc,GAAG,EAAE,CAAC;gCACpB,YAAY,GAAG,eAAe,CAAC;gCAC/B,MAAM;4BAER;gCACE,cAAc,IAAI,gBAAgB,CAAC;gCACnC,MAAM;yBACT;wBACD,MAAM;oBAER;wBACE,MAAM,IAAI,KAAK,CAAC,qCAAqC,GAAG,YAAY,CAAC,CAAC;iBACzE;aACF;YACD,IAAI,YAAY,KAAK,gBAAgB,EAAE;gBACrC,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;aAC3C;SACF;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,UAAU;IAOrB;;;OAGG;IACI,SAAS,CAAC,MAA0B;QACzC,IAAI,CAAC,MAAM,EAAE;YACX,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;SAC1B;aAAM;YACL,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SAC5B;IACH,CAAC;IAED;;OAEG;IACI,SAAS;QACd,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,IAAwB;QACrC,IAAI,CAAC,IAAI,EAAE;YACT,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;SACxB;aAAM;YACL,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;SAClC;IACH,CAAC;IAED;;OAEG;IACI,OAAO;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,IAAiC;QAC9C,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE,EAAE;YACtD,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;SACxB;aAAM;YACL,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC,CAAC;SACnC;IACH,CAAC;IAED;;OAEG;IACI,OAAO;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,IAAwB;QACrC,IAAI,CAAC,IAAI,EAAE;YACT,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;SACxB;aAAM;YACL,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACxC,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE;gBACtB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;gBACvD,0FAA0F;gBAC1F,2FAA2F;gBAC3F,IAAI,CAAC,GAAG,CAAC,WAAW,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;aAC9E;iBAAM;gBACL,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;aACxB;SACF;IACH,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,IAAwB;QACxC,IAAI,IAAI,EAAE;YACR,IAAI,WAAW,GAAuB,IAAI,CAAC,OAAO,EAAE,CAAC;YACrD,IAAI,WAAW,EAAE;gBACf,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;oBAC9B,WAAW,IAAI,GAAG,CAAC;iBACpB;gBAED,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;oBACxB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;iBAC1B;gBAED,IAAI,GAAG,WAAW,GAAG,IAAI,CAAC;aAC3B;YACD,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;SACxB;IACH,CAAC;IAED;;OAEG;IACI,OAAO;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;OAEG;IACI,QAAQ,CAAC,KAAyB;QACvC,IAAI,CAAC,KAAK,EAAE;YACV,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;SACzB;aAAM;YACL,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SACrC;IACH,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,kBAA0B,EAAE,mBAA4B;QAC/E,IAAI,kBAAkB,EAAE;YACtB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAChB,IAAI,CAAC,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;aAC9B;YACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;SAC1D;IACH,CAAC;IAED;;;OAGG;IACI,sBAAsB,CAAC,kBAA0B;QACtD,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACvE,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;IAC1D,CAAC;IAED;;OAEG;IACK,GAAG,CAAC,IAAY,EAAE,UAA6B;QACrD,MAAM,SAAS,GAAG,IAAI,YAAY,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAErD,OAAO,SAAS,CAAC,IAAI,EAAE,EAAE;YACvB,MAAM,KAAK,GAAyB,SAAS,CAAC,OAAO,EAAE,CAAC;YACxD,IAAI,SAA6B,CAAC;YAClC,IAAI,KAAK,EAAE;gBACT,QAAQ,KAAK,CAAC,IAAI,EAAE;oBAClB,KAAK,QAAQ;wBACX,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI,IAAI,SAAS,CAAC;wBACvC,MAAM;oBAER,KAAK,MAAM;wBACT,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,IAAI,SAAS,CAAC;wBACrC,MAAM;oBAER,KAAK,MAAM;wBACT,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,IAAI,SAAS,CAAC;wBACrC,MAAM;oBAER,KAAK,MAAM;wBACT,SAAS,GAAG,KAAK,CAAC,IAAI,IAAI,SAAS,CAAC;wBACpC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,SAAS,KAAK,GAAG,EAAE;4BAC1D,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;yBACxB;wBACD,MAAM;oBAER,KAAK,OAAO;wBACV,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBACzC,MAAM;oBAER;wBACE,MAAM,IAAI,KAAK,CAAC,8BAA8B,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;iBAC/D;aACF;SACF;IACH,CAAC;IAED;;;OAGG;IACI,QAAQ;QACb,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,KAAK,CAAC;SAChC;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC;SACtB;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;SAC5B;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;gBAC/B,MAAM,IAAI,GAAG,CAAC;aACf;YACD,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC;SACtB;QAED,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE;YACpC,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC;SACxC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,WAAmB,EAAE,YAAoB;QACzD,IAAI,WAAW,EAAE;YACf,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;YACxE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;YACpE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;YACpE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;YACpE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;SACvE;IACH,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,IAAY;QAC9B,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;QAChC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;QACnC,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AAMD,MAAM,OAAO,QAAQ;IACnB,YAAmC,IAAY,EAAkB,IAAkB;QAAhD,SAAI,GAAJ,IAAI,CAAQ;QAAkB,SAAI,GAAJ,IAAI,CAAc;IAAG,CAAC;IAEhF,MAAM,CAAC,MAAM,CAAC,IAAY;QAC/B,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACtC,CAAC;IAEM,MAAM,CAAC,IAAI,CAAC,IAAY;QAC7B,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACpC,CAAC;IAEM,MAAM,CAAC,IAAI,CAAC,IAAY;QAC7B,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACpC,CAAC;IAEM,MAAM,CAAC,IAAI,CAAC,IAAY;QAC7B,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACpC,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,IAAY;QAC9B,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACrC,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,UAAU,uBAAuB,CAAC,SAAiB;IACvD,MAAM,aAAa,GAAW,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACtD,OAAO,CACL,CAAC,EAAE,CAAC,SAAS,IAAI,aAAa,IAAI,aAAa,IAAI,EAAE,CAAC,CAAC,SAAS;QAChE,CAAC,EAAE,CAAC,SAAS,IAAI,aAAa,IAAI,aAAa,IAAI,EAAE,CAAC,CAAC,SAAS;QAChE,CAAC,EAAE,CAAC,SAAS,IAAI,aAAa,IAAI,aAAa,IAAI,GAAG,CAAC,CAAC,SAAS,CAClE,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,OAAO,YAAY;IAMvB,YAA4B,KAAa,EAAE,KAAyB;QAAxC,UAAK,GAAL,KAAK,CAAQ;QACvC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAI,CAAC,aAAa,GAAG,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,gBAAgB,CAAC;QACtF,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;IACzB,CAAC;IAED;;;OAGG;IACI,OAAO;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED;;OAEG;IACI,IAAI;QACT,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE;YAC9B,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;SAChC;aAAM;YACL,QAAQ,IAAI,CAAC,aAAa,EAAE;gBAC1B,KAAK,QAAQ;oBACX,UAAU,CAAC,IAAI,CAAC,CAAC;oBACjB,MAAM;gBAER,KAAK,gBAAgB;oBACnB,gBAAgB,CAAC,IAAI,CAAC,CAAC;oBACvB,MAAM;gBAER,KAAK,MAAM;oBACT,QAAQ,CAAC,IAAI,CAAC,CAAC;oBACf,MAAM;gBAER,KAAK,MAAM;oBACT,QAAQ,CAAC,IAAI,CAAC,CAAC;oBACf,MAAM;gBAER,KAAK,MAAM;oBACT,QAAQ,CAAC,IAAI,CAAC,CAAC;oBACf,MAAM;gBAER,KAAK,OAAO;oBACV,SAAS,CAAC,IAAI,CAAC,CAAC;oBAChB,MAAM;gBAER;oBACE,MAAM,IAAI,KAAK,CAAC,mCAAmC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;aAC5E;SACF;QACD,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;CACF;AAED;;GAEG;AACH,SAAS,aAAa,CAAC,SAAuB;IAC5C,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,SAAS,CAAC,aAAa,GAAG,SAAS,CAAC,WAAW,EAAE;QACnD,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAC5D,SAAS,CAAC,aAAa,GAAG,SAAS,CAAC,WAAW,CAAC;KACjD;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,SAAS,mBAAmB,CAAC,SAAuB;IAClD,OAAO,SAAS,CAAC,aAAa,GAAG,SAAS,CAAC,WAAW,CAAC;AACzD,CAAC;AAED;;GAEG;AACH,SAAS,mBAAmB,CAAC,SAAuB;IAClD,OAAO,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;AAClD,CAAC;AAED;;;GAGG;AACH,SAAS,aAAa,CAAC,SAAuB,EAAE,IAAa;IAC3D,IAAI,mBAAmB,CAAC,SAAS,CAAC,EAAE;QAClC,IAAI,CAAC,IAAI,EAAE;YACT,IAAI,GAAG,CAAC,CAAC;SACV;QACD,SAAS,CAAC,aAAa,IAAI,IAAI,CAAC;KACjC;AACH,CAAC;AAED;;;GAGG;AACH,SAAS,cAAc,CAAC,SAAuB,EAAE,gBAAwB;IACvE,IAAI,QAAQ,GAAW,SAAS,CAAC,aAAa,GAAG,gBAAgB,CAAC;IAClE,IAAI,SAAS,CAAC,WAAW,GAAG,QAAQ,EAAE;QACpC,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC;KAClC;IACD,OAAO,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;AACtE,CAAC;AAED;;;GAGG;AACH,SAAS,SAAS,CAAC,SAAuB,EAAE,SAAyC;IACnF,IAAI,MAAM,GAAG,EAAE,CAAC;IAEhB,OAAO,mBAAmB,CAAC,SAAS,CAAC,EAAE;QACrC,MAAM,gBAAgB,GAAW,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAChE,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE;YAChC,MAAM;SACP;aAAM;YACL,MAAM,IAAI,gBAAgB,CAAC;YAC3B,aAAa,CAAC,SAAS,CAAC,CAAC;SAC1B;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,sBAAsB,CAAC,SAAuB;IACrD,OAAO,SAAS,CAAC,SAAS,EAAE,CAAC,SAAiB,EAAE,EAAE,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC,CAAC;AACzF,CAAC;AAED;;;GAGG;AACH,SAAS,kBAAkB,CAAC,SAAuB,EAAE,GAAG,qBAA+B;IACrF,OAAO,SAAS,CACd,SAAS,EACT,CAAC,SAAiB,EAAE,EAAE,CAAC,qBAAqB,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CACvE,CAAC;AACJ,CAAC;AAED,SAAS,UAAU,CAAC,SAAuB;IACzC,MAAM,MAAM,GAAW,sBAAsB,CAAC,SAAS,CAAC,CAAC;IACzD,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAClD,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE;QACnC,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;KAClC;SAAM;QACL,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;KAClC;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,SAAuB;IAC/C,MAAM,YAAY,GAAW,kBAAkB,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC1E,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE;QACnC,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACtD,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;KAClC;SAAM,IAAI,mBAAmB,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;QACjD,IAAI,cAAc,CAAC,SAAS,EAAE,CAAC,CAAC,KAAK,KAAK,EAAE;YAC1C,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACxD,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;SAClC;aAAM;YACL,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACtD,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;SAClC;KACF;SAAM;QACL,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACtD,IAAI,mBAAmB,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;YAC1C,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;SAClC;aAAM;YACL,SAAS,CAAC,aAAa,GAAG,OAAO,CAAC;SACnC;KACF;AACH,CAAC;AAED,SAAS,QAAQ,CAAC,SAAuB;IACvC,IAAI,cAAc,CAAC,SAAS,EAAE,CAAC,CAAC,KAAK,KAAK,EAAE;QAC1C,aAAa,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;KAC7B;IAED,MAAM,IAAI,GAAW,kBAAkB,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAClE,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAE9C,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE;QACnC,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;KAClC;SAAM,IAAI,mBAAmB,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;QACjD,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;KAClC;SAAM,IAAI,mBAAmB,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;QACjD,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;KAClC;SAAM;QACL,SAAS,CAAC,aAAa,GAAG,OAAO,CAAC;KACnC;AACH,CAAC;AAED,SAAS,QAAQ,CAAC,SAAuB;IACvC,IAAI,mBAAmB,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;QAC1C,aAAa,CAAC,SAAS,CAAC,CAAC;KAC1B;IAED,MAAM,IAAI,GAAW,kBAAkB,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC7D,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAE9C,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE;QACnC,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;KAClC;SAAM,IAAI,mBAAmB,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;QACjD,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;KAClC;SAAM;QACL,SAAS,CAAC,aAAa,GAAG,OAAO,CAAC;KACnC;AACH,CAAC;AAED,SAAS,QAAQ,CAAC,SAAuB;IACvC,MAAM,IAAI,GAAW,kBAAkB,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;IACxD,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAE9C,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE;QACnC,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;KAClC;SAAM;QACL,SAAS,CAAC,aAAa,GAAG,OAAO,CAAC;KACnC;AACH,CAAC;AAED,SAAS,SAAS,CAAC,SAAuB;IACxC,IAAI,mBAAmB,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;QAC1C,aAAa,CAAC,SAAS,CAAC,CAAC;KAC1B;IAED,MAAM,KAAK,GAAW,aAAa,CAAC,SAAS,CAAC,CAAC;IAC/C,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAChD,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;AACnC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { replaceAll } from \"./util/utils\";\n\ntype URLQueryParseState = \"ParameterName\" | \"ParameterValue\";\n\n/**\n * A class that handles the query portion of a URLBuilder.\n */\nexport class URLQuery {\n  private readonly _rawQuery: { [queryParameterName: string]: string | string[] } = {};\n\n  /**\n   * Get whether or not there any query parameters in this URLQuery.\n   */\n  public any(): boolean {\n    return Object.keys(this._rawQuery).length > 0;\n  }\n\n  /**\n   * Get the keys of the query string.\n   */\n  public keys(): string[] {\n    return Object.keys(this._rawQuery);\n  }\n\n  /**\n   * Set a query parameter with the provided name and value. If the parameterValue is undefined or\n   * empty, then this will attempt to remove an existing query parameter with the provided\n   * parameterName.\n   */\n  public set(parameterName: string, parameterValue: unknown): void {\n    const caseParameterValue = parameterValue as {\n      toString: () => string;\n    };\n    if (parameterName) {\n      if (caseParameterValue !== undefined && caseParameterValue !== null) {\n        const newValue = Array.isArray(caseParameterValue)\n          ? caseParameterValue\n          : caseParameterValue.toString();\n        this._rawQuery[parameterName] = newValue;\n      } else {\n        delete this._rawQuery[parameterName];\n      }\n    }\n  }\n\n  /**\n   * Get the value of the query parameter with the provided name. If no parameter exists with the\n   * provided parameter name, then undefined will be returned.\n   */\n  public get(parameterName: string): string | string[] | undefined {\n    return parameterName ? this._rawQuery[parameterName] : undefined;\n  }\n\n  /**\n   * Get the string representation of this query. The return value will not start with a \"?\".\n   */\n  public toString(): string {\n    let result = \"\";\n    for (const parameterName in this._rawQuery) {\n      if (result) {\n        result += \"&\";\n      }\n      const parameterValue = this._rawQuery[parameterName];\n      if (Array.isArray(parameterValue)) {\n        const parameterStrings = [];\n        for (const parameterValueElement of parameterValue) {\n          parameterStrings.push(`${parameterName}=${parameterValueElement}`);\n        }\n        result += parameterStrings.join(\"&\");\n      } else {\n        result += `${parameterName}=${parameterValue}`;\n      }\n    }\n    return result;\n  }\n\n  /**\n   * Parse a URLQuery from the provided text.\n   */\n  public static parse(text: string): URLQuery {\n    const result = new URLQuery();\n\n    if (text) {\n      if (text.startsWith(\"?\")) {\n        text = text.substring(1);\n      }\n\n      let currentState: URLQueryParseState = \"ParameterName\";\n\n      let parameterName = \"\";\n      let parameterValue = \"\";\n      for (let i = 0; i < text.length; ++i) {\n        const currentCharacter: string = text[i];\n        switch (currentState) {\n          case \"ParameterName\":\n            switch (currentCharacter) {\n              case \"=\":\n                currentState = \"ParameterValue\";\n                break;\n\n              case \"&\":\n                parameterName = \"\";\n                parameterValue = \"\";\n                break;\n\n              default:\n                parameterName += currentCharacter;\n                break;\n            }\n            break;\n\n          case \"ParameterValue\":\n            switch (currentCharacter) {\n              case \"&\":\n                result.set(parameterName, parameterValue);\n                parameterName = \"\";\n                parameterValue = \"\";\n                currentState = \"ParameterName\";\n                break;\n\n              default:\n                parameterValue += currentCharacter;\n                break;\n            }\n            break;\n\n          default:\n            throw new Error(\"Unrecognized URLQuery parse state: \" + currentState);\n        }\n      }\n      if (currentState === \"ParameterValue\") {\n        result.set(parameterName, parameterValue);\n      }\n    }\n\n    return result;\n  }\n}\n\n/**\n * A class that handles creating, modifying, and parsing URLs.\n */\nexport class URLBuilder {\n  private _scheme: string | undefined;\n  private _host: string | undefined;\n  private _port: string | undefined;\n  private _path: string | undefined;\n  private _query: URLQuery | undefined;\n\n  /**\n   * Set the scheme/protocol for this URL. If the provided scheme contains other parts of a URL\n   * (such as a host, port, path, or query), those parts will be added to this URL as well.\n   */\n  public setScheme(scheme: string | undefined): void {\n    if (!scheme) {\n      this._scheme = undefined;\n    } else {\n      this.set(scheme, \"SCHEME\");\n    }\n  }\n\n  /**\n   * Get the scheme that has been set in this URL.\n   */\n  public getScheme(): string | undefined {\n    return this._scheme;\n  }\n\n  /**\n   * Set the host for this URL. If the provided host contains other parts of a URL (such as a\n   * port, path, or query), those parts will be added to this URL as well.\n   */\n  public setHost(host: string | undefined): void {\n    if (!host) {\n      this._host = undefined;\n    } else {\n      this.set(host, \"SCHEME_OR_HOST\");\n    }\n  }\n\n  /**\n   * Get the host that has been set in this URL.\n   */\n  public getHost(): string | undefined {\n    return this._host;\n  }\n\n  /**\n   * Set the port for this URL. If the provided port contains other parts of a URL (such as a\n   * path or query), those parts will be added to this URL as well.\n   */\n  public setPort(port: number | string | undefined): void {\n    if (port === undefined || port === null || port === \"\") {\n      this._port = undefined;\n    } else {\n      this.set(port.toString(), \"PORT\");\n    }\n  }\n\n  /**\n   * Get the port that has been set in this URL.\n   */\n  public getPort(): string | undefined {\n    return this._port;\n  }\n\n  /**\n   * Set the path for this URL. If the provided path contains a query, then it will be added to\n   * this URL as well.\n   */\n  public setPath(path: string | undefined): void {\n    if (!path) {\n      this._path = undefined;\n    } else {\n      const schemeIndex = path.indexOf(\"://\");\n      if (schemeIndex !== -1) {\n        const schemeStart = path.lastIndexOf(\"/\", schemeIndex);\n        // Make sure to only grab the URL part of the path before setting the state back to SCHEME\n        // this will handle cases such as \"/a/b/c/https://microsoft.com\" => \"https://microsoft.com\"\n        this.set(schemeStart === -1 ? path : path.substr(schemeStart + 1), \"SCHEME\");\n      } else {\n        this.set(path, \"PATH\");\n      }\n    }\n  }\n\n  /**\n   * Append the provided path to this URL's existing path. If the provided path contains a query,\n   * then it will be added to this URL as well.\n   */\n  public appendPath(path: string | undefined): void {\n    if (path) {\n      let currentPath: string | undefined = this.getPath();\n      if (currentPath) {\n        if (!currentPath.endsWith(\"/\")) {\n          currentPath += \"/\";\n        }\n\n        if (path.startsWith(\"/\")) {\n          path = path.substring(1);\n        }\n\n        path = currentPath + path;\n      }\n      this.set(path, \"PATH\");\n    }\n  }\n\n  /**\n   * Get the path that has been set in this URL.\n   */\n  public getPath(): string | undefined {\n    return this._path;\n  }\n\n  /**\n   * Set the query in this URL.\n   */\n  public setQuery(query: string | undefined): void {\n    if (!query) {\n      this._query = undefined;\n    } else {\n      this._query = URLQuery.parse(query);\n    }\n  }\n\n  /**\n   * Set a query parameter with the provided name and value in this URL's query. If the provided\n   * query parameter value is undefined or empty, then the query parameter will be removed if it\n   * existed.\n   */\n  public setQueryParameter(queryParameterName: string, queryParameterValue: unknown): void {\n    if (queryParameterName) {\n      if (!this._query) {\n        this._query = new URLQuery();\n      }\n      this._query.set(queryParameterName, queryParameterValue);\n    }\n  }\n\n  /**\n   * Get the value of the query parameter with the provided query parameter name. If no query\n   * parameter exists with the provided name, then undefined will be returned.\n   */\n  public getQueryParameterValue(queryParameterName: string): string | string[] | undefined {\n    return this._query ? this._query.get(queryParameterName) : undefined;\n  }\n\n  /**\n   * Get the query in this URL.\n   */\n  public getQuery(): string | undefined {\n    return this._query ? this._query.toString() : undefined;\n  }\n\n  /**\n   * Set the parts of this URL by parsing the provided text using the provided startState.\n   */\n  private set(text: string, startState: URLTokenizerState): void {\n    const tokenizer = new URLTokenizer(text, startState);\n\n    while (tokenizer.next()) {\n      const token: URLToken | undefined = tokenizer.current();\n      let tokenPath: string | undefined;\n      if (token) {\n        switch (token.type) {\n          case \"SCHEME\":\n            this._scheme = token.text || undefined;\n            break;\n\n          case \"HOST\":\n            this._host = token.text || undefined;\n            break;\n\n          case \"PORT\":\n            this._port = token.text || undefined;\n            break;\n\n          case \"PATH\":\n            tokenPath = token.text || undefined;\n            if (!this._path || this._path === \"/\" || tokenPath !== \"/\") {\n              this._path = tokenPath;\n            }\n            break;\n\n          case \"QUERY\":\n            this._query = URLQuery.parse(token.text);\n            break;\n\n          default:\n            throw new Error(`Unrecognized URLTokenType: ${token.type}`);\n        }\n      }\n    }\n  }\n\n  /**\n   * Serializes the URL as a string.\n   * @returns the URL as a string.\n   */\n  public toString(): string {\n    let result = \"\";\n\n    if (this._scheme) {\n      result += `${this._scheme}://`;\n    }\n\n    if (this._host) {\n      result += this._host;\n    }\n\n    if (this._port) {\n      result += `:${this._port}`;\n    }\n\n    if (this._path) {\n      if (!this._path.startsWith(\"/\")) {\n        result += \"/\";\n      }\n      result += this._path;\n    }\n\n    if (this._query && this._query.any()) {\n      result += `?${this._query.toString()}`;\n    }\n\n    return result;\n  }\n\n  /**\n   * If the provided searchValue is found in this URLBuilder, then replace it with the provided\n   * replaceValue.\n   */\n  public replaceAll(searchValue: string, replaceValue: string): void {\n    if (searchValue) {\n      this.setScheme(replaceAll(this.getScheme(), searchValue, replaceValue));\n      this.setHost(replaceAll(this.getHost(), searchValue, replaceValue));\n      this.setPort(replaceAll(this.getPort(), searchValue, replaceValue));\n      this.setPath(replaceAll(this.getPath(), searchValue, replaceValue));\n      this.setQuery(replaceAll(this.getQuery(), searchValue, replaceValue));\n    }\n  }\n\n  /**\n   * Parses a given string URL into a new {@link URLBuilder}.\n   */\n  public static parse(text: string): URLBuilder {\n    const result = new URLBuilder();\n    result.set(text, \"SCHEME_OR_HOST\");\n    return result;\n  }\n}\n\ntype URLTokenizerState = \"SCHEME\" | \"SCHEME_OR_HOST\" | \"HOST\" | \"PORT\" | \"PATH\" | \"QUERY\" | \"DONE\";\n\ntype URLTokenType = \"SCHEME\" | \"HOST\" | \"PORT\" | \"PATH\" | \"QUERY\";\n\nexport class URLToken {\n  public constructor(public readonly text: string, public readonly type: URLTokenType) {}\n\n  public static scheme(text: string): URLToken {\n    return new URLToken(text, \"SCHEME\");\n  }\n\n  public static host(text: string): URLToken {\n    return new URLToken(text, \"HOST\");\n  }\n\n  public static port(text: string): URLToken {\n    return new URLToken(text, \"PORT\");\n  }\n\n  public static path(text: string): URLToken {\n    return new URLToken(text, \"PATH\");\n  }\n\n  public static query(text: string): URLToken {\n    return new URLToken(text, \"QUERY\");\n  }\n}\n\n/**\n * Get whether or not the provided character (single character string) is an alphanumeric (letter or\n * digit) character.\n */\nexport function isAlphaNumericCharacter(character: string): boolean {\n  const characterCode: number = character.charCodeAt(0);\n  return (\n    (48 /* '0' */ <= characterCode && characterCode <= 57) /* '9' */ ||\n    (65 /* 'A' */ <= characterCode && characterCode <= 90) /* 'Z' */ ||\n    (97 /* 'a' */ <= characterCode && characterCode <= 122) /* 'z' */\n  );\n}\n\n/**\n * A class that tokenizes URL strings.\n */\nexport class URLTokenizer {\n  readonly _textLength: number;\n  _currentState: URLTokenizerState;\n  _currentIndex: number;\n  _currentToken: URLToken | undefined;\n\n  public constructor(readonly _text: string, state?: URLTokenizerState) {\n    this._textLength = _text ? _text.length : 0;\n    this._currentState = state !== undefined && state !== null ? state : \"SCHEME_OR_HOST\";\n    this._currentIndex = 0;\n  }\n\n  /**\n   * Get the current URLToken this URLTokenizer is pointing at, or undefined if the URLTokenizer\n   * hasn't started or has finished tokenizing.\n   */\n  public current(): URLToken | undefined {\n    return this._currentToken;\n  }\n\n  /**\n   * Advance to the next URLToken and return whether or not a URLToken was found.\n   */\n  public next(): boolean {\n    if (!hasCurrentCharacter(this)) {\n      this._currentToken = undefined;\n    } else {\n      switch (this._currentState) {\n        case \"SCHEME\":\n          nextScheme(this);\n          break;\n\n        case \"SCHEME_OR_HOST\":\n          nextSchemeOrHost(this);\n          break;\n\n        case \"HOST\":\n          nextHost(this);\n          break;\n\n        case \"PORT\":\n          nextPort(this);\n          break;\n\n        case \"PATH\":\n          nextPath(this);\n          break;\n\n        case \"QUERY\":\n          nextQuery(this);\n          break;\n\n        default:\n          throw new Error(`Unrecognized URLTokenizerState: ${this._currentState}`);\n      }\n    }\n    return !!this._currentToken;\n  }\n}\n\n/**\n * Read the remaining characters from this Tokenizer's character stream.\n */\nfunction readRemaining(tokenizer: URLTokenizer): string {\n  let result = \"\";\n  if (tokenizer._currentIndex < tokenizer._textLength) {\n    result = tokenizer._text.substring(tokenizer._currentIndex);\n    tokenizer._currentIndex = tokenizer._textLength;\n  }\n  return result;\n}\n\n/**\n * Whether or not this URLTokenizer has a current character.\n */\nfunction hasCurrentCharacter(tokenizer: URLTokenizer): boolean {\n  return tokenizer._currentIndex < tokenizer._textLength;\n}\n\n/**\n * Get the character in the text string at the current index.\n */\nfunction getCurrentCharacter(tokenizer: URLTokenizer): string {\n  return tokenizer._text[tokenizer._currentIndex];\n}\n\n/**\n * Advance to the character in text that is \"step\" characters ahead. If no step value is provided,\n * then step will default to 1.\n */\nfunction nextCharacter(tokenizer: URLTokenizer, step?: number): void {\n  if (hasCurrentCharacter(tokenizer)) {\n    if (!step) {\n      step = 1;\n    }\n    tokenizer._currentIndex += step;\n  }\n}\n\n/**\n * Starting with the current character, peek \"charactersToPeek\" number of characters ahead in this\n * Tokenizer's stream of characters.\n */\nfunction peekCharacters(tokenizer: URLTokenizer, charactersToPeek: number): string {\n  let endIndex: number = tokenizer._currentIndex + charactersToPeek;\n  if (tokenizer._textLength < endIndex) {\n    endIndex = tokenizer._textLength;\n  }\n  return tokenizer._text.substring(tokenizer._currentIndex, endIndex);\n}\n\n/**\n * Read characters from this Tokenizer until the end of the stream or until the provided condition\n * is false when provided the current character.\n */\nfunction readWhile(tokenizer: URLTokenizer, condition: (character: string) => boolean): string {\n  let result = \"\";\n\n  while (hasCurrentCharacter(tokenizer)) {\n    const currentCharacter: string = getCurrentCharacter(tokenizer);\n    if (!condition(currentCharacter)) {\n      break;\n    } else {\n      result += currentCharacter;\n      nextCharacter(tokenizer);\n    }\n  }\n\n  return result;\n}\n\n/**\n * Read characters from this Tokenizer until a non-alphanumeric character or the end of the\n * character stream is reached.\n */\nfunction readWhileLetterOrDigit(tokenizer: URLTokenizer): string {\n  return readWhile(tokenizer, (character: string) => isAlphaNumericCharacter(character));\n}\n\n/**\n * Read characters from this Tokenizer until one of the provided terminating characters is read or\n * the end of the character stream is reached.\n */\nfunction readUntilCharacter(tokenizer: URLTokenizer, ...terminatingCharacters: string[]): string {\n  return readWhile(\n    tokenizer,\n    (character: string) => terminatingCharacters.indexOf(character) === -1\n  );\n}\n\nfunction nextScheme(tokenizer: URLTokenizer): void {\n  const scheme: string = readWhileLetterOrDigit(tokenizer);\n  tokenizer._currentToken = URLToken.scheme(scheme);\n  if (!hasCurrentCharacter(tokenizer)) {\n    tokenizer._currentState = \"DONE\";\n  } else {\n    tokenizer._currentState = \"HOST\";\n  }\n}\n\nfunction nextSchemeOrHost(tokenizer: URLTokenizer): void {\n  const schemeOrHost: string = readUntilCharacter(tokenizer, \":\", \"/\", \"?\");\n  if (!hasCurrentCharacter(tokenizer)) {\n    tokenizer._currentToken = URLToken.host(schemeOrHost);\n    tokenizer._currentState = \"DONE\";\n  } else if (getCurrentCharacter(tokenizer) === \":\") {\n    if (peekCharacters(tokenizer, 3) === \"://\") {\n      tokenizer._currentToken = URLToken.scheme(schemeOrHost);\n      tokenizer._currentState = \"HOST\";\n    } else {\n      tokenizer._currentToken = URLToken.host(schemeOrHost);\n      tokenizer._currentState = \"PORT\";\n    }\n  } else {\n    tokenizer._currentToken = URLToken.host(schemeOrHost);\n    if (getCurrentCharacter(tokenizer) === \"/\") {\n      tokenizer._currentState = \"PATH\";\n    } else {\n      tokenizer._currentState = \"QUERY\";\n    }\n  }\n}\n\nfunction nextHost(tokenizer: URLTokenizer): void {\n  if (peekCharacters(tokenizer, 3) === \"://\") {\n    nextCharacter(tokenizer, 3);\n  }\n\n  const host: string = readUntilCharacter(tokenizer, \":\", \"/\", \"?\");\n  tokenizer._currentToken = URLToken.host(host);\n\n  if (!hasCurrentCharacter(tokenizer)) {\n    tokenizer._currentState = \"DONE\";\n  } else if (getCurrentCharacter(tokenizer) === \":\") {\n    tokenizer._currentState = \"PORT\";\n  } else if (getCurrentCharacter(tokenizer) === \"/\") {\n    tokenizer._currentState = \"PATH\";\n  } else {\n    tokenizer._currentState = \"QUERY\";\n  }\n}\n\nfunction nextPort(tokenizer: URLTokenizer): void {\n  if (getCurrentCharacter(tokenizer) === \":\") {\n    nextCharacter(tokenizer);\n  }\n\n  const port: string = readUntilCharacter(tokenizer, \"/\", \"?\");\n  tokenizer._currentToken = URLToken.port(port);\n\n  if (!hasCurrentCharacter(tokenizer)) {\n    tokenizer._currentState = \"DONE\";\n  } else if (getCurrentCharacter(tokenizer) === \"/\") {\n    tokenizer._currentState = \"PATH\";\n  } else {\n    tokenizer._currentState = \"QUERY\";\n  }\n}\n\nfunction nextPath(tokenizer: URLTokenizer): void {\n  const path: string = readUntilCharacter(tokenizer, \"?\");\n  tokenizer._currentToken = URLToken.path(path);\n\n  if (!hasCurrentCharacter(tokenizer)) {\n    tokenizer._currentState = \"DONE\";\n  } else {\n    tokenizer._currentState = \"QUERY\";\n  }\n}\n\nfunction nextQuery(tokenizer: URLTokenizer): void {\n  if (getCurrentCharacter(tokenizer) === \"?\") {\n    nextCharacter(tokenizer);\n  }\n\n  const query: string = readRemaining(tokenizer);\n  tokenizer._currentToken = URLToken.query(query);\n  tokenizer._currentState = \"DONE\";\n}\n"]}