{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAmBlC,OAAO,EAAE,eAAe,EAAE,MAAM,gBAAgB,CAAC;AACjD,OAAO,EAAE,mBAAmB,EAAE,MAAM,iBAAiB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nexport {\n  Instrumenter,\n  InstrumenterSpanOptions,\n  OperationTracingOptions,\n  OptionsWithTracingContext,\n  Resolved,\n  SpanStatus,\n  SpanStatusError,\n  SpanStatusSuccess,\n  TracingClient,\n  TracingClientOptions,\n  TracingContext,\n  TracingSpan,\n  TracingSpanKind,\n  TracingSpanLink,\n  TracingSpanOptions,\n} from \"./interfaces\";\nexport { useInstrumenter } from \"./instrumenter\";\nexport { createTracingClient } from \"./tracingClient\";\n"]}