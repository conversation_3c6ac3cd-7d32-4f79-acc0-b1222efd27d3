{"version": 3, "file": "index.js", "sources": ["../src/interfaces.ts", "../src/createSpan.ts", "../src/utils/traceParentHeader.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { context as otContext, trace as otTrace } from \"@opentelemetry/api\";\n\n/**\n * A Tracer.\n */\nexport interface Tracer {\n  /**\n   * Starts a new {@link Span}. Start the span without setting it on context.\n   *\n   * This method does NOT modify the current Context.\n   *\n   * @param name - The name of the span\n   * @param options - SpanOptions used for span creation\n   * @param context - Context to use to extract parent\n   * @returns The newly created span\n   * @example\n   *     const span = tracer.startSpan('op');\n   *     span.setAttribute('key', 'value');\n   *     span.end();\n   */\n  startSpan(name: string, options?: SpanOptions, context?: Context): Span;\n}\n\n/**\n * TraceState.\n */\nexport interface TraceState {\n  /**\n   * Create a new TraceState which inherits from this TraceState and has the\n   * given key set.\n   * The new entry will always be added in the front of the list of states.\n   *\n   * @param key - key of the TraceState entry.\n   * @param value - value of the TraceState entry.\n   */\n  set(key: string, value: string): TraceState;\n  /**\n   * Return a new TraceState which inherits from this TraceState but does not\n   * contain the given key.\n   *\n   * @param key - the key for the TraceState entry to be removed.\n   */\n  unset(key: string): TraceState;\n  /**\n   * Returns the value to which the specified key is mapped, or `undefined` if\n   * this map contains no mapping for the key.\n   *\n   * @param key - with which the specified value is to be associated.\n   * @returns the value to which the specified key is mapped, or `undefined` if\n   *     this map contains no mapping for the key.\n   */\n  get(key: string): string | undefined;\n  /**\n   * Serializes the TraceState to a `list` as defined below. The `list` is a\n   * series of `list-members` separated by commas `,`, and a list-member is a\n   * key/value pair separated by an equals sign `=`. Spaces and horizontal tabs\n   * surrounding `list-members` are ignored. There can be a maximum of 32\n   * `list-members` in a `list`.\n   *\n   * @returns the serialized string.\n   */\n  serialize(): string;\n}\n\n/**\n * Represents high resolution time.\n */\nexport declare type HrTime = [number, number];\n\n/**\n * Used to represent a Time.\n */\nexport type TimeInput = HrTime | number | Date;\n\n/**\n * The status for a span.\n */\nexport interface SpanStatus {\n  /** The status code of this message. */\n  code: SpanStatusCode;\n  /** A developer-facing error message. */\n  message?: string;\n}\n\n/**\n * The kind of span.\n */\nexport enum SpanKind {\n  /** Default value. Indicates that the span is used internally. */\n  INTERNAL = 0,\n  /**\n   * Indicates that the span covers server-side handling of an RPC or other\n   * remote request.\n   */\n  SERVER = 1,\n  /**\n   * Indicates that the span covers the client-side wrapper around an RPC or\n   * other remote request.\n   */\n  CLIENT = 2,\n  /**\n   * Indicates that the span describes producer sending a message to a\n   * broker. Unlike client and server, there is no direct critical path latency\n   * relationship between producer and consumer spans.\n   */\n  PRODUCER = 3,\n  /**\n   * Indicates that the span describes consumer receiving a message from a\n   * broker. Unlike client and server, there is no direct critical path latency\n   * relationship between producer and consumer spans.\n   */\n  CONSUMER = 4\n}\n\n/**\n * An Exception for a Span.\n */\nexport declare type Exception =\n  | ExceptionWithCode\n  | ExceptionWithMessage\n  | ExceptionWithName\n  | string;\n\n/**\n * An Exception with a code.\n */\nexport interface ExceptionWithCode {\n  /** The code. */\n  code: string | number;\n  /** The name. */\n  name?: string;\n  /** The message. */\n  message?: string;\n  /** The stack. */\n  stack?: string;\n}\n\n/**\n * An Exception with a message.\n */\nexport interface ExceptionWithMessage {\n  /** The code. */\n  code?: string | number;\n  /** The message. */\n  message: string;\n  /** The name. */\n  name?: string;\n  /** The stack. */\n  stack?: string;\n}\n\n/**\n * An Exception with a name.\n */\nexport interface ExceptionWithName {\n  /** The code. */\n  code?: string | number;\n  /** The message. */\n  message?: string;\n  /** The name. */\n  name: string;\n  /** The stack. */\n  stack?: string;\n}\n\n/**\n * Return the span if one exists\n *\n * @param context - context to get span from\n */\nexport function getSpan(context: Context): Span | undefined {\n  return otTrace.getSpan(context);\n}\n\n/**\n * Set the span on a context\n *\n * @param context - context to use as parent\n * @param span - span to set active\n */\nexport function setSpan(context: Context, span: Span): Context {\n  return otTrace.setSpan(context, span);\n}\n\n/**\n * Wrap span context in a NoopSpan and set as span in a new\n * context\n *\n * @param context - context to set active span on\n * @param spanContext - span context to be wrapped\n */\nexport function setSpanContext(context: Context, spanContext: SpanContext): Context {\n  return otTrace.setSpanContext(context, spanContext);\n}\n\n/**\n * Get the span context of the span if it exists.\n *\n * @param context - context to get values from\n */\nexport function getSpanContext(context: Context): SpanContext | undefined {\n  return otTrace.getSpanContext(context);\n}\n\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Context API\n */\nexport interface ContextAPI {\n  /**\n   * Get the currently active context\n   */\n  active(): Context;\n}\n\n/**\n * Returns true of the given {@link SpanContext} is valid.\n * A valid {@link SpanContext} is one which has a valid trace ID and span ID as per the spec.\n *\n * @param context - the {@link SpanContext} to validate.\n *\n * @returns true if the {@link SpanContext} is valid, false otherwise.\n */\nexport function isSpanContextValid(context: SpanContext): boolean {\n  return otTrace.isSpanContextValid(context);\n}\n\n/**\n * Retrieves a tracer from the global tracer provider.\n */\nexport function getTracer(): Tracer;\n/**\n * Retrieves a tracer from the global tracer provider.\n */\nexport function getTracer(name: string, version?: string): Tracer;\nexport function getTracer(name?: string, version?: string): Tracer {\n  return otTrace.getTracer(name || \"azure/core-tracing\", version);\n}\n\n/** Entrypoint for context API */\nexport const context: ContextAPI = otContext;\n\n/** SpanStatusCode */\nexport enum SpanStatusCode {\n  /**\n   * The default status.\n   */\n  UNSET = 0,\n  /**\n   * The operation has been validated by an Application developer or\n   * Operator to have completed successfully.\n   */\n  OK = 1,\n  /**\n   * The operation contains an error.\n   */\n  ERROR = 2\n}\n\n/**\n * An interface that represents a span. A span represents a single operation\n * within a trace. Examples of span might include remote procedure calls or a\n * in-process function calls to sub-components. A Trace has a single, top-level\n * \"root\" Span that in turn may have zero or more child Spans, which in turn\n * may have children.\n *\n * Spans are created by the {@link Tracer.startSpan} method.\n */\nexport interface Span {\n  /**\n   * Returns the {@link SpanContext} object associated with this Span.\n   *\n   * Get an immutable, serializable identifier for this span that can be used\n   * to create new child spans. Returned SpanContext is usable even after the\n   * span ends.\n   *\n   * @returns the SpanContext object associated with this Span.\n   */\n  spanContext(): SpanContext;\n  /**\n   * Sets an attribute to the span.\n   *\n   * Sets a single Attribute with the key and value passed as arguments.\n   *\n   * @param key - the key for this attribute.\n   * @param value - the value for this attribute. Setting a value null or\n   *              undefined is invalid and will result in undefined behavior.\n   */\n  setAttribute(key: string, value: SpanAttributeValue): this;\n  /**\n   * Sets attributes to the span.\n   *\n   * @param attributes - the attributes that will be added.\n   *                   null or undefined attribute values\n   *                   are invalid and will result in undefined behavior.\n   */\n  setAttributes(attributes: SpanAttributes): this;\n  /**\n   * Adds an event to the Span.\n   *\n   * @param name - the name of the event.\n   * @param attributesOrStartTime -  the attributes that will be added; these are\n   *     associated with this event. Can be also a start time\n   *     if type is TimeInput and 3rd param is undefined\n   * @param startTime - start time of the event.\n   */\n  addEvent(\n    name: string,\n    attributesOrStartTime?: SpanAttributes | TimeInput,\n    startTime?: TimeInput\n  ): this;\n  /**\n   * Sets a status to the span. If used, this will override the default Span\n   * status. Default is {@link SpanStatusCode.UNSET}. SetStatus overrides the value\n   * of previous calls to SetStatus on the Span.\n   *\n   * @param status - the SpanStatus to set.\n   */\n  setStatus(status: SpanStatus): this;\n  /**\n   * Marks the end of Span execution.\n   *\n   * Call to End of a Span MUST not have any effects on child spans. Those may\n   * still be running and can be ended later.\n   *\n   * Do not return `this`. The Span generally should not be used after it\n   * is ended so chaining is not desired in this context.\n   *\n   * @param endTime - the time to set as Span's end time. If not provided,\n   *     use the current time as the span's end time.\n   */\n  end(endTime?: TimeInput): void;\n  /**\n   * Returns the flag whether this span will be recorded.\n   *\n   * @returns true if this Span is active and recording information like events\n   *     with the `AddEvent` operation and attributes using `setAttributes`.\n   */\n  isRecording(): boolean;\n\n  /**\n   * Sets exception as a span event\n   * @param exception - the exception the only accepted values are string or Error\n   * @param time - the time to set as Span's event time. If not provided,\n   *     use the current time.\n   */\n  recordException(exception: Exception, time?: TimeInput): void;\n\n  /**\n   * Updates the Span name.\n   *\n   * This will override the name provided via {@link Tracer.startSpan}.\n   *\n   * Upon this update, any sampling behavior based on Span name will depend on\n   * the implementation.\n   *\n   * @param name - the Span name.\n   */\n  updateName(name: string): this;\n}\n\n/**\n * Shorthand enum for common traceFlags values inside SpanContext\n */\nexport const enum TraceFlags {\n  /** No flag set. */\n  NONE = 0x0,\n  /** Caller is collecting trace information. */\n  SAMPLED = 0x1\n}\n\n/**\n * A light interface that tries to be structurally compatible with OpenTelemetry\n */\nexport interface SpanContext {\n  /**\n   * UUID of a trace.\n   */\n  traceId: string;\n  /**\n   * UUID of a Span.\n   */\n  spanId: string;\n  /**\n   * https://www.w3.org/TR/trace-context/#trace-flags\n   */\n  traceFlags: number;\n  /**\n   * Tracing-system-specific info to propagate.\n   *\n   * The tracestate field value is a `list` as defined below. The `list` is a\n   * series of `list-members` separated by commas `,`, and a list-member is a\n   * key/value pair separated by an equals sign `=`. Spaces and horizontal tabs\n   * surrounding `list-members` are ignored. There can be a maximum of 32\n   * `list-members` in a `list`.\n   * More Info: https://www.w3.org/TR/trace-context/#tracestate-field\n   *\n   * Examples:\n   *     Single tracing system (generic format):\n   *         tracestate: rojo=00f067aa0ba902b7\n   *     Multiple tracing systems (with different formatting):\n   *         tracestate: rojo=00f067aa0ba902b7,congo=t61rcWkgMzE\n   */\n  traceState?: TraceState;\n}\n\n/**\n * Used to specify a span that is linked to another.\n */\nexport interface Link {\n  /** The {@link SpanContext} of a linked span. */\n  context: SpanContext;\n\n  /** A set of {@link SpanAttributes} on the link. */\n  attributes?: SpanAttributes;\n}\n\n/**\n * Attributes for a Span.\n */\nexport interface SpanAttributes {\n  /**\n   * Attributes for a Span.\n   */\n  [attributeKey: string]: SpanAttributeValue | undefined;\n}\n/**\n * Attribute values may be any non-nullish primitive value except an object.\n *\n * null or undefined attribute values are invalid and will result in undefined behavior.\n */\nexport declare type SpanAttributeValue =\n  | string\n  | number\n  | boolean\n  | Array<null | undefined | string>\n  | Array<null | undefined | number>\n  | Array<null | undefined | boolean>;\n\n/**\n * An interface that enables manual propagation of Spans\n */\nexport interface SpanOptions {\n  /**\n   * Attributes to set on the Span\n   */\n  attributes?: SpanAttributes;\n\n  /** {@link Link}s span to other spans */\n  links?: Link[];\n\n  /**\n   * The type of Span. Default to SpanKind.INTERNAL\n   */\n  kind?: SpanKind;\n\n  /**\n   * A manually specified start time for the created `Span` object.\n   */\n  startTime?: TimeInput;\n}\n\n/**\n * Tracing options to set on an operation.\n */\nexport interface OperationTracingOptions {\n  /**\n   * OpenTelemetry SpanOptions used to create a span when tracing is enabled.\n   */\n  spanOptions?: SpanOptions;\n\n  /**\n   * OpenTelemetry context to use for created Spans.\n   */\n  tracingContext?: Context;\n}\n\n/**\n * OpenTelemetry compatible interface for Context\n */\nexport interface Context {\n  /**\n   * Get a value from the context.\n   *\n   * @param key - key which identifies a context value\n   */\n  getValue(key: symbol): unknown;\n  /**\n   * Create a new context which inherits from this context and has\n   * the given key set to the given value.\n   *\n   * @param key - context key for which to set the value\n   * @param value - value to set for the given key\n   */\n  setValue(key: symbol, value: unknown): Context;\n  /**\n   * Return a new context which inherits from this context but does\n   * not contain a value for the given key.\n   *\n   * @param key - context key for which to clear a value\n   */\n  deleteValue(key: symbol): Context;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  OperationTracingOptions,\n  Span,\n  SpanOptions,\n  SpanKind,\n  setSpan,\n  context as otContext,\n  getTracer\n} from \"./interfaces\";\nimport { trace, INVALID_SPAN_CONTEXT } from \"@opentelemetry/api\";\n\n/**\n * Arguments for `createSpanFunction` that allow you to specify the\n * prefix for each created span as well as the `az.namespace` attribute.\n *\n * @hidden\n */\nexport interface CreateSpanFunctionArgs {\n  /**\n   * Package name prefix.\n   *\n   * NOTE: if this is empty no prefix will be applied to created Span names.\n   */\n  packagePrefix: string;\n  /**\n   * Service namespace\n   *\n   * NOTE: if this is empty no `az.namespace` attribute will be added to created Spans.\n   */\n  namespace: string;\n}\n\nexport function isTracingDisabled(): boolean {\n  if (typeof process === \"undefined\") {\n    // not supported in browser for now without polyfills\n    return false;\n  }\n\n  const azureTracingDisabledValue = process.env.AZURE_TRACING_DISABLED?.toLowerCase();\n\n  if (azureTracingDisabledValue === \"false\" || azureTracingDisabledValue === \"0\") {\n    return false;\n  }\n\n  return Boolean(azureTracingDisabledValue);\n}\n\n/**\n * Creates a function that can be used to create spans using the global tracer.\n *\n * Usage:\n *\n * ```typescript\n * // once\n * const createSpan = createSpanFunction({ packagePrefix: \"Azure.Data.AppConfiguration\", namespace: \"Microsoft.AppConfiguration\" });\n *\n * // in each operation\n * const span = createSpan(\"deleteConfigurationSetting\", operationOptions);\n *    // code...\n * span.end();\n * ```\n *\n * @hidden\n * @param args - allows configuration of the prefix for each span as well as the az.namespace field.\n */\nexport function createSpanFunction(args: CreateSpanFunctionArgs) {\n  return function<T extends { tracingOptions?: OperationTracingOptions }>(\n    operationName: string,\n    operationOptions: T | undefined\n  ): { span: Span; updatedOptions: T } {\n    const tracer = getTracer();\n    const tracingOptions = operationOptions?.tracingOptions || {};\n    const spanOptions: SpanOptions = {\n      kind: SpanKind.INTERNAL,\n      ...tracingOptions.spanOptions\n    };\n\n    const spanName = args.packagePrefix ? `${args.packagePrefix}.${operationName}` : operationName;\n\n    let span: Span;\n    if (isTracingDisabled()) {\n      span = trace.wrapSpanContext(INVALID_SPAN_CONTEXT);\n    } else {\n      span = tracer.startSpan(spanName, spanOptions, tracingOptions.tracingContext);\n    }\n\n    if (args.namespace) {\n      span.setAttribute(\"az.namespace\", args.namespace);\n    }\n\n    let newSpanOptions = tracingOptions.spanOptions || {};\n\n    if (span.isRecording() && args.namespace) {\n      newSpanOptions = {\n        ...tracingOptions.spanOptions,\n        attributes: {\n          ...spanOptions.attributes,\n          \"az.namespace\": args.namespace\n        }\n      };\n    }\n\n    const newTracingOptions: Required<OperationTracingOptions> = {\n      ...tracingOptions,\n      spanOptions: newSpanOptions,\n      tracingContext: setSpan(tracingOptions.tracingContext || otContext.active(), span)\n    };\n\n    const newOperationOptions = {\n      ...operationOptions,\n      tracingOptions: newTracingOptions\n    } as T & { tracingOptions: Required<OperationTracingOptions> };\n\n    return {\n      span,\n      updatedOptions: newOperationOptions\n    };\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { SpanContext, TraceFlags } from \"../interfaces\";\n\nconst VERSION = \"00\";\n\n/**\n * Generates a `SpanContext` given a `traceparent` header value.\n * @param traceParent - Serialized span context data as a `traceparent` header value.\n * @returns The `SpanContext` generated from the `traceparent` value.\n */\nexport function extractSpanContextFromTraceParentHeader(\n  traceParentHeader: string\n): SpanContext | undefined {\n  const parts = traceParentHeader.split(\"-\");\n\n  if (parts.length !== 4) {\n    return;\n  }\n\n  const [version, traceId, spanId, traceOptions] = parts;\n\n  if (version !== VERSION) {\n    return;\n  }\n\n  const traceFlags = parseInt(traceOptions, 16);\n\n  const spanContext: SpanContext = {\n    spanId,\n    traceId,\n    traceFlags\n  };\n\n  return spanContext;\n}\n\n/**\n * Generates a `traceparent` value given a span context.\n * @param spanContext - Contains context for a specific span.\n * @returns The `spanContext` represented as a `traceparent` value.\n */\nexport function getTraceParentHeader(spanContext: SpanContext): string | undefined {\n  const missingFields: string[] = [];\n  if (!spanContext.traceId) {\n    missingFields.push(\"traceId\");\n  }\n  if (!spanContext.spanId) {\n    missingFields.push(\"spanId\");\n  }\n\n  if (missingFields.length) {\n    return;\n  }\n\n  const flags = spanContext.traceFlags || TraceFlags.NONE;\n  const hexFlags = flags.toString(16);\n  const traceFlags = hexFlags.length === 1 ? `0${hexFlags}` : hexFlags;\n\n  // https://www.w3.org/TR/trace-context/#traceparent-header-field-values\n  return `${VERSION}-${spanContext.traceId}-${spanContext.spanId}-${traceFlags}`;\n}\n"], "names": ["SpanKind", "otTrace", "otContext", "SpanStatusCode", "trace", "INVALID_SPAN_CONTEXT"], "mappings": ";;;;;;AAAA;AA0FA,WAAY,QAAQ;;IAElB,+CAAY,CAAA;;;;;IAKZ,2CAAU,CAAA;;;;;IAKV,2CAAU,CAAA;;;;;;IAMV,+CAAY,CAAA;;;;;;IAMZ,+CAAY,CAAA;AACd,CAAC,EAzBWA,gBAAQ,KAARA,gBAAQ,QAyBnB;AAqDD;;;;;SAKgB,OAAO,CAAC,OAAgB;IACtC,OAAOC,SAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAClC,CAAC;AAED;;;;;;SAMgB,OAAO,CAAC,OAAgB,EAAE,IAAU;IAClD,OAAOA,SAAO,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACxC,CAAC;AAED;;;;;;;SAOgB,cAAc,CAAC,OAAgB,EAAE,WAAwB;IACvE,OAAOA,SAAO,CAAC,cAAc,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;AACtD,CAAC;AAED;;;;;SAKgB,cAAc,CAAC,OAAgB;IAC7C,OAAOA,SAAO,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AACzC,CAAC;AAYD;;;;;;;;SAQgB,kBAAkB,CAAC,OAAoB;IACrD,OAAOA,SAAO,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;AAC7C,CAAC;SAUe,SAAS,CAAC,IAAa,EAAE,OAAgB;IACvD,OAAOA,SAAO,CAAC,SAAS,CAAC,IAAI,IAAI,oBAAoB,EAAE,OAAO,CAAC,CAAC;AAClE,CAAC;AAED;MACa,OAAO,GAAeC,YAAU;AAG7C,WAAY,cAAc;;;;IAIxB,qDAAS,CAAA;;;;;IAKT,+CAAM,CAAA;;;;IAIN,qDAAS,CAAA;AACX,CAAC,EAdWC,sBAAc,KAAdA,sBAAc;;ACrP1B;AACA,SAkCgB,iBAAiB;;IAC/B,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;;QAElC,OAAO,KAAK,CAAC;KACd;IAED,MAAM,yBAAyB,GAAG,MAAA,OAAO,CAAC,GAAG,CAAC,sBAAsB,0CAAE,WAAW,EAAE,CAAC;IAEpF,IAAI,yBAAyB,KAAK,OAAO,IAAI,yBAAyB,KAAK,GAAG,EAAE;QAC9E,OAAO,KAAK,CAAC;KACd;IAED,OAAO,OAAO,CAAC,yBAAyB,CAAC,CAAC;AAC5C,CAAC;AAED;;;;;;;;;;;;;;;;;;AAkBA,SAAgB,kBAAkB,CAAC,IAA4B;IAC7D,OAAO,UACL,aAAqB,EACrB,gBAA+B;QAE/B,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;QAC3B,MAAM,cAAc,GAAG,CAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,cAAc,KAAI,EAAE,CAAC;QAC9D,MAAM,WAAW,mBACf,IAAI,EAAEH,gBAAQ,CAAC,QAAQ,IACpB,cAAc,CAAC,WAAW,CAC9B,CAAC;QAEF,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,GAAG,GAAG,IAAI,CAAC,aAAa,IAAI,aAAa,EAAE,GAAG,aAAa,CAAC;QAE/F,IAAI,IAAU,CAAC;QACf,IAAI,iBAAiB,EAAE,EAAE;YACvB,IAAI,GAAGI,SAAK,CAAC,eAAe,CAACC,wBAAoB,CAAC,CAAC;SACpD;aAAM;YACL,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,WAAW,EAAE,cAAc,CAAC,cAAc,CAAC,CAAC;SAC/E;QAED,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;SACnD;QAED,IAAI,cAAc,GAAG,cAAc,CAAC,WAAW,IAAI,EAAE,CAAC;QAEtD,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE;YACxC,cAAc,mCACT,cAAc,CAAC,WAAW,KAC7B,UAAU,kCACL,WAAW,CAAC,UAAU,KACzB,cAAc,EAAE,IAAI,CAAC,SAAS,MAEjC,CAAC;SACH;QAED,MAAM,iBAAiB,mCAClB,cAAc,KACjB,WAAW,EAAE,cAAc,EAC3B,cAAc,EAAE,OAAO,CAAC,cAAc,CAAC,cAAc,IAAIH,OAAS,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,GACnF,CAAC;QAEF,MAAM,mBAAmB,GAAG,gCACvB,gBAAgB,KACnB,cAAc,EAAE,iBAAiB,GAC2B,CAAC;QAE/D,OAAO;YACL,IAAI;YACJ,cAAc,EAAE,mBAAmB;SACpC,CAAC;KACH,CAAC;AACJ,CAAC;;ACzHD;AACA;AAIA,MAAM,OAAO,GAAG,IAAI,CAAC;AAErB;;;;;AAKA,SAAgB,uCAAuC,CACrD,iBAAyB;IAEzB,MAAM,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAE3C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO;KACR;IAED,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,CAAC,GAAG,KAAK,CAAC;IAEvD,IAAI,OAAO,KAAK,OAAO,EAAE;QACvB,OAAO;KACR;IAED,MAAM,UAAU,GAAG,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;IAE9C,MAAM,WAAW,GAAgB;QAC/B,MAAM;QACN,OAAO;QACP,UAAU;KACX,CAAC;IAEF,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;;;;AAKA,SAAgB,oBAAoB,CAAC,WAAwB;IAC3D,MAAM,aAAa,GAAa,EAAE,CAAC;IACnC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;QACxB,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAC/B;IACD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;QACvB,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC9B;IAED,IAAI,aAAa,CAAC,MAAM,EAAE;QACxB,OAAO;KACR;IAED,MAAM,KAAK,GAAG,WAAW,CAAC,UAAU,iBAAoB;IACxD,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACpC,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI,QAAQ,EAAE,GAAG,QAAQ,CAAC;;IAGrE,OAAO,GAAG,OAAO,IAAI,WAAW,CAAC,OAAO,IAAI,WAAW,CAAC,MAAM,IAAI,UAAU,EAAE,CAAC;AACjF,CAAC;;;;;;;;;;;;;"}