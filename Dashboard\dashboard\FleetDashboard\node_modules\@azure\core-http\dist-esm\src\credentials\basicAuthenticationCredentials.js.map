{"version": 3, "file": "basicAuthenticationCredentials.js", "sourceRoot": "", "sources": ["../../../src/credentials/basicAuthenticationCredentials.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,KAAK,MAAM,MAAM,gBAAgB,CAAC;AACzC,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAC9C,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAI7C,MAAM,eAAe,GAAG,SAAS,CAAC,eAAe,CAAC;AAClD,MAAM,4BAA4B,GAAG,OAAO,CAAC;AAE7C;;GAEG;AACH,MAAM,OAAO,8BAA8B;IAiBzC;;;;;;OAMG;IACH,YACE,QAAgB,EAChB,QAAgB,EAChB,sBAA8B,4BAA4B;QAhB5D;;;WAGG;QACH,wBAAmB,GAAW,4BAA4B,CAAC;QAczD,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,SAAS,IAAI,OAAO,QAAQ,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;YACzF,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;SACrF;QACD,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,SAAS,IAAI,OAAO,QAAQ,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;YACzF,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;SACrF;QACD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;IACjD,CAAC;IAED;;;;;OAKG;IACH,WAAW,CAAC,WAA4B;QACtC,MAAM,WAAW,GAAG,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;QACxD,MAAM,kBAAkB,GAAG,GAAG,IAAI,CAAC,mBAAmB,IAAI,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,CAAC;QAC7F,IAAI,CAAC,WAAW,CAAC,OAAO;YAAE,WAAW,CAAC,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC;QAC3E,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IACtC,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as base64 from \"../util/base64\";\nimport { Constants } from \"../util/constants\";\nimport { HttpHeaders } from \"../httpHeaders\";\nimport { ServiceClientCredentials } from \"./serviceClientCredentials\";\nimport { WebResourceLike } from \"../webResource\";\n\nconst HeaderConstants = Constants.HeaderConstants;\nconst DEFAULT_AUTHORIZATION_SCHEME = \"Basic\";\n\n/**\n * A simple {@link ServiceClientCredential} that authenticates with a username and a password.\n */\nexport class BasicAuthenticationCredentials implements ServiceClientCredentials {\n  /**\n   * Username\n   */\n  userName: string;\n\n  /**\n   * Password\n   */\n  password: string;\n\n  /**\n   * Authorization scheme. Defaults to \"Basic\".\n   * More information about authorization schemes is available here: https://developer.mozilla.org/docs/Web/HTTP/Authentication#authentication_schemes\n   */\n  authorizationScheme: string = DEFAULT_AUTHORIZATION_SCHEME;\n\n  /**\n   * Creates a new BasicAuthenticationCredentials object.\n   *\n   * @param userName - User name.\n   * @param password - Password.\n   * @param authorizationScheme - The authorization scheme.\n   */\n  constructor(\n    userName: string,\n    password: string,\n    authorizationScheme: string = DEFAULT_AUTHORIZATION_SCHEME\n  ) {\n    if (userName === null || userName === undefined || typeof userName.valueOf() !== \"string\") {\n      throw new Error(\"userName cannot be null or undefined and must be of type string.\");\n    }\n    if (password === null || password === undefined || typeof password.valueOf() !== \"string\") {\n      throw new Error(\"password cannot be null or undefined and must be of type string.\");\n    }\n    this.userName = userName;\n    this.password = password;\n    this.authorizationScheme = authorizationScheme;\n  }\n\n  /**\n   * Signs a request with the Authentication header.\n   *\n   * @param webResource - The WebResourceLike to be signed.\n   * @returns The signed request object.\n   */\n  signRequest(webResource: WebResourceLike): Promise<WebResourceLike> {\n    const credentials = `${this.userName}:${this.password}`;\n    const encodedCredentials = `${this.authorizationScheme} ${base64.encodeString(credentials)}`;\n    if (!webResource.headers) webResource.headers = new HttpHeaders();\n    webResource.headers.set(HeaderConstants.AUTHORIZATION, encodedCredentials);\n    return Promise.resolve(webResource);\n  }\n}\n"]}