{"version": 3, "file": "userAgentPlatform.js", "sourceRoot": "", "sources": ["../../../src/util/userAgentPlatform.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AAEzB;;GAEG;AACH,MAAM,UAAU,aAAa;IAC3B,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,uBAAuB,CAAC,GAAwB;IAC9D,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IACjC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAC/D,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as os from \"os\";\n\n/**\n * @internal\n */\nexport function getHeaderName(): string {\n  return \"User-Agent\";\n}\n\n/**\n * @internal\n */\nexport function setPlatformSpecificData(map: Map<string, string>): void {\n  map.set(\"Node\", process.version);\n  map.set(\"OS\", `(${os.arch()}-${os.type()}-${os.release()})`);\n}\n"]}