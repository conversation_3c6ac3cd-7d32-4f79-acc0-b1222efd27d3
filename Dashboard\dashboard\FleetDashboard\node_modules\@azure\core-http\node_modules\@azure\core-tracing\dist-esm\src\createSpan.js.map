{"version": 3, "file": "createSpan.js", "sourceRoot": "", "sources": ["../../src/createSpan.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAIL,QAAQ,EACR,OAAO,EACP,OAAO,IAAI,SAAS,EACpB,SAAS,EACV,MAAM,cAAc,CAAC;AACtB,OAAO,EAAE,KAAK,EAAE,oBAAoB,EAAE,MAAM,oBAAoB,CAAC;AAuBjE,MAAM,UAAU,iBAAiB;;IAC/B,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;QAClC,qDAAqD;QACrD,OAAO,KAAK,CAAC;KACd;IAED,MAAM,yBAAyB,GAAG,MAAA,OAAO,CAAC,GAAG,CAAC,sBAAsB,0CAAE,WAAW,EAAE,CAAC;IAEpF,IAAI,yBAAyB,KAAK,OAAO,IAAI,yBAAyB,KAAK,GAAG,EAAE;QAC9E,OAAO,KAAK,CAAC;KACd;IAED,OAAO,OAAO,CAAC,yBAAyB,CAAC,CAAC;AAC5C,CAAC;AAED;;;;;;;;;;;;;;;;;GAiBG;AACH,MAAM,UAAU,kBAAkB,CAAC,IAA4B;IAC7D,OAAO,UACL,aAAqB,EACrB,gBAA+B;QAE/B,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;QAC3B,MAAM,cAAc,GAAG,CAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,cAAc,KAAI,EAAE,CAAC;QAC9D,MAAM,WAAW,mBACf,IAAI,EAAE,QAAQ,CAAC,QAAQ,IACpB,cAAc,CAAC,WAAW,CAC9B,CAAC;QAEF,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,IAAI,aAAa,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC;QAE/F,IAAI,IAAU,CAAC;QACf,IAAI,iBAAiB,EAAE,EAAE;YACvB,IAAI,GAAG,KAAK,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC;SACpD;aAAM;YACL,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,WAAW,EAAE,cAAc,CAAC,cAAc,CAAC,CAAC;SAC/E;QAED,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;SACnD;QAED,IAAI,cAAc,GAAG,cAAc,CAAC,WAAW,IAAI,EAAE,CAAC;QAEtD,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE;YACxC,cAAc,mCACT,cAAc,CAAC,WAAW,KAC7B,UAAU,kCACL,WAAW,CAAC,UAAU,KACzB,cAAc,EAAE,IAAI,CAAC,SAAS,MAEjC,CAAC;SACH;QAED,MAAM,iBAAiB,mCAClB,cAAc,KACjB,WAAW,EAAE,cAAc,EAC3B,cAAc,EAAE,OAAO,CAAC,cAAc,CAAC,cAAc,IAAI,SAAS,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,GACnF,CAAC;QAEF,MAAM,mBAAmB,GAAG,gCACvB,gBAAgB,KACnB,cAAc,EAAE,iBAAiB,GAC2B,CAAC;QAE/D,OAAO;YACL,IAAI;YACJ,cAAc,EAAE,mBAAmB;SACpC,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  OperationTracingOptions,\n  Span,\n  SpanOptions,\n  SpanKind,\n  setSpan,\n  context as otContext,\n  getTracer\n} from \"./interfaces\";\nimport { trace, INVALID_SPAN_CONTEXT } from \"@opentelemetry/api\";\n\n/**\n * Arguments for `createSpanFunction` that allow you to specify the\n * prefix for each created span as well as the `az.namespace` attribute.\n *\n * @hidden\n */\nexport interface CreateSpanFunctionArgs {\n  /**\n   * Package name prefix.\n   *\n   * NOTE: if this is empty no prefix will be applied to created Span names.\n   */\n  packagePrefix: string;\n  /**\n   * Service namespace\n   *\n   * NOTE: if this is empty no `az.namespace` attribute will be added to created Spans.\n   */\n  namespace: string;\n}\n\nexport function isTracingDisabled(): boolean {\n  if (typeof process === \"undefined\") {\n    // not supported in browser for now without polyfills\n    return false;\n  }\n\n  const azureTracingDisabledValue = process.env.AZURE_TRACING_DISABLED?.toLowerCase();\n\n  if (azureTracingDisabledValue === \"false\" || azureTracingDisabledValue === \"0\") {\n    return false;\n  }\n\n  return Boolean(azureTracingDisabledValue);\n}\n\n/**\n * Creates a function that can be used to create spans using the global tracer.\n *\n * Usage:\n *\n * ```typescript\n * // once\n * const createSpan = createSpanFunction({ packagePrefix: \"Azure.Data.AppConfiguration\", namespace: \"Microsoft.AppConfiguration\" });\n *\n * // in each operation\n * const span = createSpan(\"deleteConfigurationSetting\", operationOptions);\n *    // code...\n * span.end();\n * ```\n *\n * @hidden\n * @param args - allows configuration of the prefix for each span as well as the az.namespace field.\n */\nexport function createSpanFunction(args: CreateSpanFunctionArgs) {\n  return function<T extends { tracingOptions?: OperationTracingOptions }>(\n    operationName: string,\n    operationOptions: T | undefined\n  ): { span: Span; updatedOptions: T } {\n    const tracer = getTracer();\n    const tracingOptions = operationOptions?.tracingOptions || {};\n    const spanOptions: SpanOptions = {\n      kind: SpanKind.INTERNAL,\n      ...tracingOptions.spanOptions\n    };\n\n    const spanName = args.packagePrefix ? `${args.packagePrefix}.${operationName}` : operationName;\n\n    let span: Span;\n    if (isTracingDisabled()) {\n      span = trace.wrapSpanContext(INVALID_SPAN_CONTEXT);\n    } else {\n      span = tracer.startSpan(spanName, spanOptions, tracingOptions.tracingContext);\n    }\n\n    if (args.namespace) {\n      span.setAttribute(\"az.namespace\", args.namespace);\n    }\n\n    let newSpanOptions = tracingOptions.spanOptions || {};\n\n    if (span.isRecording() && args.namespace) {\n      newSpanOptions = {\n        ...tracingOptions.spanOptions,\n        attributes: {\n          ...spanOptions.attributes,\n          \"az.namespace\": args.namespace\n        }\n      };\n    }\n\n    const newTracingOptions: Required<OperationTracingOptions> = {\n      ...tracingOptions,\n      spanOptions: newSpanOptions,\n      tracingContext: setSpan(tracingOptions.tracingContext || otContext.active(), span)\n    };\n\n    const newOperationOptions = {\n      ...operationOptions,\n      tracingOptions: newTracingOptions\n    } as T & { tracingOptions: Required<OperationTracingOptions> };\n\n    return {\n      span,\n      updatedOptions: newOperationOptions\n    };\n  };\n}\n"]}