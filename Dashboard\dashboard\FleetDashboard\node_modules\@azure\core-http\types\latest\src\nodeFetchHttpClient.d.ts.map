{"version": 3, "file": "nodeFetchHttpClient.d.ts", "sourceRoot": "", "sources": ["../../../src/nodeFetchHttpClient.ts"], "names": [], "mappings": ";;AAOA,OAAO,EAAe,eAAe,EAAE,MAAM,eAAe,CAAC;AAE7D,OAAO,EAAY,SAAS,EAAE,MAAM,QAAQ,CAAC;AAC7C,OAAO,EAAE,qBAAqB,EAAE,eAAe,EAAE,MAAM,eAAe,CAAC;AAEvE,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAuBhE;;GAEG;AACH,oBAAY,iBAAiB,GAAG,MAAM,CAAC;AAEvC;;GAEG;AACH,oBAAY,iBAAiB,GAAG,IAAI,CAAC,WAAW,EAAE,MAAM,GAAG,SAAS,GAAG,QAAQ,CAAC,GAAG;IACjF,IAAI,CAAC,EAAE,GAAG,CAAC;IACX,OAAO,CAAC,EAAE,GAAG,CAAC;IACd,MAAM,CAAC,EAAE,GAAG,CAAC;CACd,CAAC;AAEF;;GAEG;AACH,oBAAY,cAAc,GAAG,IAAI,CAAC,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG,UAAU,CAAC,GAAG;IAC7E,IAAI,EAAE,GAAG,CAAC;IACV,OAAO,EAAE,GAAG,CAAC;IACb,QAAQ,EAAE,GAAG,CAAC;CACf,CAAC;AAEF,qBAAa,eAAgB,SAAQ,SAAS;IAShC,OAAO,CAAC,gBAAgB;IARpC,OAAO,CAAC,WAAW,CAAa;IAChC,UAAU,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,IAAI,GAAG,IAAI;gBAOrE,gBAAgB,EAAE,CAAC,QAAQ,EAAE,qBAAqB,KAAK,IAAI;CAGhF;AAiBD;;GAEG;AACH,wBAAgB,YAAY,CAAC,OAAO,EAAE,OAAO,GAAG,eAAe,CAQ9D;AAED;;GAEG;AACH,qBAAa,mBAAoB,YAAW,UAAU;IACpD;;;;OAIG;IACG,WAAW,CAAC,WAAW,EAAE,eAAe,GAAG,OAAO,CAAC,qBAAqB,CAAC;IA4L/E,OAAO,CAAC,aAAa,CAAsC;IAC3D,OAAO,CAAC,eAAe,CAAkB;IAEzC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAuD;IAEjF,OAAO,CAAC,gBAAgB;IAqDxB;;OAEG;IAEG,KAAK,CAAC,KAAK,EAAE,iBAAiB,EAAE,IAAI,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,cAAc,CAAC;IAIxF;;OAEG;IACG,cAAc,CAAC,WAAW,EAAE,eAAe,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IAyBjF;;OAEG;IACG,cAAc,CAAC,iBAAiB,EAAE,qBAAqB,GAAG,OAAO,CAAC,IAAI,CAAC;CAqB9E"}