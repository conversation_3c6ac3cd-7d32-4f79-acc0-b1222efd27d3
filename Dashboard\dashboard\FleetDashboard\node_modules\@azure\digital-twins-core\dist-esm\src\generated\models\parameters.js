/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import { QueryCollectionFormat } from "@azure/core-http";
import { QuerySpecification as QuerySpecificationMapper, EventRoute as EventRouteMapper } from "../models/mappers";
export const contentType = {
    parameterPath: ["options", "contentType"],
    mapper: {
        defaultValue: "application/json",
        isConstant: true,
        serializedName: "Content-Type",
        type: {
            name: "String"
        }
    }
};
export const models = {
    parameterPath: ["options", "models"],
    mapper: {
        constraints: {
            MinItems: 1,
            UniqueItems: true
        },
        serializedName: "models",
        type: {
            name: "Sequence",
            element: { type: { name: "any" } }
        }
    }
};
export const $host = {
    parameterPath: "$host",
    mapper: {
        serializedName: "$host",
        required: true,
        type: {
            name: "String"
        }
    },
    skipEncoding: true
};
export const traceparent = {
    parameterPath: ["options", "traceparent"],
    mapper: {
        serializedName: "traceparent",
        type: {
            name: "String"
        }
    }
};
export const tracestate = {
    parameterPath: ["options", "tracestate"],
    mapper: {
        serializedName: "tracestate",
        type: {
            name: "String"
        }
    }
};
export const apiVersion = {
    parameterPath: "apiVersion",
    mapper: {
        defaultValue: "2022-05-31",
        isConstant: true,
        serializedName: "api-version",
        type: {
            name: "String"
        }
    }
};
export const dependenciesFor = {
    parameterPath: ["options", "dependenciesFor"],
    mapper: {
        serializedName: "dependenciesFor",
        type: {
            name: "Sequence",
            element: { type: { name: "String" } }
        }
    },
    collectionFormat: QueryCollectionFormat.Csv
};
export const includeModelDefinition = {
    parameterPath: ["options", "includeModelDefinition"],
    mapper: {
        serializedName: "includeModelDefinition",
        type: {
            name: "Boolean"
        }
    }
};
export const maxItemsPerPage = {
    parameterPath: ["options", "maxItemsPerPage"],
    mapper: {
        serializedName: "max-items-per-page",
        type: {
            name: "Number"
        }
    }
};
export const id = {
    parameterPath: "id",
    mapper: {
        serializedName: "id",
        required: true,
        type: {
            name: "String"
        }
    }
};
export const contentType1 = {
    parameterPath: ["options", "contentType"],
    mapper: {
        defaultValue: "application/json-patch+json",
        isConstant: true,
        serializedName: "Content-Type",
        type: {
            name: "String"
        }
    }
};
export const updateModel = {
    parameterPath: "updateModel",
    mapper: {
        serializedName: "updateModel",
        required: true,
        type: {
            name: "Sequence",
            element: { type: { name: "any" } }
        }
    }
};
export const nextLink = {
    parameterPath: "nextLink",
    mapper: {
        serializedName: "nextLink",
        required: true,
        type: {
            name: "String"
        }
    },
    skipEncoding: true
};
export const querySpecification = {
    parameterPath: "querySpecification",
    mapper: QuerySpecificationMapper
};
export const twin = {
    parameterPath: "twin",
    mapper: {
        serializedName: "twin",
        required: true,
        type: {
            name: "any"
        }
    }
};
export const ifNoneMatch = {
    parameterPath: ["options", "ifNoneMatch"],
    mapper: {
        serializedName: "If-None-Match",
        type: {
            name: "String"
        }
    }
};
export const ifMatch = {
    parameterPath: ["options", "ifMatch"],
    mapper: {
        serializedName: "If-Match",
        type: {
            name: "String"
        }
    }
};
export const patchDocument = {
    parameterPath: "patchDocument",
    mapper: {
        serializedName: "patchDocument",
        required: true,
        type: {
            name: "Sequence",
            element: { type: { name: "any" } }
        }
    }
};
export const relationshipId = {
    parameterPath: "relationshipId",
    mapper: {
        serializedName: "relationshipId",
        required: true,
        type: {
            name: "String"
        }
    }
};
export const relationship = {
    parameterPath: "relationship",
    mapper: {
        serializedName: "relationship",
        required: true,
        type: {
            name: "any"
        }
    }
};
export const relationshipName = {
    parameterPath: ["options", "relationshipName"],
    mapper: {
        serializedName: "relationshipName",
        type: {
            name: "String"
        }
    }
};
export const telemetry = {
    parameterPath: "telemetry",
    mapper: {
        serializedName: "telemetry",
        required: true,
        type: {
            name: "any"
        }
    }
};
export const messageId = {
    parameterPath: "messageId",
    mapper: {
        serializedName: "Message-Id",
        required: true,
        type: {
            name: "String"
        }
    }
};
export const telemetrySourceTime = {
    parameterPath: ["options", "telemetrySourceTime"],
    mapper: {
        serializedName: "Telemetry-Source-Time",
        type: {
            name: "String"
        }
    }
};
export const componentPath = {
    parameterPath: "componentPath",
    mapper: {
        serializedName: "componentPath",
        required: true,
        type: {
            name: "String"
        }
    }
};
export const eventRoute = {
    parameterPath: ["options", "eventRoute"],
    mapper: EventRouteMapper
};
//# sourceMappingURL=parameters.js.map