{"version": 3, "file": "decompressResponsePolicy.browser.js", "sourceRoot": "", "sources": ["../../../src/policies/decompressResponsePolicy.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;GAEG;AAEH,MAAM,YAAY,GAAG,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;AAEnG,MAAM,CAAC,MAAM,4BAA4B,GAAG,0BAA0B,CAAC;AAEvE;;;GAGG;AACH,MAAM,UAAU,wBAAwB;IACtC,MAAM,YAAY,CAAC;AACrB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/*\n * NOTE: When moving this file, please update \"browser\" section in package.json\n */\n\nconst NotSupported = new Error(\"decompressResponsePolicy is not supported in browser environment\");\n\nexport const decompressResponsePolicyName = \"decompressResponsePolicy\";\n\n/**\n * decompressResponsePolicy is not supported in the browser and attempting\n * to use it will raise an error.\n */\nexport function decompressResponsePolicy(): never {\n  throw NotSupported;\n}\n"]}