package com.ness.process;

import java.util.Date;

public class ProcessModel {

    int delayOccurences;
    boolean notificationSentToCustomer;
    String customerDecision;
    Date notificationSentDateTime;
    Date currentDateTime;

    public int getDelayOccurences() {
        return delayOccurences;
    }

    public void setDelayOccurences(int delayOccurences) {
        this.delayOccurences = delayOccurences;
    }

    public boolean isNotificationSentToCustomer() {
        return notificationSentToCustomer;
    }

    public void setNotificationSentToCustomer(boolean notificationSentToCustomer) {
        this.notificationSentToCustomer = notificationSentToCustomer;
    }

    public String getCustomerDecision() {
        return customerDecision;
    }

    public void setCustomerDecision(String customerDecision) {
        this.customerDecision = customerDecision;
    }

    public Date getNotificationSentDateTime() {
        return notificationSentDateTime;
    }

    public void setNotificationSentDateTime(Date notificationSentDateTime) {
        this.notificationSentDateTime = notificationSentDateTime;
    }

    public Date getCurrentDateTime() {
        return currentDateTime;
    }

    public void setCurrentDateTime(Date currentDateTime) {
        this.currentDateTime = currentDateTime;
    }

    public ProcessModel() {

        delayOccurences = 1;
        notificationSentToCustomer = false;
        customerDecision = "none";
        notificationSentDateTime = null;
        currentDateTime = null;
    }   

    public void increaseDelayOccurences() {

        delayOccurences++;
    }

    public void resetDelayOccurences() {

        delayOccurences = 0;
    }

    public void sendNotificationToCustomer() {

        notificationSentToCustomer = true;
        notificationSentDateTime = currentDateTime;
    }
} 