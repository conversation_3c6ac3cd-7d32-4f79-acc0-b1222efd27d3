{"version": 3, "file": "utf8.browser.js", "sourceRoot": "", "sources": ["../../src/utf8.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAalC,IAAI,OAAgC,CAAC;AAErC;;;GAGG;AACH,SAAS,cAAc;IACrB,IAAI,OAAO,EAAE;QACX,OAAO,OAAO,CAAC;KAChB;IAED,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;QACtC,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;KACvE;IAED,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;IAC5B,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,WAAW,CAAC,OAAe;IACzC,OAAO,cAAc,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC1C,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n// stubs for browser TextEncoder\ninterface TextEncoder {\n  encode(input?: string): Uint8Array;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare\ndeclare const TextEncoder: {\n  prototype: TextEncoder;\n  new (): TextEncoder;\n};\n\nlet encoder: TextEncoder | undefined;\n\n/**\n * Returns a cached TextEncoder.\n * @internal\n */\nfunction getTextEncoder(): TextEncoder {\n  if (encoder) {\n    return encoder;\n  }\n\n  if (typeof TextEncoder === \"undefined\") {\n    throw new Error(`Your browser environment is missing \"TextEncoder\".`);\n  }\n\n  encoder = new TextEncoder();\n  return encoder;\n}\n\n/**\n * Converts a utf8 string into a byte array.\n * @param content - The utf8 string to convert.\n * @internal\n */\nexport function utf8ToBytes(content: string): Uint8Array {\n  return getTextEncoder().encode(content);\n}\n"]}