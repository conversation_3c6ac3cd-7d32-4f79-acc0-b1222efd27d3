/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import * as coreHttp from "@azure/core-http";
import * as Mappers from "../models/mappers";
import * as Parameters from "../models/parameters";
/**
 * Class representing a Query.
 */
export class Query {
    /**
     * Initialize a new instance of the class Query class.
     * @param client Reference to the service client
     */
    constructor(client) {
        this.client = client;
    }
    /**
     * Executes a query that allows traversing relationships and filtering by property values.
     * Status codes:
     * * 200 OK
     * * 400 Bad Request
     *   * BadRequest - The continuation token is invalid.
     *   * SqlQueryError - The query contains some errors.
     * * 429 Too Many Requests
     *   * QuotaReachedError - The maximum query rate limit has been reached.
     * @param querySpecification The query specification to execute.
     * @param options The options parameters.
     */
    queryTwins(querySpecification, options) {
        const operationOptions = coreHttp.operationOptionsToRequestOptionsBase(options || {});
        return this.client.sendOperationRequest({ querySpecification, options: operationOptions }, queryTwinsOperationSpec);
    }
}
// Operation Specifications
const serializer = new coreHttp.Serializer(Mappers, /* isXml */ false);
const queryTwinsOperationSpec = {
    path: "/query",
    httpMethod: "POST",
    responses: {
        200: {
            bodyMapper: Mappers.QueryResult,
            headersMapper: Mappers.QueryQueryTwinsHeaders
        },
        default: {
            bodyMapper: Mappers.ErrorResponse
        }
    },
    requestBody: Parameters.querySpecification,
    queryParameters: [Parameters.apiVersion],
    urlParameters: [Parameters.$host],
    headerParameters: [
        Parameters.contentType,
        Parameters.traceparent,
        Parameters.tracestate,
        Parameters.maxItemsPerPage
    ],
    mediaType: "json",
    serializer
};
//# sourceMappingURL=query.js.map