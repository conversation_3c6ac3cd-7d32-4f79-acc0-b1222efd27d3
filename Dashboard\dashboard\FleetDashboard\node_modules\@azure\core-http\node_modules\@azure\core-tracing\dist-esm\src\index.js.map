{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,uBAAuB;AACvB,OAAO,EAAE,kBAAkB,EAA0B,MAAM,cAAc,CAAC;AAE1E,oBAAoB;AACpB,OAAO,EACL,OAAO,EAOP,OAAO,EACP,cAAc,EACd,SAAS,EAET,kBAAkB,EAGlB,OAAO,EACP,cAAc,EAKd,QAAQ,EAGR,cAAc,EAKf,MAAM,cAAc,CAAC;AAEtB,YAAY;AACZ,OAAO,EACL,uCAAuC,EACvC,oBAAoB,EACrB,MAAM,2BAA2B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n// Tracers and wrappers\nexport { createSpanFunction, CreateSpanFunctionArgs } from \"./createSpan\";\n\n// Shared interfaces\nexport {\n  context,\n  Context,\n  ContextAPI,\n  Exception,\n  ExceptionWithCode,\n  ExceptionWithMessage,\n  ExceptionWithName,\n  getSpan,\n  getSpanContext,\n  getTracer,\n  HrTime,\n  isSpanContextValid,\n  Link,\n  OperationTracingOptions,\n  setSpan,\n  setSpanContext,\n  Span,\n  SpanAttributes,\n  SpanAttributeValue,\n  SpanContext,\n  SpanKind,\n  SpanOptions,\n  SpanStatus,\n  SpanStatusCode,\n  TimeInput,\n  TraceFlags,\n  Tracer,\n  TraceState\n} from \"./interfaces\";\n\n// Utilities\nexport {\n  extractSpanContextFromTraceParentHeader,\n  getTraceParentHeader\n} from \"./utils/traceParentHeader\";\n"]}