{"version": 3, "file": "createPipelineFromOptions.js", "sourceRoot": "", "sources": ["../../src/createPipelineFromOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAoB,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACnE,OAAO,EAAY,mBAAmB,EAAE,MAAM,YAAY,CAAC;AAE3D,OAAO,EAAyB,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAClF,OAAO,EAA0B,eAAe,EAAE,MAAM,4BAA4B,CAAC;AAGrF,OAAO,EAAE,wBAAwB,EAAE,MAAM,qCAAqC,CAAC;AAC/E,OAAO,EAAE,kBAAkB,EAAE,MAAM,+BAA+B,CAAC;AACnE,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAC3D,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAC1C,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,wBAAwB,EAAE,MAAM,qCAAqC,CAAC;AAC/E,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AA0CzD;;;GAGG;AACH,MAAM,UAAU,yBAAyB,CAAC,OAAgC;IACxE,MAAM,QAAQ,GAAG,mBAAmB,EAAE,CAAC;IAEvC,IAAI,MAAM,EAAE;QACV,IAAI,OAAO,CAAC,UAAU,EAAE;YACtB,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;SACnD;QACD,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;QACtD,QAAQ,CAAC,SAAS,CAAC,wBAAwB,EAAE,CAAC,CAAC;KAChD;IAED,QAAQ,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC;IACrC,QAAQ,CAAC,SAAS,CAAC,eAAe,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAC9D,QAAQ,CAAC,SAAS,CAAC,wBAAwB,EAAE,CAAC,CAAC;IAC/C,QAAQ,CAAC,SAAS,CAAC,kBAAkB,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;IACjF,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC;IACrF,IAAI,MAAM,EAAE;QACV,+DAA+D;QAC/D,kDAAkD;QAClD,QAAQ,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC;KACtF;IACD,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC;IAE9E,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { LogPolicyOptions, logPolicy } from \"./policies/logPolicy\";\nimport { Pipeline, createEmptyPipeline } from \"./pipeline\";\nimport { PipelineRetryOptions, TlsSettings } from \"./interfaces\";\nimport { RedirectPolicyOptions, redirectPolicy } from \"./policies/redirectPolicy\";\nimport { UserAgentPolicyOptions, userAgentPolicy } from \"./policies/userAgentPolicy\";\n\nimport { ProxySettings } from \".\";\nimport { decompressResponsePolicy } from \"./policies/decompressResponsePolicy\";\nimport { defaultRetryPolicy } from \"./policies/defaultRetryPolicy\";\nimport { formDataPolicy } from \"./policies/formDataPolicy\";\nimport { isNode } from \"@azure/core-util\";\nimport { proxyPolicy } from \"./policies/proxyPolicy\";\nimport { setClientRequestIdPolicy } from \"./policies/setClientRequestIdPolicy\";\nimport { tlsPolicy } from \"./policies/tlsPolicy\";\nimport { tracingPolicy } from \"./policies/tracingPolicy\";\n\n/**\n * Defines options that are used to configure the HTTP pipeline for\n * an SDK client.\n */\nexport interface PipelineOptions {\n  /**\n   * Options that control how to retry failed requests.\n   */\n  retryOptions?: PipelineRetryOptions;\n\n  /**\n   * Options to configure a proxy for outgoing requests.\n   */\n  proxyOptions?: ProxySettings;\n\n  /** Options for configuring TLS authentication */\n  tlsOptions?: TlsSettings;\n\n  /**\n   * Options for how redirect responses are handled.\n   */\n  redirectOptions?: RedirectPolicyOptions;\n\n  /**\n   * Options for adding user agent details to outgoing requests.\n   */\n  userAgentOptions?: UserAgentPolicyOptions;\n}\n\n/**\n * Defines options that are used to configure internal options of\n * the HTTP pipeline for an SDK client.\n */\nexport interface InternalPipelineOptions extends PipelineOptions {\n  /**\n   * Options to configure request/response logging.\n   */\n  loggingOptions?: LogPolicyOptions;\n}\n\n/**\n * Create a new pipeline with a default set of customizable policies.\n * @param options - Options to configure a custom pipeline.\n */\nexport function createPipelineFromOptions(options: InternalPipelineOptions): Pipeline {\n  const pipeline = createEmptyPipeline();\n\n  if (isNode) {\n    if (options.tlsOptions) {\n      pipeline.addPolicy(tlsPolicy(options.tlsOptions));\n    }\n    pipeline.addPolicy(proxyPolicy(options.proxyOptions));\n    pipeline.addPolicy(decompressResponsePolicy());\n  }\n\n  pipeline.addPolicy(formDataPolicy());\n  pipeline.addPolicy(userAgentPolicy(options.userAgentOptions));\n  pipeline.addPolicy(setClientRequestIdPolicy());\n  pipeline.addPolicy(defaultRetryPolicy(options.retryOptions), { phase: \"Retry\" });\n  pipeline.addPolicy(tracingPolicy(options.userAgentOptions), { afterPhase: \"Retry\" });\n  if (isNode) {\n    // Both XHR and Fetch expect to handle redirects automatically,\n    // so only include this policy when we're in Node.\n    pipeline.addPolicy(redirectPolicy(options.redirectOptions), { afterPhase: \"Retry\" });\n  }\n  pipeline.addPolicy(logPolicy(options.loggingOptions), { afterPhase: \"Sign\" });\n\n  return pipeline;\n}\n"]}