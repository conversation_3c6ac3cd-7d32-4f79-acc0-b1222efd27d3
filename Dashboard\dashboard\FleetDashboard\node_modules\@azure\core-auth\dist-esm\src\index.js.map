{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,kBAAkB,EAAiB,MAAM,sBAAsB,CAAC;AACzE,OAAO,EACL,uBAAuB,EAEvB,oBAAoB,GACrB,MAAM,2BAA2B,CAAC;AACnC,OAAO,EAAE,kBAAkB,EAAiB,eAAe,EAAE,MAAM,sBAAsB,CAAC;AAE1F,OAAO,EAIL,iBAAiB,GAClB,MAAM,mBAAmB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nexport { AzureKeyCredential, KeyCredential } from \"./azureKeyCredential\";\nexport {\n  AzureNamedKeyCredential,\n  NamedKeyCredential,\n  isNamedKeyCredential,\n} from \"./azureNamedKeyCredential\";\nexport { AzureSASCredential, SASCredential, isSASCredential } from \"./azureSASCredential\";\n\nexport {\n  TokenCredential,\n  GetTokenOptions,\n  AccessToken,\n  isTokenCredential,\n} from \"./tokenCredential\";\n\nexport { TracingContext } from \"./tracing\";\n"]}