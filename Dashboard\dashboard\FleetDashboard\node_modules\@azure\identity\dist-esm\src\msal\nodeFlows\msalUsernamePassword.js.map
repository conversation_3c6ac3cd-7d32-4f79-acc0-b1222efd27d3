{"version": 3, "file": "msalUsernamePassword.js", "sourceRoot": "", "sources": ["../../../../src/msal/nodeFlows/msalUsernamePassword.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,QAAQ,EAAmB,MAAM,kBAAkB,CAAC;AAa7D;;;GAGG;AACH,MAAM,OAAO,oBAAqB,SAAQ,QAAQ;IAIhD,YAAY,OAAoC;QAC9C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;IACnC,CAAC;IAES,KAAK,CAAC,UAAU,CACxB,MAAgB,EAChB,OAAuC;QAEvC,IAAI;YACF,MAAM,cAAc,GAAqC;gBACvD,MAAM;gBACN,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,aAAa,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa;gBACrC,SAAS,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS;gBAC7B,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM;aACxB,CAAC;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAU,CAAC,8BAA8B,CAAC,cAAc,CAAC,CAAC;YACpF,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,CAAC;SACtE;QAAC,OAAO,KAAU,EAAE;YACnB,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAChD;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as msalNode from \"@azure/msal-node\";\nimport { MsalNode, MsalNodeOptions } from \"./msalNodeCommon\";\nimport { AccessToken } from \"@azure/core-auth\";\nimport { CredentialFlowGetTokenOptions } from \"../credentials\";\n\n/**\n * Options that can be passed to configure MSAL to handle authentication through username and password.\n * @internal\n */\nexport interface MsalUsernamePasswordOptions extends MsalNodeOptions {\n  username: string;\n  password: string;\n}\n\n/**\n * MSAL username and password client. Calls to the MSAL's public application's `acquireTokenByUsernamePassword` during `doGetToken`.\n * @internal\n */\nexport class MsalUsernamePassword extends MsalNode {\n  private username: string;\n  private password: string;\n\n  constructor(options: MsalUsernamePasswordOptions) {\n    super(options);\n    this.username = options.username;\n    this.password = options.password;\n  }\n\n  protected async doGetToken(\n    scopes: string[],\n    options?: CredentialFlowGetTokenOptions\n  ): Promise<AccessToken> {\n    try {\n      const requestOptions: msalNode.UsernamePasswordRequest = {\n        scopes,\n        username: this.username,\n        password: this.password,\n        correlationId: options?.correlationId,\n        authority: options?.authority,\n        claims: options?.claims,\n      };\n      const result = await this.publicApp!.acquireTokenByUsernamePassword(requestOptions);\n      return this.handleResult(scopes, this.clientId, result || undefined);\n    } catch (error: any) {\n      throw this.handleError(scopes, error, options);\n    }\n  }\n}\n"]}