{"version": 3, "file": "isNode.browser.js", "sourceRoot": "", "sources": ["../../src/isNode.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;GAEG;AACH,MAAM,CAAC,MAAM,MAAM,GAAG,KAAK,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * A constant that indicates whether the environment the code is running is Node.JS.\n */\nexport const isNode = false;\n"]}