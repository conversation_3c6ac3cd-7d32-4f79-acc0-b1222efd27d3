{"version": 3, "file": "keepAlivePolicy.d.ts", "sourceRoot": "", "sources": ["../../../../src/policies/keepAlivePolicy.ts"], "names": [], "mappings": "AAGA,OAAO,EACL,iBAAiB,EACjB,aAAa,EACb,oBAAoB,EACpB,oBAAoB,EACrB,MAAM,iBAAiB,CAAC;AACzB,OAAO,EAAE,qBAAqB,EAAE,MAAM,0BAA0B,CAAC;AACjE,OAAO,EAAE,eAAe,EAAE,MAAM,gBAAgB,CAAC;AAEjD;;;GAGG;AACH,MAAM,WAAW,gBAAgB;IAC/B;;;OAGG;IACH,MAAM,EAAE,OAAO,CAAC;CACjB;AAED;;GAEG;AACH,eAAO,MAAM,uBAAuB,EAAE,gBAErC,CAAC;AAEF;;;;GAIG;AACH,wBAAgB,eAAe,CAAC,gBAAgB,CAAC,EAAE,gBAAgB,GAAG,oBAAoB,CAMzF;AAED;;GAEG;AACH,qBAAa,eAAgB,SAAQ,iBAAiB;IAWlD,OAAO,CAAC,QAAQ,CAAC,gBAAgB;IAVnC;;;;;;OAMG;gBAED,UAAU,EAAE,aAAa,EACzB,OAAO,EAAE,oBAAoB,EACZ,gBAAgB,EAAE,gBAAgB;IAKrD;;;;;OAKG;IACU,WAAW,CAAC,OAAO,EAAE,eAAe,GAAG,OAAO,CAAC,qBAAqB,CAAC;CAInF"}