{"version": 3, "file": "userAgentPlatform.native.js", "sourceRoot": "", "sources": ["../../../src/util/userAgentPlatform.native.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;GAEG;AACH,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,+FAA+F;AAE7I;;GAEG;AACH,MAAM,UAAU,aAAa;IAC3B,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,uBAAuB,CAAC,GAAwB;IAC9D,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,QAAQ,CAAC,SAAS,CAAC,kBAAkB,CAAC;IACtE,GAAG,CAAC,GAAG,CAAC,cAAc,EAAE,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE,CAAC,CAAC;IACtD,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,QAAQ,CAAC,EAAE,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;AACtD,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/*\n * NOTE: When moving this file, please update \"react-native\" section in package.json.\n */\nconst { Platform } = require(\"react-native\"); // eslint-disable-line import/no-extraneous-dependencies, @typescript-eslint/no-require-imports\n\n/**\n * @internal\n */\nexport function getHeaderName(): string {\n  return \"x-ms-useragent\";\n}\n\n/**\n * @internal\n */\nexport function setPlatformSpecificData(map: Map<string, string>): void {\n  const { major, minor, patch } = Platform.constants.reactNativeVersion;\n  map.set(\"react-native\", `${major}.${minor}.${patch}`);\n  map.set(\"OS\", `${Platform.OS}-${Platform.Version}`);\n}\n"]}