{"version": 3, "file": "tracingPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/tracingPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAIL,mBAAmB,GACpB,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAG3C,OAAO,EAAE,iBAAiB,EAAE,MAAM,mBAAmB,CAAC;AACtD,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAChC,OAAO,EAAE,eAAe,EAAE,OAAO,EAAE,MAAM,kBAAkB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAE3C;;GAEG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG,eAAe,CAAC;AAcjD;;;;;GAKG;AACH,MAAM,UAAU,aAAa,CAAC,UAAgC,EAAE;IAC9D,MAAM,SAAS,GAAG,iBAAiB,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IAC7D,MAAM,aAAa,GAAG,sBAAsB,EAAE,CAAC;IAE/C,OAAO;QACL,IAAI,EAAE,iBAAiB;QACvB,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;;YAC3D,IAAI,CAAC,aAAa,IAAI,CAAC,CAAA,MAAA,OAAO,CAAC,cAAc,0CAAE,cAAc,CAAA,EAAE;gBAC7D,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;aACtB;YAED,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,MAAA,aAAa,CAAC,aAAa,EAAE,OAAO,EAAE,SAAS,CAAC,mCAAI,EAAE,CAAC;YAExF,IAAI,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE;gBAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;aACtB;YAED,IAAI;gBACF,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,WAAW,CAAC,cAAc,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;gBAChF,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACnC,OAAO,QAAQ,CAAC;aACjB;YAAC,OAAO,GAAQ,EAAE;gBACjB,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;gBAC3B,MAAM,GAAG,CAAC;aACX;QACH,CAAC;KACF,CAAC;AACJ,CAAC;AAED,SAAS,sBAAsB;IAC7B,IAAI;QACF,OAAO,mBAAmB,CAAC;YACzB,SAAS,EAAE,EAAE;YACb,WAAW,EAAE,2BAA2B;YACxC,cAAc,EAAE,WAAW;SAC5B,CAAC,CAAC;KACJ;IAAC,OAAO,CAAU,EAAE;QACnB,MAAM,CAAC,OAAO,CAAC,0CAA0C,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/E,OAAO,SAAS,CAAC;KAClB;AACH,CAAC;AAED,SAAS,aAAa,CACpB,aAA4B,EAC5B,OAAwB,EACxB,SAAkB;IAElB,IAAI;QACF,oFAAoF;QACpF,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,aAAa,CAAC,SAAS,CACtD,QAAQ,OAAO,CAAC,MAAM,EAAE,EACxB,EAAE,cAAc,EAAE,OAAO,CAAC,cAAc,EAAE,EAC1C;YACE,QAAQ,EAAE,QAAQ;YAClB,cAAc,EAAE;gBACd,aAAa,EAAE,OAAO,CAAC,MAAM;gBAC7B,UAAU,EAAE,OAAO,CAAC,GAAG;gBACvB,SAAS,EAAE,OAAO,CAAC,SAAS;aAC7B;SACF,CACF,CAAC;QAEF,wDAAwD;QACxD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YACvB,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,OAAO,SAAS,CAAC;SAClB;QAED,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC;SACjD;QAED,cAAc;QACd,MAAM,OAAO,GAAG,aAAa,CAAC,oBAAoB,CAChD,cAAc,CAAC,cAAc,CAAC,cAAc,CAC7C,CAAC;QACF,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAClD,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;SACjC;QACD,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE,cAAc,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;KAC/E;IAAC,OAAO,CAAM,EAAE;QACf,MAAM,CAAC,OAAO,CAAC,qDAAqD,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC1F,OAAO,SAAS,CAAC;KAClB;AACH,CAAC;AAED,SAAS,eAAe,CAAC,IAAiB,EAAE,KAAc;IACxD,IAAI;QACF,IAAI,CAAC,SAAS,CAAC;YACb,MAAM,EAAE,OAAO;YACf,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;SAC1C,CAAC,CAAC;QACH,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,UAAU,EAAE;YAC1C,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;SACzD;QACD,IAAI,CAAC,GAAG,EAAE,CAAC;KACZ;IAAC,OAAO,CAAM,EAAE;QACf,MAAM,CAAC,OAAO,CAAC,qDAAqD,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;KAC3F;AACH,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAiB,EAAE,QAA0B;IACvE,IAAI;QACF,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;QACvD,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACjE,IAAI,gBAAgB,EAAE;YACpB,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;SACzD;QACD,IAAI,CAAC,SAAS,CAAC;YACb,MAAM,EAAE,SAAS;SAClB,CAAC,CAAC;QACH,IAAI,CAAC,GAAG,EAAE,CAAC;KACZ;IAAC,OAAO,CAAM,EAAE;QACf,MAAM,CAAC,OAAO,CAAC,qDAAqD,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;KAC3F;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  TracingClient,\n  TracingContext,\n  TracingSpan,\n  createTracingClient,\n} from \"@azure/core-tracing\";\nimport { SDK_VERSION } from \"../constants\";\nimport { PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces\";\nimport { PipelinePolicy } from \"../pipeline\";\nimport { getUserAgentValue } from \"../util/userAgent\";\nimport { logger } from \"../log\";\nimport { getErrorMessage, isError } from \"@azure/core-util\";\nimport { isRestError } from \"../restError\";\n\n/**\n * The programmatic identifier of the tracingPolicy.\n */\nexport const tracingPolicyName = \"tracingPolicy\";\n\n/**\n * Options to configure the tracing policy.\n */\nexport interface TracingPolicyOptions {\n  /**\n   * String prefix to add to the user agent logged as metadata\n   * on the generated Span.\n   * Defaults to an empty string.\n   */\n  userAgentPrefix?: string;\n}\n\n/**\n * A simple policy to create OpenTelemetry Spans for each request made by the pipeline\n * that has SpanOptions with a parent.\n * Requests made without a parent Span will not be recorded.\n * @param options - Options to configure the telemetry logged by the tracing policy.\n */\nexport function tracingPolicy(options: TracingPolicyOptions = {}): PipelinePolicy {\n  const userAgent = getUserAgentValue(options.userAgentPrefix);\n  const tracingClient = tryCreateTracingClient();\n\n  return {\n    name: tracingPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      if (!tracingClient || !request.tracingOptions?.tracingContext) {\n        return next(request);\n      }\n\n      const { span, tracingContext } = tryCreateSpan(tracingClient, request, userAgent) ?? {};\n\n      if (!span || !tracingContext) {\n        return next(request);\n      }\n\n      try {\n        const response = await tracingClient.withContext(tracingContext, next, request);\n        tryProcessResponse(span, response);\n        return response;\n      } catch (err: any) {\n        tryProcessError(span, err);\n        throw err;\n      }\n    },\n  };\n}\n\nfunction tryCreateTracingClient(): TracingClient | undefined {\n  try {\n    return createTracingClient({\n      namespace: \"\",\n      packageName: \"@azure/core-rest-pipeline\",\n      packageVersion: SDK_VERSION,\n    });\n  } catch (e: unknown) {\n    logger.warning(`Error when creating the TracingClient: ${getErrorMessage(e)}`);\n    return undefined;\n  }\n}\n\nfunction tryCreateSpan(\n  tracingClient: TracingClient,\n  request: PipelineRequest,\n  userAgent?: string\n): { span: TracingSpan; tracingContext: TracingContext } | undefined {\n  try {\n    // As per spec, we do not need to differentiate between HTTP and HTTPS in span name.\n    const { span, updatedOptions } = tracingClient.startSpan(\n      `HTTP ${request.method}`,\n      { tracingOptions: request.tracingOptions },\n      {\n        spanKind: \"client\",\n        spanAttributes: {\n          \"http.method\": request.method,\n          \"http.url\": request.url,\n          requestId: request.requestId,\n        },\n      }\n    );\n\n    // If the span is not recording, don't do any more work.\n    if (!span.isRecording()) {\n      span.end();\n      return undefined;\n    }\n\n    if (userAgent) {\n      span.setAttribute(\"http.user_agent\", userAgent);\n    }\n\n    // set headers\n    const headers = tracingClient.createRequestHeaders(\n      updatedOptions.tracingOptions.tracingContext\n    );\n    for (const [key, value] of Object.entries(headers)) {\n      request.headers.set(key, value);\n    }\n    return { span, tracingContext: updatedOptions.tracingOptions.tracingContext };\n  } catch (e: any) {\n    logger.warning(`Skipping creating a tracing span due to an error: ${getErrorMessage(e)}`);\n    return undefined;\n  }\n}\n\nfunction tryProcessError(span: TracingSpan, error: unknown): void {\n  try {\n    span.setStatus({\n      status: \"error\",\n      error: isError(error) ? error : undefined,\n    });\n    if (isRestError(error) && error.statusCode) {\n      span.setAttribute(\"http.status_code\", error.statusCode);\n    }\n    span.end();\n  } catch (e: any) {\n    logger.warning(`Skipping tracing span processing due to an error: ${getErrorMessage(e)}`);\n  }\n}\n\nfunction tryProcessResponse(span: TracingSpan, response: PipelineResponse): void {\n  try {\n    span.setAttribute(\"http.status_code\", response.status);\n    const serviceRequestId = response.headers.get(\"x-ms-request-id\");\n    if (serviceRequestId) {\n      span.setAttribute(\"serviceRequestId\", serviceRequestId);\n    }\n    span.setStatus({\n      status: \"success\",\n    });\n    span.end();\n  } catch (e: any) {\n    logger.warning(`Skipping tracing span processing due to an error: ${getErrorMessage(e)}`);\n  }\n}\n"]}