{"version": 3, "file": "azureApplicationCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/azureApplicationCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { CredentialPersistenceOptions } from \"./credentialPersistenceOptions\";\nimport { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions\";\n\n/**\n * Provides options to configure the {@link AzureApplicationCredential} class.\n */\nexport interface AzureApplicationCredentialOptions\n  extends MultiTenantTokenCredentialOptions,\n    CredentialPersistenceOptions {\n  /**\n   * Optionally pass in a user assigned client ID to be used by the {@link ManagedIdentityCredential}.\n   * This client ID can also be passed through to the {@link ManagedIdentityCredential} through the environment variable: AZURE_CLIENT_ID.\n   */\n  managedIdentityClientId?: string;\n}\n"]}