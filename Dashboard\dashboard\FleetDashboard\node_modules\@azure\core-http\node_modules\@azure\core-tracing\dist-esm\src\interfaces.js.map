{"version": 3, "file": "interfaces.js", "sourceRoot": "", "sources": ["../../src/interfaces.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,OAAO,IAAI,SAAS,EAAE,KAAK,IAAI,OAAO,EAAE,MAAM,oBAAoB,CAAC;AAoF5E;;GAEG;AACH,MAAM,CAAN,IAAY,QAyBX;AAzBD,WAAY,QAAQ;IAClB,iEAAiE;IACjE,+CAAY,CAAA;IACZ;;;OAGG;IACH,2CAAU,CAAA;IACV;;;OAGG;IACH,2CAAU,CAAA;IACV;;;;OAIG;IACH,+CAAY,CAAA;IACZ;;;;OAIG;IACH,+CAAY,CAAA;AACd,CAAC,EAzBW,QAAQ,KAAR,QAAQ,QAyBnB;AAqDD;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,OAAgB;IACtC,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAClC,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,OAAO,CAAC,OAAgB,EAAE,IAAU;IAClD,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACxC,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,cAAc,CAAC,OAAgB,EAAE,WAAwB;IACvE,OAAO,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;AACtD,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,cAAc,CAAC,OAAgB;IAC7C,OAAO,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AACzC,CAAC;AAYD;;;;;;;GAOG;AACH,MAAM,UAAU,kBAAkB,CAAC,OAAoB;IACrD,OAAO,OAAO,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;AAC7C,CAAC;AAUD,MAAM,UAAU,SAAS,CAAC,IAAa,EAAE,OAAgB;IACvD,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,IAAI,oBAAoB,EAAE,OAAO,CAAC,CAAC;AAClE,CAAC;AAED,iCAAiC;AACjC,MAAM,CAAC,MAAM,OAAO,GAAe,SAAS,CAAC;AAE7C,qBAAqB;AACrB,MAAM,CAAN,IAAY,cAcX;AAdD,WAAY,cAAc;IACxB;;OAEG;IACH,qDAAS,CAAA;IACT;;;OAGG;IACH,+CAAM,CAAA;IACN;;OAEG;IACH,qDAAS,CAAA;AACX,CAAC,EAdW,cAAc,KAAd,cAAc,QAczB", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { context as otContext, trace as otTrace } from \"@opentelemetry/api\";\n\n/**\n * A Tracer.\n */\nexport interface Tracer {\n  /**\n   * Starts a new {@link Span}. Start the span without setting it on context.\n   *\n   * This method does NOT modify the current Context.\n   *\n   * @param name - The name of the span\n   * @param options - SpanOptions used for span creation\n   * @param context - Context to use to extract parent\n   * @returns The newly created span\n   * @example\n   *     const span = tracer.startSpan('op');\n   *     span.setAttribute('key', 'value');\n   *     span.end();\n   */\n  startSpan(name: string, options?: SpanOptions, context?: Context): Span;\n}\n\n/**\n * TraceState.\n */\nexport interface TraceState {\n  /**\n   * Create a new TraceState which inherits from this TraceState and has the\n   * given key set.\n   * The new entry will always be added in the front of the list of states.\n   *\n   * @param key - key of the TraceState entry.\n   * @param value - value of the TraceState entry.\n   */\n  set(key: string, value: string): TraceState;\n  /**\n   * Return a new TraceState which inherits from this TraceState but does not\n   * contain the given key.\n   *\n   * @param key - the key for the TraceState entry to be removed.\n   */\n  unset(key: string): TraceState;\n  /**\n   * Returns the value to which the specified key is mapped, or `undefined` if\n   * this map contains no mapping for the key.\n   *\n   * @param key - with which the specified value is to be associated.\n   * @returns the value to which the specified key is mapped, or `undefined` if\n   *     this map contains no mapping for the key.\n   */\n  get(key: string): string | undefined;\n  /**\n   * Serializes the TraceState to a `list` as defined below. The `list` is a\n   * series of `list-members` separated by commas `,`, and a list-member is a\n   * key/value pair separated by an equals sign `=`. Spaces and horizontal tabs\n   * surrounding `list-members` are ignored. There can be a maximum of 32\n   * `list-members` in a `list`.\n   *\n   * @returns the serialized string.\n   */\n  serialize(): string;\n}\n\n/**\n * Represents high resolution time.\n */\nexport declare type HrTime = [number, number];\n\n/**\n * Used to represent a Time.\n */\nexport type TimeInput = HrTime | number | Date;\n\n/**\n * The status for a span.\n */\nexport interface SpanStatus {\n  /** The status code of this message. */\n  code: SpanStatusCode;\n  /** A developer-facing error message. */\n  message?: string;\n}\n\n/**\n * The kind of span.\n */\nexport enum SpanKind {\n  /** Default value. Indicates that the span is used internally. */\n  INTERNAL = 0,\n  /**\n   * Indicates that the span covers server-side handling of an RPC or other\n   * remote request.\n   */\n  SERVER = 1,\n  /**\n   * Indicates that the span covers the client-side wrapper around an RPC or\n   * other remote request.\n   */\n  CLIENT = 2,\n  /**\n   * Indicates that the span describes producer sending a message to a\n   * broker. Unlike client and server, there is no direct critical path latency\n   * relationship between producer and consumer spans.\n   */\n  PRODUCER = 3,\n  /**\n   * Indicates that the span describes consumer receiving a message from a\n   * broker. Unlike client and server, there is no direct critical path latency\n   * relationship between producer and consumer spans.\n   */\n  CONSUMER = 4\n}\n\n/**\n * An Exception for a Span.\n */\nexport declare type Exception =\n  | ExceptionWithCode\n  | ExceptionWithMessage\n  | ExceptionWithName\n  | string;\n\n/**\n * An Exception with a code.\n */\nexport interface ExceptionWithCode {\n  /** The code. */\n  code: string | number;\n  /** The name. */\n  name?: string;\n  /** The message. */\n  message?: string;\n  /** The stack. */\n  stack?: string;\n}\n\n/**\n * An Exception with a message.\n */\nexport interface ExceptionWithMessage {\n  /** The code. */\n  code?: string | number;\n  /** The message. */\n  message: string;\n  /** The name. */\n  name?: string;\n  /** The stack. */\n  stack?: string;\n}\n\n/**\n * An Exception with a name.\n */\nexport interface ExceptionWithName {\n  /** The code. */\n  code?: string | number;\n  /** The message. */\n  message?: string;\n  /** The name. */\n  name: string;\n  /** The stack. */\n  stack?: string;\n}\n\n/**\n * Return the span if one exists\n *\n * @param context - context to get span from\n */\nexport function getSpan(context: Context): Span | undefined {\n  return otTrace.getSpan(context);\n}\n\n/**\n * Set the span on a context\n *\n * @param context - context to use as parent\n * @param span - span to set active\n */\nexport function setSpan(context: Context, span: Span): Context {\n  return otTrace.setSpan(context, span);\n}\n\n/**\n * Wrap span context in a NoopSpan and set as span in a new\n * context\n *\n * @param context - context to set active span on\n * @param spanContext - span context to be wrapped\n */\nexport function setSpanContext(context: Context, spanContext: SpanContext): Context {\n  return otTrace.setSpanContext(context, spanContext);\n}\n\n/**\n * Get the span context of the span if it exists.\n *\n * @param context - context to get values from\n */\nexport function getSpanContext(context: Context): SpanContext | undefined {\n  return otTrace.getSpanContext(context);\n}\n\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Context API\n */\nexport interface ContextAPI {\n  /**\n   * Get the currently active context\n   */\n  active(): Context;\n}\n\n/**\n * Returns true of the given {@link SpanContext} is valid.\n * A valid {@link SpanContext} is one which has a valid trace ID and span ID as per the spec.\n *\n * @param context - the {@link SpanContext} to validate.\n *\n * @returns true if the {@link SpanContext} is valid, false otherwise.\n */\nexport function isSpanContextValid(context: SpanContext): boolean {\n  return otTrace.isSpanContextValid(context);\n}\n\n/**\n * Retrieves a tracer from the global tracer provider.\n */\nexport function getTracer(): Tracer;\n/**\n * Retrieves a tracer from the global tracer provider.\n */\nexport function getTracer(name: string, version?: string): Tracer;\nexport function getTracer(name?: string, version?: string): Tracer {\n  return otTrace.getTracer(name || \"azure/core-tracing\", version);\n}\n\n/** Entrypoint for context API */\nexport const context: ContextAPI = otContext;\n\n/** SpanStatusCode */\nexport enum SpanStatusCode {\n  /**\n   * The default status.\n   */\n  UNSET = 0,\n  /**\n   * The operation has been validated by an Application developer or\n   * Operator to have completed successfully.\n   */\n  OK = 1,\n  /**\n   * The operation contains an error.\n   */\n  ERROR = 2\n}\n\n/**\n * An interface that represents a span. A span represents a single operation\n * within a trace. Examples of span might include remote procedure calls or a\n * in-process function calls to sub-components. A Trace has a single, top-level\n * \"root\" Span that in turn may have zero or more child Spans, which in turn\n * may have children.\n *\n * Spans are created by the {@link Tracer.startSpan} method.\n */\nexport interface Span {\n  /**\n   * Returns the {@link SpanContext} object associated with this Span.\n   *\n   * Get an immutable, serializable identifier for this span that can be used\n   * to create new child spans. Returned SpanContext is usable even after the\n   * span ends.\n   *\n   * @returns the SpanContext object associated with this Span.\n   */\n  spanContext(): SpanContext;\n  /**\n   * Sets an attribute to the span.\n   *\n   * Sets a single Attribute with the key and value passed as arguments.\n   *\n   * @param key - the key for this attribute.\n   * @param value - the value for this attribute. Setting a value null or\n   *              undefined is invalid and will result in undefined behavior.\n   */\n  setAttribute(key: string, value: SpanAttributeValue): this;\n  /**\n   * Sets attributes to the span.\n   *\n   * @param attributes - the attributes that will be added.\n   *                   null or undefined attribute values\n   *                   are invalid and will result in undefined behavior.\n   */\n  setAttributes(attributes: SpanAttributes): this;\n  /**\n   * Adds an event to the Span.\n   *\n   * @param name - the name of the event.\n   * @param attributesOrStartTime -  the attributes that will be added; these are\n   *     associated with this event. Can be also a start time\n   *     if type is TimeInput and 3rd param is undefined\n   * @param startTime - start time of the event.\n   */\n  addEvent(\n    name: string,\n    attributesOrStartTime?: SpanAttributes | TimeInput,\n    startTime?: TimeInput\n  ): this;\n  /**\n   * Sets a status to the span. If used, this will override the default Span\n   * status. Default is {@link SpanStatusCode.UNSET}. SetStatus overrides the value\n   * of previous calls to SetStatus on the Span.\n   *\n   * @param status - the SpanStatus to set.\n   */\n  setStatus(status: SpanStatus): this;\n  /**\n   * Marks the end of Span execution.\n   *\n   * Call to End of a Span MUST not have any effects on child spans. Those may\n   * still be running and can be ended later.\n   *\n   * Do not return `this`. The Span generally should not be used after it\n   * is ended so chaining is not desired in this context.\n   *\n   * @param endTime - the time to set as Span's end time. If not provided,\n   *     use the current time as the span's end time.\n   */\n  end(endTime?: TimeInput): void;\n  /**\n   * Returns the flag whether this span will be recorded.\n   *\n   * @returns true if this Span is active and recording information like events\n   *     with the `AddEvent` operation and attributes using `setAttributes`.\n   */\n  isRecording(): boolean;\n\n  /**\n   * Sets exception as a span event\n   * @param exception - the exception the only accepted values are string or Error\n   * @param time - the time to set as Span's event time. If not provided,\n   *     use the current time.\n   */\n  recordException(exception: Exception, time?: TimeInput): void;\n\n  /**\n   * Updates the Span name.\n   *\n   * This will override the name provided via {@link Tracer.startSpan}.\n   *\n   * Upon this update, any sampling behavior based on Span name will depend on\n   * the implementation.\n   *\n   * @param name - the Span name.\n   */\n  updateName(name: string): this;\n}\n\n/**\n * Shorthand enum for common traceFlags values inside SpanContext\n */\nexport const enum TraceFlags {\n  /** No flag set. */\n  NONE = 0x0,\n  /** Caller is collecting trace information. */\n  SAMPLED = 0x1\n}\n\n/**\n * A light interface that tries to be structurally compatible with OpenTelemetry\n */\nexport interface SpanContext {\n  /**\n   * UUID of a trace.\n   */\n  traceId: string;\n  /**\n   * UUID of a Span.\n   */\n  spanId: string;\n  /**\n   * https://www.w3.org/TR/trace-context/#trace-flags\n   */\n  traceFlags: number;\n  /**\n   * Tracing-system-specific info to propagate.\n   *\n   * The tracestate field value is a `list` as defined below. The `list` is a\n   * series of `list-members` separated by commas `,`, and a list-member is a\n   * key/value pair separated by an equals sign `=`. Spaces and horizontal tabs\n   * surrounding `list-members` are ignored. There can be a maximum of 32\n   * `list-members` in a `list`.\n   * More Info: https://www.w3.org/TR/trace-context/#tracestate-field\n   *\n   * Examples:\n   *     Single tracing system (generic format):\n   *         tracestate: rojo=00f067aa0ba902b7\n   *     Multiple tracing systems (with different formatting):\n   *         tracestate: rojo=00f067aa0ba902b7,congo=t61rcWkgMzE\n   */\n  traceState?: TraceState;\n}\n\n/**\n * Used to specify a span that is linked to another.\n */\nexport interface Link {\n  /** The {@link SpanContext} of a linked span. */\n  context: SpanContext;\n\n  /** A set of {@link SpanAttributes} on the link. */\n  attributes?: SpanAttributes;\n}\n\n/**\n * Attributes for a Span.\n */\nexport interface SpanAttributes {\n  /**\n   * Attributes for a Span.\n   */\n  [attributeKey: string]: SpanAttributeValue | undefined;\n}\n/**\n * Attribute values may be any non-nullish primitive value except an object.\n *\n * null or undefined attribute values are invalid and will result in undefined behavior.\n */\nexport declare type SpanAttributeValue =\n  | string\n  | number\n  | boolean\n  | Array<null | undefined | string>\n  | Array<null | undefined | number>\n  | Array<null | undefined | boolean>;\n\n/**\n * An interface that enables manual propagation of Spans\n */\nexport interface SpanOptions {\n  /**\n   * Attributes to set on the Span\n   */\n  attributes?: SpanAttributes;\n\n  /** {@link Link}s span to other spans */\n  links?: Link[];\n\n  /**\n   * The type of Span. Default to SpanKind.INTERNAL\n   */\n  kind?: SpanKind;\n\n  /**\n   * A manually specified start time for the created `Span` object.\n   */\n  startTime?: TimeInput;\n}\n\n/**\n * Tracing options to set on an operation.\n */\nexport interface OperationTracingOptions {\n  /**\n   * OpenTelemetry SpanOptions used to create a span when tracing is enabled.\n   */\n  spanOptions?: SpanOptions;\n\n  /**\n   * OpenTelemetry context to use for created Spans.\n   */\n  tracingContext?: Context;\n}\n\n/**\n * OpenTelemetry compatible interface for Context\n */\nexport interface Context {\n  /**\n   * Get a value from the context.\n   *\n   * @param key - key which identifies a context value\n   */\n  getValue(key: symbol): unknown;\n  /**\n   * Create a new context which inherits from this context and has\n   * the given key set to the given value.\n   *\n   * @param key - context key for which to set the value\n   * @param value - value to set for the given key\n   */\n  setValue(key: symbol, value: unknown): Context;\n  /**\n   * Return a new context which inherits from this context but does\n   * not contain a value for the given key.\n   *\n   * @param key - context key for which to clear a value\n   */\n  deleteValue(key: symbol): Context;\n}\n"]}